<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>向量化处理流程图</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 30px;
        }
        
        .title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            letter-spacing: -0.5px;
        }
        
        .subtitle {
            font-size: 16px;
            opacity: 0.9;
            font-weight: 400;
        }
        
        .flow-container {
            padding: 40px;
            background: white;
        }
        
        .flow-step {
            display: flex;
            align-items: flex-start;
            margin-bottom: 40px;
            position: relative;
        }
        
        .step-number {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: 700;
            margin-right: 30px;
            box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
            flex-shrink: 0;
            position: relative;
            z-index: 2;
        }
        
        .step-content {
            flex: 1;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 30px;
            border-radius: 15px;
            border-left: 4px solid #667eea;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
            position: relative;
        }
        
        .step-title {
            font-size: 22px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .step-description {
            color: #64748b;
            font-size: 15px;
            line-height: 1.7;
            margin-bottom: 20px;
        }
        
        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .tech-item {
            background: white;
            padding: 15px;
            border-radius: 10px;
            border: 2px solid #e2e8f0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }
        
        .tech-item:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
        }
        
        .tech-title {
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
        }
        
        .tech-desc {
            font-size: 12px;
            color: #64748b;
            line-height: 1.4;
        }
        
        /* 连接线 */
        .connection {
            position: absolute;
            left: 35px;
            top: 70px;
            width: 2px;
            height: 40px;
            background: linear-gradient(to bottom, #667eea, #764ba2);
            z-index: 1;
        }
        
        .connection::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: -6px;
            width: 0;
            height: 0;
            border-left: 7px solid transparent;
            border-right: 7px solid transparent;
            border-top: 10px solid #764ba2;
        }
        
        .flow-step:last-child .connection {
            display: none;
        }
        
        /* 特殊样式 */
        .step-upload {
            border-left-color: #f59e0b;
        }
        
        .step-parse {
            border-left-color: #3b82f6;
        }
        
        .step-split {
            border-left-color: #10b981;
        }
        
        .step-vectorize {
            border-left-color: #8b5cf6;
        }
        
        .step-store {
            border-left-color: #ec4899;
        }
        
        .step-index {
            border-left-color: #06b6d4;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .flow-step {
                flex-direction: column;
                align-items: center;
                text-align: center;
            }
            
            .step-number {
                margin-right: 0;
                margin-bottom: 20px;
            }
            
            .connection {
                left: 50%;
                transform: translateX(-50%);
            }
            
            .tech-grid {
                grid-template-columns: 1fr;
            }
        }
        
        .summary-box {
            margin-top: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            border-radius: 15px;
            border: 2px dashed #667eea;
        }
        
        .summary-title {
            color: #1e293b;
            margin-bottom: 20px;
            font-size: 20px;
            text-align: center;
            font-weight: 600;
        }
        
        .summary-content {
            color: #64748b;
            line-height: 1.6;
            text-align: center;
        }
        
        .highlight {
            color: #667eea;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">📊 向量化处理流程图</div>
            <div class="subtitle">Document Vectorization Processing Flow</div>
        </div>
        
        <div class="flow-container">
            <!-- 步骤1: 文档上传 -->
            <div class="flow-step">
                <div class="step-number">1</div>
                <div class="step-content step-upload">
                    <div class="step-title">📄 文档上传与验证</div>
                    <div class="step-description">
                        用户上传文档文件，系统验证文件格式和大小，生成唯一文件名并保存到本地存储。
                    </div>
                    <div class="tech-grid">
                        <div class="tech-item">
                            <div class="tech-title">支持格式</div>
                            <div class="tech-desc">PDF, DOCX, DOC, XLSX, XLS, TXT, MD, PPTX, PPT</div>
                        </div>
                        <div class="tech-item">
                            <div class="tech-title">文件验证</div>
                            <div class="tech-desc">格式检查、大小限制、安全扫描</div>
                        </div>
                        <div class="tech-item">
                            <div class="tech-title">存储管理</div>
                            <div class="tech-desc">唯一文件名生成、本地文件系统存储</div>
                        </div>
                    </div>
                </div>
                <div class="connection"></div>
            </div>
            
            <!-- 步骤2: 文档解析 -->
            <div class="flow-step">
                <div class="step-number">2</div>
                <div class="step-content step-parse">
                    <div class="step-title">🔍 文档内容解析</div>
                    <div class="step-description">
                        根据文件类型选择对应的处理器，提取文档中的文本内容、表格数据和结构信息。
                    </div>
                    <div class="tech-grid">
                        <div class="tech-item">
                            <div class="tech-title">PDF处理器</div>
                            <div class="tech-desc">PdfReader提取文本和页面信息</div>
                        </div>
                        <div class="tech-item">
                            <div class="tech-title">Word处理器</div>
                            <div class="tech-desc">DocxDocument提取段落和表格</div>
                        </div>
                        <div class="tech-item">
                            <div class="tech-title">Excel处理器</div>
                            <div class="tech-desc">openpyxl提取工作表数据</div>
                        </div>
                        <div class="tech-item">
                            <div class="tech-title">PowerPoint处理器</div>
                            <div class="tech-desc">python-pptx提取幻灯片内容</div>
                        </div>
                    </div>
                </div>
                <div class="connection"></div>
            </div>

            <!-- 步骤3: 智能分块 -->
            <div class="flow-step">
                <div class="step-number">3</div>
                <div class="step-content step-split">
                    <div class="step-title">✂️ 智能文本分块</div>
                    <div class="step-description">
                        使用AdvancedTextSplitter对提取的文本进行智能分块，保持语义完整性并设置重叠区域。
                    </div>
                    <div class="tech-grid">
                        <div class="tech-item">
                            <div class="tech-title">分块算法</div>
                            <div class="tech-desc">AdvancedTextSplitter智能分块器</div>
                        </div>
                        <div class="tech-item">
                            <div class="tech-title">分块参数</div>
                            <div class="tech-desc">chunk_size=1000, chunk_overlap=200</div>
                        </div>
                        <div class="tech-item">
                            <div class="tech-title">分隔符策略</div>
                            <div class="tech-desc">段落、句子、标点符号多级分隔</div>
                        </div>
                        <div class="tech-item">
                            <div class="tech-title">元数据保留</div>
                            <div class="tech-desc">分块索引、来源信息、处理方法</div>
                        </div>
                    </div>
                </div>
                <div class="connection"></div>
            </div>

            <!-- 步骤4: 向量化处理 -->
            <div class="flow-step">
                <div class="step-number">4</div>
                <div class="step-content step-vectorize">
                    <div class="step-title">🧮 文本向量化</div>
                    <div class="step-description">
                        调用SiliconFlow嵌入服务，使用BGE-M3模型将文本分块转换为1024维语义向量。
                    </div>
                    <div class="tech-grid">
                        <div class="tech-item">
                            <div class="tech-title">嵌入模型</div>
                            <div class="tech-desc">BAAI/bge-m3多语言模型</div>
                        </div>
                        <div class="tech-item">
                            <div class="tech-title">API服务</div>
                            <div class="tech-desc">SiliconFlow嵌入服务</div>
                        </div>
                        <div class="tech-item">
                            <div class="tech-title">向量维度</div>
                            <div class="tech-desc">1024维语义向量空间</div>
                        </div>
                        <div class="tech-item">
                            <div class="tech-title">文本清理</div>
                            <div class="tech-desc">控制字符过滤、长度限制</div>
                        </div>
                    </div>
                </div>
                <div class="connection"></div>
            </div>

            <!-- 步骤5: 向量存储 -->
            <div class="flow-step">
                <div class="step-number">5</div>
                <div class="step-content step-store">
                    <div class="step-title">💾 向量数据存储</div>
                    <div class="step-description">
                        将生成的向量和元数据存储到Redis数据库中，建立分块与向量的映射关系。
                    </div>
                    <div class="tech-grid">
                        <div class="tech-item">
                            <div class="tech-title">存储引擎</div>
                            <div class="tech-desc">Redis内存数据库</div>
                        </div>
                        <div class="tech-item">
                            <div class="tech-title">数据结构</div>
                            <div class="tech-desc">Hash存储分块数据和向量</div>
                        </div>
                        <div class="tech-item">
                            <div class="tech-title">键值设计</div>
                            <div class="tech-desc">kb_id_doc_id_chunk_id格式</div>
                        </div>
                        <div class="tech-item">
                            <div class="tech-title">元数据保存</div>
                            <div class="tech-desc">文档ID、分块ID、内容、元数据</div>
                        </div>
                    </div>
                </div>
                <div class="connection"></div>
            </div>

            <!-- 步骤6: 索引构建 -->
            <div class="flow-step">
                <div class="step-number">6</div>
                <div class="step-content step-index">
                    <div class="step-title">🔗 知识库索引构建</div>
                    <div class="step-description">
                        将分块键添加到知识库索引中，建立知识库级别的向量索引，支持后续的语义检索。
                    </div>
                    <div class="tech-grid">
                        <div class="tech-item">
                            <div class="tech-title">索引结构</div>
                            <div class="tech-desc">知识库级别的Set集合索引</div>
                        </div>
                        <div class="tech-item">
                            <div class="tech-title">检索算法</div>
                            <div class="tech-desc">余弦相似度计算</div>
                        </div>
                        <div class="tech-item">
                            <div class="tech-title">相似度阈值</div>
                            <div class="tech-desc">默认0.4，支持动态调节</div>
                        </div>
                        <div class="tech-item">
                            <div class="tech-title">索引管理</div>
                            <div class="tech-desc">支持增删改查操作</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 流程总结 -->
            <div class="summary-box">
                <h3 class="summary-title">🎯 向量化处理流程特点</h3>
                <div class="summary-content">
                    系统采用<span class="highlight">6步完整流程</span>实现文档向量化处理：从文档上传验证到最终的知识库索引构建。
                    使用<span class="highlight">BGE-M3模型</span>生成1024维语义向量，通过<span class="highlight">Redis数据库</span>存储向量数据，
                    支持<span class="highlight">余弦相似度</span>语义检索。整个流程确保了文档内容的完整性和检索的准确性，
                    为RAG系统提供了可靠的知识库基础。
                </div>
            </div>
        </div>
    </div>
</body>
</html>
