"""
认证相关API
"""
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status
from sqlmodel import Session, select
from app.core.database import SessionDep
from app.core.security import verify_password, get_password_hash, create_access_token
from app.core.auth import get_current_user
from app.config import settings
from app.models.user import User, UserQuota
from app.schemas.user import UserCreate, UserLogin, Token, UserResponse

router = APIRouter()


@router.post("/register", response_model=UserResponse)
async def register(user_data: UserCreate, session: SessionDep):
    """用户注册"""
    # 检查用户名是否已存在
    statement = select(User).where(User.username == user_data.username)
    existing_user = session.exec(statement).first()
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在"
        )
    
    # 检查邮箱是否已存在
    statement = select(User).where(User.email == user_data.email)
    existing_email = session.exec(statement).first()
    if existing_email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已存在"
        )
    
    # 创建新用户
    hashed_password = get_password_hash(user_data.password)
    new_user = User(
        username=user_data.username,
        email=user_data.email,
        password_hash=hashed_password,
        display_name=user_data.display_name,
        bio=user_data.bio
    )
    
    session.add(new_user)
    session.commit()
    session.refresh(new_user)
    
    # 获取系统默认设置
    from app.models.system import SystemSetting
    from app.models.user import UserSettings

    # 获取默认存储配额
    default_storage_setting = session.exec(
        select(SystemSetting).where(SystemSetting.key == "default_storage_quota")
    ).first()
    default_storage_mb = int(default_storage_setting.value) if default_storage_setting else 1024

    # 获取默认模型设置
    default_model_setting = session.exec(
        select(SystemSetting).where(SystemSetting.key == "default_model_id")
    ).first()
    default_model_id = int(default_model_setting.value) if default_model_setting and default_model_setting.value else None

    # 创建用户配额（应用系统默认设置）
    user_quota = UserQuota(
        user_id=new_user.id,
        max_storage_mb=default_storage_mb
    )
    session.add(user_quota)

    # 创建用户设置（应用系统默认设置）
    user_settings = UserSettings(
        user_id=new_user.id,
        default_model_id=default_model_id
    )
    session.add(user_settings)

    session.commit()

    return new_user


@router.post("/login", response_model=Token)
async def login(user_credentials: UserLogin, session: SessionDep):
    """用户登录"""
    import logging
    logger = logging.getLogger(__name__)

    logger.info(f"🔐 登录请求: 用户名={user_credentials.username}")
    logger.info(f"🔑 登录请求: 密码长度={len(user_credentials.password)}")

    try:
        # 查找用户
        statement = select(User).where(User.username == user_credentials.username)
        user = session.exec(statement).first()

        logger.info(f"🔍 数据库查询结果: 找到用户={user.username if user else 'None'}")
        if user:
            logger.info(f"📋 找到的用户信息: ID={user.id}, 用户名={user.username}, is_admin={user.is_admin}")

        if not user:
            logger.warning(f"❌ 用户不存在: {user_credentials.username}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误",
                headers={"WWW-Authenticate": "Bearer"},
            )

        logger.info(f"✅ 找到用户: {user.username} (ID: {user.id}, 状态: {user.status})")

        # 验证密码
        password_valid = verify_password(user_credentials.password, user.password_hash)
        logger.info(f"🔑 密码验证结果: {password_valid}")

        if not password_valid:
            logger.warning(f"❌ 密码错误: {user_credentials.username}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误",
                headers={"WWW-Authenticate": "Bearer"},
            )

        if user.status != "active":
            logger.warning(f"❌ 用户状态不是active: {user.status}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="用户账户已被禁用"
            )

        # 更新最后登录时间
        user.last_login_at = datetime.utcnow()
        session.add(user)
        session.commit()
        logger.info(f"✅ 更新登录时间成功")

        # 创建访问令牌 - 使用配置文件中的过期时间
        access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
        logger.info(f"🔑 为用户创建Token: ID={user.id}, 用户名={user.username}, is_admin={user.is_admin}")
        access_token = create_access_token(
            data={"sub": str(user.id)}, expires_delta=access_token_expires
        )
        logger.info(f"✅ Token创建成功: {access_token[:20]}...")
        logger.info(f"🎯 Token包含的用户ID: {user.id}")

        response_data = {"access_token": access_token, "token_type": "bearer"}
        logger.info(f"✅ 登录成功，返回响应")

        return response_data

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"❌ 登录过程中发生异常: {e}")
        import traceback
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"登录过程中发生错误: {str(e)}"
        )


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """获取当前用户信息"""
    return current_user


@router.post("/logout")
async def logout(current_user: User = Depends(get_current_user)):
    """用户登出"""
    import logging
    logger = logging.getLogger(__name__)

    logger.info(f"🚪 用户登出: {current_user.username} (ID: {current_user.id})")

    # 由于使用JWT，token是无状态的，这里主要是记录日志
    # 实际的token失效由前端处理

    return {"message": "登出成功"}


@router.get("/quota")
async def get_user_quota(
    current_user: User = Depends(get_current_user),
    session: SessionDep = SessionDep
):
    """获取当前用户配额信息"""
    from app.models.user import UserQuota
    from app.models.knowledge_base import KnowledgeBase, Document
    from sqlmodel import select, func

    # 获取用户配额
    quota_statement = select(UserQuota).where(UserQuota.user_id == current_user.id)
    user_quota = session.exec(quota_statement).first()

    # 如果没有配额记录，创建默认配额
    if not user_quota:
        user_quota = UserQuota(
            user_id=current_user.id,
            max_kbs=5,
            max_docs_per_kb=100,
            max_storage_mb=1024
        )
        session.add(user_quota)
        session.commit()
        session.refresh(user_quota)

    # 计算当前使用量
    # 知识库数量
    kb_count_statement = select(func.count(KnowledgeBase.id)).where(
        KnowledgeBase.owner_id == current_user.id
    )
    current_kbs = session.exec(kb_count_statement).one()

    # 存储使用量
    storage_statement = select(func.sum(Document.file_size)).join(KnowledgeBase).where(
        KnowledgeBase.owner_id == current_user.id
    )
    current_storage_bytes = session.exec(storage_statement).one() or 0
    current_storage_mb = current_storage_bytes / (1024 * 1024)

    return {
        "max_kbs": user_quota.max_kbs,
        "max_docs_per_kb": user_quota.max_docs_per_kb,
        "max_storage_mb": user_quota.max_storage_mb,
        "current_kbs": current_kbs,
        "current_storage_mb": round(current_storage_mb, 2)
    }
