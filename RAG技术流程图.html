<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAG技术架构流程图</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 30px;
        }
        
        .title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            letter-spacing: -0.5px;
        }
        
        .subtitle {
            font-size: 16px;
            opacity: 0.9;
            font-weight: 400;
        }
        
        .flow-container {
            padding: 40px;
            background: white;
        }
        
        .flow-step {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            position: relative;
        }
        
        .step-number {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: 700;
            margin-right: 30px;
            box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
            flex-shrink: 0;
        }
        
        .step-content {
            flex: 1;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 25px;
            border-radius: 12px;
            border-left: 4px solid #667eea;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        .step-title {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 10px;
        }
        
        .step-description {
            color: #64748b;
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .tech-details {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .tech-tag {
            background: #667eea;
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .arrow {
            position: absolute;
            left: 30px;
            bottom: -15px;
            width: 0;
            height: 0;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-top: 15px solid #667eea;
            z-index: 10;
        }
        
        .flow-step:last-child .arrow {
            display: none;
        }
        
        /* 特殊样式 */
        .step-input {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border-left-color: #f59e0b;
        }
        
        .step-process {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            border-left-color: #3b82f6;
        }
        
        .step-storage {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            border-left-color: #10b981;
        }
        
        .step-retrieval {
            background: linear-gradient(135deg, #fce7f3 0%, #fbcfe8 100%);
            border-left-color: #ec4899;
        }
        
        .step-generation {
            background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
            border-left-color: #6366f1;
        }
        
        .step-output {
            background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
            border-left-color: #8b5cf6;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .flow-step {
                flex-direction: column;
                text-align: center;
            }
            
            .step-number {
                margin-right: 0;
                margin-bottom: 20px;
            }
            
            .arrow {
                left: 50%;
                transform: translateX(-50%);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">📊 RAG技术架构流程图</div>
            <div class="subtitle">RAG Technology Architecture Flow Chart</div>
        </div>
        
        <div class="flow-container">
            <!-- 步骤1: 文档输入 -->
            <div class="flow-step">
                <div class="step-number">1</div>
                <div class="step-content step-input">
                    <div class="step-title">📄 文档输入与预处理</div>
                    <div class="step-description">
                        用户上传各种格式的文档，系统进行智能预处理，确保文档内容的完整性和可读性。
                    </div>
                    <div class="tech-details">
                        <div class="tech-tag">多格式解析引擎</div>
                        <div class="tech-tag">智能分块算法</div>
                        <div class="tech-tag">语义边界识别</div>
                    </div>
                </div>
                <div class="arrow"></div>
            </div>
            
            <!-- 步骤2: 文本向量化 -->
            <div class="flow-step">
                <div class="step-number">2</div>
                <div class="step-content step-process">
                    <div class="step-title">🔢 文本向量化处理</div>
                    <div class="step-description">
                        将预处理后的文本片段转换为高维语义向量，捕获文本的深层语义信息。
                    </div>
                    <div class="tech-details">
                        <div class="tech-tag">BGE-M3模型</div>
                        <div class="tech-tag">SiliconFlow API</div>
                        <div class="tech-tag">1024维语义向量空间</div>
                    </div>
                </div>
                <div class="arrow"></div>
            </div>
            
            <!-- 步骤3: 向量存储 -->
            <div class="flow-step">
                <div class="step-number">3</div>
                <div class="step-content step-storage">
                    <div class="step-title">💾 向量存储与索引</div>
                    <div class="step-description">
                        将生成的向量存储到高效的向量数据库中，建立索引以支持快速检索。
                    </div>
                    <div class="tech-details">
                        <div class="tech-tag">FAISS向量库</div>
                        <div class="tech-tag">IVF索引</div>
                        <div class="tech-tag">量化压缩</div>
                        <div class="tech-tag">高效检索</div>
                    </div>
                </div>
                <div class="arrow"></div>
            </div>
            
            <!-- 步骤4: 用户查询 -->
            <div class="flow-step">
                <div class="step-number">4</div>
                <div class="step-content step-input">
                    <div class="step-title">❓ 用户查询处理</div>
                    <div class="step-description">
                        接收用户问题，进行同样的向量化处理，准备进行语义匹配检索。
                    </div>
                    <div class="tech-details">
                        <div class="tech-tag">查询向量化</div>
                        <div class="tech-tag">语义理解</div>
                        <div class="tech-tag">意图识别</div>
                    </div>
                </div>
                <div class="arrow"></div>
            </div>

            <!-- 步骤5: 智能检索 -->
            <div class="flow-step">
                <div class="step-number">5</div>
                <div class="step-content step-retrieval">
                    <div class="step-title">🎯 智能检索匹配</div>
                    <div class="step-description">
                        使用RAG检索引擎，通过余弦相似度计算找到与用户查询最相关的文档片段。
                    </div>
                    <div class="tech-details">
                        <div class="tech-tag">FAISS向量检索</div>
                        <div class="tech-tag">余弦相似度匹配</div>
                        <div class="tech-tag">语义相似度阈值动态调节</div>
                        <div class="tech-tag">相关性排序</div>
                    </div>
                </div>
                <div class="arrow"></div>
            </div>

            <!-- 步骤6: 上下文构建 -->
            <div class="flow-step">
                <div class="step-number">6</div>
                <div class="step-content step-process">
                    <div class="step-title">🔗 上下文构建</div>
                    <div class="step-description">
                        将检索到的相关文档片段与用户查询组合，构建完整的上下文信息。
                    </div>
                    <div class="tech-details">
                        <div class="tech-tag">上下文组装</div>
                        <div class="tech-tag">信息融合</div>
                        <div class="tech-tag">提示词工程</div>
                    </div>
                </div>
                <div class="arrow"></div>
            </div>

            <!-- 步骤7: AI模型生成 -->
            <div class="flow-step">
                <div class="step-number">7</div>
                <div class="step-content step-generation">
                    <div class="step-title">🤖 AI模型生成</div>
                    <div class="step-description">
                        基于构建的上下文，调用大语言模型生成准确、相关的智能回答。
                    </div>
                    <div class="tech-details">
                        <div class="tech-tag">OpenAI GPT</div>
                        <div class="tech-tag">Claude</div>
                        <div class="tech-tag">DeepSeek</div>
                        <div class="tech-tag">Qwen</div>
                        <div class="tech-tag">SiliconFlow</div>
                        <div class="tech-tag">通义千问</div>
                    </div>
                </div>
                <div class="arrow"></div>
            </div>

            <!-- 步骤8: 流式输出 -->
            <div class="flow-step">
                <div class="step-number">8</div>
                <div class="step-content step-output">
                    <div class="step-title">📤 流式输出响应</div>
                    <div class="step-description">
                        将生成的回答通过流式输出技术实时推送给用户，提供流畅的对话体验。
                    </div>
                    <div class="tech-details">
                        <div class="tech-tag">SSE实时响应</div>
                        <div class="tech-tag">增量推送</div>
                        <div class="tech-tag">实时渲染</div>
                    </div>
                </div>
            </div>

            <!-- 流程总结 -->
            <div style="margin-top: 40px; padding: 30px; background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%); border-radius: 12px; border: 2px dashed #667eea;">
                <h3 style="color: #1e293b; margin-bottom: 15px; font-size: 18px;">🔄 RAG技术流程总结</h3>
                <p style="color: #64748b; line-height: 1.6; margin-bottom: 15px;">
                    RAG技术通过"检索-增强-生成"的核心流程，实现了基于知识库的智能问答。
                    从文档预处理到向量化存储，再到智能检索和模型生成，每个环节都采用了先进的AI技术，
                    确保回答的准确性和相关性。
                </p>
                <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                    <span style="background: #667eea; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">知识增强</span>
                    <span style="background: #667eea; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">语义理解</span>
                    <span style="background: #667eea; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">实时响应</span>
                    <span style="background: #667eea; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">多模型支持</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
