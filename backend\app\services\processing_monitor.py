"""
文档处理监控服务
"""
import time
import asyncio
from typing import Dict, List
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

@dataclass
class ProcessingStats:
    """处理统计信息"""
    total_files: int = 0
    completed_files: int = 0
    failed_files: int = 0
    processing_files: int = 0
    queue_length: int = 0
    avg_processing_time: float = 0.0
    last_updated: datetime = field(default_factory=datetime.now)

@dataclass
class FileProcessingInfo:
    """文件处理信息"""
    file_id: int
    filename: str
    file_size_mb: float
    category: str
    start_time: datetime
    status: str = "processing"
    end_time: datetime = None

class ProcessingMonitor:
    """文档处理监控器"""
    
    def __init__(self):
        self.processing_files: Dict[int, FileProcessingInfo] = {}
        self.completed_files: List[FileProcessingInfo] = []
        self.stats = ProcessingStats()
        self._lock = asyncio.Lock()
    
    async def start_processing(self, file_id: int, filename: str, file_size_mb: float, category: str):
        """开始处理文件"""
        async with self._lock:
            info = FileProcessingInfo(
                file_id=file_id,
                filename=filename,
                file_size_mb=file_size_mb,
                category=category,
                start_time=datetime.now()
            )
            self.processing_files[file_id] = info
            self.stats.processing_files += 1
            self.stats.total_files += 1
            logger.info(f"开始处理文件: {filename} ({file_size_mb:.1f}MB, {category})")
    
    async def complete_processing(self, file_id: int, success: bool = True):
        """完成文件处理"""
        async with self._lock:
            if file_id in self.processing_files:
                info = self.processing_files.pop(file_id)
                info.end_time = datetime.now()
                info.status = "completed" if success else "failed"
                
                # 计算处理时间
                processing_time = (info.end_time - info.start_time).total_seconds()
                
                # 更新统计
                self.stats.processing_files -= 1
                if success:
                    self.stats.completed_files += 1
                else:
                    self.stats.failed_files += 1
                
                # 更新平均处理时间
                self._update_avg_processing_time(processing_time)
                
                # 保存到历史记录（只保留最近100个）
                self.completed_files.append(info)
                if len(self.completed_files) > 100:
                    self.completed_files.pop(0)
                
                logger.info(f"完成处理文件: {info.filename}, 耗时: {processing_time:.1f}秒, 状态: {info.status}")
    
    def _update_avg_processing_time(self, new_time: float):
        """更新平均处理时间"""
        if self.stats.completed_files + self.stats.failed_files == 1:
            self.stats.avg_processing_time = new_time
        else:
            # 使用移动平均
            total_completed = self.stats.completed_files + self.stats.failed_files
            self.stats.avg_processing_time = (
                (self.stats.avg_processing_time * (total_completed - 1) + new_time) / total_completed
            )
    
    async def get_current_stats(self) -> Dict:
        """获取当前统计信息"""
        async with self._lock:
            # 按类别统计正在处理的文件
            category_stats = {"large": 0, "medium": 0, "small": 0}
            for info in self.processing_files.values():
                category_stats[info.category] += 1
            
            return {
                "total_files": self.stats.total_files,
                "completed_files": self.stats.completed_files,
                "failed_files": self.stats.failed_files,
                "processing_files": self.stats.processing_files,
                "avg_processing_time": round(self.stats.avg_processing_time, 2),
                "success_rate": round(
                    (self.stats.completed_files / max(1, self.stats.completed_files + self.stats.failed_files)) * 100, 2
                ),
                "category_processing": category_stats,
                "processing_details": [
                    {
                        "file_id": info.file_id,
                        "filename": info.filename,
                        "file_size_mb": info.file_size_mb,
                        "category": info.category,
                        "processing_time": (datetime.now() - info.start_time).total_seconds()
                    }
                    for info in self.processing_files.values()
                ],
                "last_updated": datetime.now().isoformat()
            }
    
    async def get_queue_status(self) -> Dict:
        """获取队列状态"""
        async with self._lock:
            return {
                "large_files_processing": sum(1 for info in self.processing_files.values() if info.category == "large"),
                "medium_files_processing": sum(1 for info in self.processing_files.values() if info.category == "medium"),
                "small_files_processing": sum(1 for info in self.processing_files.values() if info.category == "small"),
                "total_processing": len(self.processing_files)
            }

# 全局监控实例
_monitor = None

def get_processing_monitor() -> ProcessingMonitor:
    """获取处理监控实例"""
    global _monitor
    if _monitor is None:
        _monitor = ProcessingMonitor()
    return _monitor
