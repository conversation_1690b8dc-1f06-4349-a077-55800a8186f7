<template>
  <div class="demo-container">
    <div class="header">
      <h1 class="title">图表与HTML预览演示</h1>
      <p class="subtitle">展示ECharts图表和HTML内容在聊天中的渲染效果</p>
    </div>

    <div class="demo-sections">
      <!-- ECharts演示 -->
      <section class="demo-section">
        <h2 class="section-title">📊 ECharts图表演示</h2>
        <div class="demo-grid">
          <div class="demo-item">
            <h3>柱状图</h3>
            <ChartRenderer :chart-data="barChartData" />
          </div>
          <div class="demo-item">
            <h3>折线图</h3>
            <ChartRenderer :chart-data="lineChartData" />
          </div>
          <div class="demo-item">
            <h3>饼图</h3>
            <ChartRenderer :chart-data="pieChartData" />
          </div>
        </div>
      </section>

      <!-- HTML预览演示 -->
      <section class="demo-section">
        <h2 class="section-title">🌐 HTML预览演示</h2>
        <div class="demo-grid">
          <div class="demo-item">
            <h3>简单卡片</h3>
            <HtmlPreview :html-content="cardHtml" height="200px" />
          </div>
          <div class="demo-item">
            <h3>表格展示</h3>
            <HtmlPreview :html-content="tableHtml" height="250px" />
          </div>
          <div class="demo-item">
            <h3>表单示例</h3>
            <HtmlPreview :html-content="formHtml" height="300px" />
          </div>
        </div>
      </section>

      <!-- 代码示例 -->
      <section class="demo-section">
        <h2 class="section-title">💻 代码示例</h2>
        <div class="code-examples">
          <div class="code-example">
            <h3>ECharts配置示例</h3>
            <pre><code>```javascript
option = {
  title: { text: '销售数据' },
  xAxis: { data: ['1月', '2月', '3月', '4月'] },
  yAxis: {},
  series: [{
    name: '销售额',
    type: 'bar',
    data: [120, 200, 150, 80]
  }]
}
```</code></pre>
          </div>
          <div class="code-example">
            <h3>HTML片段示例</h3>
            <pre><code>```html
&lt;div class="alert"&gt;
  &lt;h4&gt;提示&lt;/h4&gt;
  &lt;p&gt;这是一个重要提示信息&lt;/p&gt;
&lt;/div&gt;
&lt;style&gt;
.alert {
  background: #f0f9ff;
  border: 1px solid #0ea5e9;
  border-radius: 6px;
  padding: 16px;
}
&lt;/style&gt;
```</code></pre>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ChartRenderer from '@/components/ChartRenderer.vue'
import HtmlPreview from '@/components/HtmlPreview.vue'

// 图表数据
const barChartData = ref({
  type: 'bar',
  title: '月度销售数据',
  data: {
    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
    datasets: [{
      label: '销售额',
      data: [120, 190, 300, 500, 200, 300],
      backgroundColor: '#3b82f6'
    }]
  }
})

const lineChartData = ref({
  type: 'line',
  title: '用户增长趋势',
  data: {
    labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
    datasets: [{
      label: '新用户',
      data: [65, 59, 80, 81, 56, 55, 40],
      borderColor: '#10b981',
      backgroundColor: 'rgba(16, 185, 129, 0.1)'
    }]
  }
})

const pieChartData = ref({
  type: 'pie',
  title: '市场份额分布',
  data: {
    labels: ['产品A', '产品B', '产品C', '产品D'],
    datasets: [{
      data: [300, 50, 100, 80],
      backgroundColor: ['#ff6384', '#36a2eb', '#ffce56', '#4bc0c0']
    }]
  }
})

// HTML内容
const cardHtml = ref(`
<div class="card">
  <div class="card-header">
    <h3>产品介绍</h3>
  </div>
  <div class="card-body">
    <p>这是一个优秀的产品，具有以下特点：</p>
    <ul>
      <li>高性能</li>
      <li>易使用</li>
      <li>可扩展</li>
    </ul>
  </div>
</div>
<style>
.card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
.card-header {
  background: #f9fafb;
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
}
.card-header h3 {
  margin: 0;
  color: #374151;
}
.card-body {
  padding: 16px;
}
</style>
`)

const tableHtml = ref(`
<table class="data-table">
  <thead>
    <tr>
      <th>姓名</th>
      <th>职位</th>
      <th>部门</th>
      <th>薪资</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>张三</td>
      <td>前端工程师</td>
      <td>技术部</td>
      <td>¥15,000</td>
    </tr>
    <tr>
      <td>李四</td>
      <td>后端工程师</td>
      <td>技术部</td>
      <td>¥18,000</td>
    </tr>
    <tr>
      <td>王五</td>
      <td>产品经理</td>
      <td>产品部</td>
      <td>¥20,000</td>
    </tr>
  </tbody>
</table>
<style>
.data-table {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
}
.data-table th,
.data-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}
.data-table th {
  background: #f9fafb;
  font-weight: 600;
  color: #374151;
}
.data-table tr:hover {
  background: #f9fafb;
}
</style>
`)

const formHtml = ref(`
<form class="demo-form">
  <div class="form-group">
    <label for="name">姓名</label>
    <input type="text" id="name" placeholder="请输入姓名">
  </div>
  <div class="form-group">
    <label for="email">邮箱</label>
    <input type="email" id="email" placeholder="请输入邮箱">
  </div>
  <div class="form-group">
    <label for="message">留言</label>
    <textarea id="message" rows="3" placeholder="请输入留言"></textarea>
  </div>
  <button type="submit" class="submit-btn">提交</button>
</form>
<style>
.demo-form {
  max-width: 400px;
  margin: 0 auto;
  padding: 20px;
}
.form-group {
  margin-bottom: 16px;
}
.form-group label {
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
  color: #374151;
}
.form-group input,
.form-group textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
}
.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
.submit-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}
.submit-btn:hover {
  background: #2563eb;
}
</style>
`)
</script>

<style scoped>
.demo-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 40px;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8px;
}

.subtitle {
  font-size: 1.1rem;
  color: #6b7280;
  margin: 0;
}

.demo-sections {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.demo-section {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.demo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.demo-item {
  background: #f9fafb;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e5e7eb;
}

.demo-item h3 {
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
}

.code-examples {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.code-example {
  background: #1f2937;
  border-radius: 8px;
  padding: 20px;
  color: white;
}

.code-example h3 {
  margin: 0 0 16px 0;
  color: #f9fafb;
  font-size: 1.1rem;
}

.code-example pre {
  margin: 0;
  overflow-x: auto;
}

.code-example code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .demo-grid {
    grid-template-columns: 1fr;
  }
  
  .code-examples {
    grid-template-columns: 1fr;
  }
}
</style>
