var e,a;(()=>{function l(e,a,l,t,s,r,d){try{var n=e[r](d),i=n.value}catch(e){return void l(e)}n.done?a(i):Promise.resolve(i).then(t,s)}function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t(e)}function s(e){var a=function(e){if("object"!=t(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var l=a.call(e,"string");if("object"!=t(l))return l;throw TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==t(a)?a:a+""}function r(e,a,l){return(a=s(a))in e?Object.defineProperty(e,a,{value:l,enumerable:!0,configurable:!0,writable:!0}):e[a]=l,e}function d(e,a){var l=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);a&&(t=t.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),l.push.apply(l,t)}return l}e=function(e){return function(){var a=this,t=arguments;return new Promise(function(s,r){var d=e.apply(a,t);function n(e){l(d,s,r,n,i,"next",e)}function i(e){l(d,s,r,n,i,"throw",e)}n(void 0)})}},a=function(e){for(var a=1;a<arguments.length;a++){var l=null==arguments[a]?{}:arguments[a];a%2?d(Object(l),!0).forEach(function(a){r(e,a,l[a])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(l)):d(Object(l)).forEach(function(a){Object.defineProperty(e,a,Object.getOwnPropertyDescriptor(l,a))})}return e}})();import{B as l,E as t,J as s,L as r,N as d,b as n,c as i,h as o,j as u,l as c,m,o as y,p,y as f,z as g}from"./elementPlus-Di4PDIm8.js";import{bB as v,bC as b,bP as x,bS as _,bT as h,bj as k,bl as w,bn as V,by as P,c4 as C,c8 as j,ca as S,d8 as K,dB as M,dD as U,dH as A,dN as I,dS as B,dU as z,d_ as T,dc as O,dd as R,de as D,df as N,dg as E,dj as L,dk as $,dl as H,dy as F,ea as q,ed as G}from"./vendor-BJ-uKP15.js";import{b as J}from"./index-Byt5TjPh.js";import{b as Z}from"./api-D-gMiCJf.js";import{b as W}from"./settings-46b1LTsi.js";import{b as Q}from"./aiModels-CcIQpxgt.js";const X={class:"flex h-full bg-gray-50 dark:bg-dark-900"},Y={class:"w-64 bg-white dark:bg-dark-800 border-r border-gray-200 dark:border-dark-700"},ee={class:"p-6"},ae={class:"space-y-2"},le=["onClick"],te={class:"flex-1 overflow-y-auto"},se={class:"max-w-4xl mx-auto p-6"},re={key:0,class:"space-y-6"},de={class:"card-tech p-6"},ne={class:"flex items-center space-x-4"},ie={class:"card-tech p-6"},oe={key:1,class:"space-y-6"},ue={class:"card-tech p-6"},ce={class:"flex items-center justify-between"},me={class:"card-tech p-6"},ye={class:"space-y-4"},pe={class:"flex items-center justify-between mb-3"},fe={class:"flex items-center space-x-3"},ge={class:"flex items-center space-x-2"},ve={class:"font-medium text-gray-900 dark:text-gray-100"},be={class:"text-sm text-gray-500"},xe={key:0,class:"space-y-3"},_e={class:"flex items-center justify-between"},he={key:0,class:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3"},ke={class:"flex items-center justify-between"},we={key:1},Ve={key:0,class:"mt-2"},Pe={class:"flex items-center justify-between"},Ce={class:"flex items-center space-x-2"},je={key:0,class:"text-xs text-gray-400"},Se={class:"flex items-center space-x-2"},Ke={key:0,class:"text-center py-8"},Me={key:2,class:"space-y-6"},Ue={class:"card-tech p-6"},Ae={class:"flex items-center space-x-2"},Ie={class:"flex items-center space-x-2"},Be={class:"flex items-center space-x-2"},ze={key:3,class:"space-y-6"},Te={class:"card-tech p-6"},Oe={class:"space-y-3"},Re={class:"flex items-center justify-between"},De={class:"flex items-center justify-between"},Ne={class:"flex items-center justify-between"},Ee={class:"space-y-3"},Le={class:"flex items-center justify-between"},$e={class:"flex items-center justify-between"},He={key:4,class:"space-y-6"},Fe={class:"card-tech p-6"},qe={class:"space-y-6"},Ge={class:"bg-gray-50 dark:bg-dark-700 rounded-lg p-4"},Je={class:"flex items-center justify-between mb-2"},Ze={class:"text-sm font-medium"},We={class:"grid grid-cols-2 gap-4 mt-4 text-sm"},Qe={class:"flex items-center justify-between"},Xe={class:"flex items-center justify-between"},Ye={class:"space-y-3"},ea={class:"space-y-3"},aa={key:5,class:"space-y-6"},la={class:"card-tech p-6"},ta={class:"space-y-6"},sa={class:"space-y-3"},ra={class:"font-medium text-gray-900 dark:text-gray-100"},da={class:"text-sm text-gray-500"},na={class:"space-y-3"},ia={class:"flex items-center justify-between"},oa={class:"flex items-center justify-between"},ua={class:"space-y-3"};var ca=H({__name:"Settings",setup(H){var ca,ma,ya,pa,fa;const ga=J(),va=W(),ba=(Q(),z("profile")),xa=z(!1),_a=z(!1),ha=z(!1),ka=z(),wa=z(),Va={UserIcon:S,Robot:V,Palette:w,Bell:k,Files:v,Lock:x},Pa=[{key:"profile",label:"个人资料",icon:"UserIcon"},{key:"ai",label:"AI模型",icon:"Robot"},{key:"appearance",label:"界面偏好",icon:"Palette"},{key:"notifications",label:"通知设置",icon:"Bell"},{key:"data",label:"数据管理",icon:"Files"},{key:"security",label:"账户安全",icon:"Lock"}],Ca=B({username:(null===(ca=ga.user)||void 0===ca?void 0:ca.username)||"",displayName:(null===(ma=ga.user)||void 0===ma?void 0:ma.display_name)||"",email:(null===(ya=ga.user)||void 0===ya?void 0:ya.email)||"",bio:(null===(pa=ga.user)||void 0===pa?void 0:pa.bio)||"",avatar:(null===(fa=ga.user)||void 0===fa?void 0:fa.avatar_url)||""}),ja={displayName:[{max:30,message:"显示名称不能超过30个字符",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱地址",trigger:"blur"},{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"}],bio:[{max:200,message:"个人简介不能超过200个字符",trigger:"blur"}]},Sa=B({oldPassword:"",newPassword:"",confirmPassword:""}),Ka={oldPassword:[{required:!0,message:"请输入当前密码",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:6,message:"密码长度至少6位",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认新密码",trigger:"blur"},{validator:(e,a,l)=>{a!==Sa.newPassword?l(new Error("两次输入的密码不一致")):l()},trigger:"blur"}]},Ma=B({defaultModel:null,defaultKnowledgeBases:[],chatRetentionDays:30}),Ua=B({theme:"light",fontSize:"medium",enableHighContrast:!1,enableReducedMotion:!1}),Aa=B({email:{documentProcessed:!0,systemMaintenance:!0,securityAlert:!0},browser:{newMessage:!0,taskCompleted:!0}}),Ia=B({loginNotification:!0}),Ba=z([]),za=z([]),Ta=z([]),Oa=z([]),Ra=O(()=>Oa.value),Da=z(**********),Na=z(10737418240),Ea=z({documents:**********.4,chats:429496729.6}),La=z([{id:1,device:"Chrome on Windows",location:"北京, 中国",time:"2024-01-20T10:30:00Z",current:!0},{id:2,device:"Safari on iPhone",location:"上海, 中国",time:"2024-01-19T15:20:00Z",current:!1}]),$a=O(()=>Math.round(Da.value/Na.value*100)),Ha=O(()=>za.value.filter(e=>{if(!e.is_active)return!1;const a=Ba.value.find(a=>a.id===e.provider_id);return!!a&&(!!a.enabled&&(Ta.value.some(e=>e.provider_id===a.id)||e.allow_system_key_use&&e.system_api_key))})),Fa=e=>{if(0===e)return"0 B";const a=Math.floor(Math.log(e)/Math.log(1024));return Math.round(e/Math.pow(1024,a)*100)/100+" "+["B","KB","MB","GB","TB"][a]},qa=()=>{i.info("头像上传功能开发中...")},Ga=(Ja=e(function*(){if(ka.value)try{yield ka.value.validate(),xa.value=!0;const e=yield ga.updateProfile({display_name:Ca.displayName,email:Ca.email,bio:Ca.bio});e.success?i.success("个人资料更新成功"):i.error(e.message||"更新失败")}catch(e){i.error("保存失败，请检查输入信息")}finally{xa.value=!1}}),function(){return Ja.apply(this,arguments)});var Ja;const Za=()=>{ga.user&&(Ca.username=ga.user.username,Ca.displayName=ga.user.display_name||"",Ca.email=ga.user.email,Ca.bio=ga.user.bio||"",Ca.avatar=ga.user.avatar_url||"")},Wa=(Qa=e(function*(){if(wa.value)try{yield wa.value.validate(),_a.value=!0;const e=yield ga.changePassword(Sa.oldPassword,Sa.newPassword);e.success?(i.success("密码修改成功"),Xa()):i.error(e.message||"密码修改失败")}catch(e){i.error("密码修改失败")}finally{_a.value=!1}}),function(){return Qa.apply(this,arguments)});var Qa;const Xa=()=>{Sa.oldPassword="",Sa.newPassword="",Sa.confirmPassword="",wa.value&&wa.value.resetFields()},Ya=(el=e(function*(){try{const e=yield Z.get("/ai/providers");Ba.value=e.data.map(e=>a(a({},e),{},{enabled:!0,apiKey:"",status:"untested",testing:!1,saving:!1,useSystemKey:!0,selectedModel:null,userApiKey:null,lastTestTime:null}))}catch(t){var e,l;i.error(`加载AI供应商失败: ${null===(e=t.response)||void 0===e?void 0:e.status} ${null===(l=t.response)||void 0===l?void 0:l.statusText}`)}}),function(){return el.apply(this,arguments)});var el;const al=(ll=e(function*(){try{const e=yield Z.get("/ai/models");za.value=e.data}catch(e){i.error("加载AI模型失败")}}),function(){return ll.apply(this,arguments)});var ll;const tl=(sl=e(function*(){try{const e=yield Z.get("/ai/api-keys");Ta.value=e.data,Ta.value.forEach(e=>{const a=Ba.value.find(a=>a.id===e.provider_id);a&&(a.userApiKey=e,a.apiKey=e.api_key||"",a.useSystemKey=!1,a.enabled=!0,a.status="valid")})}catch(e){i.error("加载用户API密钥失败")}}),function(){return sl.apply(this,arguments)});var sl;const rl=(dl=e(function*(){try{const e=yield Z.get("/knowledge-bases/");Oa.value=e.data}catch(e){}}),function(){return dl.apply(this,arguments)});var dl;const nl=(il=e(function*(){try{xa.value=!0;const e={};Ba.value.forEach(a=>{e[a.id]={enabled:a.enabled,useSystemKey:a.useSystemKey,selectedModel:a.selectedModel}});const a=yield va.updateUserSettings({defaultModel:Ma.defaultModel,defaultKnowledgeBases:Ma.defaultKnowledgeBases,chatRetentionDays:Ma.chatRetentionDays,providerPreferences:e});a.success?i.success("AI设置保存成功"):i.error(a.message||"保存失败")}catch(e){i.error("保存失败")}finally{xa.value=!1}}),function(){return il.apply(this,arguments)});var il;const ol=e=>{switch(null==e?void 0:e.toLowerCase()){case"openai":return"bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400";case"anthropic":return"bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400";case"google":return"bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-400";case"siliconflow":case"硅基流动":return"bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400";case"deepseek":return"bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-400";default:return"bg-gray-100 dark:bg-gray-900 text-gray-600 dark:text-gray-400"}},ul=e=>za.value.filter(a=>a.provider_id===e&&a.is_active),cl=e=>ul(e.id).some(e=>e.allow_system_key_use&&e.system_api_key),ml=e=>e.selectedModel&&(e.apiKey||e.useSystemKey&&cl(e)),yl=e=>{switch(e){case"valid":return"success";case"invalid":return"danger";case"testing":return"warning";default:return"info"}},pl=e=>{switch(e){case"valid":return"有效";case"invalid":return"无效";case"testing":return"测试中";default:return"未测试"}},fl=e=>{const a=new Date(e),l=(new Date).getTime()-a.getTime();return l<6e4?"刚刚":l<36e5?`${Math.floor(l/6e4)}分钟前`:l<864e5?`${Math.floor(l/36e5)}小时前`:a.toLocaleDateString()},gl=(vl=e(function*(){try{const e={};Ba.value.forEach(a=>{e[a.id]={enabled:a.enabled,useSystemKey:a.useSystemKey,selectedModel:a.selectedModel}}),yield va.updateUserSettings({providerPreferences:e})}catch(e){}}),function(){return vl.apply(this,arguments)});var vl;const bl=(xl=e(function*(e){if(ml(e)){e.testing=!0,e.status="testing";try{const a=za.value.find(a=>a.id===e.selectedModel);if(!a)throw new Error("未找到选择的模型");const l={provider_id:e.id,model_name:a.model_name,api_key:e.useSystemKey?void 0:e.apiKey};(yield Z.post("/ai/test-connection",l)).data.success?(e.status="valid",e.lastTestTime=(new Date).toISOString(),i.success(`${e.display_name} 连接测试成功`)):e.status="invalid"}catch(a){e.status="invalid"}finally{e.testing=!1}}else i.warning("请先选择模型并配置API密钥")}),function(e){return xl.apply(this,arguments)});var xl;const _l=(hl=e(function*(e){if(e.apiKey){e.saving=!0;try{if(e.userApiKey)yield Z.put(`/ai/api-keys/${e.userApiKey.id}`,{api_key:e.apiKey,description:`${e.display_name} API密钥`});else{const a=yield Z.post("/ai/api-keys",{provider_id:e.id,api_key:e.apiKey,description:`${e.display_name} API密钥`});e.userApiKey=a.data}i.success("API密钥保存成功"),yield tl()}catch(a){i.error("API密钥保存失败")}finally{e.saving=!1}}else i.warning("请输入API密钥")}),function(e){return hl.apply(this,arguments)});var hl;const kl=(wl=e(function*(e){yield va.applyTheme(e)}),function(e){return wl.apply(this,arguments)});var wl;const Vl=e=>{va.applyFontSize(e)},Pl=()=>{va.applyAccessibilitySettings()},Cl=(jl=e(function*(){try{xa.value=!0;const e=yield va.updateUserSettings({theme:Ua.theme,fontSize:Ua.fontSize,enableHighContrast:Ua.enableHighContrast,enableReducedMotion:Ua.enableReducedMotion});e.success?i.success("界面设置保存成功"):i.error(e.message||"保存失败")}catch(e){i.error("保存失败")}finally{xa.value=!1}}),function(){return jl.apply(this,arguments)});var jl;const Sl=(Kl=e(function*(){try{xa.value=!0,yield new Promise(e=>setTimeout(e,1e3)),i.success("通知设置保存成功")}catch(e){i.error("保存失败")}finally{xa.value=!1}}),function(){return Kl.apply(this,arguments)});var Kl;const Ml=(Ul=e(function*(e){try{ha.value=!0;let a={},l="";switch(e){case"profile":a={profile:Ca},l="profile.json";break;case"chats":a={chats:[]},l="chats.json";break;case"all":a={profile:Ca,chats:[],settings:Ma},l="all_data.json"}const t=new Blob([JSON.stringify(a,null,2)],{type:"application/json"}),s=URL.createObjectURL(t),r=document.createElement("a");r.href=s,r.download=l,r.click(),URL.revokeObjectURL(s),i.success("数据导出成功")}catch(a){i.error("导出失败")}finally{ha.value=!1}}),function(e){return Ul.apply(this,arguments)});var Ul;const Al=(Il=e(function*(e){let a="",l="";switch(e){case"cache":a="确定要清理缓存吗？",l="缓存清理成功";break;case"oldChats":a="确定要清理过期对话吗？",l="过期对话清理成功";break;case"all":a="确定要清空所有数据吗？此操作不可恢复！",l="所有数据已清空"}try{yield n.confirm(a,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",confirmButtonClass:"all"===e?"el-button--danger":""}),yield new Promise(e=>setTimeout(e,1e3)),i.success(l)}catch(t){}}),function(e){return Il.apply(this,arguments)});var Il;const Bl=(zl=e(function*(){try{yield n.confirm("删除账户将永久删除您的所有数据，包括知识库、对话记录等。此操作不可恢复！","确认删除账户",{confirmButtonText:"确认删除",cancelButtonText:"取消",type:"error",confirmButtonClass:"el-button--danger"}),i.success("账户删除请求已提交，请查收邮件确认")}catch(e){}}),function(){return zl.apply(this,arguments)});var zl;return F(e(function*(){if(!ga.isAuthenticated)return void i.error("请先登录");yield Ya(),yield al(),yield tl(),yield rl(),yield va.fetchUserSettings();const e=va.userSettings;Ma.defaultModel=e.defaultModel,Ma.defaultKnowledgeBases=e.defaultKnowledgeBases,Ma.chatRetentionDays=e.chatRetentionDays,Ua.theme=e.theme,Ua.fontSize=e.fontSize,Ua.enableHighContrast=e.enableHighContrast,Ua.enableReducedMotion=e.enableReducedMotion,Ba.value.forEach(e.providerPreferences?a=>{var l;const t=null===(l=e.providerPreferences)||void 0===l?void 0:l[a.id];t?(a.enabled=void 0===t.enabled||t.enabled,a.useSystemKey=void 0===t.useSystemKey||t.useSystemKey,a.selectedModel=t.selectedModel||null):a.enabled=!0}:e=>{e.enabled=!0})})),(e,a)=>{const n=d,v=s,x=t,k=p,w=r,S=y,B=f,z=c,O=m,H=o,F=g,J=l,Z=u;return M(),E("div",X,[R("div",Y,[R("div",ee,[a[26]||(a[26]=R("h2",{class:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6"}," 设置 ",-1)),R("nav",ae,[(M(),E(K,null,U(Pa,e=>R("button",{key:e.key,onClick:a=>ba.value=e.key,class:q(["w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors",ba.value===e.key?"bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300":"text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-700"])},[$(n,{class:"mr-3",size:18},{default:I(()=>[(M(),D(A(Va[e.icon])))]),_:2},1024),L(" "+G(e.label),1)],10,le)),64))])])]),R("div",te,[R("div",se,["profile"===ba.value?(M(),E("div",re,[R("div",de,[a[32]||(a[32]=R("h3",{class:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6"}," 个人资料 ",-1)),$(S,{model:Ca,rules:ja,ref_key:"profileFormRef",ref:ka,"label-width":"120px"},{default:I(()=>[$(k,{label:"头像"},{default:I(()=>[R("div",ne,[$(v,{size:80,src:Ca.avatar},{default:I(()=>{var e;return[L(G(null===(e=Ca.username)||void 0===e?void 0:e.charAt(0).toUpperCase()),1)]}),_:1},8,["src"]),R("div",null,[$(x,{size:"small",onClick:qa},{default:I(()=>[$(n,{class:"mr-1"},{default:I(()=>[$(T(j))]),_:1}),a[27]||(a[27]=L(" 更换头像 ",-1))]),_:1,__:[27]}),a[28]||(a[28]=R("p",{class:"text-xs text-gray-500 mt-1"}," 支持 JPG、PNG 格式，建议尺寸 200x200px ",-1))])])]),_:1}),$(k,{label:"用户名",prop:"username"},{default:I(()=>[$(w,{modelValue:Ca.username,"onUpdate:modelValue":a[0]||(a[0]=e=>Ca.username=e),placeholder:"请输入用户名",maxlength:"20","show-word-limit":"",disabled:""},null,8,["modelValue"]),a[29]||(a[29]=R("div",{class:"text-xs text-gray-500 mt-1"}," 用户名不可修改 ",-1))]),_:1,__:[29]}),$(k,{label:"显示名称",prop:"displayName"},{default:I(()=>[$(w,{modelValue:Ca.displayName,"onUpdate:modelValue":a[1]||(a[1]=e=>Ca.displayName=e),placeholder:"请输入显示名称",maxlength:"30","show-word-limit":""},null,8,["modelValue"])]),_:1}),$(k,{label:"邮箱地址",prop:"email"},{default:I(()=>[$(w,{modelValue:Ca.email,"onUpdate:modelValue":a[2]||(a[2]=e=>Ca.email=e),placeholder:"请输入邮箱地址",type:"email"},null,8,["modelValue"])]),_:1}),$(k,{label:"个人简介",prop:"bio"},{default:I(()=>[$(w,{modelValue:Ca.bio,"onUpdate:modelValue":a[3]||(a[3]=e=>Ca.bio=e),type:"textarea",placeholder:"请输入个人简介（可选）",rows:4,maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1}),$(k,null,{default:I(()=>[$(x,{type:"primary",onClick:Ga,loading:xa.value},{default:I(()=>a[30]||(a[30]=[L(" 保存更改 ",-1)])),_:1,__:[30]},8,["loading"]),$(x,{onClick:Za},{default:I(()=>a[31]||(a[31]=[L(" 重置 ",-1)])),_:1,__:[31]})]),_:1})]),_:1},8,["model"])]),R("div",ie,[a[35]||(a[35]=R("h3",{class:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6"}," 修改密码 ",-1)),$(S,{model:Sa,rules:Ka,ref_key:"passwordFormRef",ref:wa,"label-width":"120px"},{default:I(()=>[$(k,{label:"当前密码",prop:"oldPassword"},{default:I(()=>[$(w,{modelValue:Sa.oldPassword,"onUpdate:modelValue":a[4]||(a[4]=e=>Sa.oldPassword=e),type:"password",placeholder:"请输入当前密码","show-password":""},null,8,["modelValue"])]),_:1}),$(k,{label:"新密码",prop:"newPassword"},{default:I(()=>[$(w,{modelValue:Sa.newPassword,"onUpdate:modelValue":a[5]||(a[5]=e=>Sa.newPassword=e),type:"password",placeholder:"请输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),$(k,{label:"确认密码",prop:"confirmPassword"},{default:I(()=>[$(w,{modelValue:Sa.confirmPassword,"onUpdate:modelValue":a[6]||(a[6]=e=>Sa.confirmPassword=e),type:"password",placeholder:"请再次输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),$(k,null,{default:I(()=>[$(x,{type:"primary",onClick:Wa,loading:_a.value},{default:I(()=>a[33]||(a[33]=[L(" 修改密码 ",-1)])),_:1,__:[33]},8,["loading"]),$(x,{onClick:Xa},{default:I(()=>a[34]||(a[34]=[L(" 重置 ",-1)])),_:1,__:[34]})]),_:1})]),_:1},8,["model"])])])):"ai"===ba.value?(M(),E("div",oe,[R("div",ue,[a[39]||(a[39]=R("h3",{class:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6"}," AI模型偏好 ",-1)),$(S,{model:Ma,"label-width":"150px"},{default:I(()=>[$(k,{label:"默认AI模型"},{default:I(()=>[$(O,{modelValue:Ma.defaultModel,"onUpdate:modelValue":a[7]||(a[7]=e=>Ma.defaultModel=e),placeholder:"选择默认AI模型",style:{width:"300px"}},{default:I(()=>[(M(!0),E(K,null,U(Ha.value,e=>(M(),D(z,{key:e.id,label:e.display_name,value:e.id},{default:I(()=>[R("div",ce,[R("span",null,G(e.display_name),1),$(B,{size:"small",type:"info"},{default:I(()=>{var a;return[L(G(null===(a=e.provider)||void 0===a?void 0:a.display_name),1)]}),_:2},1024)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),$(k,{label:"默认知识库"},{default:I(()=>[$(O,{modelValue:Ma.defaultKnowledgeBases,"onUpdate:modelValue":a[8]||(a[8]=e=>Ma.defaultKnowledgeBases=e),multiple:"",placeholder:"选择默认使用的知识库",style:{width:"400px"}},{default:I(()=>[(M(!0),E(K,null,U(Ra.value,e=>(M(),D(z,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),a[36]||(a[36]=R("div",{class:"text-xs text-gray-500 mt-1"}," 新建对话时将自动选择这些知识库 ",-1))]),_:1,__:[36]}),$(k,{label:"对话保留天数"},{default:I(()=>[$(O,{modelValue:Ma.chatRetentionDays,"onUpdate:modelValue":a[9]||(a[9]=e=>Ma.chatRetentionDays=e),style:{width:"200px"}},{default:I(()=>[$(z,{label:"7天",value:7}),$(z,{label:"30天",value:30}),$(z,{label:"90天",value:90}),$(z,{label:"永久保留",value:0})]),_:1},8,["modelValue"]),a[37]||(a[37]=R("div",{class:"text-xs text-gray-500 mt-1"}," 超过保留期的对话将被自动删除 ",-1))]),_:1,__:[37]}),$(k,null,{default:I(()=>[$(x,{type:"primary",onClick:nl,loading:xa.value},{default:I(()=>a[38]||(a[38]=[L(" 保存设置 ",-1)])),_:1,__:[38]},8,["loading"])]),_:1})]),_:1},8,["model"])]),R("div",me,[a[52]||(a[52]=R("h3",{class:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6"}," API密钥管理 ",-1)),R("div",ye,[(M(!0),E(K,null,U(Ba.value,e=>(M(),E("div",{key:e.id,class:"border border-gray-200 dark:border-dark-700 rounded-lg p-4"},[R("div",pe,[R("div",fe,[R("div",{class:q(["w-8 h-8 rounded-lg flex items-center justify-center",ol(e.name)])},[$(n,{size:16},{default:I(()=>[(M(),D(A(T(V))))]),_:1})],2),R("div",null,[R("div",ge,[R("h4",ve,G(e.display_name),1),cl(e)?(M(),D(B,{key:0,type:"info",size:"small"},{default:I(()=>a[40]||(a[40]=[L(" 系统提供 ",-1)])),_:1,__:[40]})):N("",!0)]),R("p",be,G(e.description||`${e.display_name} AI模型服务`),1)])]),$(H,{modelValue:e.enabled,"onUpdate:modelValue":a=>e.enabled=a,onChange:a=>(e=>{e.enabled||(e.selectedModel=null,e.status="untested"),gl()})(e)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),e.enabled?(M(),E("div",xe,[R("div",null,[a[42]||(a[42]=R("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," 选择模型 ",-1)),$(O,{modelValue:e.selectedModel,"onUpdate:modelValue":a=>e.selectedModel=a,placeholder:"请选择要测试的模型",style:{width:"100%"},onChange:a=>(e=>{e.status="untested"})(e)},{default:I(()=>[(M(!0),E(K,null,U(ul(e.id),e=>(M(),D(z,{key:e.id,label:e.display_name,value:e.id},{default:I(()=>[R("div",_e,[R("span",null,G(e.display_name),1),e.allow_system_key_use?(M(),D(B,{key:0,size:"small",type:"success"},{default:I(()=>a[41]||(a[41]=[L(" 支持系统密钥 ",-1)])),_:1,__:[41]})):N("",!0)])]),_:2},1032,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),e.useSystemKey&&cl(e)?(M(),E("div",he,[R("div",ke,[a[44]||(a[44]=R("div",null,[R("p",{class:"text-sm font-medium text-blue-900 dark:text-blue-100"}," 使用系统提供的API密钥 "),R("p",{class:"text-xs text-blue-600 dark:text-blue-300 mt-1"}," 管理员已为此服务配置了API密钥，您可以直接使用 ")],-1)),$(x,{size:"small",type:"primary",onClick:a=>(e=>{var a;e.useSystemKey=!1,e.apiKey=(null===(a=e.userApiKey)||void 0===a?void 0:a.api_key)||"",e.status="untested",i.info("已切换到自定义API密钥模式，请输入您的密钥"),gl()})(e)},{default:I(()=>a[43]||(a[43]=[L(" 使用自定义密钥 ",-1)])),_:2,__:[43]},1032,["onClick"])])])):(M(),E("div",we,[a[47]||(a[47]=R("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," API密钥 ",-1)),$(w,{modelValue:e.apiKey,"onUpdate:modelValue":a=>e.apiKey=a,type:"password",placeholder:"请输入API密钥","show-password":""},{prepend:I(()=>a[45]||(a[45]=[L("API Key",-1)])),_:2},1032,["modelValue","onUpdate:modelValue"]),cl(e)?(M(),E("div",Ve,[$(x,{size:"small",type:"info",onClick:a=>(e=>{cl(e)?(e.useSystemKey=!0,e.apiKey="",e.status="untested",i.success("已切换到系统提供的API密钥"),gl()):i.warning("该服务没有系统提供的API密钥")})(e)},{default:I(()=>a[46]||(a[46]=[L(" 使用系统密钥 ",-1)])),_:2,__:[46]},1032,["onClick"])])):N("",!0)])),R("div",Pe,[R("div",Ce,[a[48]||(a[48]=R("span",{class:"text-sm text-gray-500"},"状态:",-1)),$(B,{type:yl(e.status),size:"small"},{default:I(()=>[L(G(pl(e.status)),1)]),_:2},1032,["type"]),e.lastTestTime?(M(),E("span",je,G(fl(e.lastTestTime)),1)):N("",!0)]),R("div",Se,[$(x,{size:"small",onClick:a=>bl(e),loading:e.testing,disabled:!ml(e)},{default:I(()=>a[49]||(a[49]=[L(" 测试连接 ",-1)])),_:2,__:[49]},1032,["onClick","loading","disabled"]),!e.useSystemKey&&e.apiKey?(M(),D(x,{key:0,size:"small",type:"success",onClick:a=>_l(e),loading:e.saving},{default:I(()=>a[50]||(a[50]=[L(" 保存密钥 ",-1)])),_:2,__:[50]},1032,["onClick","loading"])):N("",!0)])])])):N("",!0)]))),128)),0===Ba.value.length?(M(),E("div",Ke,[$(n,{size:48,class:"text-gray-400 mb-4"},{default:I(()=>[$(T(V))]),_:1}),a[51]||(a[51]=R("p",{class:"text-gray-500"},"暂无可用的AI供应商",-1))])):N("",!0)])])])):"appearance"===ba.value?(M(),E("div",Me,[R("div",Ue,[a[62]||(a[62]=R("h3",{class:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6"}," 界面偏好 ",-1)),$(S,{model:Ua,"label-width":"120px"},{default:I(()=>[$(k,{label:"主题模式"},{default:I(()=>[$(J,{modelValue:Ua.theme,"onUpdate:modelValue":a[10]||(a[10]=e=>Ua.theme=e),onChange:kl},{default:I(()=>[$(F,{value:"light"},{default:I(()=>[R("div",Ae,[$(n,null,{default:I(()=>[$(T(C))]),_:1}),a[53]||(a[53]=R("span",null,"浅色模式",-1))])]),_:1}),$(F,{value:"dark"},{default:I(()=>[R("div",Ie,[$(n,null,{default:I(()=>[$(T(h))]),_:1}),a[54]||(a[54]=R("span",null,"深色模式",-1))])]),_:1}),$(F,{value:"system"},{default:I(()=>[R("div",Be,[$(n,null,{default:I(()=>[$(T(_))]),_:1}),a[55]||(a[55]=R("span",null,"跟随系统",-1))])]),_:1})]),_:1},8,["modelValue"])]),_:1}),$(k,{label:"字体大小"},{default:I(()=>[$(J,{modelValue:Ua.fontSize,"onUpdate:modelValue":a[11]||(a[11]=e=>Ua.fontSize=e),onChange:Vl},{default:I(()=>[$(F,{value:"small"},{default:I(()=>a[56]||(a[56]=[L("小",-1)])),_:1,__:[56]}),$(F,{value:"medium"},{default:I(()=>a[57]||(a[57]=[L("中",-1)])),_:1,__:[57]}),$(F,{value:"large"},{default:I(()=>a[58]||(a[58]=[L("大",-1)])),_:1,__:[58]})]),_:1},8,["modelValue"])]),_:1}),$(k,{label:"高对比度"},{default:I(()=>[$(H,{modelValue:Ua.enableHighContrast,"onUpdate:modelValue":a[12]||(a[12]=e=>Ua.enableHighContrast=e),onChange:Pl},null,8,["modelValue"]),a[59]||(a[59]=R("div",{class:"text-xs text-gray-500 mt-1"}," 提高界面对比度，便于视力不佳的用户使用 ",-1))]),_:1,__:[59]}),$(k,{label:"减少动画"},{default:I(()=>[$(H,{modelValue:Ua.enableReducedMotion,"onUpdate:modelValue":a[13]||(a[13]=e=>Ua.enableReducedMotion=e),onChange:Pl},null,8,["modelValue"]),a[60]||(a[60]=R("div",{class:"text-xs text-gray-500 mt-1"}," 减少界面动画效果，提高性能并减少干扰 ",-1))]),_:1,__:[60]}),$(k,null,{default:I(()=>[$(x,{type:"primary",onClick:Cl,loading:xa.value},{default:I(()=>a[61]||(a[61]=[L(" 保存设置 ",-1)])),_:1,__:[61]},8,["loading"])]),_:1})]),_:1},8,["model"])])])):"notifications"===ba.value?(M(),E("div",ze,[R("div",Te,[a[69]||(a[69]=R("h3",{class:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6"}," 通知设置 ",-1)),$(S,{model:Aa,"label-width":"150px"},{default:I(()=>[$(k,{label:"邮件通知"},{default:I(()=>[R("div",Oe,[R("div",Re,[a[63]||(a[63]=R("span",{class:"text-sm text-gray-700 dark:text-gray-300"},"文档处理完成",-1)),$(H,{modelValue:Aa.email.documentProcessed,"onUpdate:modelValue":a[14]||(a[14]=e=>Aa.email.documentProcessed=e)},null,8,["modelValue"])]),R("div",De,[a[64]||(a[64]=R("span",{class:"text-sm text-gray-700 dark:text-gray-300"},"系统维护通知",-1)),$(H,{modelValue:Aa.email.systemMaintenance,"onUpdate:modelValue":a[15]||(a[15]=e=>Aa.email.systemMaintenance=e)},null,8,["modelValue"])]),R("div",Ne,[a[65]||(a[65]=R("span",{class:"text-sm text-gray-700 dark:text-gray-300"},"安全警告",-1)),$(H,{modelValue:Aa.email.securityAlert,"onUpdate:modelValue":a[16]||(a[16]=e=>Aa.email.securityAlert=e)},null,8,["modelValue"])])])]),_:1}),$(k,{label:"浏览器通知"},{default:I(()=>[R("div",Ee,[R("div",Le,[a[66]||(a[66]=R("span",{class:"text-sm text-gray-700 dark:text-gray-300"},"新消息提醒",-1)),$(H,{modelValue:Aa.browser.newMessage,"onUpdate:modelValue":a[17]||(a[17]=e=>Aa.browser.newMessage=e)},null,8,["modelValue"])]),R("div",$e,[a[67]||(a[67]=R("span",{class:"text-sm text-gray-700 dark:text-gray-300"},"任务完成提醒",-1)),$(H,{modelValue:Aa.browser.taskCompleted,"onUpdate:modelValue":a[18]||(a[18]=e=>Aa.browser.taskCompleted=e)},null,8,["modelValue"])])])]),_:1}),$(k,null,{default:I(()=>[$(x,{type:"primary",onClick:Sl,loading:xa.value},{default:I(()=>a[68]||(a[68]=[L(" 保存设置 ",-1)])),_:1,__:[68]},8,["loading"])]),_:1})]),_:1},8,["model"])])])):"data"===ba.value?(M(),E("div",He,[R("div",Fe,[a[82]||(a[82]=R("h3",{class:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6"}," 数据管理 ",-1)),R("div",qe,[R("div",null,[a[73]||(a[73]=R("h4",{class:"font-medium text-gray-900 dark:text-gray-100 mb-3"}," 存储使用情况 ",-1)),R("div",Ge,[R("div",Je,[a[70]||(a[70]=R("span",{class:"text-sm text-gray-600 dark:text-gray-400"},"已使用",-1)),R("span",Ze,G(Fa(Da.value))+" / "+G(Fa(Na.value)),1)]),$(Z,{percentage:$a.value,color:$a.value>80?"#f56565":"#48bb78","stroke-width":8},null,8,["percentage","color"]),R("div",We,[R("div",Qe,[a[71]||(a[71]=R("span",{class:"text-gray-600 dark:text-gray-400"},"文档",-1)),R("span",null,G(Fa(Ea.value.documents)),1)]),R("div",Xe,[a[72]||(a[72]=R("span",{class:"text-gray-600 dark:text-gray-400"},"对话记录",-1)),R("span",null,G(Fa(Ea.value.chats)),1)])])])]),R("div",null,[a[77]||(a[77]=R("h4",{class:"font-medium text-gray-900 dark:text-gray-100 mb-3"}," 数据导出 ",-1)),R("div",Ye,[$(x,{onClick:a[19]||(a[19]=e=>Ml("profile")),loading:ha.value},{default:I(()=>[$(n,{class:"mr-2"},{default:I(()=>[$(T(b))]),_:1}),a[74]||(a[74]=L(" 导出个人资料 ",-1))]),_:1,__:[74]},8,["loading"]),$(x,{onClick:a[20]||(a[20]=e=>Ml("chats")),loading:ha.value},{default:I(()=>[$(n,{class:"mr-2"},{default:I(()=>[$(T(b))]),_:1}),a[75]||(a[75]=L(" 导出对话记录 ",-1))]),_:1,__:[75]},8,["loading"]),$(x,{onClick:a[21]||(a[21]=e=>Ml("all")),loading:ha.value},{default:I(()=>[$(n,{class:"mr-2"},{default:I(()=>[$(T(b))]),_:1}),a[76]||(a[76]=L(" 导出所有数据 ",-1))]),_:1,__:[76]},8,["loading"])])]),R("div",null,[a[81]||(a[81]=R("h4",{class:"font-medium text-gray-900 dark:text-gray-100 mb-3"}," 数据清理 ",-1)),R("div",ea,[$(x,{type:"warning",onClick:a[22]||(a[22]=e=>Al("cache"))},{default:I(()=>[$(n,{class:"mr-2"},{default:I(()=>[$(T(P))]),_:1}),a[78]||(a[78]=L(" 清理缓存 ",-1))]),_:1,__:[78]}),$(x,{type:"warning",onClick:a[23]||(a[23]=e=>Al("oldChats"))},{default:I(()=>[$(n,{class:"mr-2"},{default:I(()=>[$(T(P))]),_:1}),a[79]||(a[79]=L(" 清理过期对话 ",-1))]),_:1,__:[79]}),$(x,{type:"danger",onClick:a[24]||(a[24]=e=>Al("all"))},{default:I(()=>[$(n,{class:"mr-2"},{default:I(()=>[$(T(P))]),_:1}),a[80]||(a[80]=L(" 清空所有数据 ",-1))]),_:1,__:[80]})])])])])])):"security"===ba.value?(M(),E("div",aa,[R("div",la,[a[91]||(a[91]=R("h3",{class:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6"}," 账户安全 ",-1)),R("div",ta,[R("div",null,[a[83]||(a[83]=R("h4",{class:"font-medium text-gray-900 dark:text-gray-100 mb-3"}," 最近登录活动 ",-1)),R("div",sa,[(M(!0),E(K,null,U(La.value,e=>(M(),E("div",{key:e.id,class:"flex items-center justify-between p-3 bg-gray-50 dark:bg-dark-700 rounded-lg"},[R("div",null,[R("div",ra,G(e.device),1),R("div",da,G(e.location)+" • "+G(fl(e.time)),1)]),$(B,{type:e.current?"success":"info",size:"small"},{default:I(()=>[L(G(e.current?"当前会话":"历史登录"),1)]),_:2},1032,["type"])]))),128))])]),R("div",null,[a[87]||(a[87]=R("h4",{class:"font-medium text-gray-900 dark:text-gray-100 mb-3"}," 安全设置 ",-1)),R("div",na,[R("div",ia,[a[85]||(a[85]=R("div",null,[R("div",{class:"font-medium text-gray-900 dark:text-gray-100"}," 双因素认证 "),R("div",{class:"text-sm text-gray-500"}," 为您的账户添加额外的安全保护 ")],-1)),$(x,{size:"small",type:"primary"},{default:I(()=>a[84]||(a[84]=[L(" 启用 ",-1)])),_:1,__:[84]})]),R("div",oa,[a[86]||(a[86]=R("div",null,[R("div",{class:"font-medium text-gray-900 dark:text-gray-100"}," 登录通知 "),R("div",{class:"text-sm text-gray-500"}," 新设备登录时发送邮件通知 ")],-1)),$(H,{modelValue:Ia.loginNotification,"onUpdate:modelValue":a[25]||(a[25]=e=>Ia.loginNotification=e)},null,8,["modelValue"])])])]),R("div",null,[a[90]||(a[90]=R("h4",{class:"font-medium text-red-600 dark:text-red-400 mb-3"}," 危险操作 ",-1)),R("div",ua,[$(x,{type:"danger",onClick:Bl},{default:I(()=>[$(n,{class:"mr-2"},{default:I(()=>[$(T(P))]),_:1}),a[88]||(a[88]=L(" 删除账户 ",-1))]),_:1,__:[88]}),a[89]||(a[89]=R("div",{class:"text-xs text-gray-500"}," 删除账户将永久删除您的所有数据，此操作不可恢复 ",-1))])])])])])):N("",!0)])])])}}});export{ca as default};