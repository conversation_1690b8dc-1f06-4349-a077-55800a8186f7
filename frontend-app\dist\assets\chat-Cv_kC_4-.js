var e;(()=>{function t(e,t,s,r,n,o,i){try{var a=e[o](i),c=a.value}catch(e){return void s(e)}a.done?t(c):Promise.resolve(c).then(r,n)}e=function(e){return function(){var s=this,r=arguments;return new Promise(function(n,o){var i=e.apply(s,r);function a(e){t(i,n,o,a,c,"next",e)}function c(e){t(i,n,o,a,c,"throw",e)}a(void 0)})}}})();import{d as t}from"./index-Byt5TjPh.js";const s=()=>"/api";function r(e){return e.includes("Model disabled")||e.includes("30003")?"当前选择的AI模型已被禁用，请在设置中选择其他可用的模型":e.includes("401")||e.includes("Unauthorized")||e.includes("Invalid API key")?"API密钥无效或已过期，请在设置中检查并更新API密钥":e.includes("quota")||e.includes("insufficient")||e.includes("limit")?"API配额不足，请检查您的账户余额或联系服务提供商":e.includes("network")||e.includes("timeout")||e.includes("connection")?"网络连接失败，请检查网络连接后重试":e.includes("500")||e.includes("Internal Server Error")?"服务器内部错误，请稍后重试":"AI服务调用失败，请检查模型设置和API密钥配置"}function n(e){if(!e)return!1;const t=e instanceof Error?e.message:String(e);return t.includes("timeout")||t.includes("超时")||t.includes("network")||t.includes("网络")||t.includes("connection")||t.includes("连接")||t.includes("500")||t.includes("服务器")||t.includes("server")}const o=e=>new Promise(t=>setTimeout(t,e)),i={getSessions:e=>t.get("/chat/sessions",{params:e}),createSession:e=>t.post("/chat/sessions",e||{}),deleteSession:e=>t.delete(`/chat/sessions/${e}`),updateSession:(e,s)=>t.put(`/chat/sessions/${e}`,s),getMessages:(e,s)=>t.get(`/chat/sessions/${e}/messages`,{params:s}),getChatHistory:(e,s)=>t.get(`/chat/sessions/${e}/messages/history`,{params:s}),streamChat:(c=e(function*(e,t,i,a,c,d){let l=0;for(;l<3;)try{var u;const n=localStorage.getItem("token"),o=s(),l=yield fetch(`${o}/chat/sessions/${e}/stream`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${n}`},body:JSON.stringify(t),signal:null==d?void 0:d.signal});if(!l.ok)throw new Error(`HTTP error! status: ${l.status}`);const g=null===(u=l.body)||void 0===u?void 0:u.getReader();if(!g)throw new Error("无法获取响应流");const h=new TextDecoder;let m="";for(;;){const{done:e,value:t}=yield g.read();if(e)break;m+=h.decode(t,{stream:!0});const s=m.split("\n");m=s.pop()||"";for(const n of s)if(n.startsWith("data: "))try{const e=JSON.parse(n.slice(6));if("content"===e.type)i(e.content);else{if("done"===e.type)return void a({ai_message:e.ai_message||e.message,user_message:e.user_message});if("error"===e.type)return void c(r(e.content))}}catch(f){}}}catch(g){if(g instanceof Error&&"AbortError"===g.name)return;const e=g instanceof Error?g.message:"发送消息失败";if(e.includes("aborted")||e.includes("AbortError")||e.includes("BodyStreamBuffer was aborted"))return;if(!n(g))return void c(e);{const e=1e3*Math.pow(2,l);yield o(e),l++}}c("发送消息失败，已达到最大重试次数")}),function(e,t,s,r,n,o){return c.apply(this,arguments)}),sendMessage:(e,s)=>t.post(`/chat/sessions/${e}/stream`,s),deleteMessage:e=>t.delete(`/chat/messages/${e}`),batchDeleteMessages:e=>t.delete("/chat/messages/batch",{data:e}),regenerateMessage:e=>t.post("/chat/messages/regenerate",e),streamRegenerateMessage:(a=e(function*(e,t,r,i,a){let c=0;for(;c<3;)try{var d;const n=localStorage.getItem("token"),o=s(),c=yield fetch(`${o}/chat/messages/regenerate/stream`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${n}`},body:JSON.stringify(e),signal:null==a?void 0:a.signal});if(!c.ok)throw new Error(`HTTP error! status: ${c.status}`);const u=null===(d=c.body)||void 0===d?void 0:d.getReader();if(!u)throw new Error("无法获取响应流");const f=new TextDecoder;let g="";for(;;){const{done:e,value:s}=yield u.read();if(e)break;g+=f.decode(s,{stream:!0});const n=g.split("\n");g=n.pop()||"";for(const o of n)if(o.startsWith("data: "))try{const e=JSON.parse(o.slice(6));if("content"===e.type)t(e.content);else{if("done"===e.type)return void r(e.message);if("error"===e.type)return void i(e.error)}}catch(l){}}}catch(u){if(u instanceof Error&&"AbortError"===u.name)return;const e=u instanceof Error?u.message:"重新生成消息失败";if(e.includes("aborted")||e.includes("AbortError")||e.includes("BodyStreamBuffer was aborted"))return;if(!n(u))return void i(e);{const e=1e3*Math.pow(2,c);yield o(e),c++}}i("重新生成消息失败，已达到最大重试次数")}),function(e,t,s,r,n){return a.apply(this,arguments)}),clearSession:e=>t.delete(`/chat/sessions/${e}/messages`),checkModelStatus:e=>t.get(`/ai-models/models/${e}/status`)};var a,c;export{i as b};