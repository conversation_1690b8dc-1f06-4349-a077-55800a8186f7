<!DOCTYPE html>
<html>
<head>
    <title>测试上传修复</title>
</head>
<body>
    <h1>前端错误修复测试</h1>
    
    <script>
        // 模拟可能导致错误的情况
        console.log("测试1: undefined.length");
        try {
            const result = undefined;
            console.log(result.uploaded_documents.length); // 这会报错
        } catch (error) {
            console.error("错误:", error.message);
        }
        
        console.log("测试2: 安全访问");
        try {
            const result = undefined;
            const count = result?.uploaded_documents?.length || 0;
            console.log("安全访问结果:", count);
        } catch (error) {
            console.error("错误:", error.message);
        }
        
        console.log("测试3: 正常数据");
        try {
            const result = { uploaded_documents: [1, 2, 3] };
            const count = result?.uploaded_documents?.length || 0;
            console.log("正常数据结果:", count);
        } catch (error) {
            console.error("错误:", error.message);
        }
    </script>
</body>
</html>
