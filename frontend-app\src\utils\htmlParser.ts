/**
 * HTML内容解析器
 * 用于从聊天消息中提取和处理HTML代码块
 */

export interface HtmlBlock {
  content: string
  language: string
  isComplete: boolean
}

/**
 * 从消息内容中提取HTML代码块
 */
export function extractHtmlBlocks(content: string): HtmlBlock[] {
  const htmlBlocks: HtmlBlock[] = []
  
  // 匹配代码块的正则表达式
  const codeBlockRegex = /```(\w*)\n?([\s\S]*?)```/g
  let match
  
  while ((match = codeBlockRegex.exec(content)) !== null) {
    const language = match[1].toLowerCase()
    const code = match[2].trim()
    
    // 检查是否为HTML相关的代码块
    if (isHtmlCode(language, code)) {
      htmlBlocks.push({
        content: code,
        language: language || 'html',
        isComplete: isCompleteHtml(code)
      })
    }
  }
  
  // 如果没有找到代码块，检查是否有裸露的HTML标签
  if (htmlBlocks.length === 0) {
    const bareHtml = extractBareHtml(content)
    if (bareHtml) {
      htmlBlocks.push({
        content: bareHtml,
        language: 'html',
        isComplete: isCompleteHtml(bareHtml)
      })
    }
  }
  
  return htmlBlocks
}

/**
 * 判断是否为HTML代码
 */
function isHtmlCode(language: string, code: string): boolean {
  // 检查语言标识
  const htmlLanguages = ['html', 'htm', 'xml', 'svg']
  if (htmlLanguages.includes(language)) {
    return true
  }
  
  // 检查代码内容
  const htmlPatterns = [
    /<\s*html\b/i,
    /<\s*head\b/i,
    /<\s*body\b/i,
    /<\s*div\b/i,
    /<\s*p\b/i,
    /<\s*h[1-6]\b/i,
    /<\s*span\b/i,
    /<\s*a\b/i,
    /<\s*img\b/i,
    /<\s*table\b/i,
    /<\s*form\b/i,
    /<\s*input\b/i,
    /<\s*button\b/i,
    /<\s*script\b/i,
    /<\s*style\b/i,
    /<\s*link\b/i,
    /<\s*meta\b/i,
    /<!DOCTYPE\s+html/i
  ]
  
  return htmlPatterns.some(pattern => pattern.test(code))
}

/**
 * 检查是否为完整的HTML文档
 */
function isCompleteHtml(code: string): boolean {
  const hasDoctype = /<!DOCTYPE\s+html/i.test(code)
  const hasHtmlTag = /<\s*html\b/i.test(code) && /<\/\s*html\s*>/i.test(code)
  const hasHeadTag = /<\s*head\b/i.test(code) && /<\/\s*head\s*>/i.test(code)
  const hasBodyTag = /<\s*body\b/i.test(code) && /<\/\s*body\s*>/i.test(code)
  
  return hasDoctype && hasHtmlTag && hasHeadTag && hasBodyTag
}

/**
 * 从文本中提取裸露的HTML标签
 */
function extractBareHtml(content: string): string | null {
  // 移除markdown语法
  const cleanContent = content
    .replace(/\*\*(.*?)\*\*/g, '$1') // 粗体
    .replace(/\*(.*?)\*/g, '$1') // 斜体
    .replace(/`(.*?)`/g, '$1') // 行内代码
    .replace(/\[(.*?)\]\(.*?\)/g, '$1') // 链接
  
  // 查找HTML标签
  const htmlTagRegex = /<[^>]+>/g
  const matches = cleanContent.match(htmlTagRegex)
  
  if (matches && matches.length > 2) {
    // 如果找到多个HTML标签，提取包含这些标签的部分
    const firstTagIndex = cleanContent.indexOf(matches[0])
    const lastTagIndex = cleanContent.lastIndexOf(matches[matches.length - 1])
    const htmlContent = cleanContent.substring(firstTagIndex, lastTagIndex + matches[matches.length - 1].length)
    
    // 验证提取的内容是否看起来像HTML
    if (htmlContent.length > 10 && htmlContent.includes('<') && htmlContent.includes('>')) {
      return htmlContent.trim()
    }
  }
  
  return null
}

/**
 * 检测HTML内容是否包含图表代码
 */
export function htmlContainsChart(html: string): boolean {
  const chartKeywords = [
    // ECharts 相关
    'echarts', 'option', 'xAxis', 'yAxis', 'series', 'legend',
    'tooltip', 'grid', 'dataZoom', 'brush', 'geo', 'parallel',

    // Chart.js 相关
    'Chart.js', 'chartjs', 'new Chart', 'type:', 'labels:', 'datasets:',

    // D3.js 相关
    'd3.', 'svg', 'selectAll', 'append',

    // 其他图表库
    'plotly', 'highcharts', 'amcharts', 'canvasjs'
  ]

  const lowerHtml = html.toLowerCase()
  return chartKeywords.some(keyword => lowerHtml.includes(keyword.toLowerCase()))
}

/**
 * 检查消息内容是否应该优先使用HTML预览
 * 如果HTML内容包含图表代码，则优先使用HTML预览
 */
export function shouldPrioritizeHtml(content: string): boolean {
  const htmlBlocks = extractHtmlBlocks(content)
  return htmlBlocks.some(block => htmlContainsChart(block.content))
}

/**
 * 清理和美化HTML代码
 */
export function cleanHtml(html: string): string {
  return html
    .replace(/^\s+|\s+$/g, '') // 移除首尾空白
    .replace(/\n\s*\n/g, '\n') // 移除多余的空行
    .replace(/\t/g, '  ') // 将制表符转换为空格
}

/**
 * 验证HTML安全性
 */
export function validateHtmlSafety(html: string): {
  isSafe: boolean
  warnings: string[]
} {
  const warnings: string[] = []
  
  // 检查潜在的危险标签和属性
  const dangerousPatterns = [
    { pattern: /<script\b/i, warning: '包含JavaScript代码' },
    { pattern: /on\w+\s*=/i, warning: '包含事件处理器' },
    { pattern: /javascript:/i, warning: '包含JavaScript协议' },
    { pattern: /<iframe\b/i, warning: '包含iframe标签' },
    { pattern: /<object\b/i, warning: '包含object标签' },
    { pattern: /<embed\b/i, warning: '包含embed标签' },
    { pattern: /<form\b/i, warning: '包含表单元素' }
  ]
  
  dangerousPatterns.forEach(({ pattern, warning }) => {
    if (pattern.test(html)) {
      warnings.push(warning)
    }
  })
  
  return {
    isSafe: warnings.length === 0,
    warnings
  }
}

/**
 * 生成HTML预览的安全配置
 */
export function getHtmlPreviewConfig(html: string) {
  const safety = validateHtmlSafety(html)
  
  return {
    allowScripts: false, // 默认不允许脚本
    sandbox: 'allow-same-origin allow-forms', // 基础沙箱权限
    height: '400px',
    warnings: safety.warnings
  }
}

/**
 * 判断消息是否包含HTML内容
 */
export function hasHtmlContent(content: string): boolean {
  const htmlBlocks = extractHtmlBlocks(content)
  return htmlBlocks.length > 0
}
