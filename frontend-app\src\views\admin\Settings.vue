<template>
  <div class="space-y-6">
    <!-- 页面头部 -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">
        AI模型配置
      </h1>
      <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
        配置AI模型参数和系统密钥，管理模型的启用状态
      </p>
    </div>

    <!-- AI供应商管理 -->
    <div class="card-tech p-6">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
          AI供应商管理
        </h2>
        <el-button type="primary" @click="showAddProviderDialog = true">
          <el-icon class="mr-2"><Plus /></el-icon>
          添加供应商
        </el-button>
      </div>

      <div class="space-y-4">
        <div
          v-for="provider in aiProviders"
          :key="provider.id"
          class="border border-gray-200 dark:border-dark-700 rounded-lg p-6"
        >
          <div class="flex items-start justify-between">
            <div class="flex items-start space-x-4">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center"
                   :class="getProviderIconClass(provider.name)">
                <el-icon :size="24">
                  <component :is="getProviderIcon(provider.name)" />
                </el-icon>
              </div>

              <div class="flex-1">
                <div class="flex items-center space-x-3 mb-2">
                  <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                    {{ provider.display_name }}
                  </h3>
                  <el-tag :type="provider.is_active ? 'success' : 'info'" size="small">
                    {{ provider.is_active ? '已启用' : '已禁用' }}
                  </el-tag>
                </div>

                <div class="space-y-2">
                  <p class="text-sm text-gray-600 dark:text-gray-400">
                    {{ provider.description || '暂无描述' }}
                  </p>
                  <p class="text-xs text-gray-500">
                    API地址: {{ provider.base_url || '默认' }}
                  </p>
                </div>
              </div>
            </div>

            <div class="flex items-center space-x-2">
              <el-switch
                v-model="provider.is_active"
                @change="updateProviderStatus(provider)"
              />
              <el-dropdown @command="(command: string) => handleProviderAction(command, provider)">
                <el-button circle size="small">
                  <el-icon><MoreFilled /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="edit">
                      <el-icon><Edit /></el-icon>
                      编辑
                    </el-dropdown-item>
                    <el-dropdown-item command="models">
                      <el-icon><ChatDotRound /></el-icon>
                      管理模型
                    </el-dropdown-item>
                    <el-dropdown-item command="delete" divided>
                      <el-icon><Delete /></el-icon>
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- AI模型管理 -->
    <div class="card-tech p-6">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
          AI模型管理
        </h2>
        <div class="flex items-center space-x-3">
          <el-select v-model="selectedProvider" placeholder="选择供应商筛选" clearable style="width: 200px">
            <el-option
              v-for="provider in aiProviders"
              :key="provider.id"
              :label="provider.display_name"
              :value="provider.id"
            />
          </el-select>
          <el-button type="primary" @click="showAddModelDialog = true">
            <el-icon class="mr-2"><Plus /></el-icon>
            添加模型
          </el-button>
        </div>
      </div>

      <div class="space-y-4">
        <div
          v-for="model in filteredModels"
          :key="model.id"
          class="border border-gray-200 dark:border-dark-700 rounded-lg p-6"
        >
          <div class="flex items-start justify-between">
            <div class="flex items-start space-x-4">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center"
                   :class="getModelIconClass(model.provider?.name)">
                <el-icon :size="24">
                  <component :is="getModelIcon(model.provider?.name)" />
                </el-icon>
              </div>

              <div class="flex-1">
                <div class="flex items-center space-x-3 mb-2">
                  <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                    {{ model.display_name }}
                  </h3>
                  <el-tag :type="model.is_active ? 'success' : 'info'" size="small">
                    {{ model.is_active ? '已启用' : '已禁用' }}
                  </el-tag>
                  <el-tag type="info" size="small">
                    {{ model.provider?.display_name }}
                  </el-tag>
                </div>

                <div class="space-y-3">
                  <div class="text-sm text-gray-600 dark:text-gray-400">
                    <p>模型名称: {{ model.model_name }}</p>
                    <p v-if="model.max_tokens">最大Token: {{ model.max_tokens }}</p>
                    <p v-if="model.cost_per_1k_tokens">成本: ${{ model.cost_per_1k_tokens }}/1K tokens</p>
                  </div>

                  <!-- 系统密钥配置 -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      系统API密钥
                    </label>
                    <div class="flex items-center space-x-3">
                      <el-input
                        v-model="model.system_api_key"
                        type="password"
                        placeholder="请输入系统API密钥"
                        show-password
                        class="flex-1"
                      />
                      <el-button @click="testModelConnection(model)" :loading="model.testing">
                        测试连接
                      </el-button>
                    </div>
                  </div>

                  <!-- 用户密钥设置 -->
                  <div class="flex items-center justify-between">
                    <div>
                      <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                        允许用户使用系统密钥
                      </span>
                      <p class="text-xs text-gray-500 mt-1">
                        启用后，用户可以使用系统配置的API密钥
                      </p>
                    </div>
                    <el-switch v-model="model.allow_system_key_use" />
                  </div>
                </div>
              </div>
            </div>

            <div class="flex items-center space-x-2">
              <el-switch
                v-model="model.is_active"
                @change="updateModelStatus(model)"
              />
              <el-dropdown @command="(command: string) => handleModelAction(command, model)">
                <el-button circle size="small">
                  <el-icon><MoreFilled /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="edit">
                      <el-icon><Edit /></el-icon>
                      编辑
                    </el-dropdown-item>
                    <el-dropdown-item command="delete" divided>
                      <el-icon><Delete /></el-icon>
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 系统配置 -->
    <div class="card-tech p-6">
      <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">
        系统配置
      </h2>

      <el-form :model="systemConfig" label-width="200px" class="max-w-2xl">
        <el-form-item label="默认模型">
          <el-select v-model="systemConfig.defaultModel" placeholder="选择默认AI模型" style="width: 300px">
            <el-option
              v-for="model in enabledModels"
              :key="model.id"
              :label="model.display_name"
              :value="model.id"
            />
          </el-select>
          <div class="text-xs text-gray-500 mt-1">
            新用户注册时的默认AI模型
          </div>
        </el-form-item>

        <el-form-item label="最大上传文件大小">
          <el-input-number
            v-model="systemConfig.maxFileSize"
            :min="1"
            :max="100"
            placeholder="MB"
          />
          <span class="ml-2 text-sm text-gray-500">MB</span>
        </el-form-item>

        <el-form-item label="用户默认存储配额">
          <el-input-number
            v-model="systemConfig.defaultStorageQuota"
            :min="1"
            :max="100"
            placeholder="GB"
          />
          <span class="ml-2 text-sm text-gray-500">GB</span>
        </el-form-item>

        <el-form-item label="允许用户注册">
          <el-switch v-model="systemConfig.allowUserRegistration" />
          <div class="text-xs text-gray-500 mt-1">
            关闭后，只有管理员可以创建新用户
          </div>
        </el-form-item>

        <el-form-item label="文档处理超时">
          <el-input-number
            v-model="systemConfig.documentProcessTimeout"
            :min="30"
            :max="600"
            placeholder="秒"
          />
          <span class="ml-2 text-sm text-gray-500">秒</span>
        </el-form-item>

        <el-form-item label="对话历史保留">
          <el-select v-model="systemConfig.chatRetentionDays" style="width: 200px">
            <el-option label="7天" :value="7" />
            <el-option label="30天" :value="30" />
            <el-option label="90天" :value="90" />
            <el-option label="永久保留" :value="0" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="saveSystemConfig" :loading="saving">
            保存配置
          </el-button>
          <el-button @click="resetSystemConfig">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 系统维护 -->
    <div class="card-tech p-6">
      <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">
        系统维护
      </h2>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="space-y-4">
          <h3 class="font-medium text-gray-900 dark:text-gray-100">
            数据库维护
          </h3>
          <div class="space-y-3">
            <el-button @click="optimizeDatabase" :loading="optimizing">
              <el-icon class="mr-2"><Tools /></el-icon>
              优化数据库
            </el-button>
            <el-button @click="cleanupTempFiles" :loading="cleaning">
              <el-icon class="mr-2"><Delete /></el-icon>
              清理临时文件
            </el-button>
            <el-button @click="rebuildSearchIndex" :loading="rebuilding">
              <el-icon class="mr-2"><Refresh /></el-icon>
              重建搜索索引
            </el-button>
          </div>
        </div>

        <div class="space-y-4">
          <h3 class="font-medium text-gray-900 dark:text-gray-100">
            系统备份
          </h3>
          <div class="space-y-3">
            <el-button @click="createBackup" :loading="backing">
              <el-icon class="mr-2"><Download /></el-icon>
              创建系统备份
            </el-button>
            <el-button @click="showBackupHistory = true">
              <el-icon class="mr-2"><FolderOpened /></el-icon>
              备份历史
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加/编辑供应商对话框 -->
    <el-dialog
      v-model="showAddProviderDialog"
      :title="editingProviderId ? '编辑AI供应商' : '添加AI供应商'"
      width="500px"
      @close="resetProviderForm"
    >
      <el-form :model="providerForm" :rules="providerFormRules" ref="providerFormRef" label-width="120px">
        <el-form-item label="供应商标识" prop="name">
          <el-input
            v-model="providerForm.name"
            placeholder="如: openai, siliconflow"
          />
        </el-form-item>

        <el-form-item label="显示名称" prop="display_name">
          <el-input
            v-model="providerForm.display_name"
            placeholder="如: OpenAI, 硅基流动"
          />
        </el-form-item>

        <el-form-item label="API地址">
          <el-input
            v-model="providerForm.base_url"
            placeholder="如: https://api.openai.com/v1"
          />
        </el-form-item>

        <el-form-item label="描述">
          <el-input
            v-model="providerForm.description"
            type="textarea"
            placeholder="供应商描述信息"
            :rows="3"
          />
        </el-form-item>

        <el-form-item label="启用状态">
          <el-switch v-model="providerForm.is_active" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showAddProviderDialog = false">取消</el-button>
          <el-button type="primary" @click="handleProviderSubmit" :loading="submitting">
            {{ editingProviderId ? '更新' : '添加' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加模型对话框 -->
    <el-dialog
      v-model="showAddModelDialog"
      title="添加AI模型"
      width="600px"
      @close="resetModelForm"
    >
      <el-form :model="modelForm" :rules="modelFormRules" ref="modelFormRef" label-width="120px">
        <el-form-item label="供应商" prop="provider_id">
          <el-select v-model="modelForm.provider_id" placeholder="选择供应商" style="width: 100%">
            <el-option
              v-for="provider in aiProviders"
              :key="provider.id"
              :label="provider.display_name"
              :value="provider.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="模型名称" prop="model_name">
          <el-input
            v-model="modelForm.model_name"
            placeholder="如: gpt-4, Qwen/Qwen2.5-7B-Instruct"
          />
        </el-form-item>

        <el-form-item label="显示名称" prop="display_name">
          <el-input
            v-model="modelForm.display_name"
            placeholder="如: GPT-4, Qwen2.5-7B-Instruct"
          />
        </el-form-item>

        <el-form-item label="系统密钥">
          <el-input
            v-model="modelForm.system_api_key"
            type="password"
            placeholder="请输入系统API密钥（可选）"
            show-password
          />
        </el-form-item>

        <el-form-item label="最大Token">
          <el-input-number
            v-model="modelForm.max_tokens"
            :min="1"
            :max="200000"
            placeholder="4000"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="成本/1K Token">
          <el-input-number
            v-model="modelForm.cost_per_1k_tokens"
            :min="0"
            :precision="4"
            :step="0.001"
            placeholder="0.002"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="启用状态">
          <el-switch v-model="modelForm.is_active" />
        </el-form-item>

        <el-form-item label="允许系统密钥">
          <el-switch v-model="modelForm.allow_system_key_use" />
          <div class="text-xs text-gray-500 mt-1">
            允许用户使用系统配置的API密钥
          </div>
        </el-form-item>

        <el-form-item label="支持流式">
          <el-switch v-model="modelForm.supports_streaming" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showAddModelDialog = false">取消</el-button>
          <el-button type="primary" @click="handleModelSubmit" :loading="submitting">
            添加
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 备份历史对话框 -->
    <el-dialog
      v-model="showBackupHistory"
      title="备份历史"
      width="600px"
    >
      <div class="space-y-3">
        <div
          v-for="backup in backupHistory"
          :key="backup.id"
          class="flex items-center justify-between p-3 border border-gray-200 dark:border-dark-700 rounded-lg"
        >
          <div>
            <div class="font-medium text-gray-900 dark:text-gray-100">
              {{ backup.filename }}
            </div>
            <div class="text-sm text-gray-500">
              {{ formatDate(backup.createdAt) }} • {{ formatStorage(backup.size) }}
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <el-button size="small" @click="downloadBackup(backup)">
              <el-icon><Download /></el-icon>
            </el-button>
            <el-button size="small" type="danger" @click="deleteBackup(backup)">
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { AIModel, AIProvider } from '@/types'
import {
  Plus,
  MoreFilled,
  Edit,
  Delete,
  Tools,
  Refresh,
  Download,
  FolderOpened,
  ChatDotRound
} from '@element-plus/icons-vue'
import { api } from '@/utils/api'
import { adminAPI } from '@/api/admin'

// 扩展的 AI Model 接口，包含前端特有的属性
interface ExtendedAIModel extends AIModel {
  testing?: boolean
}

// 扩展的 AI Provider 接口，包含前端特有的属性
interface ExtendedAIProvider extends AIProvider {
  testing?: boolean
}

// 响应式数据
const saving = ref(false)
const submitting = ref(false)
const optimizing = ref(false)
const cleaning = ref(false)
const rebuilding = ref(false)
const backing = ref(false)

// 对话框状态
const showAddProviderDialog = ref(false)
const showAddModelDialog = ref(false)
const showBackupHistory = ref(false)

// 表单引用
const providerFormRef = ref()
const modelFormRef = ref()

// AI供应商和模型数据
const aiProviders = ref<ExtendedAIProvider[]>([])
const aiModels = ref<ExtendedAIModel[]>([])
const selectedProvider = ref<number | null>(null)
const editingProviderId = ref<number | null>(null)

// 系统配置
const systemConfig = ref({
  defaultModel: 1,
  maxFileSize: 50,
  defaultStorageQuota: 10,
  allowUserRegistration: true,
  documentProcessTimeout: 300,
  chatRetentionDays: 30
})

// 供应商表单
const providerForm = ref({
  name: '',
  display_name: '',
  base_url: '',
  description: '',
  is_active: true
})

const providerFormRules = {
  name: [
    { required: true, message: '请输入供应商标识', trigger: 'blur' }
  ],
  display_name: [
    { required: true, message: '请输入显示名称', trigger: 'blur' }
  ]
}

// 模型表单
const modelForm = ref({
  provider_id: 0,
  model_name: '',
  display_name: '',
  system_api_key: '',
  is_active: true,
  allow_system_key_use: true,
  max_tokens: 4000,
  supports_streaming: true,
  cost_per_1k_tokens: undefined as number | undefined
})

const modelFormRules = {
  provider_id: [
    { required: true, message: '请选择供应商', trigger: 'change' }
  ],
  model_name: [
    { required: true, message: '请输入模型名称', trigger: 'blur' }
  ],
  display_name: [
    { required: true, message: '请输入显示名称', trigger: 'blur' }
  ]
}

// 备份历史
const backupHistory = ref([
  {
    id: 1,
    filename: 'system-backup-2024-01-20.zip',
    size: 125 * 1024 * 1024,
    createdAt: '2024-01-20T10:30:00Z'
  },
  {
    id: 2,
    filename: 'system-backup-2024-01-19.zip',
    size: 120 * 1024 * 1024,
    createdAt: '2024-01-19T10:30:00Z'
  }
])

// 计算属性
const enabledModels = computed(() => {
  return aiModels.value.filter(model => model.is_active)
})

const filteredModels = computed(() => {
  if (!selectedProvider.value) {
    return aiModels.value
  }
  return aiModels.value.filter(model => model.provider_id === selectedProvider.value)
})

// 工具方法
const formatStorage = (bytes: number) => {
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  if (bytes === 0) return '0 B'
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const getProviderIcon = (providerName?: string) => {
  return ChatDotRound // 统一使用ChatDotRound图标
}

const getProviderIconClass = (providerName?: string) => {
  switch (providerName?.toLowerCase()) {
    case 'openai':
      return 'bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400'
    case 'anthropic':
      return 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
    case 'google':
      return 'bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-400'
    case 'siliconflow':
    case '硅基流动':
      return 'bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400'
    case 'deepseek':
      return 'bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-400'
    default:
      return 'bg-gray-100 dark:bg-gray-900 text-gray-600 dark:text-gray-400'
  }
}

const getModelIcon = (providerName?: string) => {
  return ChatDotRound // 统一使用ChatDotRound图标
}

const getModelIconClass = (providerName?: string) => {
  return getProviderIconClass(providerName || '')
}

// 数据加载方法
const loadProviders = async () => {
  try {
    aiProviders.value = await adminAPI.getAIProviders()
  } catch (error) {
    console.error('加载供应商列表失败:', error)
    ElMessage.error('加载供应商列表失败')
  }
}

const loadModels = async () => {
  try {
    aiModels.value = await adminAPI.getAIModels()
  } catch (error) {
    console.error('加载模型列表失败:', error)
    ElMessage.error('加载模型列表失败')
  }
}

// 加载系统配置
const loadSystemConfig = async () => {
  try {
    const settings = await adminAPI.getSettings()

    // 解析系统设置
    const settingsMap = settings.reduce((map: any, setting: any) => {
      map[setting.key] = setting.value
      return map
    }, {})

    // 更新系统配置
    systemConfig.value = {
      defaultModel: parseInt(settingsMap.default_model_id) || 1,
      maxFileSize: parseInt(settingsMap.max_file_size_mb) || 50,
      defaultStorageQuota: parseInt(settingsMap.default_storage_quota_gb) || 10,
      allowUserRegistration: settingsMap.allow_user_registration === 'true',
      documentProcessTimeout: settingsMap.document_process_timeout !== undefined ? parseInt(settingsMap.document_process_timeout) : 300,
      chatRetentionDays: settingsMap.chat_retention_days !== undefined ? parseInt(settingsMap.chat_retention_days) : 30
    }
  } catch (error) {
    console.error('加载系统配置失败:', error)
    ElMessage.error('加载系统配置失败')
  }
}

// 供应商操作方法
const updateProviderStatus = async (provider: ExtendedAIProvider) => {
  try {
    await adminAPI.updateAIProvider(provider.id, { is_active: provider.is_active })
    ElMessage.success(`${provider.display_name} ${provider.is_active ? '已启用' : '已禁用'}`)
  } catch (error) {
    ElMessage.error('更新失败')
    provider.is_active = !provider.is_active // 回滚状态
  }
}

const handleProviderAction = async (command: string, provider: ExtendedAIProvider) => {
  switch (command) {
    case 'edit':
      editProvider(provider)
      break
    case 'models':
      selectedProvider.value = provider.id
      break
    case 'delete':
      await deleteProvider(provider)
      break
  }
}

const editProvider = (provider: ExtendedAIProvider) => {
  providerForm.value = {
    name: provider.name,
    display_name: provider.display_name,
    base_url: provider.base_url || '',
    description: provider.description || '',
    is_active: provider.is_active
  }
  editingProviderId.value = provider.id
  showAddProviderDialog.value = true
}

const deleteProvider = async (provider: ExtendedAIProvider) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除供应商"${provider.display_name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await adminAPI.deleteAIProvider(provider.id)
    await loadProviders()
    await loadModels()
    ElMessage.success('供应商删除成功')
  } catch (error: any) {
    if (error?.message !== 'cancel') {
      console.error('删除供应商失败:', error)
      ElMessage.error(error.response?.data?.detail || '删除失败')
    }
  }
}

const handleProviderSubmit = async () => {
  if (!providerFormRef.value) return

  try {
    await providerFormRef.value.validate()
    submitting.value = true

    if (editingProviderId.value) {
      // 编辑模式
      await adminAPI.updateAIProvider(editingProviderId.value, providerForm.value)
      ElMessage.success('供应商更新成功')
    } else {
      // 添加模式
      await adminAPI.createAIProvider(providerForm.value)
      ElMessage.success('供应商添加成功')
    }

    await loadProviders()
    showAddProviderDialog.value = false
    resetProviderForm()
  } catch (error: any) {
    console.error('操作供应商失败:', error)
    ElMessage.error(error.response?.data?.detail || '操作失败')
  } finally {
    submitting.value = false
  }
}

const resetProviderForm = () => {
  providerForm.value = {
    name: '',
    display_name: '',
    base_url: '',
    description: '',
    is_active: true
  }
  editingProviderId.value = null
  if (providerFormRef.value) {
    providerFormRef.value.resetFields()
  }
}

// AI模型操作方法
const testModelConnection = async (model: ExtendedAIModel) => {
  if (!model.system_api_key) {
    ElMessage.warning('请先配置API密钥')
    return
  }

  model.testing = true
  try {
    const response = await api.post('/ai/test-connection', {
      provider_id: model.provider_id,
      model_name: model.model_name,
      api_key: model.system_api_key
    })

    const result = response.data

    if (result.success) {
      ElMessage.success(`${model.display_name} 连接测试成功`)
    } else {
      ElMessage.error(`${model.display_name} 连接测试失败: ${result.message}`)
    }
  } catch (error) {
    ElMessage.error(`${model.display_name} 连接测试失败`)
  } finally {
    model.testing = false
  }
}

const updateModelStatus = async (model: ExtendedAIModel) => {
  try {
    await api.put(`/ai/models/${model.id}`, { is_active: model.is_active })
    ElMessage.success(`${model.display_name} ${model.is_active ? '已启用' : '已禁用'}`)
  } catch (error) {
    ElMessage.error('更新失败')
    model.is_active = !model.is_active // 回滚状态
  }
}

const handleModelAction = async (command: string, model: ExtendedAIModel) => {
  switch (command) {
    case 'edit':
      editModel(model)
      break
    case 'delete':
      await deleteModel(model)
      break
  }
}

const editModel = (model: ExtendedAIModel) => {
  modelForm.value = {
    provider_id: model.provider_id,
    model_name: model.model_name,
    display_name: model.display_name,
    system_api_key: model.system_api_key || '',
    is_active: model.is_active,
    allow_system_key_use: model.allow_system_key_use,
    max_tokens: model.max_tokens || 4000,
    supports_streaming: model.supports_streaming,
    cost_per_1k_tokens: model.cost_per_1k_tokens || undefined
  }
  showAddModelDialog.value = true
}

const deleteModel = async (model: ExtendedAIModel) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除模型"${model.display_name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await adminAPI.deleteAIModel(model.id)
    await loadModels()
    ElMessage.success('模型删除成功')
  } catch (error: any) {
    if (error?.message !== 'cancel') {
      console.error('删除模型失败:', error)
      ElMessage.error(error.response?.data?.detail || '删除失败')
    }
  }
}

const handleModelSubmit = async () => {
  if (!modelFormRef.value) return

  try {
    await modelFormRef.value.validate()
    submitting.value = true

    await adminAPI.createAIModel(modelForm.value)
    await loadModels()
    showAddModelDialog.value = false
    resetModelForm()
    ElMessage.success('模型添加成功')
  } catch (error: any) {
    console.error('添加模型失败:', error)
    ElMessage.error(error.response?.data?.detail || '添加失败')
  } finally {
    submitting.value = false
  }
}

const resetModelForm = () => {
  modelForm.value = {
    provider_id: 0,
    model_name: '',
    display_name: '',
    system_api_key: '',
    is_active: true,
    allow_system_key_use: true,
    max_tokens: 4000,
    supports_streaming: true,
    cost_per_1k_tokens: undefined
  }
  if (modelFormRef.value) {
    modelFormRef.value.resetFields()
  }
}

// 系统配置方法
const saveSystemConfig = async () => {
  try {
    saving.value = true

    // 保存各个系统设置
    const settings = [
      { key: 'default_model_id', value: systemConfig.value.defaultModel.toString(), description: '新用户注册时的默认AI模型' },
      { key: 'max_file_size_mb', value: systemConfig.value.maxFileSize.toString(), description: '最大上传文件大小(MB)' },
      { key: 'default_storage_quota_gb', value: systemConfig.value.defaultStorageQuota.toString(), description: '用户默认存储配额(GB)' },
      { key: 'allow_user_registration', value: systemConfig.value.allowUserRegistration.toString(), description: '是否允许用户注册' },
      { key: 'document_process_timeout', value: systemConfig.value.documentProcessTimeout.toString(), description: '文档处理超时时间(秒)' },
      { key: 'chat_retention_days', value: systemConfig.value.chatRetentionDays.toString(), description: '对话历史保留天数' }
    ]

    // 批量保存设置
    for (const setting of settings) {
      await adminAPI.updateSetting(setting.key, {
        value: setting.value,
        description: setting.description
      })
    }

    ElMessage.success('系统配置保存成功')
  } catch (error) {
    console.error('保存系统配置失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const resetSystemConfig = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重置所有系统配置为默认值吗？',
      '重置确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 重置为默认值
    systemConfig.value = {
      defaultModel: 1,
      maxFileSize: 50,
      defaultStorageQuota: 10,
      allowUserRegistration: true,
      documentProcessTimeout: 300,
      chatRetentionDays: 30
    }

    ElMessage.success('配置已重置为默认值')
  } catch (error) {
    // 用户取消重置
  }
}

// 系统维护方法
const optimizeDatabase = async () => {
  try {
    optimizing.value = true
    await new Promise(resolve => setTimeout(resolve, 3000))
    ElMessage.success('数据库优化完成')
  } catch (error) {
    ElMessage.error('数据库优化失败')
  } finally {
    optimizing.value = false
  }
}

const cleanupTempFiles = async () => {
  try {
    cleaning.value = true
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('临时文件清理完成')
  } catch (error) {
    ElMessage.error('清理失败')
  } finally {
    cleaning.value = false
  }
}

const rebuildSearchIndex = async () => {
  try {
    rebuilding.value = true
    await new Promise(resolve => setTimeout(resolve, 5000))
    ElMessage.success('搜索索引重建完成')
  } catch (error) {
    ElMessage.error('重建失败')
  } finally {
    rebuilding.value = false
  }
}

const createBackup = async () => {
  try {
    backing.value = true
    await new Promise(resolve => setTimeout(resolve, 3000))

    const newBackup = {
      id: Date.now(),
      filename: `system-backup-${new Date().toISOString().split('T')[0]}.zip`,
      size: Math.floor(Math.random() * 50 + 100) * 1024 * 1024,
      createdAt: new Date().toISOString()
    }

    backupHistory.value.unshift(newBackup)
    ElMessage.success('系统备份创建成功')
  } catch (error) {
    ElMessage.error('备份创建失败')
  } finally {
    backing.value = false
  }
}

const downloadBackup = (backup: any) => {
  ElMessage.success(`开始下载 ${backup.filename}`)
}

const deleteBackup = async (backup: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除备份"${backup.filename}"吗？`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const index = backupHistory.value.findIndex(b => b.id === backup.id)
    if (index !== -1) {
      backupHistory.value.splice(index, 1)
    }

    ElMessage.success('备份删除成功')
  } catch (error) {
    // 用户取消删除
  }
}

onMounted(async () => {
  // 加载系统配置和AI模型数据
  await Promise.all([
    loadSystemConfig(),
    loadProviders(),
    loadModels()
  ])
})
</script>
