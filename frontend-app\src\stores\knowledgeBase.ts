import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { KnowledgeBase, CreateKnowledgeBaseForm } from '@/types'
import { knowledgeBaseAPI } from '@/api/knowledgeBase'

export const useKnowledgeBaseStore = defineStore('knowledgeBase', () => {
  // 状态
  const knowledgeBases = ref<KnowledgeBase[]>([])
  const currentKnowledgeBase = ref<KnowledgeBase | null>(null)
  const loading = ref(false)

  // 获取知识库列表
  const fetchKnowledgeBases = async (params?: any) => {
    loading.value = true
    try {
      const data = await knowledgeBaseAPI.getList(params)
      knowledgeBases.value = data
      return { success: true, data }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || '获取知识库列表失败'
      }
    } finally {
      loading.value = false
    }
  }

  // 创建知识库
  const createKnowledgeBase = async (formData: CreateKnowledgeBaseForm) => {
    loading.value = true
    try {
      const newKnowledgeBase = await knowledgeBaseAPI.create(formData)
      
      knowledgeBases.value.unshift(newKnowledgeBase)
      
      return { success: true, data: newKnowledgeBase }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || '创建知识库失败' 
      }
    } finally {
      loading.value = false
    }
  }

  // 获取单个知识库
  const fetchKnowledgeBase = async (id: number) => {
    loading.value = true
    try {
      const knowledgeBase = await knowledgeBaseAPI.getDetail(id)
      currentKnowledgeBase.value = knowledgeBase
      return { success: true, data: knowledgeBase }
    } catch (error: any) {
      return {
        success: false,
        data: null,
        message: error.response?.data?.message || '获取知识库详情失败'
      }
    } finally {
      loading.value = false
    }
  }

  // 更新知识库
  const updateKnowledgeBase = async (id: number, formData: Partial<CreateKnowledgeBaseForm>) => {
    loading.value = true
    try {
      const updatedKnowledgeBase = await knowledgeBaseAPI.update(id, formData)

      // 更新列表中的数据
      const index = knowledgeBases.value.findIndex(kb => kb.id === id)
      if (index !== -1) {
        knowledgeBases.value[index] = updatedKnowledgeBase
      }
      
      // 更新当前知识库
      if (currentKnowledgeBase.value?.id === id) {
        currentKnowledgeBase.value = updatedKnowledgeBase
      }
      
      return { success: true, data: updatedKnowledgeBase }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || '更新知识库失败' 
      }
    } finally {
      loading.value = false
    }
  }

  // 删除知识库
  const deleteKnowledgeBase = async (id: number) => {
    loading.value = true
    try {
      await knowledgeBaseAPI.delete(id)

      // 从列表中移除
      knowledgeBases.value = knowledgeBases.value.filter(kb => kb.id !== id)

      // 清除当前知识库
      if (currentKnowledgeBase.value?.id === id) {
        currentKnowledgeBase.value = null
      }

      return { success: true }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || '删除知识库失败'
      }
    } finally {
      loading.value = false
    }
  }

  // 清除当前知识库
  const clearCurrentKnowledgeBase = () => {
    currentKnowledgeBase.value = null
  }

  return {
    // 状态
    knowledgeBases,
    currentKnowledgeBase,
    loading,

    // 方法
    fetchKnowledgeBases,
    createKnowledgeBase,
    fetchKnowledgeBase,
    updateKnowledgeBase,
    deleteKnowledgeBase,
    clearCurrentKnowledgeBase
  }
})
