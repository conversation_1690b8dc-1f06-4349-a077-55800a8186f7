import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { ChatSession, ChatMessage, CreateSessionRequest, ChatRequest } from '@/api/chat'
import { chatAPI } from '@/api/chat'

export const useChatStore = defineStore('chat', () => {
  // 状态
  const sessions = ref<ChatSession[]>([])
  const currentSession = ref<ChatSession | null>(null)
  const messages = ref<Record<number, ChatMessage[]>>({})
  const loading = ref(false)
  const sending = ref(false)

  // 获取聊天会话列表
  const fetchSessions = async (params?: any) => {
    loading.value = true
    try {
      const data = await chatAPI.getSessions(params)
      sessions.value = data
      return { success: true, data }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || '获取聊天会话失败' 
      }
    } finally {
      loading.value = false
    }
  }

  // 创建新的聊天会话
  const createSession = async (sessionData: CreateSessionRequest) => {
    loading.value = true
    try {
      const newSession = await chatAPI.createSession(sessionData)
      sessions.value.unshift(newSession)
      currentSession.value = newSession
      messages.value[newSession.id] = []
      return { success: true, data: newSession }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || '创建聊天会话失败' 
      }
    } finally {
      loading.value = false
    }
  }

  // 获取聊天历史
  const fetchChatHistory = async (sessionId: number, params?: any) => {
    loading.value = true
    try {
      const data = await chatAPI.getMessages(sessionId, params)
      messages.value[sessionId] = data
      return { success: true, data }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || '获取聊天历史失败' 
      }
    } finally {
      loading.value = false
    }
  }

  // 删除会话
  const deleteSession = async (sessionId: number) => {
    try {
      await chatAPI.deleteSession(sessionId)
      sessions.value = sessions.value.filter(s => s.id !== sessionId)
      delete messages.value[sessionId]
      if (currentSession.value?.id === sessionId) {
        currentSession.value = null
      }
      return { success: true }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || '删除会话失败' 
      }
    }
  }

  // 更新会话标题
  const updateSessionTitle = async (sessionId: number, title: string) => {
    try {
      const response = await chatAPI.updateSession(sessionId, { title })
      const sessionIndex = sessions.value.findIndex(s => s.id === sessionId)
      if (sessionIndex !== -1) {
        sessions.value[sessionIndex] = response
      }
      if (currentSession.value?.id === sessionId) {
        currentSession.value = response
      }
      return { success: true, data: response }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || '更新会话失败' 
      }
    }
  }

  // 发送消息（流式）
  const sendMessage = async (sessionId: number, chatData: ChatRequest, abortController?: AbortController) => {
    sending.value = true
    
    try {
      const userMessage: ChatMessage = {
        id: Date.now(),
        session_id: sessionId,
        role: 'user',
        content: chatData.message,
        created_at: new Date().toISOString()
      }
      
      if (!messages.value[sessionId]) {
        messages.value[sessionId] = []
      }
      messages.value[sessionId].push(userMessage)

      const aiMessage: ChatMessage = {
        id: Date.now() + 1,
        session_id: sessionId,
        role: 'assistant',
        content: '',
        created_at: new Date().toISOString()
      }
      messages.value[sessionId].push(aiMessage)

      await chatAPI.streamChat(
        sessionId,
        chatData,
        (content: string) => {
          const aiMsgIndex = messages.value[sessionId].findIndex(m => m.id === aiMessage.id)
          if (aiMsgIndex !== -1) {
            messages.value[sessionId][aiMsgIndex].content += content
          }
        },
        (finalMessage: ChatMessage) => {
          const aiMsgIndex = messages.value[sessionId].findIndex(m => m.id === aiMessage.id)
          if (aiMsgIndex !== -1) {
            messages.value[sessionId][aiMsgIndex] = finalMessage
          }
        },
        (error: string) => {
          // 不删除AI消息，而是更新为错误消息
          const aiMsgIndex = messages.value[sessionId].findIndex(m => m.id === aiMessage.id)
          if (aiMsgIndex !== -1) {
            messages.value[sessionId][aiMsgIndex] = {
              ...aiMessage,
              content: error,
              role: 'assistant',
              isError: true // 标记为错误消息
            }
          }
          throw new Error(error)
        },
        abortController
      )

      return { success: true }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.message || '发送消息失败' 
      }
    } finally {
      sending.value = false
    }
  }

  // 设置当前会话
  const setCurrentSession = async (sessionId: number) => {
    const session = sessions.value.find(s => s.id === sessionId)
    if (session) {
      currentSession.value = session
      if (!messages.value[sessionId]) {
        await fetchChatHistory(sessionId)
      }
      return { success: true, data: session }
    }
    return { success: false, message: '会话不存在' }
  }

  // 清空所有数据
  const clearAll = () => {
    sessions.value = []
    currentSession.value = null
    messages.value = {}
  }

  return {
    sessions,
    currentSession,
    messages,
    loading,
    sending,
    fetchSessions,
    createSession,
    fetchChatHistory,
    sendMessage,
    deleteSession,
    updateSessionTitle,
    setCurrentSession,
    clearAll
  }
})
