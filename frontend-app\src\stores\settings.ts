import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { UserSettings, AIModel, UserAPIKey } from '@/types'
import { api } from '@/utils/api'

export const useSettingsStore = defineStore('settings', () => {
  // 状态
  const userSettings = ref<UserSettings>({
    theme: 'light',
    fontSize: 'medium',
    defaultModel: null,
    defaultKnowledgeBases: [],
    chatRetentionDays: 30,
    enableHighContrast: false,
    enableReducedMotion: false,
    providerPreferences: {} // 默认为空，让组件处理默认启用逻辑
  })
  
  const aiModels = ref<AIModel[]>([])
  const userApiKeys = ref<UserAPIKey[]>([])
  const loading = ref(false)

  // 获取用户设置
  const fetchUserSettings = async () => {
    loading.value = true
    try {
      const response = await api.get('/users/me/settings')
      const settings = response.data
      console.log('后端返回的原始设置数据:', settings)

      // 直接映射字段名，后端已经处理了JSON解析
      userSettings.value = {
        theme: settings.theme || 'auto',
        fontSize: settings.font_size || 'medium',
        defaultModel: settings.default_model_id || null,
        defaultKnowledgeBases: settings.default_knowledge_bases || [],
        chatRetentionDays: settings.chat_retention_days || 30,
        enableHighContrast: settings.enable_high_contrast || false,
        enableReducedMotion: settings.enable_reduced_motion || false,
        providerPreferences: settings.provider_preferences || {}
      }

      console.log('映射后的用户设置:', userSettings.value)

      return { success: true, data: userSettings.value }
    } catch (error: any) {
      // 如果是401或403错误（未认证），抛出异常让调用者处理
      if (error.response?.status === 401 || error.response?.status === 403) {
        throw error
      }

      return {
        success: false,
        message: error.response?.data?.message || '获取设置失败'
      }
    } finally {
      loading.value = false
    }
  }

  // 更新用户设置
  const updateUserSettings = async (settings: Partial<UserSettings>) => {
    loading.value = true
    try {
      // 映射字段名到后端格式
      const backendSettings: any = {}

      if (settings.theme !== undefined) backendSettings.theme = settings.theme
      if (settings.fontSize !== undefined) backendSettings.font_size = settings.fontSize
      if (settings.defaultModel !== undefined) backendSettings.default_model_id = settings.defaultModel
      if (settings.defaultKnowledgeBases !== undefined) {
        backendSettings.default_knowledge_bases = settings.defaultKnowledgeBases
      }
      if (settings.chatRetentionDays !== undefined) backendSettings.chat_retention_days = settings.chatRetentionDays
      if (settings.enableHighContrast !== undefined) backendSettings.enable_high_contrast = settings.enableHighContrast
      if (settings.enableReducedMotion !== undefined) backendSettings.enable_reduced_motion = settings.enableReducedMotion
      if (settings.providerPreferences !== undefined) backendSettings.provider_preferences = settings.providerPreferences

      const response = await api.put('/users/me/settings', backendSettings)

      // 更新本地设置
      userSettings.value = { ...userSettings.value, ...settings }

      return { success: true, data: userSettings.value }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || '更新设置失败'
      }
    } finally {
      loading.value = false
    }
  }

  // 获取用户API密钥
  const fetchUserApiKeys = async () => {
    loading.value = true
    try {
      const response = await api.get('/ai/api-keys')
      userApiKeys.value = response.data
      return { success: true, data: response.data }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || '获取API密钥失败'
      }
    } finally {
      loading.value = false
    }
  }

  // 更新API密钥
  const updateApiKey = async (keyId: number, keyData: any) => {
    loading.value = true
    try {
      const response = await api.put(`/ai/api-keys/${keyId}`, keyData)
      const updatedKey = response.data

      // 更新本地数据
      const index = userApiKeys.value.findIndex(key => key.id === keyId)
      if (index !== -1) {
        userApiKeys.value[index] = updatedKey
      }

      return { success: true, data: updatedKey }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || '更新API密钥失败'
      }
    } finally {
      loading.value = false
    }
  }

  // 获取可用的AI模型
  const fetchAvailableModels = async () => {
    try {
      const response = await api.get('/ai/models')
      aiModels.value = response.data.filter((model: any) => model.is_active)
      return { success: true, data: aiModels.value }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || '获取AI模型列表失败'
      }
    }
  }

  // 应用主题设置
  const applyTheme = async (theme: 'light' | 'dark' | 'system') => {
    const html = document.documentElement

    if (theme === 'system') {
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      html.classList.toggle('dark', prefersDark)
    } else {
      html.classList.toggle('dark', theme === 'dark')
    }

    userSettings.value.theme = theme

    // 立即保存到后端
    try {
      await updateUserSettings({
        theme: theme
      })
    } catch (error) {
      console.error('保存主题设置失败:', error)
    }
  }

  // 应用字体大小设置
  const applyFontSize = (fontSize: 'small' | 'medium' | 'large') => {
    const html = document.documentElement
    
    html.classList.remove('text-sm', 'text-base', 'text-lg')
    
    switch (fontSize) {
      case 'small':
        html.classList.add('text-sm')
        break
      case 'large':
        html.classList.add('text-lg')
        break
      default:
        html.classList.add('text-base')
    }
    
    userSettings.value.fontSize = fontSize
  }

  // 应用无障碍设置
  const applyAccessibilitySettings = () => {
    const html = document.documentElement
    
    html.classList.toggle('high-contrast', userSettings.value.enableHighContrast)
    html.classList.toggle('reduced-motion', userSettings.value.enableReducedMotion)
  }

  // 初始化设置
  const initializeSettings = async () => {
    // 检查是否有token，如果没有则只应用默认设置
    const token = localStorage.getItem('token')
    if (token) {
      try {
        await fetchUserSettings()
      } catch (error) {
        console.warn('获取用户设置失败，使用默认设置:', error)
      }
    }

    // 应用主题和其他设置（无论是否登录都应用）
    applyTheme(userSettings.value.theme)
    applyFontSize(userSettings.value.fontSize)
    applyAccessibilitySettings()
  }

  return {
    // 状态
    userSettings,
    aiModels,
    userApiKeys,
    loading,
    
    // 方法
    fetchUserSettings,
    updateUserSettings,
    fetchUserApiKeys,
    updateApiKey,
    fetchAvailableModels,
    applyTheme,
    applyFontSize,
    applyAccessibilitySettings,
    initializeSettings
  }
})
