"""
知识库相关模型
"""
from typing import Optional
from sqlmodel import SQLModel, Field
from .base import BaseModel


class KnowledgeBase(BaseModel, table=True):
    """知识库表"""
    __tablename__ = "knowledge_bases"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    owner_id: int = Field(foreign_key="users.id")
    name: str = Field(max_length=100)
    description: Optional[str] = Field(default=None)


class Document(BaseModel, table=True):
    """文档表"""
    __tablename__ = "documents"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    kb_id: int = Field(foreign_key="knowledge_bases.id")
    uploader_id: int = Field(foreign_key="users.id")
    filename: str = Field(max_length=255)
    storage_path: str = Field(max_length=255)
    file_type: Optional[str] = Field(default=None, max_length=50)
    file_size: Optional[int] = Field(default=None)
    status: str = Field(default="pending", max_length=20)  # pending, processing, completed, failed
    error_message: Optional[str] = Field(default=None)


class DocumentChunk(BaseModel, table=True):
    """文档分块表"""
    __tablename__ = "document_chunks"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    doc_id: int = Field(foreign_key="documents.id")
    content: str = Field()
    # vector: 向量字段，具体实现取决于向量数据库选择
    chunk_metadata: Optional[str] = Field(default=None)  # JSON字符串存储元数据
