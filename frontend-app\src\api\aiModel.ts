import request from './request'

// AI供应商接口类型
export interface AIProvider {
  id: number
  name: string
  display_name: string
  base_url?: string
  is_active: boolean
  description?: string
  created_at: string
  updated_at: string
}

// AI模型相关接口类型
export interface AIModel {
  id: number
  provider_id: number
  provider: AIProvider
  model_name: string
  display_name: string
  is_active: boolean
  system_api_key?: string
  allow_system_key_use: boolean
  max_tokens?: number
  supports_streaming: boolean
  cost_per_1k_tokens?: number
  created_at: string
  updated_at: string
}

export interface UserAPIKey {
  id: number
  user_id: number
  provider_name: string
  api_key: string
  description?: string
  created_at: string
  updated_at: string
}

export interface CreateAPIKeyRequest {
  provider_name: string
  api_key: string
  description?: string
}

export interface UpdateAPIKeyRequest {
  api_key: string
  description?: string
}

// AI模型API
export const aiModelAPI = {
  // 获取AI模型列表
  getModels: (): Promise<AIModel[]> => {
    return request.get('/ai/models')
  },

  // 获取用户API密钥列表
  getUserAPIKeys: (): Promise<UserAPIKey[]> => {
    return request.get('/ai/api-keys')
  },

  // 创建用户API密钥
  createAPIKey: (data: CreateAPIKeyRequest): Promise<UserAPIKey> => {
    return request.post('/ai/api-keys', data)
  },

  // 更新用户API密钥
  updateAPIKey: (keyId: number, data: UpdateAPIKeyRequest): Promise<UserAPIKey> => {
    return request.put(`/ai/api-keys/${keyId}`, data)
  },

  // 删除用户API密钥
  deleteAPIKey: (keyId: number): Promise<{ message: string }> => {
    return request.delete(`/ai/api-keys/${keyId}`)
  }
}
