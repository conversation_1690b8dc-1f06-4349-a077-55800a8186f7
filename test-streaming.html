<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流式渲染测试</title>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-area { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .content { min-height: 200px; padding: 15px; background: #f9f9f9; border: 1px solid #ccc; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 4px; }
        .controls { margin: 10px 0; }
        button { margin: 5px; padding: 8px 16px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>流式渲染表格测试</h1>
        
        <div class="test-area">
            <h3>模拟流式渲染过程</h3>
            <div class="controls">
                <button onclick="startStreaming()">开始流式渲染</button>
                <button onclick="resetContent()">重置</button>
            </div>
            <div class="content" id="streaming-content"></div>
        </div>
    </div>

    <script>
        // 预处理函数（与应用中相同的逻辑）
        function preprocessMarkdown(text) {
            const codeBlockRegex = /```(?:markdown|table|)\s*\n([\s\S]*?)\n```/g
            
            return text.replace(codeBlockRegex, (match, content) => {
                const trimmedContent = content.trim()
                
                // 检查是否确实是表格格式
                const lines = trimmedContent.split('\n').filter(line => line.trim())
                
                // 更严格的表格完整性检查
                const hasTableHeaders = lines.some(line => line.includes('|'))
                const hasSeparatorLine = lines.some(line => /^\s*\|?[-\s\|:]+\|?\s*$/.test(line))
                const hasTableRows = lines.filter(line => line.includes('|')).length >= 2
                
                // 检查表格是否完整（至少有表头、分隔符、一行数据）
                const isCompleteTable = hasTableHeaders && hasSeparatorLine && hasTableRows
                
                // 额外检查：确保不是流式渲染中的不完整表格
                const endsWithIncompleteRow = trimmedContent.endsWith('|') && !trimmedContent.endsWith('|\n')
                
                if (isCompleteTable && !endsWithIncompleteRow) {
                    console.log('预处理：提取被代码块包裹的完整表格')
                    return '\n\n' + trimmedContent + '\n\n'
                }
                
                // 如果不是完整表格，保持原样（避免流式渲染中的闪烁）
                return match
            })
        }

        // 渲染函数
        function renderContent(text) {
            const preprocessed = preprocessMarkdown(text)
            return marked.parse(preprocessed)
        }

        // 模拟流式内容
        const streamingSteps = [
            "当然，以下是用Markdown表格形式重新输出的步骤和建议：\n\n```markdown\n",
            "当然，以下是用Markdown表格形式重新输出的步骤和建议：\n\n```markdown\n| 步骤 |",
            "当然，以下是用Markdown表格形式重新输出的步骤和建议：\n\n```markdown\n| 步骤 | 详细操作 |\n",
            "当然，以下是用Markdown表格形式重新输出的步骤和建议：\n\n```markdown\n| 步骤 | 详细操作 |\n|------|",
            "当然，以下是用Markdown表格形式重新输出的步骤和建议：\n\n```markdown\n| 步骤 | 详细操作 |\n|------|----------|\n",
            "当然，以下是用Markdown表格形式重新输出的步骤和建议：\n\n```markdown\n| 步骤 | 详细操作 |\n|------|----------|\n| 访问官网 |",
            "当然，以下是用Markdown表格形式重新输出的步骤和建议：\n\n```markdown\n| 步骤 | 详细操作 |\n|------|----------|\n| 访问官网 | 打开江苏第二师范学院官网 |\n",
            "当然，以下是用Markdown表格形式重新输出的步骤和建议：\n\n```markdown\n| 步骤 | 详细操作 |\n|------|----------|\n| 访问官网 | 打开江苏第二师范学院官网 |\n| 查找联系方式 | 点击联系我们页面 |\n```"
        ]

        let currentStep = 0

        function startStreaming() {
            currentStep = 0
            streamNext()
        }

        function streamNext() {
            if (currentStep < streamingSteps.length) {
                const content = streamingSteps[currentStep]
                const rendered = renderContent(content)
                document.getElementById('streaming-content').innerHTML = rendered
                
                console.log(`步骤 ${currentStep + 1}:`, content.substring(content.length - 50))
                
                currentStep++
                setTimeout(streamNext, 1000) // 每秒更新一次
            }
        }

        function resetContent() {
            document.getElementById('streaming-content').innerHTML = ''
            currentStep = 0
        }
    </script>
</body>
</html>
