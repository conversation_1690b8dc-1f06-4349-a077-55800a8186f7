"""
后台任务处理服务
用于处理耗时的文档处理任务，避免HTTP请求超时
"""
import asyncio
import json
from typing import Dict, Any
from sqlmodel import Session, select
from app.core.database import get_session
from app.models.knowledge_base import Document
from app.services.document_service import get_document_service
from app.services.vector_service import get_vector_service
from app.services.cache_service import get_cache_service
import logging

logger = logging.getLogger(__name__)

class BackgroundTaskManager:
    """后台任务管理器"""

    def __init__(self):
        self.running_tasks: Dict[int, asyncio.Task] = {}
        self._initialized = False
    
    async def process_document_async(self, document_id: int) -> None:
        """异步处理文档"""
        logger.info(f"开始异步处理文档 {document_id}")
        
        # 获取服务实例
        document_service = get_document_service()
        vector_service = get_vector_service()
        cache_service = get_cache_service()
        
        # 获取数据库会话
        session_gen = get_session()
        session = next(session_gen)
        
        try:
            # 获取文档信息
            statement = select(Document).where(Document.id == document_id)
            document = session.exec(statement).first()
            
            if not document:
                logger.error(f"文档 {document_id} 不存在")
                return
            
            # 更新状态为处理中
            document.status = "processing"
            document.error_message = None
            session.add(document)
            session.commit()
            
            logger.info(f"开始处理文档: {document.filename}")
            
            # 检查文件大小，如果是大文件则分批处理
            file_size_mb = document.file_size / (1024 * 1024) if document.file_size else 0
            
            if file_size_mb > 10:  # 大于10MB的文件
                logger.info(f"检测到大文件 ({file_size_mb:.1f}MB)，启用分批处理模式")
                chunks = await self._process_large_document(document, document_service)
            else:
                # 小文件直接处理
                chunks = await document_service.process_document(
                    document.storage_path,
                    document.filename
                )
            
            logger.info(f"文档处理完成，生成 {len(chunks)} 个分块")
            
            # 缓存分块数据
            await cache_service.cache_document_chunks(document_id, chunks)
            
            # 添加到向量索引
            logger.info(f"开始向量化处理")
            success = await vector_service.add_document_chunks(
                kb_id=document.kb_id,
                document_id=document_id,
                chunks=chunks
            )
            
            if success:
                document.status = "completed"
                document.error_message = None
                logger.info(f"文档 {document_id} 处理成功")
            else:
                document.status = "failed"
                document.error_message = "向量化处理失败"
                logger.error(f"文档 {document_id} 向量化失败")
            
        except Exception as e:
            logger.error(f"文档 {document_id} 处理失败: {e}")
            document.status = "failed"
            document.error_message = str(e)
        
        finally:
            # 更新数据库状态
            session.add(document)
            session.commit()
            session.close()
            
            # 从运行任务列表中移除
            if document_id in self.running_tasks:
                del self.running_tasks[document_id]
    
    async def _process_large_document(self, document: Document, document_service) -> list:
        """分批处理大文档"""
        logger.info(f"开始分批处理大文档: {document.filename}")
        
        try:
            # 使用分批处理模式
            chunks = await document_service.process_document_in_batches(
                document.storage_path,
                document.filename,
                batch_size=50  # 每批处理50页
            )
            return chunks
        except Exception as e:
            logger.error(f"分批处理失败，回退到普通处理: {e}")
            # 如果分批处理失败，回退到普通处理
            return await document_service.process_document(
                document.storage_path,
                document.filename
            )
    
    def start_document_processing(self, document_id: int) -> None:
        """启动文档处理任务"""
        if document_id in self.running_tasks:
            logger.warning(f"文档 {document_id} 已在处理中")
            return

        try:
            # 创建异步任务
            task = asyncio.create_task(self.process_document_async(document_id))
            self.running_tasks[document_id] = task

            logger.info(f"已启动文档 {document_id} 的后台处理任务")
        except RuntimeError as e:
            # 如果没有运行的事件循环，直接同步处理
            logger.warning(f"无法创建异步任务，改为同步处理: {e}")
            import threading
            thread = threading.Thread(
                target=self._sync_process_document,
                args=(document_id,)
            )
            thread.daemon = True
            thread.start()

    def _sync_process_document(self, document_id: int) -> None:
        """同步处理文档（用于线程环境）"""
        import asyncio
        try:
            # 创建新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.process_document_async(document_id))
        except Exception as e:
            logger.error(f"同步处理文档 {document_id} 失败: {e}")
        finally:
            loop.close()
    
    def is_processing(self, document_id: int) -> bool:
        """检查文档是否正在处理"""
        return document_id in self.running_tasks
    
    def get_processing_status(self) -> Dict[int, str]:
        """获取所有处理中任务的状态"""
        status = {}
        for doc_id, task in self.running_tasks.items():
            if task.done():
                status[doc_id] = "completed"
            else:
                status[doc_id] = "processing"
        return status

# 全局任务管理器实例
_task_manager = None

def get_task_manager() -> BackgroundTaskManager:
    """获取任务管理器实例"""
    global _task_manager
    if _task_manager is None:
        _task_manager = BackgroundTaskManager()
    return _task_manager
