<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基于RAG技术的个性化教学智能体系统架构</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        body {
            font-family: 'Inter', 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        h1 {
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 40px;
            font-size: 28px;
            font-weight: 700;
            letter-spacing: -0.5px;
        }

        .mermaid {
            text-align: center;
            background: transparent;
            padding: 20px;
        }

        /* 现代科技风样式 */
        .mermaid rect {
            fill: url(#techGradient) !important;
            stroke: #4f46e5 !important;
            stroke-width: 2px !important;
            filter: drop-shadow(0 4px 8px rgba(79, 70, 229, 0.2)) !important;
            rx: 8px !important;
            ry: 8px !important;
        }

        .mermaid text {
            fill: #1e293b !important;
            font-size: 12px !important;
            font-family: 'Inter', 'Microsoft YaHei', Arial, sans-serif !important;
            font-weight: 500 !important;
        }

        .mermaid .edgePath path {
            stroke: #6366f1 !important;
            stroke-width: 2px !important;
            filter: drop-shadow(0 2px 4px rgba(99, 102, 241, 0.3)) !important;
        }

        .mermaid .arrowheadPath {
            fill: #6366f1 !important;
            stroke: #6366f1 !important;
            filter: drop-shadow(0 2px 4px rgba(99, 102, 241, 0.3)) !important;
        }

        /* 添加动画效果 */
        .mermaid rect {
            transition: all 0.3s ease !important;
        }

        .mermaid rect:hover {
            filter: drop-shadow(0 8px 16px rgba(79, 70, 229, 0.4)) !important;
            transform: translateY(-2px) !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>基于RAG技术的个性化教学智能体系统架构</h1>
        
        <div class="mermaid">
            flowchart TD
                %% 用户界面层 - 使用子图确保居中
                subgraph UI["🖥️ 用户界面层"]
                    A1["Vue.js + TypeScript + Element Plus UI<br/>用户交互界面 + 智能对话 + 文件上传"]
                    A2["Chat.js + TG-web SDK + 多端适配<br/>多平台支持 + 实时通信 + 响应式设计"]
                end

                %% 网关服务层
                subgraph Gateway["🚪 网关服务层"]
                    B1["Nginx反向代理<br/>负载均衡 + 静态资源"]
                    B2["CORS跨域处理<br/>请求路由 + 安全策略"]
                    B3["JWT身份认证<br/>权限验证 + 会话管理"]
                end

                %% 应用服务层
                subgraph App["⚙️ 应用服务层"]
                    C1["用户管理<br/>多角色权限 + 数据统计 + 历史记录"]
                    C2["智能对话<br/>多轮对话 + 上下文管理 + 意图识别"]
                    C3["文档处理<br/>多格式解析 + 上传管理 + 历史记录"]
                    C4["AI核心服务<br/>模型调用 + 参数优化 + 性能监控"]
                end

                %% AI核心层
                subgraph AI["🤖 AI核心层"]
                    D1["RAG检索系统<br/>FAISS检索 + 语义匹配 + 相似度计算"]
                    D2["大语言模型<br/>OpenAI GPT-4 + Claude-3 Deepseek<br/>多模型支持 + 模型切换"]
                    D3["文档解析<br/>支持多种格式 + 智能分块 + 元数据提取"]
                    D4["向量数据库<br/>Chroma + 向量存储 + 相似度搜索"]
                    D5["缓存机制<br/>Redis缓存 + 会话存储"]
                end

                %% 数据持久层
                subgraph Data["💾 数据持久层"]
                    E1["用户数据<br/>用户数据 + 对话历史 + 文档元数据<br/>关系型数据 + 事务处理"]
                    E2["向量存储<br/>FAISS向量库<br/>文档向量 + 高效检索 + 索引优化"]
                    E3["知识库<br/>企业知识库<br/>多源数据 + 结构化存储 + 版本控制"]
                    E4["主数据库<br/>MySQL/PostgreSQL + 备份策略 + 性能优化"]
                end

                %% 层级连接
                UI --> Gateway
                Gateway --> App
                App --> AI
                AI --> Data

                %% 内部连接
                B1 --> B2
                B2 --> B3
                B3 --> C1
                B3 --> C2
                B3 --> C3
                B3 --> C4
                C1 --> D5
                C2 --> D1
                C2 --> D2
                C3 --> D3
                C4 --> D1
                C4 --> D2
                D1 --> D4
                D2 --> D5
                D3 --> D4
                C1 --> E4
                C2 --> E4
                C3 --> E4
                D1 --> E2
                D3 --> E3
                D4 --> E2
                D5 --> E4
        </div>
    </div>

    <script>
        // 初始化Mermaid - 现代科技风配置
        mermaid.initialize({
            startOnLoad: true,
            theme: 'base',
            themeVariables: {
                primaryColor: '#e0e7ff',
                primaryTextColor: '#1e293b',
                primaryBorderColor: '#4f46e5',
                lineColor: '#6366f1',
                secondaryColor: '#f1f5f9',
                tertiaryColor: '#e2e8f0',
                background: '#ffffff',
                mainBkg: '#e0e7ff',
                secondBkg: '#f1f5f9',
                tertiaryBkg: '#e2e8f0',
                nodeBorder: '#4f46e5',
                clusterBkg: 'rgba(224, 231, 255, 0.3)',
                clusterBorder: '#6366f1'
            },
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            }
        });

        // 添加SVG渐变定义
        function addGradients() {
            const svg = document.querySelector('.mermaid svg');
            if (!svg) return;

            // 创建defs元素
            let defs = svg.querySelector('defs');
            if (!defs) {
                defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
                svg.insertBefore(defs, svg.firstChild);
            }

            // 添加渐变定义
            const gradients = [
                {
                    id: 'techGradient',
                    colors: [
                        { offset: '0%', color: '#e0e7ff' },
                        { offset: '100%', color: '#c7d2fe' }
                    ]
                },
                {
                    id: 'clusterGradient',
                    colors: [
                        { offset: '0%', color: 'rgba(224, 231, 255, 0.1)' },
                        { offset: '100%', color: 'rgba(199, 210, 254, 0.2)' }
                    ]
                }
            ];

            gradients.forEach(grad => {
                const gradient = document.createElementNS('http://www.w3.org/2000/svg', 'linearGradient');
                gradient.setAttribute('id', grad.id);
                gradient.setAttribute('x1', '0%');
                gradient.setAttribute('y1', '0%');
                gradient.setAttribute('x2', '100%');
                gradient.setAttribute('y2', '100%');

                grad.colors.forEach(color => {
                    const stop = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
                    stop.setAttribute('offset', color.offset);
                    stop.setAttribute('stop-color', color.color);
                    gradient.appendChild(stop);
                });

                defs.appendChild(gradient);
            });
        }

        // 应用现代科技风样式
        function applyTechStyles() {
            addGradients();

            // 节点样式
            const rects = document.querySelectorAll('.mermaid .node rect');
            rects.forEach((rect, index) => {
                rect.style.fill = 'url(#techGradient)';
                rect.style.stroke = '#4f46e5';
                rect.style.strokeWidth = '2px';
                rect.style.filter = 'drop-shadow(0 4px 8px rgba(79, 70, 229, 0.2))';
                rect.setAttribute('rx', '8');
                rect.setAttribute('ry', '8');
            });

            // 子图样式
            const clusterRects = document.querySelectorAll('.mermaid .cluster rect');
            clusterRects.forEach(rect => {
                rect.style.fill = 'url(#clusterGradient)';
                rect.style.stroke = '#6366f1';
                rect.style.strokeWidth = '2px';
                rect.style.strokeDasharray = '5,5';
                rect.style.filter = 'drop-shadow(0 2px 4px rgba(99, 102, 241, 0.1))';
                rect.setAttribute('rx', '12');
                rect.setAttribute('ry', '12');
            });

            // 文字样式
            const texts = document.querySelectorAll('.mermaid text');
            texts.forEach(text => {
                text.style.fill = '#1e293b';
                text.style.fontSize = '12px';
                text.style.fontFamily = 'Inter, Microsoft YaHei, Arial, sans-serif';
                text.style.fontWeight = '500';
            });

            // 连线样式
            const paths = document.querySelectorAll('.mermaid .edgePath path');
            paths.forEach(path => {
                path.style.stroke = '#6366f1';
                path.style.strokeWidth = '2px';
                path.style.filter = 'drop-shadow(0 2px 4px rgba(99, 102, 241, 0.3))';
            });

            // 箭头样式
            const arrows = document.querySelectorAll('.mermaid .arrowheadPath');
            arrows.forEach(arrow => {
                arrow.style.fill = '#6366f1';
                arrow.style.stroke = '#6366f1';
                arrow.style.filter = 'drop-shadow(0 2px 4px rgba(99, 102, 241, 0.3))';
            });

            console.log('应用了现代科技风样式');
        }

        // 多次尝试应用样式
        setTimeout(applyTechStyles, 500);
        setTimeout(applyTechStyles, 1000);
        setTimeout(applyTechStyles, 2000);
    </script>
</body>
</html>
