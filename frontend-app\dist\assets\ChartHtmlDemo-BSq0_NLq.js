var t,e;(()=>{function a(t,e,a,n,r,o,l){try{var d=t[o](l),i=d.value}catch(t){return void a(t)}d.done?e(i):Promise.resolve(i).then(n,r)}function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(t)}function r(t){var e=function(t){if("object"!=n(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var a=e.call(t,"string");if("object"!=n(a))return a;throw TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==n(e)?e:e+""}function o(t,e,a){return(e=r(e))in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function l(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,n)}return a}t=function(t){return function(){var e=this,n=arguments;return new Promise(function(r,o){var l=t.apply(e,n);function d(t){a(l,r,o,d,i,"next",t)}function i(t){a(l,r,o,d,i,"throw",t)}d(void 0)})}},e=function(t){for(var e=1;e<arguments.length;e++){var a=null==arguments[e]?{}:arguments[e];e%2?l(Object(a),!0).forEach(function(e){o(t,e,a[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):l(Object(a)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))})}return t}})();import{N as a}from"./elementPlus-Di4PDIm8.js";import{U as n,ce as r,dB as o,dL as l,dN as d,dU as i,d_ as s,dd as c,df as p,dg as u,di as b,dk as h,dl as m,ds as v,dy as f,dz as g,ed as y,n as x}from"./vendor-BJ-uKP15.js";import{b as C}from"./_plugin-vue_export-helper-CjD0mXop.js";import{b as w}from"./HtmlPreview-ovS6yXAM.js";const D={class:"chart-container"},k={key:0,class:"chart-wrapper"},j={key:0,class:"chart-title"},O={key:1,class:"error-message"};var P=C(m({__name:"ChartRenderer",props:{chartData:{}},setup(b){x.register(...n);const m=b,C=i();let w=null;const P=["#FF6384","#36A2EB","#FFCE56","#4BC0C0","#9966FF","#FF9F40","#FF6384","#C9CBCF","#4BC0C0","#FF6384"],F=()=>{if(!C.value||!m.chartData)return;w&&w.destroy();const t=C.value.getContext("2d");if(!t)return;const a=m.chartData.data.datasets.map((t,a)=>e(e({},t),{},{backgroundColor:t.backgroundColor||("pie"===m.chartData.type||"doughnut"===m.chartData.type?P.slice(0,m.chartData.data.labels.length):P[a%P.length]),borderColor:t.borderColor||("line"===m.chartData.type?P[a%P.length]:"rgba(255, 255, 255, 0.8)"),borderWidth:t.borderWidth||("pie"===m.chartData.type||"doughnut"===m.chartData.type?2:1)})),n={responsive:!0,maintainAspectRatio:!1,layout:{padding:{top:20,bottom:40,left:20,right:20}},plugins:{legend:{position:"top",labels:{padding:20,usePointStyle:!0,font:{size:12}}},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"white",bodyColor:"white",borderColor:"rgba(255, 255, 255, 0.2)",borderWidth:1,cornerRadius:8,displayColors:!0}},scales:"pie"===m.chartData.type||"doughnut"===m.chartData.type?void 0:{y:{beginAtZero:!0,grid:{color:"rgba(0, 0, 0, 0.1)"},ticks:{font:{size:11}}},x:{grid:{color:"rgba(0, 0, 0, 0.1)"},ticks:{font:{size:11}}}}},r=e(e({},n),m.chartData.options);if("pie"!==m.chartData.type&&"doughnut"!==m.chartData.type){var o,l,d,i,s;const t=null===(o=m.chartData.options)||void 0===o?void 0:o.scales,a=null==t||null===(l=t.x)||void 0===l?void 0:l.title,c=null==t||null===(d=t.y)||void 0===d?void 0:d.title;r.scales={x:e(e({},null===(i=n.scales)||void 0===i?void 0:i.x),{},{title:a?{display:!0,text:a.text,font:{size:14,weight:"bold"},color:"#333"}:void 0}),y:e(e({},null===(s=n.scales)||void 0===s?void 0:s.y),{},{title:c?{display:!0,text:c.text,font:{size:14,weight:"bold"},color:"#333"}:void 0})}}w=new x(t,{type:m.chartData.type,data:{labels:m.chartData.data.labels,datasets:a},options:r})};return f(t(function*(){yield v(),F()})),g(()=>{w&&w.destroy()}),l(()=>m.chartData,t(function*(){yield v(),F()}),{deep:!0}),(t,e)=>{const n=a;return o(),u("div",D,[t.chartData?(o(),u("div",k,[t.chartData.title?(o(),u("h3",j,y(t.chartData.title),1)):p("",!0),c("canvas",{ref_key:"chartCanvas",ref:C,class:"chart-canvas"},null,512)])):(o(),u("div",O,[h(n,null,{default:d(()=>[h(s(r))]),_:1}),e[0]||(e[0]=c("span",null,"图表数据格式错误",-1))]))])}}}),[["__scopeId","data-v-cb7f6168"]]);const F={class:"demo-container"},_={class:"demo-sections"},S={class:"demo-section"},z={class:"demo-grid"},E={class:"demo-item"},A={class:"demo-item"},B={class:"demo-item"},H={class:"demo-section"},L={class:"demo-grid"},T={class:"demo-item"},M={class:"demo-item"},R={class:"demo-item"};var W=C(m({__name:"ChartHtmlDemo",setup(t){const e=i({type:"bar",title:"月度销售数据",data:{labels:["1月","2月","3月","4月","5月","6月"],datasets:[{label:"销售额",data:[120,190,300,500,200,300],backgroundColor:"#3b82f6"}]}}),a=i({type:"line",title:"用户增长趋势",data:{labels:["周一","周二","周三","周四","周五","周六","周日"],datasets:[{label:"新用户",data:[65,59,80,81,56,55,40],borderColor:"#10b981",backgroundColor:"rgba(16, 185, 129, 0.1)"}]}}),n=i({type:"pie",title:"市场份额分布",data:{labels:["产品A","产品B","产品C","产品D"],datasets:[{data:[300,50,100,80],backgroundColor:["#ff6384","#36a2eb","#ffce56","#4bc0c0"]}]}}),r=i('\n<div class="card">\n  <div class="card-header">\n    <h3>产品介绍</h3>\n  </div>\n  <div class="card-body">\n    <p>这是一个优秀的产品，具有以下特点：</p>\n    <ul>\n      <li>高性能</li>\n      <li>易使用</li>\n      <li>可扩展</li>\n    </ul>\n  </div>\n</div>\n<style>\n.card {\n  border: 1px solid #e5e7eb;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n.card-header {\n  background: #f9fafb;\n  padding: 16px;\n  border-bottom: 1px solid #e5e7eb;\n}\n.card-header h3 {\n  margin: 0;\n  color: #374151;\n}\n.card-body {\n  padding: 16px;\n}\n</style>\n'),l=i('\n<table class="data-table">\n  <thead>\n    <tr>\n      <th>姓名</th>\n      <th>职位</th>\n      <th>部门</th>\n      <th>薪资</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <td>张三</td>\n      <td>前端工程师</td>\n      <td>技术部</td>\n      <td>¥15,000</td>\n    </tr>\n    <tr>\n      <td>李四</td>\n      <td>后端工程师</td>\n      <td>技术部</td>\n      <td>¥18,000</td>\n    </tr>\n    <tr>\n      <td>王五</td>\n      <td>产品经理</td>\n      <td>产品部</td>\n      <td>¥20,000</td>\n    </tr>\n  </tbody>\n</table>\n<style>\n.data-table {\n  width: 100%;\n  border-collapse: collapse;\n  margin: 16px 0;\n}\n.data-table th,\n.data-table td {\n  padding: 12px;\n  text-align: left;\n  border-bottom: 1px solid #e5e7eb;\n}\n.data-table th {\n  background: #f9fafb;\n  font-weight: 600;\n  color: #374151;\n}\n.data-table tr:hover {\n  background: #f9fafb;\n}\n</style>\n'),d=i('\n<form class="demo-form">\n  <div class="form-group">\n    <label for="name">姓名</label>\n    <input type="text" id="name" placeholder="请输入姓名">\n  </div>\n  <div class="form-group">\n    <label for="email">邮箱</label>\n    <input type="email" id="email" placeholder="请输入邮箱">\n  </div>\n  <div class="form-group">\n    <label for="message">留言</label>\n    <textarea id="message" rows="3" placeholder="请输入留言"></textarea>\n  </div>\n  <button type="submit" class="submit-btn">提交</button>\n</form>\n<style>\n.demo-form {\n  max-width: 400px;\n  margin: 0 auto;\n  padding: 20px;\n}\n.form-group {\n  margin-bottom: 16px;\n}\n.form-group label {\n  display: block;\n  margin-bottom: 4px;\n  font-weight: 500;\n  color: #374151;\n}\n.form-group input,\n.form-group textarea {\n  width: 100%;\n  padding: 8px 12px;\n  border: 1px solid #d1d5db;\n  border-radius: 4px;\n  font-size: 14px;\n}\n.form-group input:focus,\n.form-group textarea:focus {\n  outline: none;\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n.submit-btn {\n  background: #3b82f6;\n  color: white;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 14px;\n  font-weight: 500;\n}\n.submit-btn:hover {\n  background: #2563eb;\n}\n</style>\n');return(t,i)=>(o(),u("div",F,[i[9]||(i[9]=c("div",{class:"header"},[c("h1",{class:"title"},"图表与HTML预览演示"),c("p",{class:"subtitle"},"展示ECharts图表和HTML内容在聊天中的渲染效果")],-1)),c("div",_,[c("section",S,[i[3]||(i[3]=c("h2",{class:"section-title"},"📊 ECharts图表演示",-1)),c("div",z,[c("div",E,[i[0]||(i[0]=c("h3",null,"柱状图",-1)),h(P,{"chart-data":e.value},null,8,["chart-data"])]),c("div",A,[i[1]||(i[1]=c("h3",null,"折线图",-1)),h(P,{"chart-data":a.value},null,8,["chart-data"])]),c("div",B,[i[2]||(i[2]=c("h3",null,"饼图",-1)),h(P,{"chart-data":n.value},null,8,["chart-data"])])])]),c("section",H,[i[7]||(i[7]=c("h2",{class:"section-title"},"🌐 HTML预览演示",-1)),c("div",L,[c("div",T,[i[4]||(i[4]=c("h3",null,"简单卡片",-1)),h(w,{"html-content":r.value,height:"200px"},null,8,["html-content"])]),c("div",M,[i[5]||(i[5]=c("h3",null,"表格展示",-1)),h(w,{"html-content":l.value,height:"250px"},null,8,["html-content"])]),c("div",R,[i[6]||(i[6]=c("h3",null,"表单示例",-1)),h(w,{"html-content":d.value,height:"300px"},null,8,["html-content"])])])]),i[8]||(i[8]=b('<section class="demo-section" data-v-60019834><h2 class="section-title" data-v-60019834>💻 代码示例</h2><div class="code-examples" data-v-60019834><div class="code-example" data-v-60019834><h3 data-v-60019834>ECharts配置示例</h3><pre data-v-60019834><code data-v-60019834>```javascript\noption = {\n  title: { text: &#39;销售数据&#39; },\n  xAxis: { data: [&#39;1月&#39;, &#39;2月&#39;, &#39;3月&#39;, &#39;4月&#39;] },\n  yAxis: {},\n  series: [{\n    name: &#39;销售额&#39;,\n    type: &#39;bar&#39;,\n    data: [120, 200, 150, 80]\n  }]\n}\n```</code></pre></div><div class="code-example" data-v-60019834><h3 data-v-60019834>HTML片段示例</h3><pre data-v-60019834><code data-v-60019834>```html\n&lt;div class=&quot;alert&quot;&gt;\n  &lt;h4&gt;提示&lt;/h4&gt;\n  &lt;p&gt;这是一个重要提示信息&lt;/p&gt;\n&lt;/div&gt;\n&lt;style&gt;\n.alert {\n  background: #f0f9ff;\n  border: 1px solid #0ea5e9;\n  border-radius: 6px;\n  padding: 16px;\n}\n&lt;/style&gt;\n```</code></pre></div></div></section>',1))])]))}}),[["__scopeId","data-v-60019834"]]);export{W as default};