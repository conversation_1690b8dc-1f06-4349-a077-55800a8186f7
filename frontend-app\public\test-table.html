<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格渲染测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .markdown-content table {
            width: 100%;
            border-collapse: collapse;
            margin: 16px 0;
            border: 1px solid #d1d5db;
            background: white;
        }
        .markdown-content table th {
            background: #f9fafb;
            color: #374151;
            padding: 12px 16px;
            text-align: left;
            font-weight: 600;
            font-size: 14px;
            border: 1px solid #d1d5db;
            border-bottom: 2px solid #9ca3af;
        }
        .markdown-content table td {
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            vertical-align: top;
            font-size: 14px;
            line-height: 1.5;
        }
        .markdown-content table tr:nth-child(even) {
            background-color: #f9fafb;
        }
    </style>
</head>
<body>
    <h1>Markdown表格渲染测试</h1>
    
    <div class="test-section">
        <h2>原始Markdown文本</h2>
        <pre id="markdown-input">| 列名1 | 列名2 | 列名3 |
|-------|-------|-------|
| 数据1 | 数据2 | 数据3 |
| 数据4 | 数据5 | 数据6 |</pre>
    </div>
    
    <div class="test-section">
        <h2>渲染结果</h2>
        <div id="rendered-output" class="markdown-content"></div>
    </div>
    
    <div class="test-section">
        <h2>HTML源码</h2>
        <pre id="html-source"></pre>
    </div>

    <script type="module">
        import { marked } from 'https://cdn.jsdelivr.net/npm/marked@9.1.6/+esm';
        
        // 配置marked
        marked.setOptions({
            breaks: true,
            gfm: true
        });
        
        const markdownText = `| 列名1 | 列名2 | 列名3 |
|-------|-------|-------|
| 数据1 | 数据2 | 数据3 |
| 数据4 | 数据5 | 数据6 |`;
        
        // 渲染
        const html = marked.parse(markdownText);
        
        // 显示结果
        document.getElementById('rendered-output').innerHTML = html;
        document.getElementById('html-source').textContent = html;
        
        console.log('Markdown输入:', markdownText);
        console.log('HTML输出:', html);
    </script>
</body>
</html>
