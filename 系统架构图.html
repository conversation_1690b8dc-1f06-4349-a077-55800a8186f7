<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统架构图 - 用户端和管理员端功能模块关系</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: #2563eb;
            color: white;
            text-align: center;
            padding: 20px;
        }
        
        .header h1 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .header p {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .architecture-content {
            padding: 30px;
        }
        
        .layer {
            margin-bottom: 30px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            overflow: hidden;
        }
        
        .layer-header {
            background: #f8fafc;
            padding: 15px 20px;
            border-bottom: 2px solid #e5e7eb;
            font-weight: 600;
            font-size: 16px;
            color: #374151;
            display: flex;
            align-items: center;
        }
        
        .layer-icon {
            margin-right: 10px;
            font-size: 20px;
        }
        
        .layer-content {
            padding: 20px;
            display: grid;
            gap: 15px;
        }
        
        .user-layer .layer-content {
            grid-template-columns: 1fr 1fr;
        }
        
        .service-layer .layer-content {
            grid-template-columns: repeat(4, 1fr);
        }
        
        .ai-layer .layer-content {
            grid-template-columns: repeat(3, 1fr);
        }
        
        .storage-layer .layer-content {
            grid-template-columns: repeat(4, 1fr);
        }
        
        .module {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .module:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border-color: #2563eb;
        }
        
        .module-title {
            font-weight: 600;
            font-size: 14px;
            color: #1f2937;
            margin-bottom: 8px;
        }
        
        .module-desc {
            font-size: 12px;
            color: #6b7280;
            line-height: 1.4;
        }
        
        .user-module {
            border-color: #10b981;
            background: #f0fdf4;
        }
        
        .user-module .module-title {
            color: #059669;
        }
        
        .admin-module {
            border-color: #f59e0b;
            background: #fffbeb;
        }
        
        .admin-module .module-title {
            color: #d97706;
        }
        
        .service-module {
            border-color: #3b82f6;
            background: #eff6ff;
        }
        
        .service-module .module-title {
            color: #2563eb;
        }
        
        .ai-module {
            border-color: #8b5cf6;
            background: #f5f3ff;
        }
        
        .ai-module .module-title {
            color: #7c3aed;
        }
        
        .storage-module {
            border-color: #ef4444;
            background: #fef2f2;
        }
        
        .storage-module .module-title {
            color: #dc2626;
        }
        
        .data-flow {
            margin-top: 30px;
            padding: 20px;
            background: #f8fafc;
            border-radius: 12px;
            border: 2px solid #e5e7eb;
        }
        
        .flow-title {
            font-size: 18px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .flow-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .flow-step {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 2px solid #e5e7eb;
            text-align: center;
            position: relative;
        }
        
        .step-number {
            background: #2563eb;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            margin: 0 auto 10px;
        }
        
        .step-text {
            font-size: 13px;
            color: #374151;
            line-height: 1.4;
        }
        
        @media (max-width: 768px) {
            .user-layer .layer-content,
            .service-layer .layer-content,
            .ai-layer .layer-content,
            .storage-layer .layer-content {
                grid-template-columns: 1fr;
            }
            
            .flow-steps {
                grid-template-columns: 1fr;
            }
            
            .architecture-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏗️ 系统架构图</h1>
            <p>用户端和管理员端功能模块关系图</p>
        </div>
        
        <div class="architecture-content">
            <!-- 用户界面层 -->
            <div class="layer user-layer">
                <div class="layer-header">
                    <span class="layer-icon">🖥️</span>
                    用户界面层 (User Interface Layer)
                </div>
                <div class="layer-content">
                    <div class="module user-module">
                        <div class="module-title">用户端</div>
                        <div class="module-desc">
                            Vue.js 3 + TypeScript + Element Plus UI<br>
                            • 智能问答对话<br>
                            • 多格式内容展示<br>
                            &nbsp;&nbsp;(Markdown/图表/代码)<br>
                            • 可调记忆长度<br>
                            • 专注模式切换<br>
                            • 多模型智能切换
                        </div>
                    </div>
                    <div class="module admin-module">
                        <div class="module-title">管理员端</div>
                        <div class="module-desc">
                            管理后台 + 数据分析<br>
                            • 知识库批量上传/自动向量化/版本控制<br>
                            • 多级权限管理(RBAC)<br>
                            • 模型参数调优/检索阈值调整<br>
                            • 用户行为分析/知识库覆盖度分析<br>
                            • API密钥安全管理
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 应用服务层 -->
            <div class="layer service-layer">
                <div class="layer-header">
                    <span class="layer-icon">⚙️</span>
                    应用服务层 (Application Service Layer)
                </div>
                <div class="layer-content">
                    <div class="module service-module">
                        <div class="module-title">用户认证服务</div>
                        <div class="module-desc">
                            FastAPI + SQLModel<br>
                            JWT身份认证<br>
                            权限控制管理<br>
                            会话状态维护
                        </div>
                    </div>
                    <div class="module service-module">
                        <div class="module-title">知识库管理</div>
                        <div class="module-desc">
                            文档上传解析<br>
                            批量处理引擎<br>
                            版本控制系统<br>
                            元数据管理
                        </div>
                    </div>
                    <div class="module service-module">
                        <div class="module-title">对话管理</div>
                        <div class="module-desc">
                            多轮会话维护<br>
                            上下文管理<br>
                            历史记录存储<br>
                            流式响应处理
                        </div>
                    </div>
                    <div class="module service-module">
                        <div class="module-title">AI模型管理</div>
                        <div class="module-desc">
                            多模型统一接入<br>
                            参数配置优化<br>
                            性能监控统计<br>
                            智能路由调度
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- AI算法层 -->
            <div class="layer ai-layer">
                <div class="layer-header">
                    <span class="layer-icon">🤖</span>
                    AI算法层 (AI Algorithm Layer)
                </div>
                <div class="layer-content">
                    <div class="module ai-module">
                        <div class="module-title">RAG检索引擎</div>
                        <div class="module-desc">
                            FAISS向量检索<br>
                            SentenceBERT编码<br>
                            余弦相似度计算<br>
                            0.1-1.0精确控制
                        </div>
                    </div>
                    <div class="module ai-module">
                        <div class="module-title">多模型融合</div>
                        <div class="module-desc">
                            GPT-4 / Claude-3<br>
                            通义千问 / DeepSeek<br>
                            统一API接入层<br>
                            智能模型切换
                        </div>
                    </div>
                    <div class="module ai-module">
                        <div class="module-title">文档处理引擎</div>
                        <div class="module-desc">
                            多格式解析支持<br>
                            智能语义分块<br>
                            向量化处理<br>
                            元数据提取
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 数据存储层 -->
            <div class="layer storage-layer">
                <div class="layer-header">
                    <span class="layer-icon">💾</span>
                    数据存储层 (Data Storage Layer)
                </div>
                <div class="layer-content">
                    <div class="module storage-module">
                        <div class="module-title">PostgreSQL</div>
                        <div class="module-desc">
                            关系数据存储<br>
                            用户信息管理<br>
                            系统配置数据<br>
                            索引优化
                        </div>
                    </div>
                    <div class="module storage-module">
                        <div class="module-title">FAISS向量库</div>
                        <div class="module-desc">
                            768维语义向量<br>
                            高速相似度搜索<br>
                            分布式存储<br>
                            实时索引更新
                        </div>
                    </div>
                    <div class="module storage-module">
                        <div class="module-title">Redis缓存</div>
                        <div class="module-desc">
                            会话状态缓存<br>
                            检索结果缓存<br>
                            用户权限缓存<br>
                            性能优化
                        </div>
                    </div>
                    <div class="module storage-module">
                        <div class="module-title">MinIO对象存储</div>
                        <div class="module-desc">
                            文档文件存储<br>
                            媒体资源管理<br>
                            备份恢复<br>
                            分布式存储
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 数据流程图 -->
            <div class="data-flow">
                <div class="flow-title">🔄 核心数据流程</div>
                <div class="flow-steps">
                    <div class="flow-step">
                        <div class="step-number">1</div>
                        <div class="step-text">用户上传文档到知识库管理系统</div>
                    </div>
                    <div class="flow-step">
                        <div class="step-number">2</div>
                        <div class="step-text">文档处理引擎解析并进行语义分块</div>
                    </div>
                    <div class="flow-step">
                        <div class="step-number">3</div>
                        <div class="step-text">SentenceBERT生成768维向量存储</div>
                    </div>
                    <div class="flow-step">
                        <div class="step-number">4</div>
                        <div class="step-text">用户提问触发RAG检索引擎</div>
                    </div>
                    <div class="flow-step">
                        <div class="step-number">5</div>
                        <div class="step-text">FAISS检索相关知识片段</div>
                    </div>
                    <div class="flow-step">
                        <div class="step-number">6</div>
                        <div class="step-text">AI模型生成基于知识的回答</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
