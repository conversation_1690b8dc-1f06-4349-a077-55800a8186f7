function e(){Object.keys(localStorage).forEach(e=>{e.startsWith("system-")||e.startsWith("browser-")||localStorage.removeItem(e)}),localStorage.setItem("auth_cleared","true"),localStorage.setItem("last_clear_time",Date.now().toString());Object.keys(sessionStorage).forEach(e=>{sessionStorage.removeItem(e)});document.cookie.split(";").forEach(function(e){const t=e.replace(/^ +/,"").split("=")[0];t&&(document.cookie=`${t}=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;`,document.cookie=`${t}=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;domain=${window.location.hostname};`,document.cookie=`${t}=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;domain=.${window.location.hostname};`)}),a(),i()}function t(){e(),o(),window.location.replace("/login")}function o(){try{"/login"!==window.location.pathname&&window.history.replaceState(null,"","/login");const e=window.history.length;for(let t=0;t<e-1;t++)window.history.back()}catch(e){}}function n(){window.addEventListener("popstate",o=>{const n=window.location.pathname,a=localStorage.getItem("token");if((n.startsWith("/admin")||n.startsWith("/user"))&&!a)return o.preventDefault(),void t();"/login"!==n&&"/"!==n||e()})}function a(){try{"indexedDB"in window&&["pinia","vuex","auth","user-data","app-data"].forEach(e=>{const t=indexedDB.deleteDatabase(e);t.onsuccess=()=>{},t.onerror=()=>{}})}catch(e){}}function i(){try{"serviceWorker"in navigator&&"caches"in window&&caches.keys().then(e=>{e.forEach(e=>{caches.delete(e).then(()=>{})})}),"performance"in window&&"navigation"in performance&&sessionStorage.setItem("force-reload","true")}catch(e){}}export{e as clearAllAuthData,i as clearBrowserCache,o as clearBrowserHistory,a as clearIndexedDB,t as forceRedirectToLogin,n as setupBrowserBackHandler};