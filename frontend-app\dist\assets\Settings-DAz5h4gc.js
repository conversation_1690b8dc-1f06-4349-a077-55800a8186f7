var e;(()=>{function a(e,a,l,t,s,d,i){try{var o=e[d](i),u=o.value}catch(e){return void l(e)}o.done?a(u):Promise.resolve(u).then(t,s)}e=function(e){return function(){var l=this,t=arguments;return new Promise(function(s,d){var i=e.apply(l,t);function o(e){a(i,s,d,o,u,"next",e)}function u(e){a(i,s,d,o,u,"throw",e)}o(void 0)})}}})();import{E as a,L as l,N as t,b as s,c as d,h as i,l as o,m as u,n,o as r,p as c,q as m,r as _,s as p,u as v,y}from"./elementPlus-Di4PDIm8.js";import{bC as f,bD as g,bH as x,bU as k,bY as h,b_ as b,bn as V,by as w,c6 as U,d8 as I,dB as C,dD as A,dH as P,dN as S,dU as T,d_ as z,dc as B,dd as M,de as $,df as R,dg as j,dj as D,dk as F,dl as Q,dy as q,ea as G,ed as K}from"./vendor-BJ-uKP15.js";import{b as L}from"./api-D-gMiCJf.js";import{b as N}from"./admin-BSai4urc.js";const O={class:"space-y-6"},H={class:"card-tech p-6"},Z={class:"flex items-center justify-between mb-6"},E={class:"space-y-4"},Y={class:"flex items-start justify-between"},J={class:"flex items-start space-x-4"},W={class:"flex-1"},X={class:"flex items-center space-x-3 mb-2"},ee={class:"text-lg font-medium text-gray-900 dark:text-gray-100"},ae={class:"space-y-2"},le={class:"text-sm text-gray-600 dark:text-gray-400"},te={class:"text-xs text-gray-500"},se={class:"flex items-center space-x-2"},de={class:"card-tech p-6"},ie={class:"flex items-center justify-between mb-6"},oe={class:"flex items-center space-x-3"},ue={class:"space-y-4"},ne={class:"flex items-start justify-between"},re={class:"flex items-start space-x-4"},ce={class:"flex-1"},me={class:"flex items-center space-x-3 mb-2"},_e={class:"text-lg font-medium text-gray-900 dark:text-gray-100"},pe={class:"space-y-3"},ve={class:"text-sm text-gray-600 dark:text-gray-400"},ye={key:0},fe={key:1},ge={class:"flex items-center space-x-3"},xe={class:"flex items-center justify-between"},ke={class:"flex items-center space-x-2"},he={class:"card-tech p-6"},be={class:"card-tech p-6"},Ve={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},we={class:"space-y-4"},Ue={class:"space-y-3"},Ie={class:"space-y-4"},Ce={class:"space-y-3"},Ae={class:"dialog-footer"},Pe={class:"dialog-footer"},Se={class:"space-y-3"},Te={class:"font-medium text-gray-900 dark:text-gray-100"},ze={class:"text-sm text-gray-500"},Be={class:"flex items-center space-x-2"};var Me=Q({__name:"Settings",setup(Q){const Me=T(!1),$e=T(!1),Re=T(!1),je=T(!1),De=T(!1),Fe=T(!1),Qe=T(!1),qe=T(!1),Ge=T(!1),Ke=T(),Le=T(),Ne=T([]),Oe=T([]),He=T(null),Ze=T(null),Ee=T({defaultModel:1,maxFileSize:50,defaultStorageQuota:10,allowUserRegistration:!0,documentProcessTimeout:300,chatRetentionDays:30}),Ye=T({name:"",display_name:"",base_url:"",description:"",is_active:!0}),Je={name:[{required:!0,message:"请输入供应商标识",trigger:"blur"}],display_name:[{required:!0,message:"请输入显示名称",trigger:"blur"}]},We=T({provider_id:0,model_name:"",display_name:"",system_api_key:"",is_active:!0,allow_system_key_use:!0,max_tokens:4e3,supports_streaming:!0,cost_per_1k_tokens:void 0}),Xe={provider_id:[{required:!0,message:"请选择供应商",trigger:"change"}],model_name:[{required:!0,message:"请输入模型名称",trigger:"blur"}],display_name:[{required:!0,message:"请输入显示名称",trigger:"blur"}]},ea=T([{id:1,filename:"system-backup-2024-01-20.zip",size:131072e3,createdAt:"2024-01-20T10:30:00Z"},{id:2,filename:"system-backup-2024-01-19.zip",size:125829120,createdAt:"2024-01-19T10:30:00Z"}]),aa=B(()=>Oe.value.filter(e=>e.is_active)),la=B(()=>He.value?Oe.value.filter(e=>e.provider_id===He.value):Oe.value),ta=e=>{if(0===e)return"0 B";const a=Math.floor(Math.log(e)/Math.log(1024));return Math.round(e/Math.pow(1024,a)*100)/100+" "+["B","KB","MB","GB","TB"][a]},sa=e=>{switch(null==e?void 0:e.toLowerCase()){case"openai":return"bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400";case"anthropic":return"bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400";case"google":return"bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-400";case"siliconflow":case"硅基流动":return"bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400";case"deepseek":return"bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-400";default:return"bg-gray-100 dark:bg-gray-900 text-gray-600 dark:text-gray-400"}},da=(ia=e(function*(){try{Ne.value=yield N.getAIProviders()}catch(e){d.error("加载供应商列表失败")}}),function(){return ia.apply(this,arguments)});var ia;const oa=(ua=e(function*(){try{Oe.value=yield N.getAIModels()}catch(e){d.error("加载模型列表失败")}}),function(){return ua.apply(this,arguments)});var ua;const na=(ra=e(function*(){try{const e=(yield N.getSettings()).reduce((e,a)=>(e[a.key]=a.value,e),{});Ee.value={defaultModel:parseInt(e.default_model_id)||1,maxFileSize:parseInt(e.max_file_size_mb)||50,defaultStorageQuota:parseInt(e.default_storage_quota_gb)||10,allowUserRegistration:"true"===e.allow_user_registration,documentProcessTimeout:void 0!==e.document_process_timeout?parseInt(e.document_process_timeout):300,chatRetentionDays:void 0!==e.chat_retention_days?parseInt(e.chat_retention_days):30}}catch(e){d.error("加载系统配置失败")}}),function(){return ra.apply(this,arguments)});var ra;const ca=(ma=e(function*(e){try{yield N.updateAIProvider(e.id,{is_active:e.is_active}),d.success(`${e.display_name} ${e.is_active?"已启用":"已禁用"}`)}catch(a){d.error("更新失败"),e.is_active=!e.is_active}}),function(e){return ma.apply(this,arguments)});var ma;const _a=(pa=e(function*(e,a){switch(e){case"edit":va(a);break;case"models":He.value=a.id;break;case"delete":yield ya(a)}}),function(e,a){return pa.apply(this,arguments)});var pa;const va=e=>{Ye.value={name:e.name,display_name:e.display_name,base_url:e.base_url||"",description:e.description||"",is_active:e.is_active},Ze.value=e.id,Qe.value=!0},ya=(fa=e(function*(e){try{yield s.confirm(`确定要删除供应商"${e.display_name}"吗？`,"确认删除",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}),yield N.deleteAIProvider(e.id),yield da(),yield oa(),d.success("供应商删除成功")}catch(l){var a;"cancel"!==(null==l?void 0:l.message)&&d.error((null===(a=l.response)||void 0===a||null===(a=a.data)||void 0===a?void 0:a.detail)||"删除失败")}}),function(e){return fa.apply(this,arguments)});var fa;const ga=(xa=e(function*(){if(Ke.value)try{yield Ke.value.validate(),$e.value=!0,Ze.value?(yield N.updateAIProvider(Ze.value,Ye.value),d.success("供应商更新成功")):(yield N.createAIProvider(Ye.value),d.success("供应商添加成功")),yield da(),Qe.value=!1,ka()}catch(a){var e;d.error((null===(e=a.response)||void 0===e||null===(e=e.data)||void 0===e?void 0:e.detail)||"操作失败")}finally{$e.value=!1}}),function(){return xa.apply(this,arguments)});var xa;const ka=()=>{Ye.value={name:"",display_name:"",base_url:"",description:"",is_active:!0},Ze.value=null,Ke.value&&Ke.value.resetFields()},ha=(ba=e(function*(e){if(e.system_api_key){e.testing=!0;try{const a=(yield L.post("/ai/test-connection",{provider_id:e.provider_id,model_name:e.model_name,api_key:e.system_api_key})).data;a.success?d.success(`${e.display_name} 连接测试成功`):d.error(`${e.display_name} 连接测试失败: ${a.message}`)}catch(a){d.error(`${e.display_name} 连接测试失败`)}finally{e.testing=!1}}else d.warning("请先配置API密钥")}),function(e){return ba.apply(this,arguments)});var ba;const Va=(wa=e(function*(e){try{yield L.put(`/ai/models/${e.id}`,{is_active:e.is_active}),d.success(`${e.display_name} ${e.is_active?"已启用":"已禁用"}`)}catch(a){d.error("更新失败"),e.is_active=!e.is_active}}),function(e){return wa.apply(this,arguments)});var wa;const Ua=(Ia=e(function*(e,a){switch(e){case"edit":Ca(a);break;case"delete":yield Aa(a)}}),function(e,a){return Ia.apply(this,arguments)});var Ia;const Ca=e=>{We.value={provider_id:e.provider_id,model_name:e.model_name,display_name:e.display_name,system_api_key:e.system_api_key||"",is_active:e.is_active,allow_system_key_use:e.allow_system_key_use,max_tokens:e.max_tokens||4e3,supports_streaming:e.supports_streaming,cost_per_1k_tokens:e.cost_per_1k_tokens||void 0},qe.value=!0},Aa=(Pa=e(function*(e){try{yield s.confirm(`确定要删除模型"${e.display_name}"吗？`,"确认删除",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}),yield N.deleteAIModel(e.id),yield oa(),d.success("模型删除成功")}catch(l){var a;"cancel"!==(null==l?void 0:l.message)&&d.error((null===(a=l.response)||void 0===a||null===(a=a.data)||void 0===a?void 0:a.detail)||"删除失败")}}),function(e){return Pa.apply(this,arguments)});var Pa;const Sa=(Ta=e(function*(){if(Le.value)try{yield Le.value.validate(),$e.value=!0,yield N.createAIModel(We.value),yield oa(),qe.value=!1,za(),d.success("模型添加成功")}catch(a){var e;d.error((null===(e=a.response)||void 0===e||null===(e=e.data)||void 0===e?void 0:e.detail)||"添加失败")}finally{$e.value=!1}}),function(){return Ta.apply(this,arguments)});var Ta;const za=()=>{We.value={provider_id:0,model_name:"",display_name:"",system_api_key:"",is_active:!0,allow_system_key_use:!0,max_tokens:4e3,supports_streaming:!0,cost_per_1k_tokens:void 0},Le.value&&Le.value.resetFields()},Ba=(Ma=e(function*(){try{Me.value=!0;const e=[{key:"default_model_id",value:Ee.value.defaultModel.toString(),description:"新用户注册时的默认AI模型"},{key:"max_file_size_mb",value:Ee.value.maxFileSize.toString(),description:"最大上传文件大小(MB)"},{key:"default_storage_quota_gb",value:Ee.value.defaultStorageQuota.toString(),description:"用户默认存储配额(GB)"},{key:"allow_user_registration",value:Ee.value.allowUserRegistration.toString(),description:"是否允许用户注册"},{key:"document_process_timeout",value:Ee.value.documentProcessTimeout.toString(),description:"文档处理超时时间(秒)"},{key:"chat_retention_days",value:Ee.value.chatRetentionDays.toString(),description:"对话历史保留天数"}];for(const a of e)yield N.updateSetting(a.key,{value:a.value,description:a.description});d.success("系统配置保存成功")}catch(e){d.error("保存失败")}finally{Me.value=!1}}),function(){return Ma.apply(this,arguments)});var Ma;const $a=(Ra=e(function*(){try{yield s.confirm("确定要重置所有系统配置为默认值吗？","重置确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),Ee.value={defaultModel:1,maxFileSize:50,defaultStorageQuota:10,allowUserRegistration:!0,documentProcessTimeout:300,chatRetentionDays:30},d.success("配置已重置为默认值")}catch(e){}}),function(){return Ra.apply(this,arguments)});var Ra;const ja=(Da=e(function*(){try{Re.value=!0,yield new Promise(e=>setTimeout(e,3e3)),d.success("数据库优化完成")}catch(e){d.error("数据库优化失败")}finally{Re.value=!1}}),function(){return Da.apply(this,arguments)});var Da;const Fa=(Qa=e(function*(){try{je.value=!0,yield new Promise(e=>setTimeout(e,2e3)),d.success("临时文件清理完成")}catch(e){d.error("清理失败")}finally{je.value=!1}}),function(){return Qa.apply(this,arguments)});var Qa;const qa=(Ga=e(function*(){try{De.value=!0,yield new Promise(e=>setTimeout(e,5e3)),d.success("搜索索引重建完成")}catch(e){d.error("重建失败")}finally{De.value=!1}}),function(){return Ga.apply(this,arguments)});var Ga;const Ka=(La=e(function*(){try{Fe.value=!0,yield new Promise(e=>setTimeout(e,3e3));const e={id:Date.now(),filename:`system-backup-${(new Date).toISOString().split("T")[0]}.zip`,size:1024*Math.floor(50*Math.random()+100)*1024,createdAt:(new Date).toISOString()};ea.value.unshift(e),d.success("系统备份创建成功")}catch(e){d.error("备份创建失败")}finally{Fe.value=!1}}),function(){return La.apply(this,arguments)});var La;const Na=(Oa=e(function*(e){try{yield s.confirm(`确定要删除备份"${e.filename}"吗？`,"确认删除",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"});const a=ea.value.findIndex(a=>a.id===e.id);-1!==a&&ea.value.splice(a,1),d.success("备份删除成功")}catch(a){}}),function(e){return Oa.apply(this,arguments)});var Oa;return q(e(function*(){yield Promise.all([na(),da(),oa()])})),(e,s)=>{const T=t,B=a,Q=y,q=i,L=_,N=p,Oe=m,da=o,ia=u,oa=l,ua=c,na=n,ra=r,ma=v;return C(),j("div",O,[s[61]||(s[61]=M("div",null,[M("h1",{class:"text-2xl font-bold text-gray-900 dark:text-gray-100"}," AI模型配置 "),M("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," 配置AI模型参数和系统密钥，管理模型的启用状态 ")],-1)),M("div",H,[M("div",Z,[s[30]||(s[30]=M("h2",{class:"text-lg font-semibold text-gray-900 dark:text-gray-100"}," AI供应商管理 ",-1)),F(B,{type:"primary",onClick:s[0]||(s[0]=e=>Qe.value=!0)},{default:S(()=>[F(T,{class:"mr-2"},{default:S(()=>[F(z(h))]),_:1}),s[29]||(s[29]=D(" 添加供应商 ",-1))]),_:1,__:[29]})]),M("div",E,[(C(!0),j(I,null,A(Ne.value,e=>(C(),j("div",{key:e.id,class:"border border-gray-200 dark:border-dark-700 rounded-lg p-6"},[M("div",Y,[M("div",J,[M("div",{class:G(["w-12 h-12 rounded-lg flex items-center justify-center",sa(e.name)])},[F(T,{size:24},{default:S(()=>[(C(),$(P(V)))]),_:2},1024)],2),M("div",W,[M("div",X,[M("h3",ee,K(e.display_name),1),F(Q,{type:e.is_active?"success":"info",size:"small"},{default:S(()=>[D(K(e.is_active?"已启用":"已禁用"),1)]),_:2},1032,["type"])]),M("div",ae,[M("p",le,K(e.description||"暂无描述"),1),M("p",te," API地址: "+K(e.base_url||"默认"),1)])])]),M("div",se,[F(q,{modelValue:e.is_active,"onUpdate:modelValue":a=>e.is_active=a,onChange:a=>ca(e)},null,8,["modelValue","onUpdate:modelValue","onChange"]),F(Oe,{onCommand:a=>_a(a,e)},{dropdown:S(()=>[F(N,null,{default:S(()=>[F(L,{command:"edit"},{default:S(()=>[F(T,null,{default:S(()=>[F(z(g))]),_:1}),s[31]||(s[31]=D(" 编辑 ",-1))]),_:1,__:[31]}),F(L,{command:"models"},{default:S(()=>[F(T,null,{default:S(()=>[F(z(V))]),_:1}),s[32]||(s[32]=D(" 管理模型 ",-1))]),_:1,__:[32]}),F(L,{command:"delete",divided:""},{default:S(()=>[F(T,null,{default:S(()=>[F(z(w))]),_:1}),s[33]||(s[33]=D(" 删除 ",-1))]),_:1,__:[33]})]),_:1})]),default:S(()=>[F(B,{circle:"",size:"small"},{default:S(()=>[F(T,null,{default:S(()=>[F(z(k))]),_:1})]),_:1})]),_:2},1032,["onCommand"])])])]))),128))])]),M("div",de,[M("div",ie,[s[35]||(s[35]=M("h2",{class:"text-lg font-semibold text-gray-900 dark:text-gray-100"}," AI模型管理 ",-1)),M("div",oe,[F(ia,{modelValue:He.value,"onUpdate:modelValue":s[1]||(s[1]=e=>He.value=e),placeholder:"选择供应商筛选",clearable:"",style:{width:"200px"}},{default:S(()=>[(C(!0),j(I,null,A(Ne.value,e=>(C(),$(da,{key:e.id,label:e.display_name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),F(B,{type:"primary",onClick:s[2]||(s[2]=e=>qe.value=!0)},{default:S(()=>[F(T,{class:"mr-2"},{default:S(()=>[F(z(h))]),_:1}),s[34]||(s[34]=D(" 添加模型 ",-1))]),_:1,__:[34]})])]),M("div",ue,[(C(!0),j(I,null,A(la.value,e=>{var a,l;return C(),j("div",{key:e.id,class:"border border-gray-200 dark:border-dark-700 rounded-lg p-6"},[M("div",ne,[M("div",re,[M("div",{class:G(["w-12 h-12 rounded-lg flex items-center justify-center",(l=null===(a=e.provider)||void 0===a?void 0:a.name,sa(l||""))])},[F(T,{size:24},{default:S(()=>[(C(),$(P(V)))]),_:2},1024)],2),M("div",ce,[M("div",me,[M("h3",_e,K(e.display_name),1),F(Q,{type:e.is_active?"success":"info",size:"small"},{default:S(()=>[D(K(e.is_active?"已启用":"已禁用"),1)]),_:2},1032,["type"]),F(Q,{type:"info",size:"small"},{default:S(()=>{var a;return[D(K(null===(a=e.provider)||void 0===a?void 0:a.display_name),1)]}),_:2},1024)]),M("div",pe,[M("div",ve,[M("p",null,"模型名称: "+K(e.model_name),1),e.max_tokens?(C(),j("p",ye,"最大Token: "+K(e.max_tokens),1)):R("",!0),e.cost_per_1k_tokens?(C(),j("p",fe,"成本: $"+K(e.cost_per_1k_tokens)+"/1K tokens",1)):R("",!0)]),M("div",null,[s[37]||(s[37]=M("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," 系统API密钥 ",-1)),M("div",ge,[F(oa,{modelValue:e.system_api_key,"onUpdate:modelValue":a=>e.system_api_key=a,type:"password",placeholder:"请输入系统API密钥","show-password":"",class:"flex-1"},null,8,["modelValue","onUpdate:modelValue"]),F(B,{onClick:a=>ha(e),loading:e.testing},{default:S(()=>s[36]||(s[36]=[D(" 测试连接 ",-1)])),_:2,__:[36]},1032,["onClick","loading"])])]),M("div",xe,[s[38]||(s[38]=M("div",null,[M("span",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"}," 允许用户使用系统密钥 "),M("p",{class:"text-xs text-gray-500 mt-1"}," 启用后，用户可以使用系统配置的API密钥 ")],-1)),F(q,{modelValue:e.allow_system_key_use,"onUpdate:modelValue":a=>e.allow_system_key_use=a},null,8,["modelValue","onUpdate:modelValue"])])])])]),M("div",ke,[F(q,{modelValue:e.is_active,"onUpdate:modelValue":a=>e.is_active=a,onChange:a=>Va(e)},null,8,["modelValue","onUpdate:modelValue","onChange"]),F(Oe,{onCommand:a=>Ua(a,e)},{dropdown:S(()=>[F(N,null,{default:S(()=>[F(L,{command:"edit"},{default:S(()=>[F(T,null,{default:S(()=>[F(z(g))]),_:1}),s[39]||(s[39]=D(" 编辑 ",-1))]),_:1,__:[39]}),F(L,{command:"delete",divided:""},{default:S(()=>[F(T,null,{default:S(()=>[F(z(w))]),_:1}),s[40]||(s[40]=D(" 删除 ",-1))]),_:1,__:[40]})]),_:1})]),default:S(()=>[F(B,{circle:"",size:"small"},{default:S(()=>[F(T,null,{default:S(()=>[F(z(k))]),_:1})]),_:1})]),_:2},1032,["onCommand"])])])])}),128))])]),M("div",he,[s[48]||(s[48]=M("h2",{class:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6"}," 系统配置 ",-1)),F(ra,{model:Ee.value,"label-width":"200px",class:"max-w-2xl"},{default:S(()=>[F(ua,{label:"默认模型"},{default:S(()=>[F(ia,{modelValue:Ee.value.defaultModel,"onUpdate:modelValue":s[3]||(s[3]=e=>Ee.value.defaultModel=e),placeholder:"选择默认AI模型",style:{width:"300px"}},{default:S(()=>[(C(!0),j(I,null,A(aa.value,e=>(C(),$(da,{key:e.id,label:e.display_name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),s[41]||(s[41]=M("div",{class:"text-xs text-gray-500 mt-1"}," 新用户注册时的默认AI模型 ",-1))]),_:1,__:[41]}),F(ua,{label:"最大上传文件大小"},{default:S(()=>[F(na,{modelValue:Ee.value.maxFileSize,"onUpdate:modelValue":s[4]||(s[4]=e=>Ee.value.maxFileSize=e),min:1,max:100,placeholder:"MB"},null,8,["modelValue"]),s[42]||(s[42]=M("span",{class:"ml-2 text-sm text-gray-500"},"MB",-1))]),_:1,__:[42]}),F(ua,{label:"用户默认存储配额"},{default:S(()=>[F(na,{modelValue:Ee.value.defaultStorageQuota,"onUpdate:modelValue":s[5]||(s[5]=e=>Ee.value.defaultStorageQuota=e),min:1,max:100,placeholder:"GB"},null,8,["modelValue"]),s[43]||(s[43]=M("span",{class:"ml-2 text-sm text-gray-500"},"GB",-1))]),_:1,__:[43]}),F(ua,{label:"允许用户注册"},{default:S(()=>[F(q,{modelValue:Ee.value.allowUserRegistration,"onUpdate:modelValue":s[6]||(s[6]=e=>Ee.value.allowUserRegistration=e)},null,8,["modelValue"]),s[44]||(s[44]=M("div",{class:"text-xs text-gray-500 mt-1"}," 关闭后，只有管理员可以创建新用户 ",-1))]),_:1,__:[44]}),F(ua,{label:"文档处理超时"},{default:S(()=>[F(na,{modelValue:Ee.value.documentProcessTimeout,"onUpdate:modelValue":s[7]||(s[7]=e=>Ee.value.documentProcessTimeout=e),min:30,max:600,placeholder:"秒"},null,8,["modelValue"]),s[45]||(s[45]=M("span",{class:"ml-2 text-sm text-gray-500"},"秒",-1))]),_:1,__:[45]}),F(ua,{label:"对话历史保留"},{default:S(()=>[F(ia,{modelValue:Ee.value.chatRetentionDays,"onUpdate:modelValue":s[8]||(s[8]=e=>Ee.value.chatRetentionDays=e),style:{width:"200px"}},{default:S(()=>[F(da,{label:"7天",value:7}),F(da,{label:"30天",value:30}),F(da,{label:"90天",value:90}),F(da,{label:"永久保留",value:0})]),_:1},8,["modelValue"])]),_:1}),F(ua,null,{default:S(()=>[F(B,{type:"primary",onClick:Ba,loading:Me.value},{default:S(()=>s[46]||(s[46]=[D(" 保存配置 ",-1)])),_:1,__:[46]},8,["loading"]),F(B,{onClick:$a},{default:S(()=>s[47]||(s[47]=[D(" 重置 ",-1)])),_:1,__:[47]})]),_:1})]),_:1},8,["model"])]),M("div",be,[s[56]||(s[56]=M("h2",{class:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6"}," 系统维护 ",-1)),M("div",Ve,[M("div",we,[s[52]||(s[52]=M("h3",{class:"font-medium text-gray-900 dark:text-gray-100"}," 数据库维护 ",-1)),M("div",Ue,[F(B,{onClick:ja,loading:Re.value},{default:S(()=>[F(T,{class:"mr-2"},{default:S(()=>[F(z(U))]),_:1}),s[49]||(s[49]=D(" 优化数据库 ",-1))]),_:1,__:[49]},8,["loading"]),F(B,{onClick:Fa,loading:je.value},{default:S(()=>[F(T,{class:"mr-2"},{default:S(()=>[F(z(w))]),_:1}),s[50]||(s[50]=D(" 清理临时文件 ",-1))]),_:1,__:[50]},8,["loading"]),F(B,{onClick:qa,loading:De.value},{default:S(()=>[F(T,{class:"mr-2"},{default:S(()=>[F(z(b))]),_:1}),s[51]||(s[51]=D(" 重建搜索索引 ",-1))]),_:1,__:[51]},8,["loading"])])]),M("div",Ie,[s[55]||(s[55]=M("h3",{class:"font-medium text-gray-900 dark:text-gray-100"}," 系统备份 ",-1)),M("div",Ce,[F(B,{onClick:Ka,loading:Fe.value},{default:S(()=>[F(T,{class:"mr-2"},{default:S(()=>[F(z(f))]),_:1}),s[53]||(s[53]=D(" 创建系统备份 ",-1))]),_:1,__:[53]},8,["loading"]),F(B,{onClick:s[9]||(s[9]=e=>Ge.value=!0)},{default:S(()=>[F(T,{class:"mr-2"},{default:S(()=>[F(z(x))]),_:1}),s[54]||(s[54]=D(" 备份历史 ",-1))]),_:1,__:[54]})])])])]),F(ma,{modelValue:Qe.value,"onUpdate:modelValue":s[16]||(s[16]=e=>Qe.value=e),title:Ze.value?"编辑AI供应商":"添加AI供应商",width:"500px",onClose:ka},{footer:S(()=>[M("div",Ae,[F(B,{onClick:s[15]||(s[15]=e=>Qe.value=!1)},{default:S(()=>s[57]||(s[57]=[D("取消",-1)])),_:1,__:[57]}),F(B,{type:"primary",onClick:ga,loading:$e.value},{default:S(()=>[D(K(Ze.value?"更新":"添加"),1)]),_:1},8,["loading"])])]),default:S(()=>[F(ra,{model:Ye.value,rules:Je,ref_key:"providerFormRef",ref:Ke,"label-width":"120px"},{default:S(()=>[F(ua,{label:"供应商标识",prop:"name"},{default:S(()=>[F(oa,{modelValue:Ye.value.name,"onUpdate:modelValue":s[10]||(s[10]=e=>Ye.value.name=e),placeholder:"如: openai, siliconflow"},null,8,["modelValue"])]),_:1}),F(ua,{label:"显示名称",prop:"display_name"},{default:S(()=>[F(oa,{modelValue:Ye.value.display_name,"onUpdate:modelValue":s[11]||(s[11]=e=>Ye.value.display_name=e),placeholder:"如: OpenAI, 硅基流动"},null,8,["modelValue"])]),_:1}),F(ua,{label:"API地址"},{default:S(()=>[F(oa,{modelValue:Ye.value.base_url,"onUpdate:modelValue":s[12]||(s[12]=e=>Ye.value.base_url=e),placeholder:"如: https://api.openai.com/v1"},null,8,["modelValue"])]),_:1}),F(ua,{label:"描述"},{default:S(()=>[F(oa,{modelValue:Ye.value.description,"onUpdate:modelValue":s[13]||(s[13]=e=>Ye.value.description=e),type:"textarea",placeholder:"供应商描述信息",rows:3},null,8,["modelValue"])]),_:1}),F(ua,{label:"启用状态"},{default:S(()=>[F(q,{modelValue:Ye.value.is_active,"onUpdate:modelValue":s[14]||(s[14]=e=>Ye.value.is_active=e)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),F(ma,{modelValue:qe.value,"onUpdate:modelValue":s[27]||(s[27]=e=>qe.value=e),title:"添加AI模型",width:"600px",onClose:za},{footer:S(()=>[M("div",Pe,[F(B,{onClick:s[26]||(s[26]=e=>qe.value=!1)},{default:S(()=>s[59]||(s[59]=[D("取消",-1)])),_:1,__:[59]}),F(B,{type:"primary",onClick:Sa,loading:$e.value},{default:S(()=>s[60]||(s[60]=[D(" 添加 ",-1)])),_:1,__:[60]},8,["loading"])])]),default:S(()=>[F(ra,{model:We.value,rules:Xe,ref_key:"modelFormRef",ref:Le,"label-width":"120px"},{default:S(()=>[F(ua,{label:"供应商",prop:"provider_id"},{default:S(()=>[F(ia,{modelValue:We.value.provider_id,"onUpdate:modelValue":s[17]||(s[17]=e=>We.value.provider_id=e),placeholder:"选择供应商",style:{width:"100%"}},{default:S(()=>[(C(!0),j(I,null,A(Ne.value,e=>(C(),$(da,{key:e.id,label:e.display_name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),F(ua,{label:"模型名称",prop:"model_name"},{default:S(()=>[F(oa,{modelValue:We.value.model_name,"onUpdate:modelValue":s[18]||(s[18]=e=>We.value.model_name=e),placeholder:"如: gpt-4, Qwen/Qwen2.5-7B-Instruct"},null,8,["modelValue"])]),_:1}),F(ua,{label:"显示名称",prop:"display_name"},{default:S(()=>[F(oa,{modelValue:We.value.display_name,"onUpdate:modelValue":s[19]||(s[19]=e=>We.value.display_name=e),placeholder:"如: GPT-4, Qwen2.5-7B-Instruct"},null,8,["modelValue"])]),_:1}),F(ua,{label:"系统密钥"},{default:S(()=>[F(oa,{modelValue:We.value.system_api_key,"onUpdate:modelValue":s[20]||(s[20]=e=>We.value.system_api_key=e),type:"password",placeholder:"请输入系统API密钥（可选）","show-password":""},null,8,["modelValue"])]),_:1}),F(ua,{label:"最大Token"},{default:S(()=>[F(na,{modelValue:We.value.max_tokens,"onUpdate:modelValue":s[21]||(s[21]=e=>We.value.max_tokens=e),min:1,max:2e5,placeholder:"4000",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),F(ua,{label:"成本/1K Token"},{default:S(()=>[F(na,{modelValue:We.value.cost_per_1k_tokens,"onUpdate:modelValue":s[22]||(s[22]=e=>We.value.cost_per_1k_tokens=e),min:0,precision:4,step:.001,placeholder:"0.002",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),F(ua,{label:"启用状态"},{default:S(()=>[F(q,{modelValue:We.value.is_active,"onUpdate:modelValue":s[23]||(s[23]=e=>We.value.is_active=e)},null,8,["modelValue"])]),_:1}),F(ua,{label:"允许系统密钥"},{default:S(()=>[F(q,{modelValue:We.value.allow_system_key_use,"onUpdate:modelValue":s[24]||(s[24]=e=>We.value.allow_system_key_use=e)},null,8,["modelValue"]),s[58]||(s[58]=M("div",{class:"text-xs text-gray-500 mt-1"}," 允许用户使用系统配置的API密钥 ",-1))]),_:1,__:[58]}),F(ua,{label:"支持流式"},{default:S(()=>[F(q,{modelValue:We.value.supports_streaming,"onUpdate:modelValue":s[25]||(s[25]=e=>We.value.supports_streaming=e)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),F(ma,{modelValue:Ge.value,"onUpdate:modelValue":s[28]||(s[28]=e=>Ge.value=e),title:"备份历史",width:"600px"},{default:S(()=>[M("div",Se,[(C(!0),j(I,null,A(ea.value,e=>{return C(),j("div",{key:e.id,class:"flex items-center justify-between p-3 border border-gray-200 dark:border-dark-700 rounded-lg"},[M("div",null,[M("div",Te,K(e.filename),1),M("div",ze,K((a=e.createdAt,new Date(a).toLocaleString("zh-CN")))+" • "+K(ta(e.size)),1)]),M("div",Be,[F(B,{size:"small",onClick:a=>(e=>{d.success(`开始下载 ${e.filename}`)})(e)},{default:S(()=>[F(T,null,{default:S(()=>[F(z(f))]),_:1})]),_:2},1032,["onClick"]),F(B,{size:"small",type:"danger",onClick:a=>Na(e)},{default:S(()=>[F(T,null,{default:S(()=>[F(z(w))]),_:1})]),_:2},1032,["onClick"])])]);var a}),128))])]),_:1},8,["modelValue"])])}}});export{Me as default};