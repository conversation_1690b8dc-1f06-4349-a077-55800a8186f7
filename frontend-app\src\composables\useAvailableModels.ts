import { computed } from 'vue'
import { useAIModelsStore } from '@/stores/aiModels'
import { useSettingsStore } from '@/stores/settings'

/**
 * 获取用户可用的AI模型
 * 根据用户的供应商偏好设置和API密钥过滤模型
 */
export function useAvailableModels() {
  const aiModelsStore = useAIModelsStore()
  const settingsStore = useSettingsStore()

  // 可用模型列表
  const availableModels = computed(() => {
    const providerPreferences = settingsStore.userSettings.providerPreferences || {}

    // 如果没有供应商偏好设置，默认启用所有供应商
    if (Object.keys(providerPreferences).length === 0) {
      // 为所有供应商创建默认启用的偏好设置
      const defaultPreferences: Record<string, any> = {}
      aiModelsStore.aiProviders.forEach(provider => {
        defaultPreferences[provider.id] = {
          enabled: true,
          useSystemKey: true,
          selectedModel: null
        }
      })
      return aiModelsStore.getAvailableModels(defaultPreferences)
    }

    return aiModelsStore.getAvailableModels(providerPreferences)
  })

  // 根据供应商ID获取可用模型
  const getModelsByProvider = (providerId: number) => {
    return availableModels.value.filter(model => model.provider_id === providerId)
  }

  // 检查模型是否可用
  const isModelAvailable = (modelId: number) => {
    return availableModels.value.some(model => model.id === modelId)
  }

  // 获取默认模型（如果设置的默认模型不可用，返回第一个可用模型）
  const getDefaultModel = () => {
    const defaultModelId = settingsStore.userSettings.defaultModel
    
    if (defaultModelId && isModelAvailable(defaultModelId)) {
      return aiModelsStore.getModelById(defaultModelId)
    }
    
    // 如果默认模型不可用，返回第一个可用模型
    return availableModels.value[0] || null
  }

  // 初始化数据
  const initializeData = async () => {
    await aiModelsStore.initializeData()
    await settingsStore.fetchUserSettings()
  }

  return {
    availableModels,
    getModelsByProvider,
    isModelAvailable,
    getDefaultModel,
    initializeData
  }
}
