{"info": {"name": "智能问答系统API", "description": "智能问答系统后端API集合", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8000", "type": "string"}, {"key": "token", "value": "", "type": "string"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "item": [{"name": "认证API", "item": [{"name": "用户注册", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"test123\",\n  \"display_name\": \"测试用户\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "register"]}}}, {"name": "用户登录", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('token', response.access_token);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser\",\n  \"password\": \"test123\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}}}, {"name": "获取当前用户信息", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/auth/me", "host": ["{{baseUrl}}"], "path": ["api", "auth", "me"]}}}]}, {"name": "知识库API", "item": [{"name": "获取知识库列表", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/knowledge-bases/", "host": ["{{baseUrl}}"], "path": ["api", "knowledge-bases", ""]}}}, {"name": "创建知识库", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200 || pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('kb_id', response.id);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"测试知识库\",\n  \"description\": \"这是一个测试知识库\"\n}"}, "url": {"raw": "{{baseUrl}}/api/knowledge-bases/", "host": ["{{baseUrl}}"], "path": ["api", "knowledge-bases", ""]}}}, {"name": "获取知识库详情", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/knowledge-bases/{{kb_id}}", "host": ["{{baseUrl}}"], "path": ["api", "knowledge-bases", "{{kb_id}}"]}}}, {"name": "删除知识库", "request": {"method": "DELETE", "url": {"raw": "{{baseUrl}}/api/knowledge-bases/{{kb_id}}", "host": ["{{baseUrl}}"], "path": ["api", "knowledge-bases", "{{kb_id}}"]}}}]}, {"name": "文档API", "item": [{"name": "获取文档列表", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/documents/kb/{{kb_id}}", "host": ["{{baseUrl}}"], "path": ["api", "documents", "kb", "{{kb_id}}"]}}}, {"name": "上传文档", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": []}]}, "url": {"raw": "{{baseUrl}}/api/documents/upload/{{kb_id}}", "host": ["{{baseUrl}}"], "path": ["api", "documents", "upload", "{{kb_id}}"]}}}, {"name": "批量删除文档", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"document_ids\": [1, 2, 3]\n}"}, "url": {"raw": "{{baseUrl}}/api/documents/batch-delete", "host": ["{{baseUrl}}"], "path": ["api", "documents", "batch-delete"]}}}]}, {"name": "聊天API", "item": [{"name": "获取聊天会话列表", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/chat/sessions", "host": ["{{baseUrl}}"], "path": ["api", "chat", "sessions"]}}}, {"name": "创建聊天会话", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200 || pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('session_id', response.id);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"测试聊天会话\"\n}"}, "url": {"raw": "{{baseUrl}}/api/chat/sessions", "host": ["{{baseUrl}}"], "path": ["api", "chat", "sessions"]}}}, {"name": "流式聊天", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"message\": \"你好，请介绍一下机器学习\",\n  \"model_id\": \"Qwen/Qwen2.5-7B-Instruct\",\n  \"knowledge_base_ids\": [{{kb_id}}],\n  \"history_limit\": 10\n}"}, "url": {"raw": "{{baseUrl}}/api/chat/sessions/{{session_id}}/stream", "host": ["{{baseUrl}}"], "path": ["api", "chat", "sessions", "{{session_id}}", "stream"]}}}, {"name": "获取聊天历史", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/chat/sessions/{{session_id}}/messages/history?limit=10", "host": ["{{baseUrl}}"], "path": ["api", "chat", "sessions", "{{session_id}}", "messages", "history"], "query": [{"key": "limit", "value": "10"}]}}}]}, {"name": "AI模型API", "item": [{"name": "获取AI模型列表", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/ai/models", "host": ["{{baseUrl}}"], "path": ["api", "ai", "models"]}}}]}, {"name": "统计API", "item": [{"name": "获取基础统计", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/stats/dashboard", "host": ["{{baseUrl}}"], "path": ["api", "stats", "dashboard"]}}}, {"name": "获取详细统计", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/stats/detailed", "host": ["{{baseUrl}}"], "path": ["api", "stats", "detailed"]}}}, {"name": "获取活动统计", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/stats/activity?days=7", "host": ["{{baseUrl}}"], "path": ["api", "stats", "activity"], "query": [{"key": "days", "value": "7"}]}}}]}]}