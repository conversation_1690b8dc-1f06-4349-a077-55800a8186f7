<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>双端架构功能详述图</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Microsoft YaHei', Arial, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            padding: 40px 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .header p {
            font-size: 16px;
            color: #6b7280;
        }

        .architecture-layers {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .layer {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            overflow: hidden;
        }

        .layer-header {
            padding: 20px;
            font-size: 20px;
            font-weight: 700;
            color: white;
            text-align: center;
        }

        .ui-layer .layer-header {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        }

        .service-layer .layer-header {
            background: linear-gradient(135deg, #10b981 0%, #047857 100%);
        }

        .ai-layer .layer-header {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        }

        .storage-layer .layer-header {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

        .dual-architecture {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            padding: 20px;
        }

        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            padding: 20px;
        }

        .user-side, .admin-side {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .side-header {
            padding: 20px;
            text-align: center;
            color: white;
            font-size: 18px;
            font-weight: 600;
        }

        .user-side .side-header {
            background: linear-gradient(135deg, #c4b5fd 0%, #a78bfa 100%);
        }

        .admin-side .side-header {
            background: linear-gradient(135deg, #fb923c 0%, #f97316 100%);
        }

        .side-icon {
            font-size: 24px;
            margin-right: 8px;
        }

        .functions-container {
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .module-card {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            transition: all 0.3s ease;
        }

        .module-card:hover {
            border-color: #3b82f6;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .module-title {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
        }

        .module-desc {
            font-size: 12px;
            color: #6b7280;
            line-height: 1.4;
        }

        .flow-section {
            background: #f8fafc;
            border-radius: 16px;
            padding: 30px;
            margin-top: 30px;
        }

        .flow-title {
            font-size: 20px;
            font-weight: 700;
            color: #374151;
            text-align: center;
            margin-bottom: 20px;
        }

        .flow-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .flow-step {
            background: white;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .step-number {
            background: #3b82f6;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            margin: 0 auto 10px;
        }

        .step-text {
            font-size: 13px;
            color: #374151;
            line-height: 1.4;
        }

        @media (max-width: 768px) {
            .dual-architecture {
                grid-template-columns: 1fr;
            }

            .modules-grid {
                grid-template-columns: 1fr;
            }

            .flow-steps {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>双端架构功能对比</h1>
            <p>用户端与管理员端功能模块详述</p>
        </div>

        <div class="dual-architecture">
            <!-- 用户端 -->
            <div class="user-side">
                <div class="side-header">
                    <span class="side-icon">�</span>
                    <div class="side-title">用户</div>
                </div>

                <div class="functions-container">
                    <div class="function-row">
                        <div class="function-label">智能问答</div>
                        <div class="function-card">
                            基于RAG的，多轮，<br>实时响应
                        </div>
                    </div>

                    <div class="function-row">
                        <div class="function-label">内容展示</div>
                        <div class="function-card">
                            Markdown，图表，<br>代码高亮
                        </div>
                    </div>

                    <div class="function-row">
                        <div class="function-label">记忆长度</div>
                        <div class="function-card">
                            可自定义的上下文记<br>忆
                        </div>
                    </div>

                    <div class="function-row">
                        <div class="function-label">专注模式</div>
                        <div class="function-card">
                            AI仅从知识库回答
                        </div>
                    </div>

                    <div class="function-row">
                        <div class="function-label">模型切换</div>
                        <div class="function-card">
                            支持多种模型，智能<br>推荐
                        </div>
                    </div>
                </div>
            </div>

            <!-- 管理员端 -->
            <div class="admin-side">
                <div class="side-header">
                    <span class="side-icon">⚙️</span>
                    <div class="side-title">管理员</div>
                </div>

                <div class="functions-container">
                    <div class="function-row">
                        <div class="function-label">知识库管理</div>
                        <div class="function-card">
                            知识库上传，向量<br>化，版本控制
                        </div>
                    </div>

                    <div class="function-row">
                        <div class="function-label">权限管理</div>
                        <div class="function-card">
                            多级权限管理<br>(RBAC)
                        </div>
                    </div>

                    <div class="function-row">
                        <div class="function-label">参数调优</div>
                        <div class="function-card">
                            模型参数调优，检索<br>阈值
                        </div>
                    </div>

                    <div class="function-row">
                        <div class="function-label">数据分析</div>
                        <div class="function-card">
                            用户行为，知识库分<br>析
                        </div>
                    </div>

                    <div class="function-row">
                        <div class="function-label">安全管理</div>
                        <div class="function-card">
                            API密钥安全管理
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
