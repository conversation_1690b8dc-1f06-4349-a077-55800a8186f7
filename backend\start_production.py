#!/usr/bin/env python3
"""
生产环境启动脚本
专门用于宝塔环境部署，解决大文件处理超时问题
"""
import uvicorn
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

if __name__ == "__main__":
    # 生产环境配置
    config = {
        "app": "app.main:app",
        "host": "127.0.0.1",
        "port": 8000,
        "workers": 1,  # 单进程，避免资源冲突
        "timeout_keep_alive": 300,  # 保持连接超时时间：5分钟
        "timeout_graceful_shutdown": 60,  # 优雅关闭超时时间：1分钟
        "limit_concurrency": 100,  # 限制并发连接数
        "limit_max_requests": 1000,  # 最大请求数
        "log_level": "info",
        "access_log": True,
        "use_colors": False,
        "reload": False,  # 生产环境不启用热重载
    }
    
    print("🚀 启动AI知识库后端服务（生产环境）")
    print(f"📍 监听地址: {config['host']}:{config['port']}")
    print(f"⏱️  连接超时: {config['timeout_keep_alive']}秒")
    print(f"👥 工作进程: {config['workers']}")
    print("=" * 50)
    
    try:
        uvicorn.run(**config)
    except KeyboardInterrupt:
        print("\n🛑 服务已停止")
    except Exception as e:
        print(f"❌ 服务启动失败: {e}")
        sys.exit(1)
