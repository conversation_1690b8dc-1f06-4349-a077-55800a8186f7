const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/echarts-I8QroFHb.js","assets/vendor-BJ-uKP15.js","assets/elementPlus-Di4PDIm8.js","assets/rolldown-runtime-BaowUlwK.js","assets/elementPlus-DvU4m8M3.css","assets/vendor-B-oHczHB.css","assets/chart-DuSYZwvj.js"])))=>i.map(i=>d[i]);
var e,t;(()=>{function a(e,t,a,l,s,n,r){try{var o=e[n](r),i=o.value}catch(e){return void a(e)}o.done?t(i):Promise.resolve(i).then(l,s)}function l(e){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},l(e)}function s(e){var t=function(e){if("object"!=l(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var a=t.call(e,"string");if("object"!=l(a))return a;throw TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==l(t)?t:t+""}function n(e,t,a){return(t=s(t))in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function r(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),a.push.apply(a,l)}return a}e=function(e){return function(){var t=this,l=arguments;return new Promise(function(s,n){var r=e.apply(t,l);function o(e){a(r,s,n,o,i,"next",e)}function i(e){a(r,s,n,o,i,"throw",e)}o(void 0)})}},t=function(e){for(var t=1;t<arguments.length;t++){var a=null==arguments[t]?{}:arguments[t];t%2?r(Object(a),!0).forEach(function(t){n(e,t,a[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):r(Object(a)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))})}return e}})();import{A as a,B as l,C as s,E as n,J as r,L as o,N as i,b as d,c as u,i as c,l as v,m,n as g,o as p,p as f,q as h,r as x,s as y,u as b,y as k}from"./elementPlus-Di4PDIm8.js";import{b as w,b$ as _,bC as S,bD as C,bI as I,bO as j,bQ as z,bU as M,bW as T,bY as B,bZ as $,be as L,bf as V,bg as O,bh as A,bi as E,bn as H,bs as U,bt as D,bu as X,by as P,c as N,c0 as R,c1 as J,cT as K,cW as F,cb as q,cf as W,d4 as Y,d8 as Q,dA as Z,dB as G,dD as ee,dL as te,dN as ae,dO as le,dU as se,d_ as ne,dc as re,dd as oe,de as ie,df as de,dg as ue,di as ce,dj as ve,dk as me,dl as ge,ds as pe,dy as fe,ea as he,ed as xe}from"./vendor-BJ-uKP15.js";import{b as ye,f as be}from"./index-Byt5TjPh.js";import"./api-D-gMiCJf.js";import{b as ke}from"./settings-46b1LTsi.js";import{b as we}from"./_plugin-vue_export-helper-CjD0mXop.js";import{b as _e}from"./ui-xhrfN-Sd.js";import"./knowledgeBase-yaqAZvLB.js";import{b as Se}from"./chat-Cv_kC_4-.js";import{b as Ce}from"./HtmlPreview-ovS6yXAM.js";import{b as Ie}from"./knowledgeBase-Dn54ZAUS.js";import{b as je}from"./aiModels-CcIQpxgt.js";const ze=F("chat",()=>{const a=se([]),l=se(null),s=se({}),n=se(!1),r=se(!1),o=(i=e(function*(e){n.value=!0;try{const t=yield Se.getSessions(e);return a.value=t,{success:!0,data:t}}catch(l){var t;return{success:!1,message:(null===(t=l.response)||void 0===t||null===(t=t.data)||void 0===t?void 0:t.message)||"获取聊天会话失败"}}finally{n.value=!1}}),function(e){return i.apply(this,arguments)});var i;const d=(u=e(function*(e){n.value=!0;try{const t=yield Se.createSession(e);return a.value.unshift(t),l.value=t,s.value[t.id]=[],{success:!0,data:t}}catch(r){var t;return{success:!1,message:(null===(t=r.response)||void 0===t||null===(t=t.data)||void 0===t?void 0:t.message)||"创建聊天会话失败"}}finally{n.value=!1}}),function(e){return u.apply(this,arguments)});var u;const c=(v=e(function*(e,t){n.value=!0;try{const a=yield Se.getMessages(e,t);return s.value[e]=a,{success:!0,data:a}}catch(l){var a;return{success:!1,message:(null===(a=l.response)||void 0===a||null===(a=a.data)||void 0===a?void 0:a.message)||"获取聊天历史失败"}}finally{n.value=!1}}),function(e,t){return v.apply(this,arguments)});var v;const m=(g=e(function*(e){try{var t;return yield Se.deleteSession(e),a.value=a.value.filter(t=>t.id!==e),delete s.value[e],(null===(t=l.value)||void 0===t?void 0:t.id)===e&&(l.value=null),{success:!0}}catch(r){var n;return{success:!1,message:(null===(n=r.response)||void 0===n||null===(n=n.data)||void 0===n?void 0:n.message)||"删除会话失败"}}}),function(e){return g.apply(this,arguments)});var g;const p=(f=e(function*(e,t){try{var s;const n=yield Se.updateSession(e,{title:t}),r=a.value.findIndex(t=>t.id===e);return-1!==r&&(a.value[r]=n),(null===(s=l.value)||void 0===s?void 0:s.id)===e&&(l.value=n),{success:!0,data:n}}catch(r){var n;return{success:!1,message:(null===(n=r.response)||void 0===n||null===(n=n.data)||void 0===n?void 0:n.message)||"更新会话失败"}}}),function(e,t){return f.apply(this,arguments)});var f;const h=(x=e(function*(e,a,l){r.value=!0;try{const n={id:Date.now(),session_id:e,role:"user",content:a.message,created_at:(new Date).toISOString()};s.value[e]||(s.value[e]=[]),s.value[e].push(n);const r={id:Date.now()+1,session_id:e,role:"assistant",content:"",created_at:(new Date).toISOString()};return s.value[e].push(r),yield Se.streamChat(e,a,t=>{const a=s.value[e].findIndex(e=>e.id===r.id);-1!==a&&(s.value[e][a].content+=t)},t=>{const a=s.value[e].findIndex(e=>e.id===r.id);-1!==a&&(s.value[e][a]=t)},a=>{const l=s.value[e].findIndex(e=>e.id===r.id);throw-1!==l&&(s.value[e][l]=t(t({},r),{},{content:a,role:"assistant",isError:!0})),new Error(a)},l),{success:!0}}catch(n){return{success:!1,message:n.message||"发送消息失败"}}finally{r.value=!1}}),function(e,t,a){return x.apply(this,arguments)});var x;const y=(b=e(function*(e){const t=a.value.find(t=>t.id===e);return t?(l.value=t,s.value[e]||(yield c(e)),{success:!0,data:t}):{success:!1,message:"会话不存在"}}),function(e){return b.apply(this,arguments)});var b;return{sessions:a,currentSession:l,messages:s,loading:n,sending:r,fetchSessions:o,createSession:d,fetchChatHistory:c,sendMessage:h,deleteSession:m,updateSessionTitle:p,setCurrentSession:y,clearAll:()=>{a.value=[],l.value=null,s.value={}}}});N.setOptions({highlight:function(e,t){const a=String(e||"");if(t&&w.getLanguage(t))try{return w.highlight(a,{language:t}).value}catch(l){}return w.highlightAuto(a).value},breaks:!0,gfm:!0,tables:!0});const Me=new N.Renderer;Me.code=function(e){const t=String(e.text||""),a=e.lang||"",l=a&&w.getLanguage(a)?a:"plaintext",s=w.highlight(t,{language:l}).value,n=["html","javascript","js","python","echarts","chart","json"].includes(l.toLowerCase())||t.includes("echarts")||t.includes("Chart.js")||t.includes("option")||t.includes("plt.")||t.includes("matplotlib"),r="code_"+Math.random().toString(36).substr(2,9);return`\n    <div class="code-block-container relative group rounded-xl overflow-hidden border border-gray-200 dark:border-gray-700 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 shadow-lg" data-code-id="${r}">\n      <div class="code-block-header flex items-center justify-between bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 px-4 py-3 border-b border-gray-200 dark:border-gray-600">\n        <div class="flex items-center space-x-2">\n          <div class="flex space-x-1">\n            <div class="w-3 h-3 rounded-full bg-red-400"></div>\n            <div class="w-3 h-3 rounded-full bg-yellow-400"></div>\n            <div class="w-3 h-3 rounded-full bg-green-400"></div>\n          </div>\n          <span class="text-sm font-medium text-gray-600 dark:text-gray-300">${l}</span>\n        </div>\n        <div class="flex items-center space-x-2">\n          ${n?`\n          <button\n            class="run-code-btn transition-all duration-200 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white px-3 py-1.5 rounded-lg text-xs font-medium shadow-md hover:shadow-lg transform hover:scale-105"\n            onclick="runCode('${r}', '${l}')"\n            data-code="${encodeURIComponent(t)}"\n            title="运行代码"\n          >\n            <svg class="w-3 h-3 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">\n              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m6-10V7a3 3 0 11-6 0V4a3 3 0 11-6 0v3a3 3 0 11-6 0v3"></path>\n            </svg>\n            运行\n          </button>\n          `:""}\n          <button\n            class="copy-code-btn transition-all duration-200 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-3 py-1.5 rounded-lg text-xs font-medium shadow-md hover:shadow-lg transform hover:scale-105"\n            onclick="copyCode(this)"\n            data-code="${encodeURIComponent(t)}"\n            title="复制代码"\n          >\n            <svg class="w-3 h-3 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">\n              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>\n            </svg>\n            复制\n          </button>\n        </div>\n      </div>\n      <div class="relative">\n        <pre class="hljs p-6 overflow-x-auto text-sm leading-relaxed bg-transparent"><code class="language-${l}">${s}</code></pre>\n        <div class="absolute bottom-3 right-3 bg-black/20 dark:bg-white/10 backdrop-blur-sm px-2 py-1 rounded-md">\n          <span class="text-xs font-mono text-gray-600 dark:text-gray-300">${l}</span>\n        </div>\n      </div>\n      \x3c!-- 运行结果容器 --\x3e\n      <div id="result_${r}" class="code-result hidden border-t border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-800/50"></div>\n    </div>\n  `},Me.codespan=function(e){return`<code class="inline-code bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/30 dark:to-purple-900/30 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-md text-sm font-mono border border-blue-200 dark:border-blue-700/50">${String(e.text||"")}</code>`},Me.link=function(e){return`<a href="${String(e.href||"")}" title="${String(e.title||"")}" target="_blank" rel="noopener noreferrer" class="text-blue-600 dark:text-blue-400 hover:text-purple-600 dark:hover:text-purple-400 transition-colors duration-200 underline decoration-2 underline-offset-2 hover:decoration-purple-500">${this.parser.parseInline(e.tokens||[])}</a>`},N.use({renderer:Me});const Te=document.createElement("style");function Be(e){try{const t=function(e){const t=new RegExp("```(?:markdown|table|)\\s*\\n([\\s\\S]*?)\\n```","g");return e.replace(t,(e,t)=>{const a=t.trim(),l=a.split("\n").filter(e=>e.trim()),s=l.some(e=>e.includes("|")),n=l.some(e=>/^\s*\|?[-\s\|:]+\|?\s*$/.test(e)),r=l.filter(e=>e.includes("|")).length>=2,o=s&&n&&r,i=a.endsWith("|")&&!a.endsWith("|\n");return o&&!i?"\n\n"+a+"\n\n":e})}(e),a=N.parse(t,{async:!1});return"string"==typeof a?a:String(a)}catch(t){return`<pre>${e}</pre>`}}function $e(e){const t=document.createElement("textarea");t.value=e,t.style.position="fixed",t.style.left="-999999px",t.style.top="-999999px",document.body.appendChild(t),t.focus(),t.select();try{document.execCommand("copy")}catch(a){}document.body.removeChild(t)}function Le(){return(Le=e(function*(a,l){try{let d,u=a.replace(/XX/g,"0").replace(/\[XX,\s*XX,\s*XX,\s*XX,\s*XX\]/g,"[85, 90, 78, 92, 88]").replace(/\[XX,\s*XX,\s*XX\]/g,"[75, 80, 85]").replace(/\[XX,\s*XX\]/g,"[70, 80]").replace(/\{\s*XX\s*\}/g,"{}").replace(/"\s*XX\s*"/g,'"示例数据"');try{d=JSON.parse(u)}catch(r){try{d=new Function("return "+u)()}catch(o){throw new Error(`无法解析图表配置: JSON错误(${r.message}), Function错误(${o.message})`)}}if(!d||"object"!=typeof d)throw new Error("图表配置必须是一个对象");const c=d.series||d.dataset,v=d.type&&d.data;if(!c&&!v)throw new Error("图表配置必须包含ECharts格式(series/dataset)或Chart.js格式(type/data)的必要字段");const m=document.createElement("div");m.style.width="100%",m.style.minHeight="300px",m.style.height="auto",m.style.aspectRatio="16/9",m.style.maxHeight="600px",m.style.backgroundColor="white",m.style.borderRadius="8px",m.style.padding="10px",m.id="chart_"+Math.random().toString(36).substring(2,11),Ve(l,m,"success");try{if(c){const e=(yield be(()=>import("./echarts-I8QroFHb.js"),__vite__mapDeps([0,1,2,3,4,5]))).init(m),a=t({backgroundColor:"white"},d);e.setOption(a,!0);const l=()=>e.resize();window.addEventListener("resize",l),setTimeout(()=>{window.removeEventListener("resize",l)},3e4)}else if(v){var s,n;const a=document.createElement("canvas");m.appendChild(a),m.style.position="relative",m.style.height="400px";const{Chart:l,registerables:r}=yield be(e(function*(){const{Chart:e,registerables:t}=yield import("./chart-DuSYZwvj.js");return{Chart:e,registerables:t}}),__vite__mapDeps([6,1,2,3,4,5]));l.register(...r),new l(a,t(t({},d),{},{options:t({responsive:!0,maintainAspectRatio:!1,plugins:t(t({},null===(s=d.options)||void 0===s?void 0:s.plugins),{},{legend:t({},null===(n=d.options)||void 0===n||null===(n=n.plugins)||void 0===n?void 0:n.legend)}),backgroundColor:"white"},d.options)}))}}catch(i){m.innerHTML=`<div class="p-4 text-center text-gray-500">图表渲染失败: ${(null==i?void 0:i.message)||"未知错误"}</div>`}}catch(d){const e=(null==d?void 0:d.message)||"未知错误",t=document.createElement("pre");t.className="bg-gray-100 p-4 rounded text-sm overflow-auto",t.textContent=a;const s=document.createElement("div");s.className="space-y-2",s.innerHTML=`\n      <div class="text-red-600 font-medium">图表配置解析错误: ${e}</div>\n      <div class="text-gray-600 text-sm">原始代码:</div>\n    `,s.appendChild(t),Ve(l,s,"error")}})).apply(this,arguments)}function Ve(e,t,a){e.innerHTML="",e.className=`code-result border-t border-gray-200 dark:border-gray-600 p-4 ${{success:"bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-200",error:"bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200",info:"bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200"}[a]}`,"string"==typeof t?e.innerHTML=`<pre class="whitespace-pre-wrap font-mono text-sm">${t}</pre>`:e.appendChild(t)}Te.textContent="\n  /* 简洁的Markdown表格样式 */\n  .markdown-content table {\n    width: 100%;\n    border-collapse: collapse;\n    margin: 16px 0;\n    border: 1px solid #d1d5db;\n    background: white;\n  }\n\n  .markdown-content table th {\n    background: #f9fafb;\n    color: #374151;\n    padding: 12px 16px;\n    text-align: left;\n    font-weight: 600;\n    font-size: 14px;\n    border: 1px solid #d1d5db;\n    border-bottom: 2px solid #9ca3af;\n  }\n\n  .markdown-content table td {\n    padding: 12px 16px;\n    border: 1px solid #d1d5db;\n    vertical-align: top;\n    font-size: 14px;\n    line-height: 1.5;\n    max-width: 300px;\n    word-wrap: break-word;\n  }\n\n  .markdown-content table tr:nth-child(even) {\n    background-color: #f9fafb;\n  }\n\n  /* 表格内的代码块样式 */\n  .markdown-content table code {\n    background: #f3f4f6;\n    padding: 2px 6px;\n    border-radius: 3px;\n    font-family: 'Courier New', monospace;\n    font-size: 12px;\n    color: #374151;\n    border: 1px solid #e5e7eb;\n  }\n\n  .markdown-content table pre {\n    background: #f9fafb;\n    border: 1px solid #d1d5db;\n    border-radius: 4px;\n    padding: 8px;\n    margin: 4px 0;\n    overflow-x: auto;\n    font-size: 12px;\n    line-height: 1.4;\n    max-width: 280px;\n  }\n\n  .markdown-content table pre code {\n    background: transparent;\n    padding: 0;\n    border: none;\n  }\n\n  /* 响应式表格 */\n  @media (max-width: 768px) {\n    .markdown-content table {\n      font-size: 12px;\n    }\n\n    .markdown-content table th,\n    .markdown-content table td {\n      padding: 8px 12px;\n    }\n  }\n",document.head.querySelector("#markdown-table-style")||(Te.id="markdown-table-style",document.head.appendChild(Te)),window.copyCode=function(e){const t=decodeURIComponent(e.getAttribute("data-code")||"");navigator.clipboard.writeText(t).then(()=>{const t=e.innerHTML;e.innerHTML='\n      <svg class="w-3 h-3 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">\n        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>\n      </svg>\n      已复制\n    ',e.style.background="linear-gradient(135deg, #10b981, #059669)",setTimeout(()=>{e.innerHTML=t,e.style.background="linear-gradient(135deg, #3b82f6, #8b5cf6)"},2e3)}).catch(t=>{e.innerHTML="复制失败",setTimeout(()=>{e.innerHTML='\n        <svg class="w-3 h-3 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">\n          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>\n        </svg>\n        复制\n      '},2e3)})},window.runCode=function(e,t){const a=document.querySelector(`[data-code-id="${e}"]`),l=null==a?void 0:a.querySelector(".run-code-btn"),s=document.getElementById(`result_${e}`);if(!a||!l||!s)return;const n=decodeURIComponent(l.getAttribute("data-code")||""),r=l.innerHTML;l.innerHTML='\n    <svg class="w-3 h-3 mr-1 inline animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">\n      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>\n    </svg>\n    运行中...\n  ',l.disabled=!0;try{"html"===t.toLowerCase()||n.includes("<html")||n.includes("<!DOCTYPE")?function(e,t){const a=document.createElement("iframe");a.style.width="100%",a.style.height="400px",a.style.border="1px solid #e5e7eb",a.style.borderRadius="8px",a.style.background="white",a.sandbox.add("allow-scripts"),Ve(t,a,"success");let l=e.replace(/src="https:\/\/cdn\.jsdelivr\.net\/npm\/chart\.js"/g,'src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"').replace(/src="https:\/\/cdn\.jsdelivr\.net\/npm\/echarts"/g,'src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"');a.srcdoc=l}(n,s):"javascript"===t.toLowerCase()||"js"===t.toLowerCase()?function(e,t){try{const a=new Function(e)();Ve(t,`执行结果: ${JSON.stringify(a,null,2)}`,"success")}catch(a){Ve(t,`JavaScript执行错误: ${a}`,"error")}}(n,s):"python"===t.toLowerCase()||n.includes("plt.")||n.includes("matplotlib")?function(e,t){Ve(t,"Python代码执行需要后端支持，当前仅显示代码内容。","info")}(0,s):"json"===t.toLowerCase()||"echarts"===t.toLowerCase()||n.includes("option")?function(e,t){Le.apply(this,arguments)}(n,s):Ve(s,"不支持的代码类型","error"),s.classList.remove("hidden")}catch(o){Ve(s,`执行失败: ${o}`,"error"),s.classList.remove("hidden")}finally{setTimeout(()=>{l.innerHTML=r,l.disabled=!1},1e3)}},"undefined"!=typeof window&&(window.copyCode=function(e){const t=decodeURIComponent(e.dataset.code||"");navigator.clipboard?navigator.clipboard.writeText(t).then(()=>{const t=e.textContent;e.textContent="已复制!",e.classList.add("bg-green-500"),e.classList.remove("bg-blue-500"),setTimeout(()=>{e.textContent=t,e.classList.remove("bg-green-500"),e.classList.add("bg-blue-500")},2e3)}).catch(e=>{$e(t)}):$e(t)});const Oe={key:0,class:"h-full w-full flex items-center justify-center bg-white dark:bg-[#121212]"},Ae={class:"text-center"},Ee={key:1,class:"h-full w-full flex bg-white dark:bg-[#121212] overflow-hidden"},He={class:"p-4 border-b border-gray-200/80 dark:border-zinc-800/80"},Ue={key:0,class:"flex items-center justify-between mb-4"},De={class:"flex items-center space-x-2"},Xe={key:1,class:"flex flex-col items-center space-y-2 py-2"},Pe={class:"flex-1 overflow-y-auto"},Ne={key:0,class:"p-4 text-center text-gray-500 dark:text-gray-400"},Re={class:"text-sm"},Je=["onClick","title"],Ke={key:0,class:"flex justify-center"},Fe={key:1,class:"flex items-start justify-between"},qe={class:"flex-1 min-w-0"},We={class:"font-medium text-sm truncate"},Ye={class:"text-xs text-gray-500 dark:text-gray-400 mt-1"},Qe={class:"flex items-center mt-2 text-xs text-gray-400"},Ze={key:0,class:"ml-2 flex items-center"},Ge={class:"p-4 border-t border-gray-200/80 dark:border-zinc-800/80"},et={class:"flex items-center space-x-3"},tt={class:"flex-1 min-w-0"},at={class:"text-sm font-medium text-gray-900 dark:text-gray-100 truncate"},lt={class:"flex-1 flex flex-col min-w-0 overflow-hidden transition-all duration-300",style:{width:"100% !important","max-width":"none !important"}},st={class:"bg-white/95 dark:bg-[#1e1e1e]/95 backdrop-blur-xl border-b border-gray-200/80 dark:border-zinc-800/80 p-3 shadow-sm flex-shrink-0 transition-all duration-300"},nt={class:"flex items-center justify-between"},rt={class:"flex items-center space-x-4"},ot={class:"text-xl font-bold text-slate-800 dark:text-slate-200"},it={style:{float:"right",color:"#8492a6","font-size":"13px"}},dt={key:0,class:"flex items-center space-x-2"},ut={class:"flex-1 flex overflow-hidden",style:{width:"100% !important","max-width":"none !important"}},ct={class:"flex-1 flex flex-col min-w-0 overflow-hidden message-container relative",style:{width:"100% !important","max-width":"none !important"}},vt={key:0,class:"absolute top-4 left-4 z-50"},mt={key:0,class:"h-full flex items-center justify-center"},gt={class:"text-center"},pt={key:1,class:"h-full flex items-center justify-center"},ft={class:"text-center"},ht={key:2,class:"flex-shrink-0"},xt={class:"messages-list"},yt={key:0,class:"flex space-x-4 w-full"},bt={class:"w-10 h-10 bg-zinc-800 rounded-xl flex items-center justify-center flex-shrink-0 shadow-lg"},kt={class:"flex-1 min-w-0"},wt={key:0,class:"flex items-center space-x-2 text-gray-600 dark:text-gray-400"},_t={key:1},St={key:0,class:"flex items-start space-x-3"},Ct={class:"flex-1"},It={class:"text-red-700 dark:text-red-300 text-sm leading-relaxed"},jt=["innerHTML"],zt={key:0,class:"mt-4"},Mt={key:2,class:"mt-4 pt-4 border-t border-gray-200 dark:border-dark-700"},Tt=["onClick"],Bt={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},$t={class:"flex items-center space-x-2"},Lt={class:"text-xs text-gray-500"},Vt={class:"mt-3 space-y-2 animate-fade-in"},Ot={class:"flex items-center justify-between mb-2"},At={class:"font-medium text-gray-900 dark:text-gray-100 text-sm"},Et={class:"text-xs text-gray-500 bg-gray-200 dark:bg-dark-600 px-2 py-1 rounded"},Ht={class:"text-gray-600 dark:text-gray-400 text-xs leading-relaxed"},Ut=["onClick"],Dt={class:"text-blue-600 dark:text-blue-400 text-xs font-medium"},Xt=["onClick"],Pt={key:2,class:"p-2 bg-orange-50 dark:bg-orange-900/20 rounded text-sm text-center border border-dashed border-orange-300 dark:border-orange-700"},Nt={class:"text-orange-600 dark:text-orange-400 text-xs"},Rt={class:"opacity-75"},Jt={class:"flex items-center space-x-2 mt-2 opacity-100 transition-opacity duration-200"},Kt={class:"text-xs text-gray-500"},Ft={key:1,class:"flex space-x-4 w-full justify-end"},qt={class:"max-w-3xl"},Wt={class:"bg-emerald-600 text-white rounded-2xl p-5 shadow-lg"},Yt={class:"whitespace-pre-wrap break-words text-sm leading-relaxed"},Qt={class:"flex items-center justify-end space-x-2 mt-2 opacity-100 transition-opacity duration-200"},Zt={class:"text-xs text-gray-500"},Gt={class:"w-10 h-10 bg-zinc-800 rounded-xl flex items-center justify-center flex-shrink-0 shadow-lg"},ea={class:"text-emerald-400 font-bold text-sm"},ta={key:0,class:"text-center py-4"},aa={key:1,class:"mt-6"},la={class:"flex flex-wrap gap-2"},sa={key:1,class:"bg-white/90 dark:bg-gray-900/90 backdrop-blur-xl border-t border-gray-200/50 dark:border-gray-700/50 px-4 py-3 shadow-lg flex-shrink-0"},na={class:"w-full"},ra={class:"flex items-start space-x-3"},oa={class:"flex-1"},ia={class:"flex items-center justify-between mt-2 text-xs text-gray-500"},da={class:"flex items-center space-x-4"},ua={key:0},ca={class:"flex items-center space-x-2"},va={key:0,class:"mt-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700"},ma={class:"flex items-center justify-between mb-2"},ga={class:"space-y-3"},pa={class:"flex items-center space-x-3"},fa={class:"text-xs font-medium text-blue-600 dark:text-blue-400"},ha={class:"space-y-2"},xa={class:"flex items-center space-x-3"},ya={class:"flex-1"},ba={class:"flex items-center space-x-2"},ka={class:"text-xs text-gray-500 dark:text-gray-400"},wa={key:1,class:"fixed right-0 top-0 w-80 h-screen bg-white dark:bg-dark-800 border-l border-gray-200 dark:border-dark-700 flex flex-col shadow-2xl z-[9999] transform transition-transform duration-300"},_a={class:"p-4 border-b border-gray-200 dark:border-dark-700"},Sa={class:"flex items-center justify-between"},Ca={class:"flex-1 overflow-hidden flex flex-col"},Ia={class:"flex-1 overflow-hidden p-4 pb-0"},ja={class:"h-full overflow-y-auto border border-gray-200/30 dark:border-gray-700/30 rounded-xl bg-gray-50/50 dark:bg-gray-800/50 p-2"},za={class:"space-y-2"},Ma={class:"flex items-center space-x-2"},Ta={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},Ba={class:"flex-shrink-0 border-t border-gray-200/30 dark:border-gray-700/30 p-4"},$a={class:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2"},La={key:0,class:"text-xs text-blue-500 ml-2"},Va={class:"text-xs text-gray-500 mt-2 space-y-2"},Oa={key:0},Aa={key:1,class:"text-orange-500"},Ea={class:"mt-4 pt-4 border-t border-gray-200/30 dark:border-gray-700/30"},Ha={class:"mb-3"},Ua={key:0},Da={class:"text-xs text-gray-500 mb-2"},Xa={key:1,class:"text-xs text-gray-500 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg"},Pa={class:"mb-1"},Na={class:"text-xs text-gray-500 mt-2 space-y-1"},Ra={key:0},Ja={key:1,class:"text-orange-600 dark:text-orange-400"},Ka={style:{float:"right",color:"#8492a6","font-size":"13px"}},Fa={class:"flex items-center"},qa={class:"dialog-footer"};var Wa=we(ge({__name:"Chat",setup(w){const N=K(),F=ye(),ge=ze(),be=Ie(),we=_e(),Me=ke(),{availableModels:Te,initializeData:Le}=function(){const t=je(),a=ke(),l=re(()=>{const e=a.userSettings.providerPreferences||{};if(0===Object.keys(e).length){const e={};return t.aiProviders.forEach(t=>{e[t.id]={enabled:!0,useSystemKey:!0,selectedModel:null}}),t.getAvailableModels(e)}return t.getAvailableModels(e)}),s=e=>l.value.some(t=>t.id===e),n=(r=e(function*(){yield t.initializeData(),yield a.fetchUserSettings()}),function(){return r.apply(this,arguments)});var r;return{availableModels:l,getModelsByProvider:e=>l.value.filter(t=>t.provider_id===e),isModelAvailable:s,getDefaultModel:()=>{const e=a.userSettings.defaultModel;return e&&s(e)?t.getModelById(e):l.value[0]||null},initializeData:n}}(),Ve=se(!1),Wa=se(""),Ya=re(()=>ge.currentSession),Qa=se(localStorage.getItem("selectedModelId")||""),Za=se(""),Ga=se(!1),el=se(!1),tl=se(null),al=se(new Set),ll=se(!1),sl=se(!1),nl=se(!1),rl=se(!1),ol=se(parseInt(localStorage.getItem("contextLength")||"10")),il=se(10),dl=se(!1),ul=se(!1),cl=se(),vl=se(),ml=se(50),gl=se(!0);let pl=null;(()=>{const e=localStorage.getItem("selectedKnowledgeBases");if(e)try{const t=JSON.parse(e),a=[...new Set(t)];a.length!==t.length&&localStorage.setItem("selectedKnowledgeBases",JSON.stringify(a))}catch(t){localStorage.removeItem("selectedKnowledgeBases")}})();const fl=se([...new Set(JSON.parse(localStorage.getItem("selectedKnowledgeBases")||"[]"))]),hl=se(parseFloat(localStorage.getItem("relevanceThreshold")||"0.4")),xl=se(parseInt(localStorage.getItem("maxSources")||"5")),yl=se(localStorage.getItem("sourceMode")||"limited"),bl=se(10),kl=se(6),wl=se(new Set),_l=se(new Set),Sl=se(["请详细解释一下","有什么相关的例子吗？","这个有什么优缺点？","如何实际应用？","还有其他方法吗？","请用Markdown表格形式重新输出"]),Cl=se({title:"",modelId:0,knowledgeBaseId:0,contextLength:10}),Il={modelId:[{required:!0,message:"请选择AI模型",trigger:"change"}]},jl=re(()=>ge.sessions),zl=re(()=>ge.messages),Ml=re(()=>Te.value.map(e=>{var t,a;return{id:e.id,name:e.model_name,display_name:e.display_name,modelName:e.model_name,provider:(null===(t=e.provider)||void 0===t?void 0:t.display_name)||(null===(a=e.provider)||void 0===a?void 0:a.name)||"Unknown",max_tokens:e.max_tokens,is_active:e.is_active}})),Tl=re(()=>be.knowledgeBases),Bl=re(()=>{if(!Wa.value)return jl.value;const e=Wa.value.toLowerCase();return jl.value.filter(t=>t.title.toLowerCase().includes(e))}),$l=re(()=>Ya.value&&zl.value[Ya.value.id]||[]),Ll=re(()=>{const e=$l.value;if(e.length<=50)return e;const t=Math.max(0,e.length-ml.value);return e.slice(t)}),Vl=re(()=>$l.value.length),Ol=re(()=>{const e=$l.value.map(e=>e.content).join(" ");return Math.ceil(e.length/4)}),Al=re(()=>{if(!Ya.value)return 0;const e=Ml.value.find(e=>e.id.toString()===Qa.value);return e?e.max_tokens||"无限制":8e3});re(()=>{const e=Ml.value.find(e=>e.id.toString()===Qa.value);return e?{name:e.name,provider:e.provider,maxTokens:e.max_tokens,isActive:e.is_active,tokenDisplay:e.max_tokens?`${e.max_tokens} tokens`:"无限制"}:null});const El=e=>wl.value.has(e),Hl=e=>{_l.value.has(e)?_l.value.delete(e):_l.value.add(e)},Ul=e=>_l.value.has(e),Dl=e=>{const t=new Date(e),a=(new Date).getTime()-t.getTime(),l=Math.floor(a/6e4),s=Math.floor(a/36e5),n=Math.floor(a/864e5);return l<60?`${l} 分钟前`:s<24?`${s} 小时前`:`${n} 天前`},Xl=e=>new Date(e).toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"}),Pl=(Nl=e(function*(e){const t=yield ge.setCurrentSession(e.id);t.success?(localStorage.setItem("currentSessionId",e.id.toString()),Bs=0,ml.value=50,yield pe(),Ms(!0)):u.error(t.message||"切换会话失败")}),function(e){return Nl.apply(this,arguments)});var Nl;const Rl=(Jl=e(function*(e,t){switch(e){case"rename":yield Kl(t);break;case"export":yield ql(t);break;case"delete":yield Yl(t)}}),function(e,t){return Jl.apply(this,arguments)});var Jl;const Kl=(Fl=e(function*(e){try{const{value:t}=yield d.prompt("请输入新的对话标题","重命名对话",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:e.title,inputValidator:e=>!(!e||0===e.trim().length)||"标题不能为空"}),a=yield ge.updateSessionTitle(e.id,t.trim());a.success?u.success("重命名成功"):u.error(a.message||"重命名失败")}catch(t){}}),function(e){return Fl.apply(this,arguments)});var Fl;const ql=(Wl=e(function*(e){try{const t=new Blob([JSON.stringify({title:e.title,createdAt:e.created_at,messages:zl.value[e.id]||[]},null,2)],{type:"application/json"}),a=URL.createObjectURL(t),l=document.createElement("a");l.href=a,l.download=`${e.title}.json`,l.click(),URL.revokeObjectURL(a),u.success("导出成功")}catch(t){u.error("导出失败")}}),function(e){return Wl.apply(this,arguments)});var Wl;const Yl=(Ql=e(function*(e){try{yield d.confirm(`确定要删除对话"${e.title}"吗？此操作不可恢复。`,"确认删除",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning",confirmButtonClass:"el-button--danger"});const t=yield ge.deleteSession(e.id);t.success?(u.success("对话删除成功"),localStorage.getItem("currentSessionId")===e.id.toString()&&localStorage.removeItem("currentSessionId")):u.error(t.message||"删除失败")}catch(t){}}),function(e){return Ql.apply(this,arguments)});var Ql;const Zl=(Gl=e(function*(){if(vl.value)try{yield vl.value.validate(),ll.value=!0;const e={title:Cl.value.title||`新对话 ${jl.value.length+1}`},t=yield ge.createSession(e);t.success?(nl.value=!1,Cl.value.knowledgeBaseId&&Cl.value.knowledgeBaseId>0&&(fl.value.includes(Cl.value.knowledgeBaseId)||(fl.value.push(Cl.value.knowledgeBaseId),localStorage.setItem("selectedKnowledgeBases",JSON.stringify(fl.value)))),es(),u.success("新对话创建成功"),t.data&&t.data.id&&localStorage.setItem("currentSessionId",t.data.id.toString())):u.error(t.message||"创建失败")}catch(e){u.error("创建失败")}finally{ll.value=!1}}),function(){return Gl.apply(this,arguments)});var Gl;const es=()=>{let e=0;Qa.value&&parseInt(Qa.value)?e=parseInt(Qa.value):Ml.value.length>0&&(e=Ml.value[0].id),Cl.value={title:"",modelId:e,knowledgeBaseId:0,contextLength:10},vl.value&&vl.value.resetFields()},ts=e=>{dl.value||(ol.value=e)},as=()=>{dl.value=!0,ol.value=-1,il.value=Vl.value||10},ls=()=>{dl.value=!1,ol.value=Math.min(Vl.value||10,10),il.value=ol.value},ss=(ns=e(function*(){if(!Za.value.trim()||!Ya.value||el.value)return;const e=Za.value.trim();Za.value="";try{const a=Ml.value.find(e=>e.id.toString()===Qa.value);if(!a)return u.error("请选择一个AI模型"),void(Za.value=e);if(!a.is_active){const t=Ml.value.find(e=>e.is_active);if(!t)return u.error("没有可用的AI模型，请联系管理员"),void(Za.value=e);ks(t.id.toString()),u.warning(`当前模型已被禁用，已自动切换到 ${t.name}`)}if(a.max_tokens&&"number"==typeof a.max_tokens){const t=Math.ceil(e.length/4)+Ol.value;if(t>a.max_tokens)return u.warning(`消息过长，预估token数(${t})超过模型限制(${a.max_tokens})，请缩短消息内容`),void(Za.value=e)}el.value=!0,Ga.value=!0,tl.value=new AbortController;const l=t({message:e,model_id:a.id,knowledge_base_ids:fl.value,history_limit:-1===ol.value?999999:ol.value,relevance_threshold:hl.value},"limited"===yl.value?{max_sources:xl.value}:{}),s={id:Date.now(),session_id:Ya.value.id,role:"user",content:e,created_at:(new Date).toISOString()};zl.value[Ya.value.id]||(zl.value[Ya.value.id]=[]),zl.value[Ya.value.id].push(s);const n={id:Date.now()+1,session_id:Ya.value.id,role:"assistant",content:"",created_at:(new Date).toISOString()};zl.value[Ya.value.id].push(n),al.value.add(n.id.toString()),yield Se.streamChat(Ya.value.id,l,e=>{if(Ya.value){const t=zl.value[Ya.value.id].findIndex(e=>e.id===n.id);-1!==t&&(zl.value[Ya.value.id][t].content+=e)}},e=>{if(Ya.value){const t=zl.value[Ya.value.id];if(e.user_message){const a=t.findIndex(e=>e.id===s.id);-1!==a&&(t[a]=e.user_message)}const a=e.ai_message||e,l=t.findIndex(e=>e.id===n.id);-1!==l&&(t[l]=a)}setTimeout(()=>{al.value.delete(n.id.toString())},100),pe(()=>{}),pe(()=>{Ms()})},a=>{if(Ya.value){const e=zl.value[Ya.value.id].findIndex(e=>e.id===n.id);-1!==e&&(zl.value[Ya.value.id][e]=t(t({},n),{},{content:a,role:"assistant",isError:!0}))}al.value.delete(n.id.toString()),u.error(a||"发送失败"),Za.value=e},tl.value)}catch(a){u.error("发送失败，请重试"),Za.value=e}finally{el.value=!1,Ga.value=!1,tl.value=null}}),function(){return ns.apply(this,arguments)});var ns;const rs=e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),ss())},os=()=>{Ga.value?is():ss()},is=()=>{tl.value&&(tl.value.abort(),tl.value=null),al.value.clear(),Ga.value=!1,el.value=!1,u.info("已停止生成，保留当前内容")},ds=(us=e(function*(e){try{!function(e){navigator.clipboard?navigator.clipboard.writeText(e).then(()=>{}).catch(()=>{$e(e)}):$e(e)}(e),u.success("已复制到剪贴板")}catch(t){u.error("复制失败")}}),function(e){return us.apply(this,arguments)});var us;const cs=(vs=e(function*(e){if(Ya.value&&"assistant"===e.role)try{Ga.value=!0;const a=Ml.value.find(e=>e.id.toString()===Qa.value);if(!a)return void u.error("请选择一个AI模型");tl.value=new AbortController;const l=zl.value[Ya.value.id],s=l.findIndex(t=>t.id===e.id);-1!==s&&(l[s].content=""),al.value.add(e.id.toString()),yield Se.streamRegenerateMessage(t({message_id:e.id,model_id:a.id,knowledge_base_ids:fl.value,history_limit:-1===ol.value?999999:ol.value,relevance_threshold:hl.value},"limited"===yl.value?{max_sources:xl.value}:{}),e=>{-1!==s&&(l[s].content+=e)},t=>{-1!==s&&(l[s]=t),setTimeout(()=>{al.value.delete(e.id.toString())},100),pe(()=>{}),u.success("消息已重新生成")},t=>{al.value.delete(e.id.toString()),u.error(t||"重新生成失败")},tl.value)}catch(l){var a;u.error((null===(a=l.response)||void 0===a||null===(a=a.data)||void 0===a?void 0:a.message)||"重新生成失败")}finally{Ga.value=!1,tl.value=null}}),function(e){return vs.apply(this,arguments)});var vs;const ms=(gs=e(function*(e){if(Ya.value&&"user"===e.role)try{Za.value=e.content,yield ss()}catch(t){u.error("重新发送失败")}}),function(e){return gs.apply(this,arguments)});var gs;const ps=(fs=e(function*(e){try{if(yield d.confirm("确定要删除这条消息吗？此操作不可恢复。","确认删除",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning",confirmButtonClass:"el-button--danger"}),yield Se.deleteMessage(e.id),Ya.value){const t=zl.value[Ya.value.id];if(t){const a=t.findIndex(t=>t.id===e.id);-1!==a&&(t.splice(a,1),yield pe(),u.success("消息删除成功"))}}}catch(a){var t;"cancel"!==a&&u.error((null===(t=a.response)||void 0===t||null===(t=t.data)||void 0===t?void 0:t.message)||"删除失败")}}),function(e){return fs.apply(this,arguments)});var fs;const hs=(xs=e(function*(e){try{const{value:t}=yield d.prompt("编辑消息内容","编辑消息",{confirmButtonText:"确定",cancelButtonText:"取消",inputType:"textarea",inputValue:e.content});if(Ya.value){const a=zl.value[Ya.value.id],l=a.findIndex(t=>t.id===e.id);-1!==l&&(a[l].content=t,u.success("消息编辑成功"))}}catch(t){}}),function(e){return xs.apply(this,arguments)});var xs;const ys=(bs=e(function*(e){Za.value=e,yield ss()}),function(e){return bs.apply(this,arguments)});var bs;const ks=e=>{Qa.value=e,localStorage.setItem("selectedModelId",e);const t=Ml.value.find(t=>t.id.toString()===e);t&&u.success(`已切换到 ${t.name}（${t.max_tokens?`${t.max_tokens} tokens`:"无限制"}，${t.is_active?"可用":"已禁用"}）`)},ws=()=>{Ya.value&&ql(Ya.value)},_s=(Ss=e(function*(){try{if(yield d.confirm("确定要清空当前对话的上下文吗？这将删除所有消息记录。","清空上下文",{confirmButtonText:"清空",cancelButtonText:"取消",type:"warning"}),Ya.value)try{yield Se.clearSession(Ya.value.id),zl.value[Ya.value.id]=[],u.success("上下文已清空")}catch(t){var e;u.error((null===(e=t.response)||void 0===e||null===(e=e.data)||void 0===e?void 0:e.message)||"清空失败，请重试")}}catch(t){}}),function(){return Ss.apply(this,arguments)});var Ss;const Cs=()=>{u.info("文件附加功能开发中...")},Is=e=>{if(!Ya.value)return!1;const t=(zl.value[Ya.value.id]||[]).filter(e=>"assistant"===e.role);return t.length>0&&t[t.length-1].id===e.id},js=(e,t,a=!1)=>{const l=[],s=[];return l.length>0?{showHtml:!0,showCharts:!1,htmlBlocks:l,chartBlocks:[]}:l.length>0&&s.length>0?{showHtml:!0,showCharts:!0,htmlBlocks:l,chartBlocks:s}:{showHtml:l.length>0,showCharts:s.length>0,htmlBlocks:l,chartBlocks:s}},zs=()=>{we.toggleFocusMode()},Ms=(e=!1)=>{if(cl.value){const t=cl.value,a=()=>{e?(t.style.scrollBehavior="auto",t.scrollTop=t.scrollHeight,setTimeout(()=>{t.style.scrollBehavior="smooth"},0)):(t.style.scrollBehavior="smooth",t.scrollTop=t.scrollHeight)};a(),e&&(setTimeout(a,0),setTimeout(a,10),setTimeout(a,50),setTimeout(a,100))}},Ts=()=>{const e=Math.min(ml.value+50,$l.value.length);ml.value=e};te(()=>N.query,e=>{if(e.session){const t=Number(e.session),a=jl.value.find(e=>e.id===t);a&&Pl(a)}if(e.kb){const t=Number(e.kb);fl.value.includes(t)||fl.value.push(t)}},{immediate:!0});let Bs=0;var $s,Ls;te(()=>Ya.value?zl.value[Ya.value.id]:[],($s=e(function*(e){if(Ya.value&&e){const t=e.length;t>Bs&&(t>ml.value&&(ml.value=Math.max(50,t)),yield pe(),Ms()),Bs=t}}),function(e){return $s.apply(this,arguments)}),{deep:!0}),te(()=>N.path,(Ls=e(function*(e){"/user/chat"===e&&Ya.value&&(yield pe(),Ms(!0))}),function(e){return Ls.apply(this,arguments)})),te(ol,e=>{localStorage.setItem("contextLength",e.toString())}),te(hl,e=>{localStorage.setItem("relevanceThreshold",e.toString())}),te(xl,e=>{localStorage.setItem("maxSources",e.toString())}),te(yl,e=>{localStorage.setItem("sourceMode",e)});const Vs=(Os=e(function*(){try{yield Le(),yield ge.fetchSessions(),yield be.fetchKnowledgeBases(),yield pe();let e=null;const t=localStorage.getItem("selectedModelId");t&&(e=Ml.value.find(e=>e.id.toString()===t)),!e&&Me.userSettings.defaultModel&&(e=Ml.value.find(e=>e.id===Me.userSettings.defaultModel)),!e&&Ml.value.length>0&&(e=Ml.value[0]),e&&(Qa.value=e.id.toString(),localStorage.setItem("selectedModelId",Qa.value),Cl.value.modelId=parseInt(Qa.value))}catch(e){}}),function(){return Os.apply(this,arguments)});var Os;return fe(e(function*(){pe(()=>{cl.value&&(cl.value.style.scrollBehavior="auto",cl.value.scrollTop=cl.value.scrollHeight,pl=new MutationObserver(()=>{gl.value&&cl.value&&(cl.value.scrollTop=cl.value.scrollHeight)}),pl.observe(cl.value,{childList:!0,subtree:!0,attributes:!1}))}),Vs().then(()=>{if(-1===ol.value?(dl.value=!0,il.value=Vl.value||10):(dl.value=!1,il.value=ol.value),jl.value.length>0&&!Ya.value){const e=localStorage.getItem("currentSessionId");let t=null;e&&(t=jl.value.find(t=>t.id.toString()===e)),t||(t=jl.value[0]),t&&(ge.setCurrentSession(t.id),localStorage.setItem("currentSessionId",t.id.toString()))}pe().then(()=>{gl.value=!1,pl&&(pl.disconnect(),pl=null),pe().then(()=>{Ms(!0),requestAnimationFrame(()=>{cl.value&&(cl.value.style.scrollBehavior="smooth")})})})}).catch(e=>{})})),Z(()=>{if(gl.value&&cl.value&&Ya.value){const e=cl.value;e.scrollHeight>e.clientHeight&&(e.style.scrollBehavior="auto",e.scrollTop=e.scrollHeight)}}),(e,t)=>{var d,u,w,N;const K=i,Z=n,te=o,se=x,re=y,ge=h,pe=r,fe=v,ye=m,be=Ce,ke=c,_e=g,Se=s,Ie=a,je=l,ze=f,Me=k,Te=p,$e=b;return Ve.value?(G(),ue("div",Oe,[oe("div",Ae,[me(K,{size:48,class:"text-emerald-500 animate-spin mb-4"},{default:ae(()=>[me(ne(j))]),_:1}),t[25]||(t[25]=oe("p",{class:"text-gray-600 dark:text-gray-400"},"正在加载聊天数据...",-1))])])):(G(),ue("div",Ee,[ne(we).focusMode?de("",!0):(G(),ue("div",{key:0,class:he(["flex-shrink-0 bg-white/95 dark:bg-[#1e1e1e]/95 backdrop-blur-xl border-r border-gray-200/80 dark:border-zinc-800/80 flex flex-col shadow-2xl h-full transition-all duration-300",ul.value?"w-16":"w-64"])},[oe("div",He,[ul.value?(G(),ue("div",Xe,[me(Z,{type:"primary",size:"small",onClick:t[2]||(t[2]=e=>nl.value=!0),class:"tech-button w-10 h-10 p-0 flex items-center justify-center",title:"新建对话"},{default:ae(()=>[me(K,{size:16},{default:ae(()=>[me(ne(B))]),_:1})]),_:1}),me(Z,{onClick:t[3]||(t[3]=e=>ul.value=!ul.value),size:"small",text:"",title:"展开侧边栏",class:"text-gray-500 hover:text-blue-600 transition-colors w-10 h-10 p-0 flex items-center justify-center rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"},{default:ae(()=>[me(K,{size:16},{default:ae(()=>[me(ne(O))]),_:1})]),_:1})])):(G(),ue("div",Ue,[t[27]||(t[27]=oe("h2",{class:"text-xl font-bold text-slate-800 dark:text-slate-200"}," 对话历史 ",-1)),oe("div",De,[me(Z,{size:"small",onClick:t[0]||(t[0]=e=>nl.value=!0),class:"tech-button"},{default:ae(()=>[me(K,{class:"mr-1"},{default:ae(()=>[me(ne(B))]),_:1}),t[26]||(t[26]=ve(" 新建对话 ",-1))]),_:1,__:[26]}),me(Z,{onClick:t[1]||(t[1]=e=>ul.value=!ul.value),size:"small",text:"",title:"折叠侧边栏",class:"text-gray-500 hover:text-blue-600 transition-colors"},{default:ae(()=>[me(K,null,{default:ae(()=>[me(ne(V))]),_:1})]),_:1})])])),ul.value?de("",!0):(G(),ie(te,{key:2,modelValue:Wa.value,"onUpdate:modelValue":t[4]||(t[4]=e=>Wa.value=e),placeholder:"搜索对话...",size:"small",clearable:"",class:"search-input"},{prefix:ae(()=>[me(K,null,{default:ae(()=>[me(ne(R))]),_:1})]),_:1},8,["modelValue"]))]),oe("div",Pe,[0===Bl.value.length?(G(),ue("div",Ne,[me(K,{size:32,class:"mb-2"},{default:ae(()=>[me(ne(H))]),_:1}),oe("p",Re,xe(Wa.value?"未找到匹配的对话":"还没有对话记录"),1)])):(G(),ue("div",{key:1,class:he(ul.value?"p-1 space-y-2":"p-3 space-y-3")},[(G(!0),ue(Q,null,ee(Bl.value,e=>{var a;return G(),ue("div",{key:e.id,class:he(["cursor-pointer transition-all duration-300 group backdrop-blur-sm border shadow-sm hover:shadow-md",ul.value?"p-2 rounded-lg mx-1":"p-4 rounded-xl bg-white dark:bg-[#2a2a2a] border-gray-200/50 dark:border-zinc-800",(null===(a=Ya.value)||void 0===a?void 0:a.id)===e.id?"bg-emerald-500/10 border-emerald-500/50 shadow-lg ring-2 ring-emerald-500/20":"hover:bg-gray-50 dark:hover:bg-zinc-800 hover:border-gray-300/50 dark:hover:border-zinc-700"]),onClick:t=>Pl(e),title:ul.value?e.title:""},[ul.value?(G(),ue("div",Ke,[me(K,{size:16,class:"text-emerald-500"},{default:ae(()=>[me(ne(H))]),_:1})])):(G(),ue("div",Fe,[oe("div",qe,[oe("h3",We,xe(e.title),1),oe("p",Ye,xe(Dl(e.updated_at)),1),oe("div",Qe,[me(K,{class:"mr-1"},{default:ae(()=>[me(ne(E))]),_:1}),t[28]||(t[28]=ve(" AI助手 ",-1)),fl.value.length>0?(G(),ue("span",Ze,[me(K,{class:"mr-1"},{default:ae(()=>[me(ne(D))]),_:1}),ve(" "+xe(fl.value.length),1)])):de("",!0)])]),me(ge,{onCommand:t=>Rl(t,e),trigger:"click"},{dropdown:ae(()=>[me(re,null,{default:ae(()=>[me(se,{command:"rename"},{default:ae(()=>[me(K,null,{default:ae(()=>[me(ne(C))]),_:1}),t[29]||(t[29]=ve(" 重命名 ",-1))]),_:1,__:[29]}),me(se,{command:"export"},{default:ae(()=>[me(K,null,{default:ae(()=>[me(ne(S))]),_:1}),t[30]||(t[30]=ve(" 导出 ",-1))]),_:1,__:[30]}),me(se,{command:"delete",divided:""},{default:ae(()=>[me(K,null,{default:ae(()=>[me(ne(P))]),_:1}),t[31]||(t[31]=ve(" 删除 ",-1))]),_:1,__:[31]})]),_:1})]),default:ae(()=>[me(Z,{circle:"",size:"small",class:"opacity-0 group-hover:opacity-100"},{default:ae(()=>[me(K,null,{default:ae(()=>[me(ne(M))]),_:1})]),_:1})]),_:2},1032,["onCommand"])]))],10,Je)}),128))],2))]),oe("div",Ge,[oe("div",et,[me(pe,{size:32,src:null===(d=ne(F).user)||void 0===d?void 0:d.avatar_url},{default:ae(()=>{var e;return[ve(xe(null===(e=ne(F).user)||void 0===e||null===(e=e.username)||void 0===e?void 0:e.charAt(0).toUpperCase()),1)]}),_:1},8,["src"]),oe("div",tt,[oe("p",at,xe((null===(u=ne(F).user)||void 0===u?void 0:u.display_name)||(null===(w=ne(F).user)||void 0===w?void 0:w.username)),1),t[32]||(t[32]=oe("p",{class:"text-xs text-gray-500 dark:text-gray-400"}," 在线 ",-1))]),me(Z,{circle:"",size:"small",onClick:t[5]||(t[5]=t=>e.$router.push("/user/settings"))},{default:ae(()=>[me(K,null,{default:ae(()=>[me(ne(J))]),_:1})]),_:1})])])],2)),oe("div",lt,[oe("div",st,[oe("div",nt,[oe("div",rt,[oe("h1",ot,xe((null===(N=Ya.value)||void 0===N?void 0:N.title)||"选择或创建对话"),1),Ya.value?(G(),ie(ye,{key:0,modelValue:Qa.value,"onUpdate:modelValue":t[6]||(t[6]=e=>Qa.value=e),placeholder:"选择AI模型",size:"small",style:{width:"180px"},onChange:ks,class:"model-select"},{default:ae(()=>[(G(!0),ue(Q,null,ee(Ml.value,e=>(G(),ie(fe,{key:e.id,label:`${e.display_name||e.name} (${e.provider})`,value:e.id.toString()},{default:ae(()=>[oe("span",null,xe(e.display_name||e.name),1),oe("span",it,xe(e.provider),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])):de("",!0)]),Ya.value?(G(),ue("div",dt,[me(Z,{size:"small",onClick:zs,type:ne(we).focusMode?"primary":"default",class:"tech-button",title:ne(we).focusMode?"退出专注模式":"进入专注模式"},{default:ae(()=>[me(K,{class:"mr-1"},{default:ae(()=>[me(ne(I))]),_:1}),ve(" "+xe(ne(we).focusMode?"退出专注":"专注模式"),1)]),_:1},8,["type","title"]),me(Z,{size:"small",onClick:t[7]||(t[7]=e=>sl.value=!sl.value),class:"tech-button",type:sl.value?"primary":"default"},{default:ae(()=>[me(K,{class:"mr-1"},{default:ae(()=>[me(ne(D))]),_:1}),ve(" 知识库 ("+xe(fl.value.length)+") ",1)]),_:1},8,["type"]),me(Z,{size:"small",onClick:ws,class:"tech-button"},{default:ae(()=>[me(K,{class:"mr-1"},{default:ae(()=>[me(ne(S))]),_:1}),t[33]||(t[33]=ve(" 导出 ",-1))]),_:1,__:[33]}),me(Z,{size:"small",onClick:_s,class:"tech-button-warning"},{default:ae(()=>[me(K,{class:"mr-1"},{default:ae(()=>[me(ne(_))]),_:1}),t[34]||(t[34]=ve(" 清空上下文 ",-1))]),_:1,__:[34]})])):de("",!0)])]),oe("div",ut,[oe("div",ct,[ne(we).focusMode?(G(),ue("div",vt,[me(Z,{onClick:zs,type:"primary",size:"small",circle:"",class:"shadow-lg hover:shadow-xl transition-all duration-200",title:"退出专注模式"},{default:ae(()=>[me(K,null,{default:ae(()=>[me(ne(U))]),_:1})]),_:1})])):de("",!0),oe("div",{ref_key:"messagesContainer",ref:cl,class:he(["flex-1 overflow-y-auto px-4 py-4 space-y-6 min-h-0",{"messages-loading":gl.value}]),style:{"scroll-behavior":"auto","overflow-anchor":"none"}},[oe("div",{class:he(["w-full messages-container-initial",{loaded:!gl.value}])},[Ya.value?0===$l.value.length?(G(),ue("div",pt,[oe("div",ft,[me(K,{size:48,class:"text-gray-400 mb-4"},{default:ae(()=>[me(ne(z))]),_:1}),t[38]||(t[38]=oe("p",{class:"text-gray-600 dark:text-gray-400"}," 输入您的问题或想法... ",-1))])])):(G(),ue("div",ht,[oe("div",xt,[(G(!0),ue(Q,null,ee(Ll.value,e=>{var a,l,s;return G(),ue("div",{key:e.id,class:he(["flex w-full group message-item","user"===e.role?"justify-end":"justify-start"])},["assistant"===e.role?(G(),ue("div",yt,[oe("div",bt,[me(K,{size:18,class:"text-emerald-400"},{default:ae(()=>[me(ne(H))]),_:1})]),oe("div",kt,[oe("div",{class:he(["backdrop-blur-xl rounded-2xl p-6 shadow-lg border ai-message-content",e.isError?"bg-red-50/90 dark:bg-red-900/20 border-red-200/50 dark:border-red-700/50 overflow-hidden":"bg-white dark:bg-[#2a2a2a] border-gray-200/30 dark:border-zinc-800 overflow-visible"])},[!e.content.trim()&&(Ga.value||el.value)&&Is(e)?(G(),ue("div",wt,t[39]||(t[39]=[ce('<div class="flex space-x-1" data-v-1e891754><div class="w-2 h-2 bg-emerald-500 rounded-full animate-bounce" data-v-1e891754></div><div class="w-2 h-2 bg-emerald-500 rounded-full animate-bounce" style="animation-delay:0.1s;" data-v-1e891754></div><div class="w-2 h-2 bg-emerald-500 rounded-full animate-bounce" style="animation-delay:0.2s;" data-v-1e891754></div></div><span class="text-sm" data-v-1e891754>思考中...</span>',2)]))):(G(),ue("div",_t,[e.isError?(G(),ue("div",St,[me(K,{class:"text-red-500 mt-1 flex-shrink-0",size:"18"},{default:ae(()=>[me(ne(W))]),_:1}),oe("div",Ct,[oe("div",It,xe(e.content),1),t[40]||(t[40]=oe("div",{class:"mt-2 text-xs text-red-600 dark:text-red-400"}," 请检查模型设置或联系技术支持 ",-1))])])):(G(),ue("div",{key:1,class:"prose dark:prose-invert max-w-none markdown-content break-words overflow-hidden text-sm leading-relaxed",innerHTML:ne(Be)(e.content)},null,8,jt)),null!=e.id?(G(!0),ue(Q,{key:2},ee([js(0,0,al.value.has(e.id.toString()))],(t,a)=>(G(),ue(Q,{key:`${e.id}-strategy-${a}`},[t.showHtml&&t.htmlBlocks.length>0?(G(),ue("div",zt,[(G(!0),ue(Q,null,ee(t.htmlBlocks,(t,a)=>(G(),ie(be,{key:`${e.id}-html-${a}`,"html-content":t.content,height:"auto","min-height":"400px","max-height":"800px"},null,8,["html-content"]))),128))])):de("",!0)],64))),128)):de("",!0)])),e.sources&&e.sources.length>0?(G(),ue("div",Mt,[oe("div",{class:"flex items-center justify-between cursor-pointer hover:bg-gray-50 dark:hover:bg-dark-600 rounded p-2 -m-2 transition-colors",onClick:t=>{var a;wl.value.has(a=e.id)?wl.value.delete(a):wl.value.add(a)}},[oe("h4",Bt," 参考来源 ("+xe(e.sources.length)+"条) ",1),oe("div",$t,[oe("span",Lt,xe(El(e.id)?"收起":"展开"),1),me(K,{class:he(["text-gray-500 transition-transform duration-200",El(e.id)?"rotate-180":""]),size:14},{default:ae(()=>[me(ne(L))]),_:2},1032,["class"])])],8,Tt),le(oe("div",Vt,[(G(!0),ue(Q,null,ee((l=e.sources,s=e.id,l&&0!==l.length?Ul(s)?l.slice(0,bl.value):l.slice(0,kl.value):[]),e=>(G(),ue("div",{key:e.documentId,class:"p-3 bg-gray-50 dark:bg-dark-700 rounded text-sm border border-gray-200 dark:border-dark-600"},[oe("div",Ot,[oe("span",At," 📄 "+xe(e.documentName),1),oe("span",Et," 相关度: "+xe(Math.round(100*e.relevance))+"% ",1)]),oe("p",Ht,xe(e.excerpt),1)]))),128)),e.sources.length>kl.value&&!Ul(e.id)?(G(),ue("div",{key:0,class:"p-2 bg-gray-100 dark:bg-dark-600 rounded text-sm text-center border border-dashed border-gray-300 dark:border-dark-500 cursor-pointer hover:bg-gray-200 dark:hover:bg-dark-500 transition-colors",onClick:t=>Hl(e.id)},[oe("span",Dt," 展开更多 (还有 "+xe(e.sources.length-kl.value)+" 条来源) ",1)],8,Ut)):de("",!0),e.sources.length>kl.value&&Ul(e.id)?(G(),ue("div",{key:1,class:"p-2 bg-gray-100 dark:bg-dark-600 rounded text-sm text-center border border-dashed border-gray-300 dark:border-dark-500 cursor-pointer hover:bg-gray-200 dark:hover:bg-dark-500 transition-colors",onClick:t=>Hl(e.id)},t[41]||(t[41]=[oe("span",{class:"text-blue-600 dark:text-blue-400 text-xs font-medium"}," 收起部分来源 ",-1)]),8,Xt)):de("",!0),e.sources.length>bl.value&&Ul(e.id)?(G(),ue("div",Pt,[oe("span",Nt,[ve(" ⚠️ 来源过多，仅显示前 "+xe(bl.value)+" 条最相关的内容 ",1),t[42]||(t[42]=oe("br",null,null,-1)),oe("span",Rt,"还有 "+xe(e.sources.length-bl.value)+" 条来源未显示",1)])])):de("",!0)],512),[[Y,El(e.id)]])])):de("",!0)],2),oe("div",Jt,[me(Z,{size:"small",text:"",onClick:t=>ds(e.content),title:"复制"},{default:ae(()=>[me(K,null,{default:ae(()=>[me(ne(X))]),_:1})]),_:2},1032,["onClick"]),me(Z,{size:"small",text:"",onClick:t=>cs(e),title:"重新生成"},{default:ae(()=>[me(K,null,{default:ae(()=>[me(ne(_))]),_:1})]),_:2},1032,["onClick"]),me(Z,{size:"small",text:"",onClick:t=>ps(e),title:"删除"},{default:ae(()=>[me(K,null,{default:ae(()=>[me(ne(P))]),_:1})]),_:2},1032,["onClick"]),oe("span",Kt,xe(Xl(e.created_at)),1)])])])):(G(),ue("div",Ft,[oe("div",qt,[oe("div",Wt,[oe("div",Yt,xe(e.content),1)]),oe("div",Qt,[me(Z,{size:"small",text:"",onClick:t=>ms(e),title:"重新发送"},{default:ae(()=>[me(K,null,{default:ae(()=>[me(ne($))]),_:1})]),_:2},1032,["onClick"]),me(Z,{size:"small",text:"",onClick:t=>hs(e),title:"编辑"},{default:ae(()=>[me(K,null,{default:ae(()=>[me(ne(C))]),_:1})]),_:2},1032,["onClick"]),me(Z,{size:"small",text:"",onClick:t=>ps(e),title:"删除"},{default:ae(()=>[me(K,null,{default:ae(()=>[me(ne(P))]),_:1})]),_:2},1032,["onClick"]),oe("span",Zt,xe(Xl(e.created_at)),1)])]),oe("div",Gt,[oe("span",ea,xe(null===(a=ne(F).user)||void 0===a||null===(a=a.username)||void 0===a?void 0:a.charAt(0).toUpperCase()),1)])]))],2)}),128)),$l.value.length>Ll.value.length?(G(),ue("div",ta,[me(Z,{size:"small",type:"primary",plain:"",onClick:Ts,class:"tech-button"},{default:ae(()=>[me(K,{class:"mr-1"},{default:ae(()=>[me(ne(A))]),_:1}),ve(" 加载更多消息 ("+xe($l.value.length-Ll.value.length)+" 条) ",1)]),_:1})])):de("",!0),$l.value.length>0&&!Ga.value&&!el.value?(G(),ue("div",aa,[t[43]||(t[43]=oe("div",{class:"text-sm text-gray-600 dark:text-gray-400 mb-3"},"💡 您可能还想了解：",-1)),oe("div",la,[(G(!0),ue(Q,null,ee(Sl.value,e=>(G(),ie(Z,{key:e,size:"small",type:"primary",plain:"",onClick:t=>ys(e),class:"suggestion-button"},{default:ae(()=>[ve(xe(e),1)]),_:2},1032,["onClick"]))),128))])])):de("",!0)])])):(G(),ue("div",mt,[oe("div",gt,[me(K,{size:64,class:"text-gray-400 mb-4"},{default:ae(()=>[me(ne(H))]),_:1}),t[36]||(t[36]=oe("h3",{class:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2"}," 开始新的对话 ",-1)),t[37]||(t[37]=oe("p",{class:"text-gray-600 dark:text-gray-400 mb-6"}," 选择一个现有对话或创建新的对话来开始与AI助手交流 ",-1)),me(Z,{type:"primary",onClick:t[8]||(t[8]=e=>nl.value=!0),class:"btn-tech"},{default:ae(()=>[me(K,{class:"mr-2"},{default:ae(()=>[me(ne(B))]),_:1}),t[35]||(t[35]=ve(" 创建新对话 ",-1))]),_:1,__:[35]})])]))],2)],2),Ya.value?(G(),ue("div",sa,[oe("div",na,[oe("div",ra,[oe("div",oa,[me(te,{modelValue:Za.value,"onUpdate:modelValue":t[9]||(t[9]=e=>Za.value=e),type:"textarea",placeholder:"输入您的消息... (Shift+Enter 换行，Enter 发送)",rows:Math.min(Za.value.split("\n").length,3),"max-rows":3,resize:"none",onKeydown:rs,class:"message-input"},null,8,["modelValue","rows"]),oe("div",ia,[oe("div",da,[oe("span",null,"Token 使用: "+xe(Ol.value)+"/"+xe(Al.value),1),fl.value.length>0?(G(),ue("span",ua," 知识库: "+xe(fl.value.length)+" 个 ",1)):de("",!0),oe("span",null,"上下文: "+xe(ol.value)+"/"+xe(Vl.value),1)]),oe("div",ca,[me(Z,{size:"small",text:"",onClick:t[10]||(t[10]=e=>rl.value=!rl.value),title:"上下文设置"},{default:ae(()=>[me(K,null,{default:ae(()=>[me(ne(J))]),_:1})]),_:1}),me(Z,{size:"small",text:"",onClick:Cs},{default:ae(()=>[me(K,null,{default:ae(()=>[me(ne(T))]),_:1})]),_:1})])]),rl.value?(G(),ue("div",va,[oe("div",ma,[t[44]||(t[44]=oe("span",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"AI上下文长度设置",-1)),me(Z,{size:"small",text:"",onClick:t[11]||(t[11]=e=>rl.value=!1)},{default:ae(()=>[me(K,null,{default:ae(()=>[me(ne(U))]),_:1})]),_:1})]),oe("div",ga,[oe("div",pa,[t[45]||(t[45]=oe("span",{class:"text-xs text-gray-600 dark:text-gray-400 w-20"},"当前会话:",-1)),oe("span",fa,xe(Vl.value)+" 条消息",1)]),oe("div",ha,[oe("div",xa,[t[46]||(t[46]=oe("span",{class:"text-xs text-gray-600 dark:text-gray-400 w-20"},"AI获取:",-1)),oe("div",ya,[me(ke,{modelValue:il.value,"onUpdate:modelValue":t[12]||(t[12]=e=>il.value=e),min:1,max:Math.max(Vl.value,20),step:1,"show-input":"","show-input-controls":!1,size:"small",class:"context-slider",disabled:dl.value,onInput:ts},null,8,["modelValue","max","disabled"])]),me(_e,{modelValue:il.value,"onUpdate:modelValue":t[13]||(t[13]=e=>il.value=e),min:1,max:Math.max(Vl.value,20),size:"small",class:"w-20",disabled:dl.value,onInput:ts},null,8,["modelValue","max","disabled"])]),oe("div",ba,[me(Z,{size:"small",type:dl.value?"primary":"",onClick:as,class:"text-xs"},{default:ae(()=>t[47]||(t[47]=[ve(" 全部消息 ",-1)])),_:1,__:[47]},8,["type"]),dl.value?(G(),ie(Z,{key:0,size:"small",onClick:ls,class:"text-xs"},{default:ae(()=>t[48]||(t[48]=[ve(" 恢复限制 ",-1)])),_:1,__:[48]})):de("",!0)])]),oe("div",ka,[dl.value?(G(),ue(Q,{key:0},[ve(" AI将使用当前会话的全部 "+xe(Vl.value)+" 条消息作为上下文。 ",1)],64)):(G(),ue(Q,{key:1},[ve(" AI将使用最近 "+xe(ol.value)+" 条消息作为上下文。更多上下文可能提供更好的连贯性，但会消耗更多tokens。 ",1)],64))])])])):de("",!0)]),me(Z,{type:Ga.value?"danger":"primary",loading:el.value&&!Ga.value,disabled:!Ga.value&&(!Za.value.trim()||el.value),onClick:os,class:"send-button",size:"large",title:Ga.value?"停止生成":"发送消息"},{default:ae(()=>[Ga.value?(G(),ie(K,{key:1},{default:ae(()=>[me(ne(q))]),_:1})):(G(),ie(K,{key:0},{default:ae(()=>[me(ne($))]),_:1}))]),_:1},8,["type","loading","disabled","title"])])])])):de("",!0)]),sl.value&&Ya.value?(G(),ue("div",{key:0,class:"fixed inset-0 bg-black bg-opacity-20 z-[9998] transition-opacity duration-300",onClick:t[14]||(t[14]=e=>sl.value=!1)})):de("",!0),sl.value&&Ya.value?(G(),ue("div",wa,[oe("div",_a,[oe("div",Sa,[t[49]||(t[49]=oe("h3",{class:"font-medium text-gray-900 dark:text-gray-100"}," 知识库设置 ",-1)),me(Z,{circle:"",size:"small",onClick:t[15]||(t[15]=e=>sl.value=!1)},{default:ae(()=>[me(K,null,{default:ae(()=>[me(ne(U))]),_:1})]),_:1})])]),oe("div",Ca,[oe("div",Ia,[t[51]||(t[51]=oe("h4",{class:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2"}," 选择知识库 ",-1)),oe("div",ja,[oe("div",za,[(G(!0),ue(Q,null,ee(Tl.value,e=>(G(),ue("div",{key:e.id,class:"flex items-center justify-between p-3 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-200/30 dark:border-gray-700/30 rounded-lg shadow-sm hover:shadow-md transition-all duration-300"},[oe("div",Ma,[me(Se,{"model-value":fl.value.includes(e.id),onChange:t=>(e=>{const t=fl.value.indexOf(e);-1!==t?fl.value.splice(t,1):fl.value.push(e),fl.value=[...new Set(fl.value)],localStorage.setItem("selectedKnowledgeBases",JSON.stringify(fl.value))})(e.id)},null,8,["model-value","onChange"]),oe("div",null,[oe("div",Ta,xe(e.name),1),t[50]||(t[50]=oe("div",{class:"text-xs text-gray-500"}," 知识库 ",-1))])])]))),128))])])]),oe("div",Ba,[oe("h4",$a,[t[52]||(t[52]=ve(" 相关度阈值 ",-1)),fl.value.length>0?(G(),ue("span",La," (已选择 "+xe(fl.value.length)+" 个知识库) ",1)):de("",!0)]),me(ke,{modelValue:hl.value,"onUpdate:modelValue":t[16]||(t[16]=e=>hl.value=e),min:0,max:1,step:.1,"show-stops":"","show-input":"",disabled:0===fl.value.length},null,8,["modelValue","disabled"]),oe("div",Va,[fl.value.length>0?(G(),ue("p",Oa," 只有相关度高于此阈值的知识库内容才会被AI引用 ")):(G(),ue("p",Aa," 请先选择知识库，然后调整相关度阈值 ")),t[53]||(t[53]=oe("div",{class:"grid grid-cols-2 gap-2 text-xs opacity-75"},[oe("div",null,"0.0-0.3: 几乎不相关"),oe("div",null,"0.3-0.5: 低相关"),oe("div",null,"0.5-0.7: 中等相关"),oe("div",null,"0.7-1.0: 高相关")],-1)),t[54]||(t[54]=oe("p",{class:"opacity-75 leading-relaxed"}," 建议值：0.4-0.6，过高可能错过有用信息，过低可能引入噪音 ",-1))]),oe("div",Ea,[t[59]||(t[59]=oe("h4",{class:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2"}," 参考来源数量 ",-1)),oe("div",Ha,[me(je,{modelValue:yl.value,"onUpdate:modelValue":t[17]||(t[17]=e=>yl.value=e),size:"small",disabled:0===fl.value.length},{default:ae(()=>[me(Ie,{value:"limited"},{default:ae(()=>t[55]||(t[55]=[ve("限制数量",-1)])),_:1,__:[55]}),me(Ie,{value:"all"},{default:ae(()=>t[56]||(t[56]=[ve("显示所有相关",-1)])),_:1,__:[56]})]),_:1},8,["modelValue","disabled"])]),"limited"===yl.value?(G(),ue("div",Ua,[oe("div",Da," 最多显示 "+xe(xl.value)+" 条最相关的内容 ",1),me(ke,{modelValue:xl.value,"onUpdate:modelValue":t[18]||(t[18]=e=>xl.value=e),min:1,max:15,step:1,"show-stops":"","show-input":"",disabled:0===fl.value.length},null,8,["modelValue","disabled"])])):(G(),ue("div",Xa,[t[57]||(t[57]=oe("p",{class:"font-medium text-blue-700 dark:text-blue-300 mb-1"},"显示所有相关内容",-1)),oe("p",Pa,"AI将引用所有相关度高于阈值("+xe(hl.value)+")的知识库内容",1),t[58]||(t[58]=oe("p",{class:"text-xs opacity-75"},"注意：系统会自动限制总内容长度，避免超过模型处理能力",-1))])),oe("div",Na,[fl.value.length>0&&"limited"===yl.value?(G(),ue("p",Ra," 建议值：3-7条，过多可能影响响应速度 ")):fl.value.length>0?(G(),ue("p",Ja," 注意：显示所有相关内容可能会增加响应时间，但信息更全面 ")):de("",!0)])])])])])):de("",!0)])]),me($e,{modelValue:nl.value,"onUpdate:modelValue":t[24]||(t[24]=e=>nl.value=e),title:"创建新对话",width:"600px",onClose:es,class:"custom-dialog","show-close":!0,center:""},{footer:ae(()=>[oe("div",qa,[me(Z,{onClick:t[23]||(t[23]=e=>nl.value=!1)},{default:ae(()=>t[60]||(t[60]=[ve("取消",-1)])),_:1,__:[60]}),me(Z,{type:"primary",onClick:Zl,loading:ll.value},{default:ae(()=>t[61]||(t[61]=[ve(" 创建 ",-1)])),_:1,__:[61]},8,["loading"])])]),default:ae(()=>[me(Te,{model:Cl.value,rules:Il,ref_key:"newChatFormRef",ref:vl,"label-width":"100px"},{default:ae(()=>[me(ze,{label:"对话标题",prop:"title"},{default:ae(()=>[me(te,{modelValue:Cl.value.title,"onUpdate:modelValue":t[19]||(t[19]=e=>Cl.value.title=e),placeholder:"请输入对话标题（可选）",maxlength:"50","show-word-limit":""},null,8,["modelValue"])]),_:1}),me(ze,{label:"AI模型",prop:"modelId"},{default:ae(()=>[me(ye,{modelValue:Cl.value.modelId,"onUpdate:modelValue":t[20]||(t[20]=e=>Cl.value.modelId=e),placeholder:"选择AI模型",style:{width:"100%"}},{default:ae(()=>[(G(!0),ue(Q,null,ee(Ml.value,e=>(G(),ie(fe,{key:e.id,label:`${e.display_name||e.name} (${e.provider})`,value:e.id},{default:ae(()=>[oe("span",null,xe(e.display_name||e.name),1),oe("span",Ka,xe(e.provider),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),me(ze,{label:"知识库"},{default:ae(()=>[me(ye,{modelValue:Cl.value.knowledgeBaseId,"onUpdate:modelValue":t[21]||(t[21]=e=>Cl.value.knowledgeBaseId=e),placeholder:"选择要使用的知识库（可选）",style:{width:"100%"},clearable:""},{default:ae(()=>[(G(!0),ue(Q,null,ee(Tl.value,e=>(G(),ie(fe,{key:e.id,label:e.name,value:e.id},{default:ae(()=>[oe("div",Fa,[oe("span",null,xe(e.name),1),me(Me,{size:"small",class:"ml-2",type:"info"},{default:ae(()=>[ve(xe(e.document_count||0)+" 个文档 ",1)]),_:2},1024)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),me(ze,{label:"上下文长度"},{default:ae(()=>[me(ye,{modelValue:Cl.value.contextLength,"onUpdate:modelValue":t[22]||(t[22]=e=>Cl.value.contextLength=e),style:{width:"100%"}},{default:ae(()=>[me(fe,{label:"短 (最近5条消息)",value:5}),me(fe,{label:"中 (最近10条消息)",value:10}),me(fe,{label:"长 (最近20条消息)",value:20}),me(fe,{label:"超长 (最近50条消息)",value:50}),me(fe,{label:"全部消息",value:-1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])]))}}}),[["__scopeId","data-v-1e891754"]]);export{Wa as default};