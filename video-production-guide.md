# 慧由数生 - AI驱动的职业教育内容创生引擎
## 10分钟演示视频制作完整指南

### 📋 视频制作总览

**目标**：制作一个10分钟内的专业演示视频，全面展示智能体的核心搭建步骤、核心应用功能及效果

**评分重点**：
- 演示效果的逻辑性与感染力 (2分)
- 技术文档的完整性 (3分)
- 突出创新性和实际应用效果

---

## 🎬 视频结构设计 (10分钟)

### 第一部分：项目介绍与背景 (1.5分钟)
**时间**：0:00 - 1:30
**内容重点**：
- 项目名称和核心价值主张
- 职业教育数字化转型需求
- 现有AI教育工具的局限性
- 我们的解决方案概述

### 第二部分：核心技术架构展示 (2.5分钟)
**时间**：1:30 - 4:00
**内容重点**：
- RAG技术架构详解
- 多模型融合调度机制
- 向量化和检索引擎
- 零幻觉保证技术

### 第三部分：智能体搭建步骤演示 (3分钟)
**时间**：4:00 - 7:00
**内容重点**：
- 知识库创建和文档上传
- 向量化处理过程
- AI模型配置
- 专业助教生成

### 第四部分：核心功能应用演示 (2.5分钟)
**时间**：7:00 - 9:30
**内容重点**：
- 专业问答演示
- 可视化图表生成
- 多场景应用展示
- 实际效果对比

### 第五部分：创新特色与总结 (0.5分钟)
**时间**：9:30 - 10:00
**内容重点**：
- 核心创新点总结
- 应用价值和前景
- 结束语

---

## 📝 详细脚本内容

### 开场白 (0:00-0:30)
**画面**：项目Logo + 系统主界面
**解说词**：
"大家好！我是[您的姓名]，欢迎观看我们的参赛作品——'慧由数生：AI驱动的职业教育内容创生引擎'的演示视频。在接下来的10分钟里，我将为大家展示这个基于RAG技术的创新教育平台，如何解决职业教育中的实际问题，实现从通用AI到专业技能AI助教的智能转化。"

### 项目背景介绍 (0:30-1:30)
**画面**：问题分析图表 + 对比展示
**解说词**：
"随着职业教育数字化转型的深入推进，传统AI教育工具面临三大核心痛点：

第一，AI幻觉问题严重。传统AI在回答专业技能问题时经常产生虚假信息，可能误导学生掌握错误的操作方法。

第二，现有平台功能受限。Coze平台存储空间小于100MB，无法容纳完整专业资料；Dify平台成本高昂，对职业院校负担较重。

第三，缺乏专业适配。通用AI无法提供特定职业技能的专业指导，缺乏对行业标准和操作规范的深度理解。

我们的解决方案是基于RAG技术构建智能教育平台，实现零幻觉、低成本、高专业度的AI助教服务。"

### 核心技术架构 (1:30-4:00)
**画面**：系统架构图 + 技术流程动画
**解说词**：
"让我们深入了解核心技术架构。

首先是RAG检索增强生成技术。我们采用Sentence-BERT进行文本向量化，生成768维语义向量，特别针对职业教育专业术语进行优化。使用FAISS高效检索引擎，支持百万级文档的毫秒级检索。

其次是多模型融合调度架构。系统支持国内外主流AI模型，包括通义千问、GPT-4、Claude等，根据专业领域和问题类型智能选择最优模型，实现成本与性能的最佳平衡。

最关键的是零幻觉保证机制。所有回答均基于上传的真实专业文档，知识来源100%可追溯，AI幻觉发生率小于1%，专业知识准确率超过95%。

这套技术架构确保了专业知识的准确性和权威性，特别适合职业教育对专业技能要求极高的场景。"

### 智能体搭建演示 (4:00-7:00)
**画面**：实际操作录屏
**解说词**：
"现在演示智能体的搭建过程。我们的系统基于Vue.js + FastAPI的微服务架构，核心采用RAG检索增强生成技术。

第一步，创建专业知识库。通过Vue.js前端界面，我以'机械制造技术'专业为例，上传相关的教材PDF、实训手册、行业标准等文档。后端FastAPI服务支持PDF、Word、PPT等多种格式解析，基于SQLModel数据库模型管理，存储空间可达GB级别。

第二步，智能文档处理。系统后端采用先进的语义分块算法，自动将文档切分为最优的知识片段。使用Sentence-BERT模型进行768维向量化编码，构建高密度语义空间。可以看到处理进度和向量化状态。

第三步，FAISS向量索引构建。利用Facebook开源的FAISS分布式检索引擎，为所有文档片段建立高效的相似度索引。支持余弦相似度计算，检索速度达到毫秒级响应。

第四步，多模型AI助教生成。通过统一API接入层，选择GPT-4、Claude-3或通义千问等模型，配置专业参数。系统基于微服务架构，自动生成专属的机械制造AI助教，具备零幻觉、高精度的专业问答能力。

整个搭建过程基于工程化的技术架构，简单高效，普通教师也能轻松操作，3分钟内完成专业AI助教的创建。"

### 核心功能演示 (7:00-9:30)
**画面**：功能演示录屏
**解说词**：
"接下来展示核心应用功能。

首先是专业问答功能。学生问：'数控车床G01指令的具体用法是什么？'

AI助教基于上传的数控编程教材，详细解释G01直线插补指令的语法、参数和应用实例。注意，系统显示了知识来源，来自《数控编程技术》教材第45页，确保答案的权威性和可追溯性。

再看护理专业的应用。学生问：'静脉输液的操作步骤和注意事项？'

AI助教基于护理操作规范，逐步详解操作流程、无菌技术要求和安全注意事项。可以看到检索过程，显示了相关度评分和多个知识源。

最后是数据可视化功能。学生说：'帮我生成本学期各专业技能考核通过率的柱状图'

系统自动生成Chart.js图表，支持交互功能，一键导出。这大大降低了数据分析的门槛。

这些功能覆盖了职业教育的备课、授课、实训、评价全流程，真正实现了智能化教学支持。"

### 创新特色总结 (9:30-10:00)
**画面**：创新亮点展示 + 应用前景
**解说词**：
"总结我们的核心创新：

技术创新方面，首次在职业教育领域实现RAG技术深度应用和多模型融合调度，Token消耗降低80%，实现接近零成本运行。

教育创新方面，从通用AI到职业技能专家AI的转化，深度适配8大专业类别，支持产教融合和个性化教学。

应用创新方面，零幻觉保证专业知识准确性，私有化部署确保数据安全，一键部署降低技术门槛。

'慧由数生'平台为职业教育数字化转型提供了完整解决方案，将持续推动AI技术在职业教育领域的创新应用。谢谢大家！"

---

## 🎥 录制技术要求

### 录屏设置
- **分辨率**：1920x1080 (Full HD)
- **帧率**：30fps
- **音频**：48kHz, 16bit
- **格式**：MP4 (H.264编码)

### 音频要求
- 使用专业麦克风或耳机麦克风
- 录制环境安静，无杂音
- 语速适中，吐字清晰
- 音量稳定，避免忽高忽低

### 画面要求
- 界面清晰，字体大小适中
- 鼠标操作流畅，避免频繁点击
- 切换自然，避免突兀跳转
- 重点内容用箭头或高亮标注

---

## 📊 演示数据准备

### 知识库内容
1. **机械制造专业**：数控编程教材、机械设计手册
2. **护理专业**：护理操作规范、临床护理指南
3. **计算机专业**：Java编程教材、数据结构算法
4. **成绩分析**：模拟学生成绩数据、统计分析方法

### 测试问题库
```
专业技能类：
- "数控车床G01指令的具体用法是什么？"
- "静脉输液的操作步骤和注意事项？"
- "Java中ArrayList和LinkedList的区别？"

可视化类：
- "生成本学期各专业技能考核通过率的柱状图"
- "显示学生学习进度趋势图"
- "制作知识点掌握度雷达图"

对比验证类：
- 传统AI vs RAG系统的回答对比
- 不同模型的性能表现对比
- 成本效益分析对比
```

### 界面截图素材
- 系统主界面
- 知识库管理界面
- 对话界面
- 数据可视化界面
- 系统架构图
- 技术流程图

---

## ⚙️ 技术环境检查清单

### 系统运行检查
- [ ] 后端服务正常启动
- [ ] 前端界面正常访问
- [ ] 数据库连接正常
- [ ] AI模型接口正常

### 功能测试检查
- [ ] 知识库上传功能正常
- [ ] 文档解析和向量化正常
- [ ] AI对话功能正常
- [ ] 可视化图表生成正常
- [ ] 多模型切换正常

### 演示数据检查
- [ ] 测试知识库已准备
- [ ] 演示问题已测试
- [ ] 可视化数据已准备
- [ ] 界面截图已保存

---

## 🛠️ 录制设备和软件

### 推荐录屏软件
1. **OBS Studio** (免费，功能强大)
2. **Camtasia** (付费，易用性好)
3. **Bandicam** (付费，性能优秀)

### 音频设备
1. **专业麦克风**：Blue Yeti、Audio-Technica AT2020
2. **耳机麦克风**：SteelSeries Arctis、HyperX Cloud
3. **内置麦克风**：确保环境安静

### 视频编辑软件
1. **Adobe Premiere Pro** (专业级)
2. **DaVinci Resolve** (免费，功能全面)
3. **剪映专业版** (简单易用)

---

## 📅 制作时间安排

### 第1天：脚本和素材准备
- 上午：完善视频脚本
- 下午：准备演示素材和测试数据

### 第2天：技术测试和试录
- 上午：系统功能全面测试
- 下午：试录制和脚本调整

### 第3天：正式录制和后期
- 上午：正式录制
- 下午：视频剪辑和优化

### 第4天：最终检查和提交
- 上午：视频质量检查
- 下午：格式转换和文件准备

---

这个制作指南涵盖了视频录制的所有关键环节。你觉得哪个部分需要我进一步详细说明？

<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 1289.486328125 1166" style="max-width: 1289.486328125px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-73a39db8-148d-4438-814d-f48f5196eb47"><style>#mermaid-73a39db8-148d-4438-814d-f48f5196eb47{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .error-icon{fill:#a44141;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .edge-thickness-normal{stroke-width:1px;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .marker.cross{stroke:lightgrey;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 p{margin:0;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .cluster-label text{fill:#F9FFFE;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .cluster-label span{color:#F9FFFE;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .cluster-label span p{background-color:transparent;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .label text,#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 span{fill:#ccc;color:#ccc;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .node rect,#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .node circle,#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .node ellipse,#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .node polygon,#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .rough-node .label text,#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .node .label text,#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .image-shape .label,#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .icon-shape .label{text-anchor:middle;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .rough-node .label,#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .node .label,#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .image-shape .label,#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .icon-shape .label{text-align:center;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .node.clickable{cursor:pointer;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .arrowheadPath{fill:lightgrey;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .cluster text{fill:#F9FFFE;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .cluster span{color:#F9FFFE;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 rect.text{fill:none;stroke-width:0;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .icon-shape,#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .icon-shape p,#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .icon-shape rect,#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-73a39db8-148d-4438-814d-f48f5196eb47 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-73a39db8-148d-4438-814d-f48f5196eb47_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-73a39db8-148d-4438-814d-f48f5196eb47_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-73a39db8-148d-4438-814d-f48f5196eb47_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-73a39db8-148d-4438-814d-f48f5196eb47_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-73a39db8-148d-4438-814d-f48f5196eb47_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-73a39db8-148d-4438-814d-f48f5196eb47_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"><g data-look="classic" id="subGraph4" class="cluster"><rect height="200" width="456" y="233" x="825.486328125" style=""></rect><g transform="translate(987.083984375, 233)" class="cluster-label"><foreignObject height="24" width="132.8046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>样式处理层 Styling</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph3" class="cluster"><rect height="425" width="255.2109375" y="8" x="550.275390625" style=""></rect><g transform="translate(597.376953125, 8)" class="cluster-label"><foreignObject height="24" width="161.0078125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>构建工具层 Build Tools</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph2" class="cluster"><rect height="200" width="488" y="958" x="34.275390625" style=""></rect><g transform="translate(178.275390625, 958)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>数据可视化层 Data Visualization</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph1" class="cluster"><rect height="200" width="419.9140625" y="708" x="121.880859375" style=""></rect><g transform="translate(231.837890625, 708)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>状态管理层 State Management</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph0" class="cluster"><rect height="425" width="522.275390625" y="233" x="8" style=""></rect><g transform="translate(169.1376953125, 233)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>用户界面层 User Interface Layer</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-73a39db8-148d-4438-814d-f48f5196eb47_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A1_B1_0" d="M395.881,633L395.881,637.167C395.881,641.333,395.881,649.667,395.881,658C395.881,666.333,395.881,674.667,395.881,683C395.881,691.333,395.881,699.667,395.881,707.333C395.881,715,395.881,722,395.881,725.5L395.881,729"></path><path marker-end="url(#mermaid-73a39db8-148d-4438-814d-f48f5196eb47_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A2_A1_1" d="M135.605,408L135.605,412.167C135.605,416.333,135.605,424.667,135.605,433C135.605,441.333,135.605,449.667,161.797,463.896C187.988,478.126,240.37,498.251,266.561,508.314L292.752,518.377"></path><path marker-end="url(#mermaid-73a39db8-148d-4438-814d-f48f5196eb47_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A3_A1_2" d="M377.02,408L377.02,412.167C377.02,416.333,377.02,424.667,377.02,433C377.02,441.333,377.02,449.667,377.682,457.345C378.344,465.023,379.669,472.046,380.331,475.558L380.993,479.069"></path><path marker-end="url(#mermaid-73a39db8-148d-4438-814d-f48f5196eb47_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B1_C1_3" d="M284.967,855.399L264.452,864.166C243.938,872.933,202.91,890.466,182.395,903.4C161.881,916.333,161.881,924.667,161.881,933C161.881,941.333,161.881,949.667,161.881,957.333C161.881,965,161.881,972,161.881,975.5L161.881,979"></path><path marker-end="url(#mermaid-73a39db8-148d-4438-814d-f48f5196eb47_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B1_C2_4" d="M395.881,883L395.881,887.167C395.881,891.333,395.881,899.667,395.881,908C395.881,916.333,395.881,924.667,395.881,933C395.881,941.333,395.881,949.667,395.881,957.333C395.881,965,395.881,972,395.881,975.5L395.881,979"></path><path marker-end="url(#mermaid-73a39db8-148d-4438-814d-f48f5196eb47_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D1_A1_5" d="M677.881,408L677.881,412.167C677.881,416.333,677.881,424.667,631.071,433C584.26,441.333,490.64,449.667,443.79,457.333C396.94,465,396.86,472,396.82,475.5L396.78,479"></path><path marker-end="url(#mermaid-73a39db8-148d-4438-814d-f48f5196eb47_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D2_D1_6" d="M677.881,183L677.881,187.167C677.881,191.333,677.881,199.667,677.881,208C677.881,216.333,677.881,224.667,677.881,232.333C677.881,240,677.881,247,677.881,250.5L677.881,254"></path><path marker-end="url(#mermaid-73a39db8-148d-4438-814d-f48f5196eb47_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E1_A1_7" d="M943.881,408L943.881,412.167C943.881,416.333,943.881,424.667,862.08,433C780.28,441.333,616.679,449.667,532.826,457.421C448.974,465.176,444.869,472.352,442.817,475.94L440.765,479.528"></path><path marker-end="url(#mermaid-73a39db8-148d-4438-814d-f48f5196eb47_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E2_A1_8" d="M1161.881,408L1161.881,412.167C1161.881,416.333,1161.881,424.667,1047.08,433C932.28,441.333,702.679,449.667,585.069,457.472C467.46,465.278,461.841,472.556,459.032,476.195L456.223,479.834"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(395.880859375, 558)" id="flowchart-A1-202" class="node default"><rect height="150" width="198.7890625" y="-75" x="-99.39453125" style="fill:#42b883 !important;stroke:#333 !important;stroke-width:3px !important" class="basic label-container"></rect><g transform="translate(-69.39453125, -60)" style="color:#fff !important" class="label"><rect></rect><foreignObject height="120" width="138.7890625"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(255, 255, 255) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#fff !important"><p>Vue.js 3<br>🎯 现代化前端框架<br>• Composition API<br>• 响应式数据绑定<br>• 组件化开发</p></span></div></foreignObject></g></g><g transform="translate(135.60546875, 333)" id="flowchart-A2-203" class="node default"><rect height="150" width="185.2109375" y="-75" x="-92.60546875" style="fill:#3178c6 !important;stroke:#333 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-62.60546875, -60)" style="color:#fff !important" class="label"><rect></rect><foreignObject height="120" width="125.2109375"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(255, 255, 255) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#fff !important"><p>TypeScript<br>🛡️ 类型安全保障<br>• 静态类型检查<br>• 代码智能提示<br>• 编译时错误检测</p></span></div></foreignObject></g></g><g transform="translate(377.01953125, 333)" id="flowchart-A3-204" class="node default"><rect height="150" width="197.6171875" y="-75" x="-98.80859375" style="fill:#409eff !important;stroke:#333 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-68.80859375, -60)" style="color:#fff !important" class="label"><rect></rect><foreignObject height="120" width="137.6171875"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(255, 255, 255) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#fff !important"><p>Element Plus<br>🎨 企业级UI组件库<br>• 丰富的组件生态<br>• 统一的设计语言<br>• 开箱即用的样式</p></span></div></foreignObject></g></g><g transform="translate(395.880859375, 808)" id="flowchart-B1-205" class="node default"><rect height="150" width="221.828125" y="-75" x="-110.9140625" style="fill:#ffd93d !important;stroke:#333 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-80.9140625, -60)" style="color:#333 !important" class="label"><rect></rect><foreignObject height="120" width="161.828125"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(51, 51, 51) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#333 !important"><p>Pinia<br>🗃️ Vue 3官方状态管理<br>• 轻量级状态存储<br>• TypeScript友好<br>• 模块化状态管理</p></span></div></foreignObject></g></g><g transform="translate(161.880859375, 1058)" id="flowchart-C1-206" class="node default"><rect height="150" width="185.2109375" y="-75" x="-92.60546875" style="fill:#ff6384 !important;stroke:#333 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-62.60546875, -60)" style="color:#fff !important" class="label"><rect></rect><foreignObject height="120" width="125.2109375"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(255, 255, 255) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#fff !important"><p>Chart.js<br>📊 轻量级图表库<br>• 响应式图表<br>• 丰富的图表类型<br>• 易于集成</p></span></div></foreignObject></g></g><g transform="translate(395.880859375, 1058)" id="flowchart-C2-207" class="node default"><rect height="150" width="182.7890625" y="-75" x="-91.39453125" style="fill:#5470c6 !important;stroke:#333 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-61.39453125, -60)" style="color:#fff !important" class="label"><rect></rect><foreignObject height="120" width="122.7890625"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(255, 255, 255) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#fff !important"><p>ECharts<br>📈 企业级可视化<br>• 复杂数据展示<br>• 交互式图表<br>• 高性能渲染</p></span></div></foreignObject></g></g><g transform="translate(677.880859375, 333)" id="flowchart-D1-208" class="node default"><rect height="150" width="185.2109375" y="-75" x="-92.60546875" style="" class="basic label-container"></rect><g transform="translate(-62.60546875, -60)" style="" class="label"><rect></rect><foreignObject height="120" width="125.2109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Vite<br>⚡ 现代构建工具<br>• 极速热重载<br>• ES模块支持<br>• 优化的生产构建</p></span></div></foreignObject></g></g><g transform="translate(677.880859375, 108)" id="flowchart-D2-209" class="node default"><rect height="150" width="166.7890625" y="-75" x="-83.39453125" style="" class="basic label-container"></rect><g transform="translate(-53.39453125, -60)" style="" class="label"><rect></rect><foreignObject height="120" width="106.7890625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>npm/yarn<br>📦 包管理工具<br>• 依赖管理<br>• 脚本执行<br>• 版本控制</p></span></div></foreignObject></g></g><g transform="translate(943.880859375, 333)" id="flowchart-E1-210" class="node default"><rect height="150" width="166.7890625" y="-75" x="-83.39453125" style="" class="basic label-container"></rect><g transform="translate(-53.39453125, -60)" style="" class="label"><rect></rect><foreignObject height="120" width="106.7890625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>CSS3/SCSS<br>🎨 样式预处理<br>• 变量和混入<br>• 嵌套规则<br>• 模块化样式</p></span></div></foreignObject></g></g><g transform="translate(1161.880859375, 333)" id="flowchart-E2-211" class="node default"><rect height="150" width="169.2109375" y="-75" x="-84.60546875" style="" class="basic label-container"></rect><g transform="translate(-54.60546875, -60)" style="" class="label"><rect></rect><foreignObject height="120" width="109.2109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Tailwind CSS<br>🎯 原子化CSS<br>• 快速样式开发<br>• 响应式设计<br>• 一致性保障</p></span></div></foreignObject></g></g></g></g></g></svg>
