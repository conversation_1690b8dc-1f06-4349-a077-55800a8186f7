var e,t;(()=>{function n(e,t,n,a,o,s,r){try{var i=e[s](r),l=i.value}catch(e){return void n(e)}i.done?t(l):Promise.resolve(l).then(a,o)}function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function o(e){var t=function(e){if("object"!=a(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=a(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==a(t)?t:t+""}function s(e,t,n){return(t=o(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function r(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,a)}return n}e=function(e){return function(){var t=this,a=arguments;return new Promise(function(o,s){var r=e.apply(t,a);function i(e){n(r,o,s,i,l,"next",e)}function l(e){n(r,o,s,i,l,"throw",e)}i(void 0)})}},t=function(e){for(var t=1;t<arguments.length;t++){var n=null==arguments[t]?{}:arguments[t];t%2?r(Object(n),!0).forEach(function(t){s(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):r(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}})();import{cW as n,dU as a}from"./vendor-BJ-uKP15.js";import{b as o}from"./api-D-gMiCJf.js";const s=n("settings",()=>{const n=a({theme:"light",fontSize:"medium",defaultModel:null,defaultKnowledgeBases:[],chatRetentionDays:30,enableHighContrast:!1,enableReducedMotion:!1,providerPreferences:{}}),s=a([]),r=a([]),i=a(!1),l=(u=e(function*(){i.value=!0;try{const e=(yield o.get("/users/me/settings")).data;return n.value={theme:e.theme||"auto",fontSize:e.font_size||"medium",defaultModel:e.default_model_id||null,defaultKnowledgeBases:e.default_knowledge_bases||[],chatRetentionDays:e.chat_retention_days||30,enableHighContrast:e.enable_high_contrast||!1,enableReducedMotion:e.enable_reduced_motion||!1,providerPreferences:e.provider_preferences||{}},{success:!0,data:n.value}}catch(s){var e,t,a;if(401===(null===(e=s.response)||void 0===e?void 0:e.status)||403===(null===(t=s.response)||void 0===t?void 0:t.status))throw s;return{success:!1,message:(null===(a=s.response)||void 0===a||null===(a=a.data)||void 0===a?void 0:a.message)||"获取设置失败"}}finally{i.value=!1}}),function(){return u.apply(this,arguments)});var u;const c=(d=e(function*(e){i.value=!0;try{const a={};return void 0!==e.theme&&(a.theme=e.theme),void 0!==e.fontSize&&(a.font_size=e.fontSize),void 0!==e.defaultModel&&(a.default_model_id=e.defaultModel),void 0!==e.defaultKnowledgeBases&&(a.default_knowledge_bases=e.defaultKnowledgeBases),void 0!==e.chatRetentionDays&&(a.chat_retention_days=e.chatRetentionDays),void 0!==e.enableHighContrast&&(a.enable_high_contrast=e.enableHighContrast),void 0!==e.enableReducedMotion&&(a.enable_reduced_motion=e.enableReducedMotion),void 0!==e.providerPreferences&&(a.provider_preferences=e.providerPreferences),yield o.put("/users/me/settings",a),n.value=t(t({},n.value),e),{success:!0,data:n.value}}catch(s){var a;return{success:!1,message:(null===(a=s.response)||void 0===a||null===(a=a.data)||void 0===a?void 0:a.message)||"更新设置失败"}}finally{i.value=!1}}),function(e){return d.apply(this,arguments)});var d;const v=(f=e(function*(){i.value=!0;try{const e=yield o.get("/ai/api-keys");return r.value=e.data,{success:!0,data:e.data}}catch(t){var e;return{success:!1,message:(null===(e=t.response)||void 0===e||null===(e=e.data)||void 0===e?void 0:e.message)||"获取API密钥失败"}}finally{i.value=!1}}),function(){return f.apply(this,arguments)});var f;const p=(m=e(function*(e,t){i.value=!0;try{const n=(yield o.put(`/ai/api-keys/${e}`,t)).data,a=r.value.findIndex(t=>t.id===e);return-1!==a&&(r.value[a]=n),{success:!0,data:n}}catch(a){var n;return{success:!1,message:(null===(n=a.response)||void 0===n||null===(n=n.data)||void 0===n?void 0:n.message)||"更新API密钥失败"}}finally{i.value=!1}}),function(e,t){return m.apply(this,arguments)});var m;const y=(g=e(function*(){try{const e=yield o.get("/ai/models");return s.value=e.data.filter(e=>e.is_active),{success:!0,data:s.value}}catch(t){var e;return{success:!1,message:(null===(e=t.response)||void 0===e||null===(e=e.data)||void 0===e?void 0:e.message)||"获取AI模型列表失败"}}}),function(){return g.apply(this,arguments)});var g;const h=(b=e(function*(e){const t=document.documentElement;if("system"===e){const e=window.matchMedia("(prefers-color-scheme: dark)").matches;t.classList.toggle("dark",e)}else t.classList.toggle("dark","dark"===e);n.value.theme=e;try{yield c({theme:e})}catch(a){}}),function(e){return b.apply(this,arguments)});var b;const _=e=>{const t=document.documentElement;switch(t.classList.remove("text-sm","text-base","text-lg"),e){case"small":t.classList.add("text-sm");break;case"large":t.classList.add("text-lg");break;default:t.classList.add("text-base")}n.value.fontSize=e},S=()=>{const e=document.documentElement;e.classList.toggle("high-contrast",n.value.enableHighContrast),e.classList.toggle("reduced-motion",n.value.enableReducedMotion)},w=(P=e(function*(){if(localStorage.getItem("token"))try{yield l()}catch(e){}h(n.value.theme),_(n.value.fontSize),S()}),function(){return P.apply(this,arguments)});var P;return{userSettings:n,aiModels:s,userApiKeys:r,loading:i,fetchUserSettings:l,updateUserSettings:c,fetchUserApiKeys:v,updateApiKey:p,fetchAvailableModels:y,applyTheme:h,applyFontSize:_,applyAccessibilitySettings:S,initializeSettings:w}});export{s as b};