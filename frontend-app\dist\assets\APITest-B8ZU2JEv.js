var e;(()=>{function t(e,t,s,a,l,n,o){try{var r=e[n](o),i=r.value}catch(e){return void s(e)}r.done?t(i):Promise.resolve(i).then(a,l)}e=function(e){return function(){var s=this,a=arguments;return new Promise(function(l,n){var o=e.apply(s,a);function r(e){t(o,l,n,r,i,"next",e)}function i(e){t(o,l,n,r,i,"throw",e)}r(void 0)})}}})();import{D as t,E as s,M as a,w as l,x as n,y as o}from"./elementPlus-Di4PDIm8.js";import{d8 as r,dB as i,dD as d,dN as u,dU as c,dd as v,de as y,df as p,dg as h,dj as m,dk as f,dl as g,dy as _,ea as A,ed as j}from"./vendor-BJ-uKP15.js";import{c as k,d as I}from"./index-Byt5TjPh.js";import{b as w}from"./_plugin-vue_export-helper-CjD0mXop.js";import{b as B}from"./knowledgeBase-yaqAZvLB.js";import"./document-q3qKo7UT.js";import{b}from"./chat-Cv_kC_4-.js";import{b as P}from"./stats-DK53Rw0v.js";import"./admin-BSai4urc.js";const M={testAuth:()=>e(function*(){try{const e=yield k.login({username:"testuser",password:"test123"});return localStorage.setItem("token",e.access_token),yield k.getCurrentUser(),!0}catch(e){return!1}})(),testAIModels:()=>e(function*(){try{return yield I.get("/ai/models"),!0}catch(e){return!1}})(),testKnowledgeBase:()=>e(function*(){try{yield B.getList();const e=yield B.create({name:"前端测试知识库",description:"用于前端API测试"});return yield B.getDetail(e.id),yield B.delete(e.id),!0}catch(e){return!1}})(),testChat:()=>e(function*(){try{yield b.getSessions();const e=yield b.createSession({title:"前端API测试聊天"});return yield b.getChatHistory(e.id),yield b.deleteSession(e.id),!0}catch(e){return!1}})(),testStats:()=>e(function*(){try{return yield P.getDashboardStats(),yield P.getDetailedStats(),yield P.getActivityStats({days:7}),!0}catch(e){return!1}})(),runAllTests(){var t=this;return e(function*(){const e={auth:!1,aiModels:!1,knowledgeBase:!1,chat:!1,stats:!1};return e.auth=yield t.testAuth(),e.auth&&(e.aiModels=yield t.testAIModels(),e.knowledgeBase=yield t.testKnowledgeBase(),e.chat=yield t.testChat(),e.stats=yield t.testStats()),Object.values(e).filter(Boolean),Object.keys(e),e})()}},S={class:"api-test-page"},x={class:"card-header"},O={class:"test-results"},C={class:"test-item"},D={class:"test-item"},T={class:"test-item"},U={class:"test-item"},V={class:"test-item"},K={key:0,class:"console-output"},$={class:"console-content"};var z=w(g({__name:"APITest",setup(g){const k=c(!1),I=c(null),w=c(["auth","aiModels","knowledgeBase","chat","stats"]),B=c([]),b=console.log,P=console.error,z=(E=e(function*(){k.value=!0,B.value=[];try{console.log=(...e)=>{B.value.push({type:"log",message:e.join(" ")}),b(...e)},console.error=(...e)=>{B.value.push({type:"error",message:e.join(" ")}),P(...e)};const e=yield M.runAllTests();I.value=e}catch(e){}finally{console.log=b,console.error=P,k.value=!1}}),function(){return E.apply(this,arguments)});var E;const H=()=>{if(!I.value)return"info";const e=Object.values(I.value).filter(Boolean).length;return e===Object.keys(I.value).length?"success":0===e?"error":"warning"},L=e=>void 0===e?"info":e?"success":"danger";return _(()=>{z()}),(e,c)=>{const g=s,_=a,b=o,P=n,M=l,E=t;return i(),h("div",S,[f(E,{class:"test-card"},{header:u(()=>[v("div",x,[c[1]||(c[1]=v("span",null,"API连接测试",-1)),f(g,{type:"primary",onClick:z,loading:k.value},{default:u(()=>[m(j(k.value?"测试中...":"运行所有测试"),1)]),_:1},8,["loading"])])]),default:u(()=>[v("div",O,[I.value?(i(),y(_,{key:0,title:I.value?`测试完成: ${Object.values(I.value).filter(Boolean).length}/${Object.keys(I.value).length} 个API测试通过`:"",type:H(),closable:!1,"show-icon":"",class:"mb-4"},null,8,["title","type"])):p("",!0),f(M,{modelValue:w.value,"onUpdate:modelValue":c[0]||(c[0]=e=>w.value=e)},{default:u(()=>[f(P,{title:"认证API测试",name:"auth"},{default:u(()=>{var e;return[v("div",C,[f(b,{type:L(null===(e=I.value)||void 0===e?void 0:e.auth)},{default:u(()=>{var e;return[m(j((null===(e=I.value)||void 0===e?void 0:e.auth)?"✅ 通过":"❌ 失败"),1)]}),_:1},8,["type"]),c[2]||(c[2]=v("span",{class:"test-desc"},"登录、获取用户信息",-1))])]}),_:1}),f(P,{title:"AI模型API测试",name:"aiModels"},{default:u(()=>{var e;return[v("div",D,[f(b,{type:L(null===(e=I.value)||void 0===e?void 0:e.aiModels)},{default:u(()=>{var e;return[m(j((null===(e=I.value)||void 0===e?void 0:e.aiModels)?"✅ 通过":"❌ 失败"),1)]}),_:1},8,["type"]),c[3]||(c[3]=v("span",{class:"test-desc"},"获取AI模型列表",-1))])]}),_:1}),f(P,{title:"知识库API测试",name:"knowledgeBase"},{default:u(()=>{var e;return[v("div",T,[f(b,{type:L(null===(e=I.value)||void 0===e?void 0:e.knowledgeBase)},{default:u(()=>{var e;return[m(j((null===(e=I.value)||void 0===e?void 0:e.knowledgeBase)?"✅ 通过":"❌ 失败"),1)]}),_:1},8,["type"]),c[4]||(c[4]=v("span",{class:"test-desc"},"创建、获取、删除知识库",-1))])]}),_:1}),f(P,{title:"聊天API测试",name:"chat"},{default:u(()=>{var e;return[v("div",U,[f(b,{type:L(null===(e=I.value)||void 0===e?void 0:e.chat)},{default:u(()=>{var e;return[m(j((null===(e=I.value)||void 0===e?void 0:e.chat)?"✅ 通过":"❌ 失败"),1)]}),_:1},8,["type"]),c[5]||(c[5]=v("span",{class:"test-desc"},"创建会话、获取历史记录",-1))])]}),_:1}),f(P,{title:"统计API测试",name:"stats"},{default:u(()=>{var e;return[v("div",V,[f(b,{type:L(null===(e=I.value)||void 0===e?void 0:e.stats)},{default:u(()=>{var e;return[m(j((null===(e=I.value)||void 0===e?void 0:e.stats)?"✅ 通过":"❌ 失败"),1)]}),_:1},8,["type"]),c[6]||(c[6]=v("span",{class:"test-desc"},"获取统计数据",-1))])]}),_:1})]),_:1},8,["modelValue"])]),B.value.length>0?(i(),h("div",K,[c[7]||(c[7]=v("h4",null,"控制台输出:",-1)),v("div",$,[(i(!0),h(r,null,d(B.value,(e,t)=>(i(),h("div",{key:t,class:A(["console-line",e.type])},j(e.message),3))),128))])])):p("",!0)]),_:1})])}}}),[["__scopeId","data-v-755a3799"]]);export{z as default};