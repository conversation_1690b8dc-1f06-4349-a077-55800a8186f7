#!/bin/bash

# AI教育智能体平台 - 宝塔部署脚本
# 解决API地址配置问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境
check_environment() {
    log_info "检查部署环境..."
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js未安装，请先安装Node.js"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        log_error "npm未安装，请先安装npm"
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 配置环境变量
setup_environment() {
    log_info "配置环境变量..."
    
    # 读取用户输入的域名或IP
    read -p "请输入您的服务器域名或IP地址 (例如: example.com 或 *************): " SERVER_ADDRESS
    
    if [ -z "$SERVER_ADDRESS" ]; then
        log_error "服务器地址不能为空"
        exit 1
    fi
    
    # 询问是否使用HTTPS
    read -p "是否使用HTTPS? (y/N): " USE_HTTPS
    
    if [[ "$USE_HTTPS" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        PROTOCOL="https"
    else
        PROTOCOL="http"
    fi
    
    # 询问后端端口
    read -p "请输入后端API端口 (默认8000): " API_PORT
    API_PORT=${API_PORT:-8000}
    
    # 生成生产环境配置
    cat > frontend-app/.env.production << EOF
# 生产环境配置 - 自动生成
VITE_API_BASE_URL=${PROTOCOL}://${SERVER_ADDRESS}:${API_PORT}/api
VITE_APP_TITLE=AI教育智能体平台
VITE_APP_VERSION=1.0.0
EOF
    
    log_success "环境配置完成"
    log_info "API地址: ${PROTOCOL}://${SERVER_ADDRESS}:${API_PORT}/api"
}

# 安装依赖
install_dependencies() {
    log_info "安装前端依赖..."
    
    cd frontend-app
    
    # 清理缓存
    npm cache clean --force
    
    # 安装依赖
    npm install
    
    log_success "依赖安装完成"
}

# 构建前端
build_frontend() {
    log_info "构建前端项目..."
    
    # 设置生产环境
    export NODE_ENV=production
    
    # 构建项目
    npm run build
    
    if [ ! -d "dist" ]; then
        log_error "构建失败，dist目录不存在"
        exit 1
    fi
    
    log_success "前端构建完成"
    
    cd ..
}

# 部署到宝塔
deploy_to_baota() {
    log_info "部署到宝塔..."
    
    # 询问网站根目录
    read -p "请输入宝塔网站根目录路径 (例如: /www/wwwroot/ai-education): " WEBSITE_ROOT
    
    if [ -z "$WEBSITE_ROOT" ]; then
        log_error "网站根目录不能为空"
        exit 1
    fi
    
    # 检查目录是否存在
    if [ ! -d "$WEBSITE_ROOT" ]; then
        log_warning "目录不存在，正在创建: $WEBSITE_ROOT"
        mkdir -p "$WEBSITE_ROOT"
    fi
    
    # 备份现有文件
    if [ "$(ls -A $WEBSITE_ROOT)" ]; then
        log_info "备份现有文件..."
        BACKUP_DIR="${WEBSITE_ROOT}_backup_$(date +%Y%m%d_%H%M%S)"
        cp -r "$WEBSITE_ROOT" "$BACKUP_DIR"
        log_success "备份完成: $BACKUP_DIR"
    fi
    
    # 复制构建文件
    log_info "复制构建文件到网站目录..."
    cp -r frontend-app/dist/* "$WEBSITE_ROOT/"
    
    # 设置权限
    chown -R www:www "$WEBSITE_ROOT"
    chmod -R 755 "$WEBSITE_ROOT"
    
    log_success "文件部署完成"
}

# 生成Nginx配置
generate_nginx_config() {
    log_info "生成Nginx配置建议..."
    
    cat > nginx_config_suggestion.conf << EOF
# AI教育智能体平台 Nginx配置建议
# 请将以下配置添加到您的宝塔网站配置中

server {
    listen 80;
    server_name ${SERVER_ADDRESS};
    root ${WEBSITE_ROOT};
    index index.html;
    
    # 前端路由支持
    location / {
        try_files \$uri \$uri/ /index.html;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://127.0.0.1:${API_PORT};
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # 支持大文件上传
        client_max_body_size 100M;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
    
    # WebSocket支持 (如果需要)
    location /ws/ {
        proxy_pass http://127.0.0.1:${API_PORT};
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF
    
    log_success "Nginx配置已生成: nginx_config_suggestion.conf"
}

# 测试部署
test_deployment() {
    log_info "测试部署..."
    
    # 检查前端文件
    if [ -f "${WEBSITE_ROOT}/index.html" ]; then
        log_success "前端文件部署成功"
    else
        log_error "前端文件部署失败"
        exit 1
    fi
    
    # 测试API连接
    log_info "测试API连接..."
    if curl -f -s "${PROTOCOL}://${SERVER_ADDRESS}:${API_PORT}/api/health" > /dev/null 2>&1; then
        log_success "API连接正常"
    else
        log_warning "API连接失败，请检查后端服务是否启动"
    fi
}

# 显示部署信息
show_deployment_info() {
    log_success "🎉 部署完成！"
    echo
    echo "📋 部署信息："
    echo "  网站地址: ${PROTOCOL}://${SERVER_ADDRESS}"
    echo "  API地址:  ${PROTOCOL}://${SERVER_ADDRESS}:${API_PORT}/api"
    echo "  网站目录: ${WEBSITE_ROOT}"
    echo
    echo "📝 后续步骤："
    echo "  1. 在宝塔面板中配置Nginx (参考: nginx_config_suggestion.conf)"
    echo "  2. 确保后端服务在端口 ${API_PORT} 上运行"
    echo "  3. 重启Nginx服务"
    echo "  4. 访问网站测试功能"
    echo
    echo "🔧 故障排除："
    echo "  - 如果登录失败，检查API地址配置"
    echo "  - 如果出现CORS错误，检查Nginx代理配置"
    echo "  - 如果静态资源加载失败，检查文件权限"
    echo
    echo "📞 技术支持："
    echo "  - 检查浏览器控制台错误信息"
    echo "  - 查看Nginx错误日志: /var/log/nginx/error.log"
    echo "  - 查看后端服务日志"
}

# 主函数
main() {
    echo "🚀 AI教育智能体平台 - 宝塔部署脚本"
    echo "=================================="
    
    case "${1:-deploy}" in
        "deploy")
            check_environment
            setup_environment
            install_dependencies
            build_frontend
            deploy_to_baota
            generate_nginx_config
            test_deployment
            show_deployment_info
            ;;
        "build-only")
            check_environment
            setup_environment
            install_dependencies
            build_frontend
            log_success "构建完成，请手动复制 frontend-app/dist/ 目录到网站根目录"
            ;;
        "config-only")
            setup_environment
            generate_nginx_config
            log_success "配置文件已生成"
            ;;
        *)
            echo "用法: $0 [deploy|build-only|config-only]"
            echo "  deploy     - 完整部署 (默认)"
            echo "  build-only - 仅构建前端"
            echo "  config-only- 仅生成配置"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
