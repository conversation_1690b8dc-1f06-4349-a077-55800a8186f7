/**
 * 简单的调试工具
 */

export function debugAuth() {
  try {
    console.log('🔍 认证状态调试信息:')
    console.log('==================================================')
    
    // 检查localStorage
    const token = localStorage.getItem('token')
    const user = localStorage.getItem('user')
    console.log('📦 localStorage:')
    console.log('  token:', token ? `${token.substring(0, 20)}...` : 'null')
    console.log('  user:', user || 'null')
    
    // 检查环境
    console.log('🌐 环境信息:')
    console.log('  当前URL:', window.location.href)
    console.log('  API基础URL:', import.meta.env.VITE_API_BASE_URL || '/api')
    console.log('  是否生产环境:', import.meta.env.PROD)
    
  } catch (error) {
    console.error('调试失败:', error)
  }
}

// 暴露到window对象
if (typeof window !== 'undefined') {
  (window as any).debugAuth = debugAuth
}
