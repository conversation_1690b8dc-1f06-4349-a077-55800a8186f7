<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教育领域覆盖饼图</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 30px;
        }
        
        .title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            letter-spacing: -0.5px;
        }
        
        .subtitle {
            font-size: 16px;
            opacity: 0.9;
            font-weight: 400;
        }
        
        .content {
            padding: 40px;
            background: white;
        }
        
        .charts-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }
        
        .chart-card {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
            border: 2px solid #f1f5f9;
            transition: all 0.3s ease;
        }
        
        .chart-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 12px 30px rgba(102, 126, 234, 0.15);
        }
        
        .chart-title {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .chart-container {
            position: relative;
            height: 400px;
        }
        
        .coverage-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            border-left: 4px solid #3b82f6;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(59, 130, 246, 0.15);
        }
        
        .stat-icon {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .stat-title {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .stat-value {
            font-size: 20px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 5px;
        }
        
        .stat-desc {
            font-size: 12px;
            color: #6b7280;
        }
        
        .coverage-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-top: 40px;
        }
        
        .detail-card {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            padding: 25px;
            border-radius: 12px;
            border-left: 4px solid #667eea;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        .detail-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .detail-content {
            color: #64748b;
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .subject-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 8px;
        }
        
        .subject-tag {
            background: #667eea;
            color: white;
            padding: 6px 10px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 500;
            text-align: center;
        }
        
        .level-list {
            list-style: none;
        }
        
        .level-list li {
            padding: 6px 0;
            color: #475569;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .level-list li::before {
            content: '📚';
            font-size: 14px;
        }
        
        .summary-section {
            background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
            padding: 30px;
            border-radius: 15px;
            margin-top: 40px;
        }
        
        .summary-title {
            font-size: 22px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .summary-content {
            color: #64748b;
            font-size: 16px;
            line-height: 1.6;
            text-align: center;
        }
        
        .highlight {
            color: #667eea;
            font-weight: 600;
        }
        
        @media (max-width: 768px) {
            .charts-section {
                grid-template-columns: 1fr;
            }
            
            .coverage-stats {
                grid-template-columns: 1fr;
            }
            
            .coverage-details {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">📊 教育领域覆盖饼图</div>
            <div class="subtitle">Educational Field Coverage Distribution</div>
        </div>
        
        <div class="content">
            <!-- 覆盖统计概览 -->
            <div class="coverage-stats">
                <div class="stat-card">
                    <div class="stat-icon">🎓</div>
                    <div class="stat-title">教育层次覆盖</div>
                    <div class="stat-value">4大层次</div>
                    <div class="stat-desc">基础/高等/继续/企业</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">📚</div>
                    <div class="stat-title">学科领域覆盖</div>
                    <div class="stat-value">4大类别</div>
                    <div class="stat-desc">理工/人文/艺术/语言</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">🌍</div>
                    <div class="stat-title">应用范围</div>
                    <div class="stat-value">全覆盖</div>
                    <div class="stat-desc">各年龄段学习者</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">⚡</div>
                    <div class="stat-title">适配程度</div>
                    <div class="stat-value">100%</div>
                    <div class="stat-desc">个性化定制</div>
                </div>
            </div>
            
            <!-- 饼图展示 -->
            <div class="charts-section">
                <div class="chart-card">
                    <div class="chart-title">🎓 教育层次分布</div>
                    <div class="chart-container">
                        <canvas id="levelChart"></canvas>
                    </div>
                </div>
                
                <div class="chart-card">
                    <div class="chart-title">📚 学科领域分布</div>
                    <div class="chart-container">
                        <canvas id="subjectChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- 详细覆盖说明 -->
            <div class="coverage-details">
                <div class="detail-card">
                    <div class="detail-title">
                        🎓 教育层次覆盖
                    </div>
                    <div class="detail-content">
                        平台全面覆盖各个教育阶段，从基础教育到企业培训，
                        为不同年龄段和学习需求的用户提供专业的AI教学服务。
                    </div>
                    <ul class="level-list">
                        <li>基础教育：小学/初中/高中</li>
                        <li>高等教育：本科/硕士研究生</li>
                        <li>继续教育：在职教育/技能培训</li>
                        <li>企业培训：职业认证/专业培训</li>
                    </ul>
                </div>

                <div class="detail-card">
                    <div class="detail-title">
                        📚 学科领域覆盖
                    </div>
                    <div class="detail-content">
                        涵盖理工、人文社科、艺术、语言四大学科类别，
                        为各专业领域提供精准的知识问答和学习指导。
                    </div>
                    <div class="subject-list">
                        <div class="subject-tag">数学</div>
                        <div class="subject-tag">物理</div>
                        <div class="subject-tag">化学</div>
                        <div class="subject-tag">计算机</div>
                        <div class="subject-tag">工程</div>
                        <div class="subject-tag">语文</div>
                        <div class="subject-tag">历史</div>
                        <div class="subject-tag">地理</div>
                        <div class="subject-tag">政治</div>
                        <div class="subject-tag">美术</div>
                        <div class="subject-tag">音乐</div>
                        <div class="subject-tag">设计</div>
                        <div class="subject-tag">英语</div>
                        <div class="subject-tag">日语</div>
                        <div class="subject-tag">法语</div>
                    </div>
                </div>

                <div class="detail-card">
                    <div class="detail-title">
                        🌟 应用特色
                    </div>
                    <div class="detail-content">
                        基于RAG技术的智能教学系统，能够根据不同教育层次和学科特点，
                        提供个性化的教学内容和学习指导。
                    </div>
                    <ul class="level-list">
                        <li>个性化内容推荐</li>
                        <li>多层次难度适配</li>
                        <li>跨学科知识整合</li>
                        <li>实时学习反馈</li>
                    </ul>
                </div>

                <div class="detail-card">
                    <div class="detail-title">
                        🎯 覆盖优势
                    </div>
                    <div class="detail-content">
                        广泛的教育领域覆盖确保了平台的通用性和实用性，
                        满足不同用户群体的多样化学习需求。
                    </div>
                    <ul class="level-list">
                        <li>全年龄段适用</li>
                        <li>全学科支持</li>
                        <li>全场景覆盖</li>
                        <li>全方位服务</li>
                    </ul>
                </div>
            </div>

            <!-- 总结 -->
            <div class="summary-section">
                <div class="summary-title">🎯 教育领域全覆盖优势</div>
                <div class="summary-content">
                    平台实现了<span class="highlight">4大教育层次</span>和<span class="highlight">4大学科类别</span>的全面覆盖，
                    从<span class="highlight">基础教育到企业培训</span>，从<span class="highlight">理工科到人文艺术</span>，
                    为各个教育领域提供专业、精准的AI教学服务，真正实现了教育数字化的<span class="highlight">全场景应用</span>。
                </div>
            </div>
        </div>
    </div>

    <script>
        // 教育层次分布饼图
        const levelCtx = document.getElementById('levelChart').getContext('2d');
        new Chart(levelCtx, {
            type: 'pie',
            data: {
                labels: ['基础教育', '高等教育', '继续教育', '企业培训'],
                datasets: [{
                    data: [30, 35, 20, 15], // 假设的分布比例
                    backgroundColor: [
                        '#3b82f6', // 蓝色 - 基础教育
                        '#10b981', // 绿色 - 高等教育
                        '#f59e0b', // 橙色 - 继续教育
                        '#ef4444'  // 红色 - 企业培训
                    ],
                    borderColor: '#ffffff',
                    borderWidth: 3,
                    hoverOffset: 10
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            font: {
                                size: 12,
                                weight: '500'
                            },
                            color: '#1e293b',
                            usePointStyle: true,
                            pointStyle: 'circle',
                            padding: 15
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: '#667eea',
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                const label = context.label;
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);

                                let description = '';
                                if (label === '基础教育') {
                                    description = '小学/初中/高中';
                                } else if (label === '高等教育') {
                                    description = '本科/硕士研究生';
                                } else if (label === '继续教育') {
                                    description = '在职教育/技能培训';
                                } else if (label === '企业培训') {
                                    description = '职业认证/专业培训';
                                }

                                return [label + ': ' + percentage + '%', description];
                            }
                        }
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeInOutQuart'
                }
            }
        });

        // 学科领域分布饼图
        const subjectCtx = document.getElementById('subjectChart').getContext('2d');
        new Chart(subjectCtx, {
            type: 'pie',
            data: {
                labels: ['理工类', '人文社科', '艺术类', '语言类'],
                datasets: [{
                    data: [40, 30, 15, 15], // 假设的分布比例
                    backgroundColor: [
                        '#8b5cf6', // 紫色 - 理工类
                        '#06b6d4', // 青色 - 人文社科
                        '#f97316', // 橙色 - 艺术类
                        '#84cc16'  // 绿色 - 语言类
                    ],
                    borderColor: '#ffffff',
                    borderWidth: 3,
                    hoverOffset: 10
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            font: {
                                size: 12,
                                weight: '500'
                            },
                            color: '#1e293b',
                            usePointStyle: true,
                            pointStyle: 'circle',
                            padding: 15
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: '#667eea',
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                const label = context.label;
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);

                                let description = '';
                                if (label === '理工类') {
                                    description = '数理化/计算机/工程';
                                } else if (label === '人文社科') {
                                    description = '语文/历史/地理/政治';
                                } else if (label === '艺术类') {
                                    description = '美术/音乐/设计';
                                } else if (label === '语言类') {
                                    description = '英/日/法等外语';
                                }

                                return [label + ': ' + percentage + '%', description];
                            }
                        }
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeInOutQuart'
                }
            }
        });
    </script>
</body>
</html>
