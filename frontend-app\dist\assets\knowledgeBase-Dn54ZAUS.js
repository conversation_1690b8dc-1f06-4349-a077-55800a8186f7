var e;(()=>{function n(e,n,a,s,t,l,u){try{var r=e[l](u),o=r.value}catch(e){return void a(e)}r.done?n(o):Promise.resolve(o).then(s,t)}e=function(e){return function(){var a=this,s=arguments;return new Promise(function(t,l){var u=e.apply(a,s);function r(e){n(u,t,l,r,o,"next",e)}function o(e){n(u,t,l,r,o,"throw",e)}r(void 0)})}}})();import{cW as n,dU as a}from"./vendor-BJ-uKP15.js";import{b as s}from"./knowledgeBase-yaqAZvLB.js";const t=n("knowledgeBase",()=>{const n=a([]),t=a(null),l=a(!1),u=(r=e(function*(e){l.value=!0;try{const a=yield s.getList(e);return n.value=a,{success:!0,data:a}}catch(t){var a;return{success:!1,message:(null===(a=t.response)||void 0===a||null===(a=a.data)||void 0===a?void 0:a.message)||"获取知识库列表失败"}}finally{l.value=!1}}),function(e){return r.apply(this,arguments)});var r;const o=(i=e(function*(e){l.value=!0;try{const a=yield s.create(e);return n.value.unshift(a),{success:!0,data:a}}catch(t){var a;return{success:!1,message:(null===(a=t.response)||void 0===a||null===(a=a.data)||void 0===a?void 0:a.message)||"创建知识库失败"}}finally{l.value=!1}}),function(e){return i.apply(this,arguments)});var i;const c=(v=e(function*(e){l.value=!0;try{const n=yield s.getDetail(e);return t.value=n,{success:!0,data:n}}catch(a){var n;return{success:!1,data:null,message:(null===(n=a.response)||void 0===n||null===(n=n.data)||void 0===n?void 0:n.message)||"获取知识库详情失败"}}finally{l.value=!1}}),function(e){return v.apply(this,arguments)});var v;const d=(f=e(function*(e,a){l.value=!0;try{var u;const l=yield s.update(e,a),r=n.value.findIndex(n=>n.id===e);return-1!==r&&(n.value[r]=l),(null===(u=t.value)||void 0===u?void 0:u.id)===e&&(t.value=l),{success:!0,data:l}}catch(o){var r;return{success:!1,message:(null===(r=o.response)||void 0===r||null===(r=r.data)||void 0===r?void 0:r.message)||"更新知识库失败"}}finally{l.value=!1}}),function(e,n){return f.apply(this,arguments)});var f;const g=(p=e(function*(e){l.value=!0;try{var a;return yield s.delete(e),n.value=n.value.filter(n=>n.id!==e),(null===(a=t.value)||void 0===a?void 0:a.id)===e&&(t.value=null),{success:!0}}catch(r){var u;return{success:!1,message:(null===(u=r.response)||void 0===u||null===(u=u.data)||void 0===u?void 0:u.message)||"删除知识库失败"}}finally{l.value=!1}}),function(e){return p.apply(this,arguments)});var p;return{knowledgeBases:n,currentKnowledgeBase:t,loading:l,fetchKnowledgeBases:u,createKnowledgeBase:o,fetchKnowledgeBase:c,updateKnowledgeBase:d,deleteKnowledgeBase:g,clearCurrentKnowledgeBase:()=>{t.value=null}}});export{t as b};