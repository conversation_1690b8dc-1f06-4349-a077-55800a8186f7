<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格渲染测试</title>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.9.0/lib/highlight.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.9.0/styles/github.min.css">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-case { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .test-case h3 { color: #333; }
        .result { margin-top: 10px; padding: 10px; background: #f5f5f5; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>表格渲染测试</h1>
    
    <div class="test-case">
        <h3>测试1: 正常的Markdown表格</h3>
        <div id="test1-result" class="result"></div>
    </div>
    
    <div class="test-case">
        <h3>测试2: 被```markdown包裹的表格</h3>
        <div id="test2-result" class="result"></div>
    </div>
    
    <div class="test-case">
        <h3>测试3: 被```table包裹的表格</h3>
        <div id="test3-result" class="result"></div>
    </div>
    
    <div class="test-case">
        <h3>测试4: 被```包裹的表格（无语言标识）</h3>
        <div id="test4-result" class="result"></div>
    </div>

    <script>
        // 创建自定义渲染器
        const renderer = new marked.Renderer()

        // 自定义代码块渲染
        renderer.code = function(code, lang) {
            const codeStr = String(code || '')
            const language = lang || ''

            // 特殊处理：检查是否为被错误标记的表格内容
            if ((language === 'markdown' || language === 'table' || language === '') &&
                codeStr.includes('|') && (codeStr.includes('---') || codeStr.includes('--'))) {
                console.log('检测到代码块中的表格，直接渲染为表格:', codeStr.substring(0, 100))
                // 直接渲染为表格，而不是代码块
                return marked.parse(codeStr.trim());
            }

            // 正常的代码块处理
            const validLang = language && hljs.getLanguage(language) ? language : 'plaintext'
            const highlightedCode = hljs.highlight(codeStr, { language: validLang }).value
            return `<pre><code class="hljs language-${validLang}">${highlightedCode}</code></pre>`
        }

        // 配置marked
        marked.setOptions({
            renderer: renderer,
            breaks: true,
            gfm: true
        })

        // 测试用例
        const testCases = {
            test1: `| 步骤 | 详细操作 |
|------|----------|
| 访问官网 | 打开江苏第二师范学院官网：[https://www.jssnu.edu.cn/](https://www.jssnu.edu.cn/) |
| 查找联系方式 | 点击"联系我们"或"招生就业"页面，记录联系电话或联系邮箱 |
| 联系学院电话 | 拨打联系电话：025-83758102 |`,

            test2: `当然，以下是用Markdown表格形式重新输出的步骤和建议：

\`\`\`markdown
| 步骤 | 详细操作 |
|------|----------|
| 访问官网 | 打开江苏第二师范学院官网：[https://www.jssnu.edu.cn/](https://www.jssnu.edu.cn/) |
| 查找联系方式 | 点击"联系我们"或"招生就业"页面，记录联系电话或联系邮箱 |
| 联系学院电话 | 拨打联系电话：025-83758102 |
\`\`\``,

            test3: `\`\`\`table
| 步骤 | 详细操作 |
|------|----------|
| 访问官网 | 打开江苏第二师范学院官网：[https://www.jssnu.edu.cn/](https://www.jssnu.edu.cn/) |
| 查找联系方式 | 点击"联系我们"或"招生就业"页面，记录联系电话或联系邮箱 |
| 联系学院电话 | 拨打联系电话：025-83758102 |
\`\`\``,

            test4: `\`\`\`
| 步骤 | 详细操作 |
|------|----------|
| 访问官网 | 打开江苏第二师范学院官网：[https://www.jssnu.edu.cn/](https://www.jssnu.edu.cn/) |
| 查找联系方式 | 点击"联系我们"或"招生就业"页面，记录联系电话或联系邮箱 |
| 联系学院电话 | 拨打联系电话：025-83758102 |
\`\`\``
        }

        // 渲染测试用例
        Object.keys(testCases).forEach(testId => {
            const resultElement = document.getElementById(testId + '-result')
            try {
                const html = marked.parse(testCases[testId])
                resultElement.innerHTML = html
            } catch (error) {
                resultElement.innerHTML = `<p style="color: red;">渲染失败: ${error.message}</p>`
            }
        })
    </script>
</body>
</html>
