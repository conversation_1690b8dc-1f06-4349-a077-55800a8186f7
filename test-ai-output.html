<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI输出测试</title>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-area { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .content { min-height: 200px; padding: 15px; background: #f9f9f9; border: 1px solid #ccc; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 4px; }
        .raw-content { background: #fff; border: 1px solid #ccc; padding: 10px; margin: 10px 0; white-space: pre-wrap; }
    </style>
</head>
<body>
    <div class="container">
        <h1>AI输出格式测试</h1>
        
        <div class="test-area">
            <h3>测试1：直接表格输出（正确格式）</h3>
            <div class="raw-content" id="raw1"></div>
            <div class="content" id="content1"></div>
        </div>
        
        <div class="test-area">
            <h3>测试2：代码块包裹的表格（AI错误输出）</h3>
            <div class="raw-content" id="raw2"></div>
            <div class="content" id="content2"></div>
        </div>
        
        <div class="test-area">
            <h3>测试3：markdown代码块包裹的表格</h3>
            <div class="raw-content" id="raw3"></div>
            <div class="content" id="content3"></div>
        </div>
    </div>

    <script>
        // 测试用例
        const testCases = [
            {
                name: "直接表格输出",
                content: `以下是步骤和建议：

| 步骤 | 详细操作 |
|------|----------|
| 访问官网 | 打开江苏第二师范学院官网 |
| 查找联系方式 | 点击联系我们页面 |
| 电话咨询 | 拨打招生办电话 |`
            },
            {
                name: "代码块包裹的表格",
                content: `以下是步骤和建议：

\`\`\`
| 步骤 | 详细操作 |
|------|----------|
| 访问官网 | 打开江苏第二师范学院官网 |
| 查找联系方式 | 点击联系我们页面 |
| 电话咨询 | 拨打招生办电话 |
\`\`\``
            },
            {
                name: "markdown代码块包裹的表格",
                content: `以下是步骤和建议：

\`\`\`markdown
| 步骤 | 详细操作 |
|------|----------|
| 访问官网 | 打开江苏第二师范学院官网 |
| 查找联系方式 | 点击联系我们页面 |
| 电话咨询 | 拨打招生办电话 |
\`\`\``
            }
        ]

        // 渲染测试用例
        testCases.forEach((testCase, index) => {
            const rawElement = document.getElementById(`raw${index + 1}`)
            const contentElement = document.getElementById(`content${index + 1}`)
            
            rawElement.textContent = testCase.content
            contentElement.innerHTML = marked.parse(testCase.content)
        })
    </script>
</body>
</html>
