var e,a;(()=>{function t(e,a,t,l,s,r,n){try{var u=e[r](n),o=u.value}catch(e){return void t(e)}u.done?a(o):Promise.resolve(o).then(l,s)}function l(e){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},l(e)}function s(e){var a=function(e){if("object"!=l(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var t=a.call(e,"string");if("object"!=l(t))return t;throw TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==l(a)?a:a+""}function r(e,a,t){return(a=s(a))in e?Object.defineProperty(e,a,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[a]=t,e}function n(e,a){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);a&&(l=l.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,l)}return t}e=function(e){return function(){var a=this,l=arguments;return new Promise(function(s,r){var n=e.apply(a,l);function u(e){t(n,s,r,u,o,"next",e)}function o(e){t(n,s,r,u,o,"throw",e)}u(void 0)})}},a=function(e){for(var a=1;a<arguments.length;a++){var t=null==arguments[a]?{}:arguments[a];a%2?n(Object(t),!0).forEach(function(a){r(e,a,t[a])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):n(Object(t)).forEach(function(a){Object.defineProperty(e,a,Object.getOwnPropertyDescriptor(t,a))})}return e}})();import{E as t,G as l,H as s,K as r,L as n,N as u,b as o,c,d as i,e as d,f as v,g as f,j as m,k as p,o as g,p as y,u as h,y as x}from"./elementPlus-Di4PDIm8.js";import{bB as b,bC as _,bD as w,bF as k,bK as j,bX as C,bf as z,bk as D,by as B,c0 as O,c8 as P,c9 as E,cT as S,cU as V,cW as M,cc as U,cd as L,ce as N,d8 as T,dB as F,dD as $,dF as R,dH as I,dN as K,dO as q,dU as G,d_ as H,dc as W,dd as X,de as A,df as J,dg as Q,dj as Y,dk as Z,dl as ee,dy as ae,ea as te,ed as le}from"./vendor-BJ-uKP15.js";import{b as se}from"./_plugin-vue_export-helper-CjD0mXop.js";import"./knowledgeBase-yaqAZvLB.js";import{b as re}from"./document-q3qKo7UT.js";import{b as ne}from"./knowledgeBase-Dn54ZAUS.js";const ue=M("document",()=>{const t=G([]),l=G(null),s=G(!1),r=(n=e(function*(e,l){s.value=!0;try{const s=a(a({},l),{},{_t:Date.now()}),r=yield re.getList(e,s);return t.value=r,{success:!0,data:r}}catch(n){var r;return{success:!1,message:(null===(r=n.response)||void 0===r||null===(r=r.data)||void 0===r?void 0:r.message)||"获取文档列表失败"}}finally{s.value=!1}}),function(e,a){return n.apply(this,arguments)});var n;const u=(o=e(function*(e){s.value=!0;try{const a=yield re.getDetail(e);return l.value=a,{success:!0,data:a}}catch(t){var a;return{success:!1,message:(null===(a=t.response)||void 0===a||null===(a=a.data)||void 0===a?void 0:a.message)||"获取文档详情失败"}}finally{s.value=!1}}),function(e){return o.apply(this,arguments)});var o;const c=(i=e(function*(e,a,l){s.value=!0;try{const s=yield re.batchUpload(e,a,l);return s.uploaded_documents&&s.uploaded_documents.length>0&&(t.value.unshift(...s.uploaded_documents),t.value=[...t.value]),{success:!0,data:s}}catch(n){var r;return{success:!1,message:(null===(r=n.response)||void 0===r||null===(r=r.data)||void 0===r?void 0:r.message)||n.message||"上传文档失败"}}finally{s.value=!1}}),function(e,a,t){return i.apply(this,arguments)});var i;const d=(v=e(function*(e){s.value=!0;try{var a;return yield re.delete(e),t.value=t.value.filter(a=>a.id!==e),(null===(a=l.value)||void 0===a?void 0:a.id)===e&&(l.value=null),t.value=[...t.value],{success:!0}}catch(n){var r;return{success:!1,message:(null===(r=n.response)||void 0===r||null===(r=r.data)||void 0===r?void 0:r.message)||"删除文档失败"}}finally{s.value=!1}}),function(e){return v.apply(this,arguments)});var v;const f=(m=e(function*(e){s.value=!0;try{const a=yield re.batchDelete({document_ids:e});return t.value=t.value.filter(a=>!e.includes(a.id)),{success:!0,data:a}}catch(l){var a;return{success:!1,message:(null===(a=l.response)||void 0===a||null===(a=a.data)||void 0===a?void 0:a.message)||"批量删除文档失败"}}finally{s.value=!1}}),function(e){return m.apply(this,arguments)});var m;const p=(g=e(function*(e){try{const a=yield re.download(e),l=window.URL.createObjectURL(a),s=document.createElement("a");s.href=l;const r=t.value.find(a=>a.id===e);return s.download=(null==r?void 0:r.filename)||`document_${e}`,document.body.appendChild(s),s.click(),document.body.removeChild(s),window.URL.revokeObjectURL(l),{success:!0}}catch(l){var a;return{success:!1,message:(null===(a=l.response)||void 0===a||null===(a=a.data)||void 0===a?void 0:a.message)||"下载文档失败"}}}),function(e){return g.apply(this,arguments)});var g;return{documents:t,currentDocument:l,loading:s,fetchDocuments:r,fetchDocument:u,uploadDocuments:c,deleteDocument:d,batchDeleteDocuments:f,downloadDocument:p,clearCurrentDocument:()=>{l.value=null}}}),oe={class:"space-y-6"},ce={class:"flex items-center space-x-2 text-sm"},ie={class:"text-gray-900 dark:text-gray-100 font-medium"},de={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},ve={class:"text-2xl font-bold text-gray-900 dark:text-gray-100 flex items-center"},fe={class:"mt-4 sm:mt-0 space-x-2"},me={class:"p-4 bg-white dark:bg-[#1e1e1e] rounded-lg border border-gray-200 dark:border-gray-800"},pe={class:"flex flex-col md:flex-row md:items-center md:justify-between space-y-2 md:space-y-0"},ge={class:"flex-1 md:max-w-md"},ye={key:0,class:"flex items-center space-x-3"},he={class:"text-sm text-gray-600 dark:text-gray-400"},xe={class:"bg-white dark:bg-[#1e1e1e] rounded-lg border border-gray-200/80 dark:border-zinc-800/80 overflow-hidden"},be={key:0,class:"text-center py-12"},_e={class:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2"},we={class:"text-gray-600 dark:text-gray-400 mb-6"},ke={class:"flex items-center space-x-3"},je={class:"font-medium text-gray-900 dark:text-gray-100"},Ce={class:"text-xs text-gray-500"},ze={class:"flex flex-col items-center space-y-1"},De={key:0,class:"flex flex-col items-center space-y-1"},Be={class:"text-gray-600 dark:text-gray-400 text-sm"},Oe={class:"text-gray-500 dark:text-gray-400 text-sm"},Pe={class:"flex items-center justify-center space-x-2"},Ee={key:0,class:"flex justify-center"},Se={class:"space-y-4"},Ve={key:0,class:"mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg"},Me={class:"flex items-center justify-between text-sm"},Ue={class:"text-blue-700 dark:text-blue-300"},Le={class:"text-blue-600 dark:text-blue-400"},Ne={key:1,class:"space-y-2"},Te={class:"flex-1"},Fe={class:"flex items-center justify-between mb-1"},$e={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},Re={key:0,class:"text-red-500 text-xs mt-1"},Ie={class:"dialog-footer"},Ke={class:"h-96 bg-gray-50 dark:bg-dark-700 rounded-lg flex items-center justify-center"},qe={class:"text-center"},Ge={class:"dialog-footer"};var He=se(ee({__name:"DocumentList",setup(a){const M=S(),ee=V(),se=ue(),He=ne(),We=G(!1),Xe=G(!1),Ae=G(!1),Je=G(""),Qe=G(1),Ye=G(20),Ze=G([]),ea=G(new Set),aa=G(!1),ta=G(!1),la=G(!1),sa=G(),ra=G([]),na=G([]),ua=G(),oa=G(null),ca=G(null),ia=G({name:""}),da={name:[{required:!0,message:"请输入文档名称",trigger:"blur"},{min:1,max:100,message:"名称长度在 1 到 100 个字符",trigger:"blur"}]},va=G(null),fa=W(()=>se.documents),ma=W(()=>{if(!Je.value)return fa.value;const e=Je.value.toLowerCase();return fa.value.filter(a=>a.filename.toLowerCase().includes(e))}),pa=W(()=>{const e=(Qe.value-1)*Ye.value;return ma.value.slice(e,e+Ye.value)}),ga=e=>{if(!e)return"未知";const a=new Date(e);return isNaN(a.getTime())?"无效日期":a.toLocaleDateString("zh-CN")},ya=e=>{if(!e||isNaN(e))return"未知";if(0===e)return"0 B";const a=Math.floor(Math.log(e)/Math.log(1024));return Math.round(e/Math.pow(1024,a)*100)/100+" "+["B","KB","MB","GB"][a]},ha=()=>{const e=ra.value.reduce((e,a)=>e+(a.raw?a.raw.size:0),0);return ya(e)},xa=e=>{var a;if(!e)return k;switch(null===(a=e.split(".").pop())||void 0===a?void 0:a.toLowerCase()){case"pdf":default:return b;case"doc":case"docx":return k;case"jpg":case"jpeg":case"png":case"gif":return C;case"mp4":case"avi":case"mov":return U;case"mp3":case"wav":return j}},ba=e=>{var a;if(!e)return"bg-gray-100 dark:bg-zinc-700 text-gray-600 dark:text-zinc-400";switch(null===(a=e.split(".").pop())||void 0===a?void 0:a.toLowerCase()){case"pdf":return"bg-red-500/10 text-red-500";case"doc":case"docx":return"bg-blue-500/10 text-blue-500";case"xls":case"xlsx":return"bg-emerald-500/10 text-emerald-500";case"ppt":case"pptx":return"bg-orange-500/10 text-orange-500";case"jpg":case"jpeg":case"png":case"gif":return"bg-purple-500/10 text-purple-500";default:return"bg-zinc-500/10 text-zinc-500"}},_a=e=>{if(!e)return"info";switch(e){case"completed":return"success";case"processing":return"warning";case"failed":return"danger";default:return"info"}},wa=e=>{if(!e)return"未知";switch(e){case"completed":return"已完成";case"processing":return"处理中";case"failed":return"失败";case"pending":return"等待处理";default:return"未知"}},ka=()=>{Qe.value=1},ja=e=>{Qe.value=e},Ca=e=>{Ze.value=e},za=()=>{Ze.value=[]},Da=(Ba=e(function*(e){c({message:"文档下载功能开发中...",type:"info"})}),function(e){return Ba.apply(this,arguments)});var Ba;const Oa=(Pa=e(function*(e){try{ea.value.add(e.id);const a=yield re.retry(e.id);c.success(a.message||"文档重新处理成功");const t=Number(M.params.id);t&&(yield Ha(t,!0))}catch(t){let e="重试失败";var a;t.response?500===t.response.status?e="服务器内部错误，请稍后重试或联系管理员":404===t.response.status?e="文档不存在或已被删除":403===t.response.status?e="没有权限操作此文档":(null===(a=t.response.data)||void 0===a?void 0:a.detail)&&(e=t.response.data.detail):e=t.request?"网络连接失败，请检查网络连接":t.message||"未知错误",c.error(e)}finally{ea.value.delete(e.id)}}),function(e){return Pa.apply(this,arguments)});var Pa;const Ea=(Sa=e(function*(e){try{yield o.confirm(`确定要删除文档"${e.filename}"吗？`,"确认删除",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning",confirmButtonClass:"el-button--danger"});const a=yield se.deleteDocument(e.id);if(a.success){c.success("文档删除成功");const e=Number(M.params.id);e&&(yield Ha(e,!0))}else c.error(a.message||"删除失败")}catch(a){}}),function(e){return Sa.apply(this,arguments)});var Sa;const Va=(Ma=e(function*(){if(0!==Ze.value.length)try{c.success(`开始打包下载 ${Ze.value.length} 个文档`)}catch(e){c.error("打包下载失败")}}),function(){return Ma.apply(this,arguments)});var Ma;const Ua=(La=e(function*(){if(0!==Ze.value.length)try{yield o.confirm(`确定要删除选中的 ${Ze.value.length} 个文档吗？`,"确认批量删除",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning",confirmButtonClass:"el-button--danger"});const e=Ze.value.map(e=>e.id),a=yield se.batchDeleteDocuments(e);if(a.success){Ze.value=[],c.success("批量删除成功");const e=Number(M.params.id);e&&(yield Ha(e))}else c.error(a.message||"批量删除失败")}catch(e){}}),function(){return La.apply(this,arguments)});var La;const Na=(Ta=e(function*(){if(ua.value&&oa.value)try{yield ua.value.validate(),Ae.value=!0,c.success("重命名成功"),la.value=!1}catch(e){c.error("重命名失败")}finally{Ae.value=!1}}),function(){return Ta.apply(this,arguments)});var Ta;const Fa=(e,a)=>{if(a.length>5){c.error("批量上传最多支持5个文件，请重新选择");const e=a.slice(0,5);return ra.value=e,void(sa.value&&(sa.value.clearFiles(),e.forEach(e=>{e.raw&&sa.value.handleStart(e.raw)})))}const t=314572800,l=a.filter(e=>e.raw&&e.raw.size>t);if(l.length>0){const e=l.map(e=>e.name).join(", ");c.error(`文件太大，请选择较小的文件: ${e}。单个文件不超过 300MB`);const s=a.filter(e=>!e.raw||e.raw.size<=t);return ra.value=s,void(sa.value&&(sa.value.clearFiles(),s.forEach(e=>{e.raw&&sa.value.handleStart(e.raw)})))}const s=a.reduce((e,a)=>e+(a.raw?a.raw.size:0),0);if(s>t){const e=Math.round(s/1048576);return c.error(`所选文件总大小为 ${e}MB，超过300MB限制，请重新选择`),ra.value=[],void(sa.value&&sa.value.clearFiles())}const r=fa.value.map(e=>e.filename),n=a.filter(e=>r.includes(e.name));if(n.length>0){const e=n.map(e=>e.name).join(", ");o.confirm(`检测到同名文件: ${e}。是否继续上传？上传后将覆盖原文件。`,"同名文件警告",{confirmButtonText:"继续上传",cancelButtonText:"取消",type:"warning"}).then(()=>{ra.value=a}).catch(()=>{const e=a.filter(e=>!r.includes(e.name));ra.value=e,sa.value&&(sa.value.clearFiles(),e.forEach(e=>{sa.value.handleStart(e.raw)}))})}else ra.value=a},$a=(e,a)=>{ra.value=a},Ra=(Ia=e(function*(){if(0===ra.value.length)return;const e=parseInt(M.params.id);if(e){Xe.value=!0,na.value=[];try{const t=ra.value.map(e=>e.raw).filter(Boolean);if(0===t.length)return void c.error("没有有效的文件");t.forEach(e=>{const a={id:Date.now()+Math.random(),name:e.name,progress:0,status:"uploading"};na.value.push(a)});const l=e=>{na.value.forEach(a=>{"uploading"===a.status&&(a.progress=e)})},s=yield se.uploadDocuments(e,t,l);if(s.success&&s.data){var a;na.value.forEach(e=>{"uploading"===e.status&&(e.progress=100,e.status="success")});const t=(null===(a=s.data.uploaded_documents)||void 0===a?void 0:a.length)||0,l=s.data.failed_uploads||[];l.length>0&&(l.forEach(e=>{const a=na.value.find(a=>a.name===e.filename);a&&(a.status="error",a.error=e.error,a.progress=0)}),c.warning(`${l.length} 个文件上传失败`)),t>0&&c.success(`成功上传 ${t} 个文档，正在后台处理中...`),setTimeout(()=>{aa.value=!1,Ka(),Ha(e,!0)},1e3)}else na.value.forEach(e=>{e.status="error",e.error=s.message}),c.error(s.message||"上传失败")}catch(t){na.value.forEach(e=>{e.status="error",e.error="上传异常"}),c.error("上传失败: "+(t.message||"未知错误"))}finally{Xe.value=!1}}else c.error("知识库ID无效")}),function(){return Ia.apply(this,arguments)});var Ia;const Ka=()=>{ra.value=[],na.value=[],sa.value&&sa.value.clearFiles()},qa=(Ga=e(function*(e){try{const a=yield He.fetchKnowledgeBase(e);a.success&&a.data?va.value=a.data:c.error(a.message||"获取知识库信息失败")}catch(a){c.error("获取知识库信息失败")}}),function(e){return Ga.apply(this,arguments)});var Ga;const Ha=(Wa=e(function*(e,a=!1){try{a&&(se.documents=[]);const t=yield se.fetchDocuments(e);t.success||c.error(t.message||"获取文档列表失败")}catch(t){c.error("获取文档列表失败")}}),function(e){return Wa.apply(this,arguments)});var Wa;return ae(()=>{const e=Number(M.params.id);e&&(qa(e),Ha(e))}),(e,a)=>{var o;const c=R("router-link"),b=s,k=l,j=x,C=u,S=t,V=n,M=f,U=r,G=v,W=p,ae=d,se=m,re=h,ne=R("Document"),ue=y,He=g,fa=i;return F(),Q("div",oe,[X("div",ce,[Z(k,{separator:"/"},{default:K(()=>[Z(b,null,{default:K(()=>[Z(c,{to:"/user/knowledge-base",class:"text-blue-600 hover:text-blue-800"},{default:K(()=>a[11]||(a[11]=[Y(" 我的知识库 ",-1)])),_:1,__:[11]})]),_:1}),Z(b,null,{default:K(()=>{var e;return[X("span",ie,le((null===(e=va.value)||void 0===e?void 0:e.name)||"知识库"),1)]}),_:1})]),_:1})]),X("div",de,[X("div",null,[X("h1",ve,[a[12]||(a[12]=X("span",null,"文档管理",-1)),va.value?(F(),A(j,{key:0,class:"ml-3",type:"info",size:"small"},{default:K(()=>[Y(le(va.value.name),1)]),_:1})):J("",!0)]),a[13]||(a[13]=X("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," 管理知识库中的文档 ",-1))]),X("div",fe,[Z(S,{onClick:a[0]||(a[0]=e=>H(ee).back()),class:"bg-gray-200 text-gray-800 dark:bg-zinc-700 dark:text-gray-300 dark:border-zinc-600 hover:bg-gray-300 dark:hover:bg-zinc-600"},{default:K(()=>[Z(C,{class:"mr-1"},{default:K(()=>[Z(H(z))]),_:1}),a[14]||(a[14]=Y(" 返回 ",-1))]),_:1,__:[14]}),Z(S,{type:"primary",onClick:a[1]||(a[1]=e=>aa.value=!0),class:"bg-emerald-600 text-white border-0 hover:bg-emerald-700"},{default:K(()=>[Z(C,{class:"mr-1"},{default:K(()=>[Z(H(P))]),_:1}),a[15]||(a[15]=Y(" 上传文档 ",-1))]),_:1,__:[15]})])]),X("div",me,[X("div",pe,[X("div",ge,[Z(V,{modelValue:Je.value,"onUpdate:modelValue":a[2]||(a[2]=e=>Je.value=e),placeholder:"搜索文档...",clearable:"",onInput:ka},{prefix:K(()=>[Z(C,null,{default:K(()=>[Z(H(O))]),_:1})]),_:1},8,["modelValue"])]),Ze.value.length>0?(F(),Q("div",ye,[X("span",he," 已选择 "+le(Ze.value.length)+" 个文档 ",1),Z(S,{size:"small",onClick:Va},{default:K(()=>[Z(C,{class:"mr-1"},{default:K(()=>[Z(H(_))]),_:1}),a[16]||(a[16]=Y(" 打包下载 ",-1))]),_:1,__:[16]}),Z(S,{size:"small",type:"danger",onClick:Ua},{default:K(()=>[Z(C,{class:"mr-1"},{default:K(()=>[Z(H(B))]),_:1}),a[17]||(a[17]=Y(" 批量删除 ",-1))]),_:1,__:[17]}),Z(S,{size:"small",onClick:za},{default:K(()=>a[18]||(a[18]=[Y(" 取消选择 ",-1)])),_:1,__:[18]})])):J("",!0)])]),q((F(),Q("div",xe,[0!==ma.value.length||We.value?(F(),A(G,{key:1,data:pa.value,style:{width:"100%"},onSelectionChange:Ca},{default:K(()=>[Z(M,{type:"selection",width:"55"}),Z(M,{prop:"filename",label:"文件名","min-width":"250"},{default:K(({row:e})=>[X("div",ke,[X("div",{class:te(["w-8 h-8 rounded-lg flex items-center justify-center",ba(e.filename)])},[Z(C,{size:16},{default:K(()=>[(F(),A(I(xa(e.filename))))]),_:2},1024)],2),X("div",null,[X("div",je,le(e.originalName||e.filename),1),X("div",Ce,le(e.filename),1)])])]),_:1}),Z(M,{prop:"status",label:"状态",width:"160",align:"center"},{default:K(({row:e})=>[X("div",ze,[Z(j,{type:_a(e.status),size:"small"},{default:K(()=>[Y(le(wa(e.status)),1)]),_:2},1032,["type"]),"failed"===e.status?(F(),Q("div",De,[e.error_message?(F(),A(U,{key:0,content:e.error_message,placement:"top","show-after":500},{default:K(()=>[Z(C,{class:"text-red-500 cursor-help",size:14},{default:K(()=>[Z(H(N))]),_:1})]),_:2},1032,["content"])):J("",!0),Z(S,{size:"small",type:"primary",plain:"",onClick:a=>Oa(e),loading:ea.value.has(e.id),class:"text-xs px-2 py-1"},{default:K(()=>a[20]||(a[20]=[Y(" 重试 ",-1)])),_:2,__:[20]},1032,["onClick","loading"])])):J("",!0)])]),_:1}),Z(M,{prop:"file_size",label:"大小",width:"100",align:"right"},{default:K(({row:e})=>[X("span",Be,le(ya(e.file_size)),1)]),_:1}),Z(M,{prop:"created_at",label:"上传日期",width:"150"},{default:K(({row:e})=>[X("span",Oe,le(ga(e.created_at)),1)]),_:1}),Z(M,{label:"操作",width:"200",align:"center"},{default:K(({row:e})=>[X("div",Pe,[Z(S,{size:"small",onClick:a=>(ca.value=e,void(ta.value=!0))},{default:K(()=>[Z(C,null,{default:K(()=>[Z(H(L))]),_:1})]),_:2},1032,["onClick"]),Z(S,{size:"small",onClick:a=>Da(e)},{default:K(()=>[Z(C,null,{default:K(()=>[Z(H(_))]),_:1})]),_:2},1032,["onClick"]),Z(S,{size:"small",onClick:a=>{return oa.value=t=e,ia.value.name=t.filename,void(la.value=!0);var t}},{default:K(()=>[Z(C,null,{default:K(()=>[Z(H(w))]),_:1})]),_:2},1032,["onClick"]),Z(S,{size:"small",type:"danger",onClick:a=>Ea(e)},{default:K(()=>[Z(C,null,{default:K(()=>[Z(H(B))]),_:1})]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data"])):(F(),Q("div",be,[Z(C,{size:64,class:"text-gray-400 mb-4"},{default:K(()=>[Z(H(D))]),_:1}),X("h3",_e,le(Je.value?"未找到匹配的文档":"还没有文档"),1),X("p",we,le(Je.value?"尝试调整搜索条件":"上传您的第一个文档来开始构建知识库"),1),Je.value?J("",!0):(F(),A(S,{key:0,type:"primary",onClick:a[3]||(a[3]=e=>aa.value=!0),class:"bg-emerald-600 text-white border-0 hover:bg-emerald-700 shadow-lg"},{default:K(()=>[Z(C,{class:"mr-2"},{default:K(()=>[Z(H(P))]),_:1}),a[19]||(a[19]=Y(" 上传文档 ",-1))]),_:1,__:[19]}))]))])),[[fa,We.value]]),ma.value.length>Ye.value?(F(),Q("div",Ee,[Z(W,{"current-page":Qe.value,"onUpdate:currentPage":a[4]||(a[4]=e=>Qe.value=e),"page-size":Ye.value,total:ma.value.length,layout:"prev, pager, next, jumper, total",onCurrentChange:ja},null,8,["current-page","page-size","total"])])):J("",!0),Z(re,{modelValue:aa.value,"onUpdate:modelValue":a[6]||(a[6]=e=>aa.value=e),title:"上传文档",width:"600px",onClose:Ka,class:"custom-dialog"},{footer:K(()=>[X("div",Ie,[Z(S,{onClick:a[5]||(a[5]=e=>aa.value=!1)},{default:K(()=>a[25]||(a[25]=[Y("取消",-1)])),_:1,__:[25]}),Z(S,{type:"primary",onClick:Ra,loading:Xe.value,disabled:0===ra.value.length},{default:K(()=>a[26]||(a[26]=[Y(" 开始上传 ",-1)])),_:1,__:[26]},8,["loading","disabled"])])]),default:K(()=>[X("div",Se,[Z(ae,{ref_key:"uploadRef",ref:sa,class:"upload-demo",drag:"",multiple:"","auto-upload":!1,"on-change":Fa,"on-remove":$a,"file-list":ra.value,accept:".pdf,.doc,.docx,.txt,.md,.ppt,.pptx,.xls,.xlsx"},{tip:K(()=>a[21]||(a[21]=[X("div",{class:"el-upload__tip"},[Y(" 支持 PDF、Word、PowerPoint、Excel、Markdown、文本文件"),X("br"),Y(" 批量上传：最多5个文件，总大小不超过300MB，单个文件不超过300MB ")],-1)])),default:K(()=>[Z(C,{class:"el-icon--upload"},{default:K(()=>[Z(H(E))]),_:1}),a[22]||(a[22]=X("div",{class:"el-upload__text"},[Y(" 将文件拖到此处，或"),X("em",null,"点击上传")],-1))]),_:1,__:[22]},8,["file-list"]),ra.value.length>0&&0===na.value.length?(F(),Q("div",Ve,[X("div",Me,[X("span",Ue," 已选择 "+le(ra.value.length)+"/5 个文件 ",1),X("span",Le," 总大小: "+le(ha()),1)])])):J("",!0),na.value.length>0?(F(),Q("div",Ne,[a[24]||(a[24]=X("h4",{class:"font-medium text-gray-900 dark:text-gray-100"},"上传进度",-1)),(F(!0),Q(T,null,$(na.value,e=>(F(),Q("div",{key:e.id,class:"flex items-center space-x-3 p-3 bg-gray-50 dark:bg-dark-700 rounded-lg"},[X("div",Te,[X("div",Fe,[X("span",$e,le(e.name),1),X("span",{class:te(["text-sm",{"text-gray-500":"uploading"===e.status,"text-blue-500":"processing"===e.status,"text-green-500":"success"===e.status,"text-red-500":"error"===e.status}])},["uploading"===e.status?(F(),Q(T,{key:0},[Y(le(e.progress)+"%",1)],64)):"processing"===e.status?(F(),Q(T,{key:1},[Y("处理中...")],64)):"success"===e.status?(F(),Q(T,{key:2},[Y("✓ 完成")],64)):"error"===e.status?(F(),Q(T,{key:3},[Y("✗ 失败")],64)):J("",!0)],2)]),Z(se,{percentage:e.progress,status:"error"===e.status?"exception":"success"===e.status?"success":void 0,"show-text":!1,"stroke-width":6},null,8,["percentage","status"]),"error"===e.status&&e.error?(F(),Q("div",Re,le(e.error),1)):J("",!0)]),"error"===e.status?(F(),A(S,{key:0,size:"small",type:"danger",onClick:a=>(e=>{const a=na.value.findIndex(a=>a.id===e);-1!==a&&na.value.splice(a,1)})(e.id)},{default:K(()=>a[23]||(a[23]=[Y(" 移除 ",-1)])),_:2,__:[23]},1032,["onClick"])):J("",!0)]))),128))])):J("",!0)])]),_:1},8,["modelValue"]),Z(re,{modelValue:ta.value,"onUpdate:modelValue":a[7]||(a[7]=e=>ta.value=e),title:(null===(o=ca.value)||void 0===o?void 0:o.filename)||"文档预览",width:"80%",top:"5vh",class:"custom-dialog"},{default:K(()=>[X("div",Ke,[X("div",qe,[Z(C,{size:48,class:"text-gray-400 mb-4"},{default:K(()=>[Z(ne)]),_:1}),a[27]||(a[27]=X("p",{class:"text-gray-600 dark:text-gray-400"}," 文档预览功能开发中... ",-1))])])]),_:1},8,["modelValue","title"]),Z(re,{modelValue:la.value,"onUpdate:modelValue":a[10]||(a[10]=e=>la.value=e),title:"重命名文档",width:"400px",class:"custom-dialog"},{footer:K(()=>[X("div",Ge,[Z(S,{onClick:a[9]||(a[9]=e=>la.value=!1)},{default:K(()=>a[28]||(a[28]=[Y("取消",-1)])),_:1,__:[28]}),Z(S,{type:"primary",onClick:Na,loading:Ae.value},{default:K(()=>a[29]||(a[29]=[Y(" 确认 ",-1)])),_:1,__:[29]},8,["loading"])])]),default:K(()=>[Z(He,{model:ia.value,rules:da,ref_key:"renameFormRef",ref:ua},{default:K(()=>[Z(ue,{label:"新名称",prop:"name"},{default:K(()=>[Z(V,{modelValue:ia.value.name,"onUpdate:modelValue":a[8]||(a[8]=e=>ia.value.name=e),placeholder:"请输入新的文档名称",maxlength:"100","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-97e42673"]]);export{He as default};