<template>
  <!-- 页面加载状态 -->
  <div v-if="pageLoading" class="h-full flex items-center justify-center">
    <div class="text-center">
      <el-icon :size="48" class="text-blue-500 animate-spin mb-4">
        <Loading />
      </el-icon>
      <p class="text-gray-600 dark:text-gray-400">正在加载操作日志...</p>
    </div>
  </div>

  <!-- 主要内容 -->
  <div v-else class="space-y-6">
    <!-- 页面头部 -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">
          操作日志
        </h1>
        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
          查看系统管理员的所有操作记录
        </p>
      </div>
      <div class="mt-4 sm:mt-0">
        <el-button @click="loadLogs" :loading="loading">
          <el-icon class="mr-2"><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
        <!-- 操作类型筛选 -->
        <div class="flex items-center space-x-4">
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300">操作类型：</span>
          <el-select v-model="actionFilter" placeholder="选择操作类型" style="width: 200px" @change="loadLogs">
            <el-option label="全部操作" value="" />
            <el-option label="创建用户" value="create_user" />
            <el-option label="更新用户" value="update_user" />
            <el-option label="删除用户" value="delete_user" />
            <el-option label="更新配额" value="update_user_quota" />
            <el-option label="更新AI模型" value="update_ai_model" />
            <el-option label="更新系统设置" value="update_system_setting" />
          </el-select>
        </div>

        <!-- 分页设置 -->
        <div class="flex items-center space-x-4">
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300">每页显示：</span>
          <el-select v-model="pageSize" style="width: 100px" @change="loadLogs">
            <el-option label="10" :value="10" />
            <el-option label="20" :value="20" />
            <el-option label="50" :value="50" />
            <el-option label="100" :value="100" />
          </el-select>
        </div>
      </div>
    </div>

    <!-- 操作日志列表 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow" v-loading="loading">
      <el-table :data="logs" style="width: 100%">
        <el-table-column prop="created_at" label="时间" width="180">
          <template #default="{ row }">
            <span class="text-gray-600 dark:text-gray-400 text-sm">
              {{ formatDate(row.created_at) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="action" label="操作" width="150">
          <template #default="{ row }">
            <el-tag :type="getActionType(row.action)" size="small">
              {{ getActionText(row.action) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="target_type" label="目标类型" width="120">
          <template #default="{ row }">
            <span class="text-gray-600 dark:text-gray-400">
              {{ getTargetTypeText(row.target_type) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="target_id" label="目标ID" width="100" align="center">
          <template #default="{ row }">
            <span class="text-gray-600 dark:text-gray-400">
              {{ row.target_id || '-' }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="details" label="详细信息" min-width="300">
          <template #default="{ row }">
            <div class="text-sm">
              <div v-if="row.details" class="text-gray-600 dark:text-gray-400">
                <pre class="whitespace-pre-wrap text-xs">{{ formatDetails(row.details) }}</pre>
              </div>
              <div v-else class="text-gray-400">
                无详细信息
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="ip_address" label="IP地址" width="140">
          <template #default="{ row }">
            <span class="text-gray-600 dark:text-gray-400 text-sm">
              {{ row.ip_address || '-' }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="100" align="center">
          <template #default="{ row }">
            <el-button size="small" @click="viewDetails(row)">
              <el-icon><View /></el-icon>
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="p-4 border-t border-gray-200 dark:border-gray-700">
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="totalLogs"
          layout="total, prev, pager, next, jumper"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="showDetailsDialog"
      title="操作详情"
      width="700px"
    >
      <div v-if="selectedLog" class="space-y-4">
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              操作时间
            </label>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              {{ formatDate(selectedLog.created_at) }}
            </div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              操作类型
            </label>
            <el-tag :type="getActionType(selectedLog.action)" size="small">
              {{ getActionText(selectedLog.action) }}
            </el-tag>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              目标类型
            </label>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              {{ getTargetTypeText(selectedLog.target_type) }}
            </div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              目标ID
            </label>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              {{ selectedLog.target_id || '-' }}
            </div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              操作用户ID
            </label>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              {{ selectedLog.user_id }}
            </div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              IP地址
            </label>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              {{ selectedLog.ip_address || '-' }}
            </div>
          </div>
        </div>
        
        <div v-if="selectedLog.details">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            详细信息
          </label>
          <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <pre class="text-sm text-gray-600 dark:text-gray-400 whitespace-pre-wrap">{{ formatDetails(selectedLog.details) }}</pre>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDetailsDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Refresh,
  View,
  Loading
} from '@element-plus/icons-vue'
import { adminAPI, type OperationLog } from '@/api/admin'

// 状态管理
const pageLoading = ref(true)
const loading = ref(false)

// 数据
const logs = ref<OperationLog[]>([])
const totalLogs = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const actionFilter = ref('')

// 对话框状态
const showDetailsDialog = ref(false)
const selectedLog = ref<OperationLog | null>(null)

// 工具函数
const getActionText = (action: string) => {
  const actionMap: Record<string, string> = {
    create_user: '创建用户',
    update_user: '更新用户',
    delete_user: '删除用户',
    update_user_quota: '更新配额',
    update_ai_model: '更新AI模型',
    update_system_setting: '更新系统设置'
  }
  return actionMap[action] || action
}

const getActionType = (action: string) => {
  if (action.includes('create')) return 'success'
  if (action.includes('delete')) return 'danger'
  if (action.includes('update')) return 'warning'
  return 'info'
}

const getTargetTypeText = (targetType: string) => {
  const typeMap: Record<string, string> = {
    user: '用户',
    user_quota: '用户配额',
    ai_model: 'AI模型',
    system_setting: '系统设置'
  }
  return typeMap[targetType] || targetType
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const formatDetails = (details: string) => {
  try {
    const parsed = JSON.parse(details)
    return JSON.stringify(parsed, null, 2)
  } catch {
    return details
  }
}

// 加载日志
const loadLogs = async () => {
  try {
    loading.value = true
    const params = {
      offset: (currentPage.value - 1) * pageSize.value,
      limit: pageSize.value,
      action_filter: actionFilter.value || undefined
    }
    
    logs.value = await adminAPI.getLogs(params)
    // 注意：这里假设API返回了总数，实际可能需要调整
    totalLogs.value = logs.value.length >= pageSize.value ? 1000 : logs.value.length
  } catch (error) {
    console.error('加载操作日志失败:', error)
    ElMessage.error('加载操作日志失败')
  } finally {
    loading.value = false
  }
}

// 分页处理
const handlePageChange = (page: number) => {
  currentPage.value = page
  loadLogs()
}

// 查看详情
const viewDetails = (log: OperationLog) => {
  selectedLog.value = log
  showDetailsDialog.value = true
}

// 页面初始化
onMounted(async () => {
  try {
    await loadLogs()
  } finally {
    pageLoading.value = false
  }
})
</script>
