# 图表组件

这个目录包含了基于 ECharts 的可复用图表组件。

## 组件列表

### BaseChart.vue
基础图表组件，其他图表组件都基于此组件构建。

**Props:**
- `option`: ECharts 配置对象
- `width`: 图表宽度 (默认: '100%')
- `height`: 图表高度 (默认: '300px')
- `theme`: 主题 ('light' | 'dark', 默认: 'light')

### PieChart.vue
饼图组件

**Props:**
- `data`: 数据数组 `{ name: string, value: number, color?: string }[]`
- `title`: 图表标题
- `showLegend`: 是否显示图例 (默认: true)
- `radius`: 饼图半径 (默认: ['40%', '70%'])

**使用示例:**
```vue
<PieChart 
  :data="[
    { name: '已使用', value: 320, color: '#3b82f6' },
    { name: '可用空间', value: 680, color: '#e5e7eb' }
  ]" 
  title="存储使用情况"
  height="300px"
/>
```

### LineChart.vue
折线图组件

**Props:**
- `data`: 数据数组 `{ name: string, data: number[], color?: string, smooth?: boolean }[]`
- `xAxisData`: X轴数据 `string[]`
- `title`: 图表标题
- `yAxisName`: Y轴名称
- `showGrid`: 是否显示网格 (默认: true)

**使用示例:**
```vue
<LineChart 
  :data="[
    { name: '新用户', data: [120, 132, 101, 134], color: '#3b82f6' }
  ]"
  :x-axis-data="['1月', '2月', '3月', '4月']"
  title="用户增长趋势"
  y-axis-name="用户数"
/>
```

### BarChart.vue
柱状图组件

**Props:**
- `data`: 数据数组 `{ name: string, data: number[], color?: string }[]`
- `xAxisData`: X轴数据 `string[]`
- `title`: 图表标题
- `yAxisName`: Y轴名称
- `horizontal`: 是否为水平柱状图 (默认: false)
- `showGrid`: 是否显示网格 (默认: true)

**使用示例:**
```vue
<BarChart 
  :data="[
    { name: '本月', data: [120, 200, 150, 80], color: '#3b82f6' }
  ]"
  :x-axis-data="['知识库', '文档', '聊天', '用户']"
  title="数据统计"
  y-axis-name="数量"
/>
```

## 主题支持

所有图表组件都支持明暗主题切换：
- `theme="light"`: 浅色主题
- `theme="dark"`: 深色主题

主题会自动调整文字颜色、背景色、网格线颜色等。

## 响应式设计

所有图表组件都支持响应式设计，会自动适应容器大小变化。

## 演示页面

访问 `/chart-demo` 路由可以查看所有图表组件的演示效果。
