var e;(()=>{function a(e,a,l,t,s,r,d){try{var u=e[r](d),n=u.value}catch(e){return void l(e)}u.done?a(n):Promise.resolve(n).then(t,s)}e=function(e){return function(){var l=this,t=arguments;return new Promise(function(s,r){var d=e.apply(l,t);function u(e){a(d,s,r,u,n,"next",e)}function n(e){a(d,s,r,u,n,"throw",e)}u(void 0)})}}})();import{E as a,F as l,L as t,N as s,b as r,c as d,d as u,f as n,g as o,k as i,l as c,m,o as v,p,q as g,r as f,s as y,u as x}from"./elementPlus-Di4PDIm8.js";import{bB as _,bD as b,bJ as h,bN as w,bO as k,bU as C,bY as j,bk as B,bt as V,by as z,c0 as U,c2 as D,c3 as T,cU as $,d6 as L,d8 as K,dB as P,dD as F,dL as M,dN as N,dO as q,dU as A,d_ as I,dc as O,dd as E,de as G,df as J,dg as Q,dj as R,dk as Y,dl as H,dy as S,ed as W}from"./vendor-BJ-uKP15.js";import{c as X}from"./index-Byt5TjPh.js";import{b as Z}from"./_plugin-vue_export-helper-CjD0mXop.js";import"./knowledgeBase-yaqAZvLB.js";import{b as ee}from"./knowledgeBase-Dn54ZAUS.js";const ae={key:0,class:"h-full flex items-center justify-center bg-white dark:bg-[#121212]"},le={class:"text-center"},te={key:1,class:"space-y-6 bg-white dark:bg-[#121212] p-6 rounded-lg"},se={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},re={key:0,class:"mt-2 flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400"},de={class:"mt-4 sm:mt-0"},ue={class:"bg-white dark:bg-[#1e1e1e] p-6 rounded-lg border border-gray-200/80 dark:border-zinc-800/80"},ne={class:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4"},oe={class:"flex-1 max-w-md"},ie={class:"flex items-center space-x-2"},ce={key:0,class:"text-center py-12"},me={class:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2"},ve={class:"text-gray-600 dark:text-gray-400 mb-6"},pe={key:1,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},ge=["onClick"],fe={class:"flex items-start justify-between mb-4"},ye={class:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2 group-hover:text-emerald-500 dark:group-hover:text-emerald-400 transition-colors"},xe={class:"text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-2"},_e={class:"flex items-center justify-between text-sm text-gray-500 dark:text-gray-400"},be={class:"flex items-center space-x-4"},he={class:"flex items-center"},we={key:2,class:"bg-white dark:bg-[#1e1e1e] rounded-lg border border-gray-200/80 dark:border-zinc-800/80 overflow-hidden"},ke=["onClick"],Ce={class:"w-8 h-8 bg-zinc-800 rounded-lg flex items-center justify-center"},je={class:"font-medium text-gray-900 dark:text-gray-100 hover:text-emerald-500 dark:hover:text-emerald-400 transition-colors"},Be={class:"text-gray-600 dark:text-gray-400"},Ve={class:"text-gray-500 dark:text-gray-400 text-sm"},ze={key:0,class:"flex justify-center"},Ue={class:"dialog-footer"};var De=Z(H({__name:"KnowledgeBase",setup(H){const Z=$(),De=ee(),Te=A(!1),$e=A(!1),Le=A(!1),Ke=A(""),Pe=A("lastUpdated"),Fe=A("desc"),Me=A("grid"),Ne=A(1),qe=A(12),Ae=A(null),Ie=A(!1),Oe=A(null),Ee=A(),Ge=A({name:"",description:""}),Je={name:[{required:!0,message:"请输入知识库名称",trigger:"blur"},{min:2,max:50,message:"名称长度在 2 到 50 个字符",trigger:"blur"}]},Qe=O(()=>De.knowledgeBases),Re=O(()=>{let e=Qe.value||[];if(Ke.value){const a=Ke.value.toLowerCase();e=e.filter(e=>e.name.toLowerCase().includes(a)||e.description&&e.description.toLowerCase().includes(a))}return e.sort((e,a)=>{let l,t;switch(Pe.value){case"name":l=e.name.toLowerCase(),t=a.name.toLowerCase();break;case"createdAt":l=new Date(e.created_at).getTime(),t=new Date(a.created_at).getTime();break;default:l=new Date(e.updated_at).getTime(),t=new Date(a.updated_at).getTime()}return"asc"===Fe.value?l>t?1:-1:l<t?1:-1}),e}),Ye=O(()=>{const e=(Ne.value-1)*qe.value;return Re.value.slice(e,e+qe.value)}),He=O(()=>!!Ae.value&&(Ae.value.current_kbs||0)>=Ae.value.max_kbs),Se=e=>e<1024?`${e.toFixed(1)} MB`:`${(e/1024).toFixed(1)} GB`,We=e=>{const a=new Date(e),l=(new Date).getTime()-a.getTime(),t=Math.floor(l/6e4),s=Math.floor(l/36e5),r=Math.floor(l/864e5);return t<60?`${t} 分钟前`:s<24?`${s} 小时前`:`${r} 天前`},Xe=()=>{Ne.value=1},Ze=()=>{Ne.value=1},ea=e=>{Ne.value=e},aa=e=>{Z.push(`/user/knowledge-base/${e.id}/documents`)},la=(ta=e(function*(e,a){switch(e){case"edit":sa(a);break;case"delete":yield ra(a)}}),function(e,a){return ta.apply(this,arguments)});var ta;const sa=e=>{Oe.value=e,Ge.value={name:e.name,description:e.description||""},Ie.value=!0},ra=(da=e(function*(e){try{yield r.confirm(`确定要删除知识库"${e.name}"吗？此操作不可恢复。`,"确认删除",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning",confirmButtonClass:"el-button--danger"});const a=yield De.deleteKnowledgeBase(e.id);a.success?d.success("知识库删除成功"):d.error(a.message||"删除失败")}catch(a){}}),function(e){return da.apply(this,arguments)});var da;const ua=(na=e(function*(){if(Ee.value)try{if(yield Ee.value.validate(),Le.value=!0,Oe.value){const e=yield De.updateKnowledgeBase(Oe.value.id,{name:Ge.value.name,description:Ge.value.description});e.success?(d.success("知识库更新成功"),Ie.value=!1,oa()):d.error(e.message||"更新失败")}else{const e=yield De.createKnowledgeBase({name:Ge.value.name,description:Ge.value.description||""});e.success?(d.success("知识库创建成功"),Ie.value=!1,oa()):d.error(e.message||"创建失败")}}catch(e){d.error("操作失败，请重试")}finally{Le.value=!1}}),function(){return na.apply(this,arguments)});var na;const oa=()=>{Oe.value=null,Ge.value={name:"",description:""},Ee.value&&Ee.value.resetFields()};M(Ke,()=>{Ne.value=1});const ia=(ca=e(function*(){try{Ae.value=yield X.getUserQuota()}catch(e){}}),function(){return ca.apply(this,arguments)});var ca;const ma=()=>{var e;He.value?d.warning(`已达到知识库数量限制，最多可创建 ${null===(e=Ae.value)||void 0===e?void 0:e.max_kbs} 个知识库`):Ie.value=!0};return S(e(function*(){$e.value=!0;try{yield Promise.all([De.fetchKnowledgeBases(),ia()])}catch(e){d.error("加载知识库失败")}finally{$e.value=!1}})),(e,r)=>{const d=s,$=a,M=t,A=c,O=m,H=l,S=f,X=y,Z=g,ee=o,De=n,Qe=i,ta=p,sa=v,ra=x,da=u;return Te.value?(P(),Q("div",ae,[E("div",le,[Y(d,{size:48,class:"text-emerald-500 animate-spin mb-4"},{default:N(()=>[Y(I(k))]),_:1}),r[13]||(r[13]=E("p",{class:"text-gray-600 dark:text-gray-400"},"正在加载知识库数据...",-1))])])):(P(),Q("div",te,[E("div",se,[E("div",null,[r[14]||(r[14]=E("h1",{class:"text-2xl font-bold text-gray-900 dark:text-gray-100"}," 知识库管理 ",-1)),r[15]||(r[15]=E("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," 创建、管理和组织您的知识内容 ",-1)),Ae.value?(P(),Q("div",re,[E("span",null,"知识库: "+W(Ae.value.current_kbs||0)+"/"+W(Ae.value.max_kbs),1),E("span",null,"存储: "+W(Se(Ae.value.current_storage_mb||0))+"/"+W(Se(Ae.value.max_storage_mb)),1)])):J("",!0)]),E("div",de,[Y($,{type:"primary",onClick:ma,class:"bg-emerald-600 text-white border-0 hover:bg-emerald-700 shadow-lg",disabled:He.value},{default:N(()=>[Y(d,{class:"mr-2"},{default:N(()=>[Y(I(j))]),_:1}),r[16]||(r[16]=R(" 创建知识库 ",-1))]),_:1,__:[16]},8,["disabled"])])]),E("div",ue,[E("div",ne,[E("div",oe,[Y(M,{modelValue:Ke.value,"onUpdate:modelValue":r[0]||(r[0]=e=>Ke.value=e),placeholder:"搜索知识库...",clearable:"",onInput:Xe},{prefix:N(()=>[Y(d,null,{default:N(()=>[Y(I(U))]),_:1})]),_:1},8,["modelValue"])]),E("div",ie,[Y(O,{modelValue:Pe.value,"onUpdate:modelValue":r[1]||(r[1]=e=>Pe.value=e),placeholder:"排序方式",style:{width:"160px"},onChange:Ze},{default:N(()=>[Y(A,{label:"最近更新",value:"lastUpdated"}),Y(A,{label:"创建时间",value:"createdAt"}),Y(A,{label:"名称",value:"name"})]),_:1},8,["modelValue"]),Y(H,{class:"tech-button-group"},{default:N(()=>[Y($,{type:"desc"===Fe.value?"primary":"default",onClick:r[2]||(r[2]=e=>{Fe.value="desc",Ze()})},{default:N(()=>[Y(d,null,{default:N(()=>[Y(I(D))]),_:1})]),_:1},8,["type"]),Y($,{type:"asc"===Fe.value?"primary":"default",onClick:r[3]||(r[3]=e=>{Fe.value="asc",Ze()})},{default:N(()=>[Y(d,null,{default:N(()=>[Y(I(T))]),_:1})]),_:1},8,["type"])]),_:1}),Y(H,{class:"tech-button-group"},{default:N(()=>[Y($,{type:"grid"===Me.value?"primary":"default",onClick:r[4]||(r[4]=e=>Me.value="grid")},{default:N(()=>[Y(d,null,{default:N(()=>[Y(I(h))]),_:1})]),_:1},8,["type"]),Y($,{type:"list"===Me.value?"primary":"default",onClick:r[5]||(r[5]=e=>Me.value="list")},{default:N(()=>[Y(d,null,{default:N(()=>[Y(I(w))]),_:1})]),_:1},8,["type"])]),_:1})])])]),q((P(),Q("div",null,[0!==Re.value.length||$e.value?"grid"===Me.value?(P(),Q("div",pe,[(P(!0),Q(K,null,F(Ye.value,e=>(P(),Q("div",{key:e.id,class:"bg-white dark:bg-[#2a2a2a] p-6 rounded-xl border border-gray-200/50 dark:border-zinc-800 hover:border-emerald-500/50 hover:ring-2 hover:ring-emerald-500/20 transition-all duration-300 cursor-pointer group",onClick:a=>aa(e)},[E("div",fe,[Y(d,{size:32,class:"text-emerald-500"},{default:N(()=>[Y(I(V))]),_:1}),E("div",{onClick:r[7]||(r[7]=L(()=>{},["stop"]))},[Y(Z,{onCommand:a=>la(a,e),trigger:"click"},{dropdown:N(()=>[Y(X,null,{default:N(()=>[Y(S,{command:"edit"},{default:N(()=>[Y(d,null,{default:N(()=>[Y(I(b))]),_:1}),r[18]||(r[18]=R(" 编辑 ",-1))]),_:1,__:[18]}),Y(S,{command:"delete",divided:""},{default:N(()=>[Y(d,null,{default:N(()=>[Y(I(z))]),_:1}),r[19]||(r[19]=R(" 删除 ",-1))]),_:1,__:[19]})]),_:1})]),default:N(()=>[Y($,{circle:"",size:"small",class:"opacity-0 group-hover:opacity-100 transition-opacity"},{default:N(()=>[Y(d,null,{default:N(()=>[Y(I(C))]),_:1})]),_:1})]),_:2},1032,["onCommand"])])]),E("h3",ye,W(e.name),1),E("p",xe,W(e.description||"暂无描述"),1),E("div",_e,[E("div",be,[E("span",he,[Y(d,{class:"mr-1"},{default:N(()=>[Y(I(_))]),_:1}),r[20]||(r[20]=R(" 知识库 ",-1))])]),E("span",null,W(We(e.updated_at)),1)])],8,ge))),128))])):(P(),Q("div",we,[Y(De,{data:Ye.value,style:{width:"100%"}},{default:N(()=>[Y(ee,{prop:"name",label:"名称","min-width":"200"},{default:N(({row:e})=>[E("div",{class:"flex items-center space-x-3 cursor-pointer",onClick:a=>aa(e)},[E("div",Ce,[Y(d,{size:16,class:"text-emerald-500"},{default:N(()=>[Y(I(V))]),_:1})]),E("div",null,[E("div",je,W(e.name),1)])],8,ke)]),_:1}),Y(ee,{prop:"description",label:"描述","min-width":"300"},{default:N(({row:e})=>[E("span",Be,W(e.description||"暂无描述"),1)]),_:1}),Y(ee,{prop:"updated_at",label:"最后更新",width:"150"},{default:N(({row:e})=>[E("span",Ve,W(We(e.updated_at)),1)]),_:1}),Y(ee,{label:"操作",width:"120",align:"center"},{default:N(({row:e})=>[Y(Z,{onCommand:a=>la(a,e),trigger:"click"},{dropdown:N(()=>[Y(X,null,{default:N(()=>[Y(S,{command:"edit"},{default:N(()=>[Y(d,null,{default:N(()=>[Y(I(b))]),_:1}),r[21]||(r[21]=R(" 编辑 ",-1))]),_:1,__:[21]}),Y(S,{command:"delete",divided:""},{default:N(()=>[Y(d,null,{default:N(()=>[Y(I(z))]),_:1}),r[22]||(r[22]=R(" 删除 ",-1))]),_:1,__:[22]})]),_:1})]),default:N(()=>[Y($,{circle:"",size:"small"},{default:N(()=>[Y(d,null,{default:N(()=>[Y(I(C))]),_:1})]),_:1})]),_:2},1032,["onCommand"])]),_:1})]),_:1},8,["data"])])):(P(),Q("div",ce,[Y(d,{size:64,class:"text-gray-400 mb-4"},{default:N(()=>[Y(I(B))]),_:1}),E("h3",me,W(Ke.value?"未找到匹配的知识库":"还没有知识库"),1),E("p",ve,W(Ke.value?"尝试调整搜索条件":"创建您的第一个知识库来开始管理知识内容"),1),Ke.value?J("",!0):(P(),G($,{key:0,type:"primary",onClick:r[6]||(r[6]=e=>Ie.value=!0),class:"bg-emerald-600 text-white border-0 hover:bg-emerald-700 shadow-lg"},{default:N(()=>[Y(d,{class:"mr-2"},{default:N(()=>[Y(I(j))]),_:1}),r[17]||(r[17]=R(" 创建知识库 ",-1))]),_:1,__:[17]}))]))])),[[da,$e.value]]),Re.value.length>qe.value?(P(),Q("div",ze,[Y(Qe,{"current-page":Ne.value,"onUpdate:currentPage":r[8]||(r[8]=e=>Ne.value=e),"page-size":qe.value,total:Re.value.length,layout:"prev, pager, next, jumper, total",onCurrentChange:ea},null,8,["current-page","page-size","total"])])):J("",!0),Y(ra,{modelValue:Ie.value,"onUpdate:modelValue":r[12]||(r[12]=e=>Ie.value=e),title:Oe.value?"编辑知识库":"创建知识库",width:"500px",onClose:oa,class:"custom-dialog"},{footer:N(()=>[E("div",Ue,[Y($,{onClick:r[11]||(r[11]=e=>Ie.value=!1)},{default:N(()=>r[23]||(r[23]=[R("取消",-1)])),_:1,__:[23]}),Y($,{type:"primary",onClick:ua,loading:Le.value},{default:N(()=>[R(W(Oe.value?"更新":"创建"),1)]),_:1},8,["loading"])])]),default:N(()=>[Y(sa,{model:Ge.value,rules:Je,ref_key:"formRef",ref:Ee,"label-width":"80px"},{default:N(()=>[Y(ta,{label:"名称",prop:"name"},{default:N(()=>[Y(M,{modelValue:Ge.value.name,"onUpdate:modelValue":r[9]||(r[9]=e=>Ge.value.name=e),placeholder:"请输入知识库名称",maxlength:"50","show-word-limit":""},null,8,["modelValue"])]),_:1}),Y(ta,{label:"描述",prop:"description"},{default:N(()=>[Y(M,{modelValue:Ge.value.description,"onUpdate:modelValue":r[10]||(r[10]=e=>Ge.value.description=e),type:"textarea",placeholder:"请输入知识库描述（可选）",rows:4,maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])]))}}}),[["__scopeId","data-v-084a51b6"]]);export{De as default};