const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/Login-Rr38SpIB.js","assets/_plugin-vue_export-helper-CjD0mXop.js","assets/logo-D-wHJMD-.js","assets/vendor-BJ-uKP15.js","assets/elementPlus-Di4PDIm8.js","assets/rolldown-runtime-BaowUlwK.js","assets/elementPlus-DvU4m8M3.css","assets/vendor-B-oHczHB.css","assets/Login-hyiIEU4M.css","assets/Test-B3CvglEp.js","assets/APITest-B8ZU2JEv.js","assets/admin-BSai4urc.js","assets/chat-Cv_kC_4-.js","assets/document-q3qKo7UT.js","assets/knowledgeBase-yaqAZvLB.js","assets/stats-DK53Rw0v.js","assets/APITest-6sVocaCC.css","assets/ChartDemo-hsh5id6f.js","assets/BarChart-r5W9w9bw.js","assets/LineChart-Bgmbl9FC.js","assets/LineChart-Co1K24Z0.css","assets/ChartDemo-x1XGuNl0.css","assets/ChartHtmlDemo-BSq0_NLq.js","assets/HtmlPreview-ovS6yXAM.js","assets/HtmlPreview-s0EgSj9R.css","assets/ChartHtmlDemo-S22IEKz1.css","assets/TestPage-6f4dQ3RD.js","assets/UserLayout-CrXFmKWH.js","assets/settings-46b1LTsi.js","assets/api-D-gMiCJf.js","assets/ui-xhrfN-Sd.js","assets/Home-D_-GonMq.js","assets/KnowledgeBase-BD_0Zkud.js","assets/knowledgeBase-Dn54ZAUS.js","assets/KnowledgeBase-BS_ccXkp.css","assets/DocumentList-BaIVJdR5.js","assets/DocumentList-BfQfWYQv.css","assets/Chat-LinpgeUV.js","assets/aiModels-CcIQpxgt.js","assets/Chat-C3gC6R8K.css","assets/Settings-B7Sp6b_K.js","assets/AdminLayout-D-EL1K0V.js","assets/AdminLayout-OKWfGB6c.css","assets/Dashboard-BpCuDMMS.js","assets/Users-Lw6aox4U.js","assets/AIModels-BPCW1PAk.js","assets/OperationLogs-BiVYGpHh.js","assets/Settings-DAz5h4gc.js","assets/Logs-BZCHM1W5.js","assets/NotFound-B5rt9k-f.js","assets/settings-C73ubz75.js"])))=>i.map(i=>d[i]);
var e,t;(()=>{function r(e,t,r,o,n,s,a){try{var i=e[s](a),c=i.value}catch(e){return void r(e)}i.done?t(c):Promise.resolve(c).then(o,n)}function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function n(e){var t=function(e){if("object"!=o(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=o(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==o(t)?t:t+""}function s(e,t,r){return(t=n(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function a(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,o)}return r}e=function(e){return function(){var t=this,o=arguments;return new Promise(function(n,s){var a=e.apply(t,o);function i(e){r(a,n,s,i,c,"next",e)}function c(e){r(a,n,s,i,c,"throw",e)}i(void 0)})}},t=function(e){for(var t=1;t<arguments.length;t++){var r=null==arguments[t]?{}:arguments[t];t%2?a(Object(r),!0).forEach(function(t){s(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}})();import{c as r}from"./elementPlus-Di4PDIm8.js";import{Z as o,bz as n,cQ as s,cR as a,cS as i,cV as c,cW as u,cZ as l,dB as d,dF as m,dU as p,dc as h,dd as f,df as g,dg as _,dk as v,dl as y,ds as b,dx as P,dy as E}from"./vendor-BJ-uKP15.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver(e=>{for(const r of e)if("childList"===r.type)for(const e of r.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)}).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),t.credentials="use-credentials"===e.crossOrigin?"include":"anonymous"===e.crossOrigin?"omit":"same-origin",t}(e);fetch(e.href,t)}}();const A={},O=function(e,t,r){let o=Promise.resolve();if(t&&t.length>0){const e=document.getElementsByTagName("link"),s=document.querySelector("meta[property=csp-nonce]"),a=(null==s?void 0:s.nonce)||(null==s?void 0:s.getAttribute("nonce"));n=t.map(t=>{if((t=function(e){return"/"+e}(t))in A)return;A[t]=!0;const o=t.endsWith(".css"),n=o?'[rel="stylesheet"]':"";if(r)for(let r=e.length-1;r>=0;r--){const n=e[r];if(n.href===t&&(!o||"stylesheet"===n.rel))return}else if(document.querySelector(`link[href="${t}"]${n}`))return;const s=document.createElement("link");return s.rel=o?"stylesheet":"modulepreload",o||(s.as="script"),s.crossOrigin="",s.href=t,a&&s.setAttribute("nonce",a),document.head.appendChild(s),o?new Promise((e,r)=>{s.addEventListener("load",e),s.addEventListener("error",()=>r(new Error(`Unable to preload CSS for ${t}`)))}):void 0}),o=Promise.all(n.map(e=>Promise.resolve(e).then(e=>({status:"fulfilled",value:e}),e=>({status:"rejected",reason:e}))))}var n;function s(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return o.then(t=>{for(const e of t||[])"rejected"===e.status&&s(e.reason);return e().catch(s)})},j=s.create({baseURL:"/api",timeout:3e4,headers:{"Content-Type":"application/json","Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}}),w=s.create({baseURL:"/api",timeout:3e5,headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}});j.interceptors.request.use(e=>{var r;const o=k().token||localStorage.getItem("token");return o&&(e.headers.Authorization=`Bearer ${o}`),"get"===(null===(r=e.method)||void 0===r?void 0:r.toLowerCase())&&(e.params=t(t({},e.params),{},{_t:Date.now()})),e},e=>Promise.reject(e)),j.interceptors.response.use(e=>e.data,e=>{if(e.response){const{status:t,data:o}=e.response;switch(t){case 401:k().logout(),r.error("登录已过期，请重新登录"),"/login"!==window.location.pathname&&(window.location.href="/login");break;case 403:r.error("没有权限访问该资源");break;case 404:r.error("请求的资源不存在");break;case 422:o.detail?Array.isArray(o.detail)?r.error(o.detail[0].msg||"请求参数错误"):r.error(o.detail||"请求参数错误"):r.error("请求参数错误");break;case 500:r.error("服务器内部错误");break;default:r.error(o.detail||o.message||"请求失败")}}else r.error(e.request?"网络连接失败，请检查网络":"请求配置错误");return Promise.reject(e)}),w.interceptors.request.use(e=>{const t=k().token||localStorage.getItem("token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e)),w.interceptors.response.use(e=>e,e=>{if(e.response){const{status:t,data:o}=e.response;switch(t){case 413:r.error("文件太大，请选择较小的文件");break;case 408:case 504:r.error("文件上传超时，请重试或选择较小的文件");break;case 500:r.error("服务器处理文件时出错，请重试");break;default:r.error((null==o?void 0:o.detail)||(null==o?void 0:o.message)||"文件上传失败")}}else r.error("ECONNABORTED"===e.code?"文件上传超时，请重试或选择较小的文件":"网络连接失败，请检查网络");return Promise.reject(e)});var L=j;const I={login:e=>L.post("/auth/login",e),register:e=>L.post("/auth/register",e),getCurrentUser:()=>L.get("/auth/me"),updateProfile:e=>L.put("/auth/profile",e),changePassword:e=>L.put("/auth/password",e),getUserQuota:()=>L.get("/auth/quota"),logout:()=>L.post("/auth/logout")},k=u("auth",()=>{const t=p(null),r=p(null),o=p(!1),n=h(()=>!!r.value&&!!t.value),s=h(()=>{var e;return!0===(null===(e=t.value)||void 0===e?void 0:e.is_admin)}),a=h(()=>{var e;return!1===(null===(e=t.value)||void 0===e?void 0:e.is_admin)}),i=e=>{t.value=e,localStorage.setItem("user",JSON.stringify(e))},c=e=>{r.value=e,localStorage.setItem("token",e)},u=()=>{t.value=null,r.value=null,localStorage.removeItem("token"),localStorage.removeItem("user")},l=(d=e(function*(e){o.value=!0;try{u();const t=yield I.login(e),{access_token:r}=t;c(r);const o=yield I.getCurrentUser();return i(o),{success:!0,user:o}}catch(r){var t;return u(),{success:!1,message:(null===(t=r.response)||void 0===t||null===(t=t.data)||void 0===t?void 0:t.detail)||r.message||"登录失败"}}finally{o.value=!1}}),function(e){return d.apply(this,arguments)});var d;const m=(f=e(function*(e){o.value=!0;try{const t=yield I.register(e);return i(t),{success:!0,user:t}}catch(r){var t;return{success:!1,message:(null===(t=r.response)||void 0===t||null===(t=t.data)||void 0===t?void 0:t.detail)||"注册失败"}}finally{o.value=!1}}),function(e){return f.apply(this,arguments)});var f;const g=(_=e(function*(){try{try{yield I.logout()}catch(e){}u(),window.location.href="/login"}catch(e){u(),window.location.href="/login"}}),function(){return _.apply(this,arguments)});var _;const v=(y=e(function*(){if(!r.value)return!1;if(t.value)return!0;try{const e=yield I.getCurrentUser();return i(e),!0}catch(e){return u(),!1}}),function(){return y.apply(this,arguments)});var y;const b=(P=e(function*(e){o.value=!0;try{const t=yield I.updateProfile(e);return i(t),{success:!0,user:t}}catch(r){var t;return{success:!1,message:(null===(t=r.response)||void 0===t||null===(t=t.data)||void 0===t?void 0:t.detail)||"更新失败"}}finally{o.value=!1}}),function(e){return P.apply(this,arguments)});var P;const E=(A=e(function*(e,t){o.value=!0;try{return yield I.changePassword({current_password:e,new_password:t}),{success:!0,message:"密码修改成功"}}catch(n){var r;return{success:!1,message:(null===(r=n.response)||void 0===r||null===(r=r.data)||void 0===r?void 0:r.detail)||"密码修改失败"}}finally{o.value=!1}}),function(e,t){return A.apply(this,arguments)});var A;return(()=>{const e=localStorage.getItem("token"),o=localStorage.getItem("user");if(e&&o)try{r.value=e,t.value=JSON.parse(o)}catch(n){localStorage.removeItem("token"),localStorage.removeItem("user")}})(),{user:t,token:r,loading:o,isAuthenticated:n,isAdmin:s,isUser:a,setUser:i,setToken:c,clearAuth:u,login:l,register:m,logout:g,fetchUserInfo:v,updateProfile:b,changePassword:E}}),S=a({history:i("/"),routes:[{path:"/",redirect:"/login"},{path:"/login",name:"Login",component:()=>O(()=>import("./Login-Rr38SpIB.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8])),meta:{requiresAuth:!1}},{path:"/test",name:"Test",component:()=>O(()=>import("./Test-B3CvglEp.js"),__vite__mapDeps([9,3,4,5,6,7])),meta:{requiresAuth:!1}},{path:"/api-test",name:"APITest",component:()=>O(()=>import("./APITest-B8ZU2JEv.js"),__vite__mapDeps([10,1,3,4,5,6,7,11,12,13,14,15,16])),meta:{requiresAuth:!1}},{path:"/chart-demo",name:"ChartDemo",component:()=>O(()=>import("./ChartDemo-hsh5id6f.js"),__vite__mapDeps([17,1,3,4,5,6,7,18,19,20,21])),meta:{requiresAuth:!1}},{path:"/chart-html-demo",name:"ChartHtmlDemo",component:()=>O(()=>import("./ChartHtmlDemo-BSq0_NLq.js"),__vite__mapDeps([22,1,3,4,5,6,7,23,24,25])),meta:{requiresAuth:!1}},{path:"/test-page",name:"TestPage",component:()=>O(()=>import("./TestPage-6f4dQ3RD.js"),__vite__mapDeps([26,3,4,5,6,7,11])),meta:{requiresAuth:!0,requiresAdmin:!0}},{path:"/user",component:()=>O(()=>import("./UserLayout-CrXFmKWH.js"),__vite__mapDeps([27,2,3,4,5,6,7,28,29,30])),meta:{requiresAuth:!0,role:"user"},children:[{path:"",redirect:"/user/home"},{path:"home",name:"Home",component:()=>O(()=>import("./Home-D_-GonMq.js"),__vite__mapDeps([31,1,3,4,5,6,7,12,14,15,19,20]))},{path:"knowledge-base",name:"KnowledgeBase",component:()=>O(()=>import("./KnowledgeBase-BD_0Zkud.js"),__vite__mapDeps([32,1,3,4,5,6,7,14,33,34]))},{path:"knowledge-base/:id/documents",name:"DocumentList",component:()=>O(()=>import("./DocumentList-BaIVJdR5.js"),__vite__mapDeps([35,1,3,4,5,6,7,13,14,33,36]))},{path:"chat",name:"Chat",component:()=>O(()=>import("./Chat-LinpgeUV.js"),__vite__mapDeps([37,1,3,4,5,6,7,12,14,23,24,38,29,33,28,30,39]))},{path:"settings",name:"Settings",component:()=>O(()=>import("./Settings-B7Sp6b_K.js"),__vite__mapDeps([40,3,4,5,6,7,38,29,28]))}]},{path:"/admin",component:()=>O(()=>import("./AdminLayout-D-EL1K0V.js"),__vite__mapDeps([41,1,3,4,5,6,7,28,29,42])),meta:{requiresAuth:!0,role:"admin"},children:[{path:"",redirect:"/admin/dashboard"},{path:"dashboard",name:"AdminDashboard",component:()=>O(()=>import("./Dashboard-BpCuDMMS.js"),__vite__mapDeps([43,1,3,4,5,6,7,11,18,19,20]))},{path:"users",name:"AdminUsers",component:()=>O(()=>import("./Users-Lw6aox4U.js"),__vite__mapDeps([44,3,4,5,6,7,11]))},{path:"ai-models",name:"AdminAIModels",component:()=>O(()=>import("./AIModels-BPCW1PAk.js"),__vite__mapDeps([45,3,4,5,6,7,11]))},{path:"operation-logs",name:"AdminOperationLogs",component:()=>O(()=>import("./OperationLogs-BiVYGpHh.js"),__vite__mapDeps([46,3,4,5,6,7,11]))},{path:"settings",name:"AdminSettings",component:()=>O(()=>import("./Settings-DAz5h4gc.js"),__vite__mapDeps([47,3,4,5,6,7,11,29]))},{path:"logs",name:"AdminLogs",component:()=>O(()=>import("./Logs-BZCHM1W5.js"),__vite__mapDeps([48,3,4,5,6,7]))}]},{path:"/:pathMatch(.*)*",name:"NotFound",component:()=>O(()=>import("./NotFound-B5rt9k-f.js"),__vite__mapDeps([49,3,4,5,6,7]))}]});var D;S.beforeEach((D=e(function*(e,t,r){const o=k();if("/"===e.path)return void r("/login");if(!e.meta.requiresAuth)return void r();if(!o.isAuthenticated){if(!o.token)return void r("/login");if(!(yield o.fetchUserInfo()))return o.clearAuth(),void r("/login")}const n=e.meta.role;if(n){if("admin"===n&&!o.isAdmin)return void r("/user/home");if("user"===n&&o.isAdmin)return void r("/admin/dashboard")}r()}),function(e,t,r){return D.apply(this,arguments)}));var T=S;const R={id:"app",class:"min-h-screen w-full bg-gray-50 dark:bg-dark-900"},V={key:0,class:"fixed inset-0 bg-white dark:bg-dark-900 flex items-center justify-center z-50"};var C=y({__name:"App",setup(t){const r=p(!1);return E(e(function*(){try{yield b(),localStorage.getItem("token")}catch(e){}})),P((e,t,r)=>(e.message&&e.message.includes("getActivePinia"),!1)),(e,t)=>{const o=m("router-view");return d(),_("div",R,[r.value?(d(),_("div",V,t[0]||(t[0]=[f("div",{class:"text-center"},[f("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),f("p",{class:"text-gray-600 dark:text-gray-400"},"加载中...")],-1)]))):g("",!0),v(o),t[1]||(t[1]=f("div",{id:"notifications",class:"fixed top-4 right-4 z-50 space-y-2"},null,-1))])}}});window.echarts=o;const q=l(C),U=c();for(const[B,x]of Object.entries(n))q.component(B,x);q.use(U),q.use(T),q.mount("#app"),O(e(function*(){const{useSettingsStore:e}=yield import("./settings-C73ubz75.js");return{useSettingsStore:e}}),__vite__mapDeps([50,3,4,5,6,7,28,29])).then(({useSettingsStore:e})=>{e().initializeSettings()}),O(e(function*(){const{setupBrowserBackHandler:e}=yield import("./auth-C0vxPHuI.js");return{setupBrowserBackHandler:e}}),[]).then(({setupBrowserBackHandler:e})=>{e()});export{k as b,I as c,L as d,w as e,O as f};