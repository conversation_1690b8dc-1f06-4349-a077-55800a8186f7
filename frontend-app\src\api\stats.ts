import request from './request'

// 统计相关接口类型
export interface DashboardStats {
  knowledge_bases: number
  documents: number
  chat_sessions: number
  storage_used: number
  storage_total: number
  storage_percent: number
}

export interface DetailedStats {
  knowledge_bases: number
  documents: number
  chat_sessions: number
  total_messages: number
  storage_used_bytes: number
  storage_used_mb: number
  storage_used_gb: number
  storage_total_gb: number
  storage_percent: number
  recent_activity: {
    knowledge_bases_created: number
    documents_uploaded: number
    chat_sessions_created: number
    messages_sent: number
  }
}

export interface ActivityStats {
  date: string
  knowledge_bases_created: number
  documents_uploaded: number
  chat_sessions_created: number
  messages_sent: number
}

export interface ActivityStatsParams {
  days?: number
}

// 统计API
export const statsAPI = {
  // 获取仪表盘统计
  getDashboardStats: (): Promise<DashboardStats> => {
    return request.get('/stats/dashboard')
  },

  // 获取详细统计
  getDetailedStats: (): Promise<DetailedStats> => {
    return request.get('/stats/detailed')
  },

  // 获取活动统计
  getActivityStats: (params?: ActivityStatsParams): Promise<ActivityStats[]> => {
    return request.get('/stats/activity', { params })
  }
}
