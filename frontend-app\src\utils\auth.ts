/**
 * 认证相关工具函数
 */

/**
 * 完全清除用户认证信息
 * 解决用户切换后仍显示之前用户的问题
 */
export function clearAllAuthData() {
  console.log('🧹 开始清除所有认证数据...')

  // 清除localStorage中的所有认证相关数据
  const authKeys = [
    'token',
    'user',
    'userInfo',
    'auth_token',
    'access_token',
    'refresh_token',
    'user_data',
    'current_user',
    'pinia-auth', // Pinia可能的持久化key
    'pinia-chat',
    'pinia-knowledgeBase',
    'pinia-ui',
    'pinia-settings'
  ]

  // 清除所有localStorage数据（更彻底的方式）
  const allLocalStorageKeys = Object.keys(localStorage)
  allLocalStorageKeys.forEach(key => {
    // 保留一些系统设置，清除其他所有数据
    if (!key.startsWith('system-') && !key.startsWith('browser-')) {
      localStorage.removeItem(key)
      console.log(`🗑️ 已清除localStorage: ${key}`)
    }
  })

  // 强制设置清除标志
  localStorage.setItem('auth_cleared', 'true')
  localStorage.setItem('last_clear_time', Date.now().toString())

  // 清除sessionStorage
  const sessionKeys = Object.keys(sessionStorage)
  sessionKeys.forEach(key => {
    sessionStorage.removeItem(key)
  })
  if (sessionKeys.length > 0) {
    console.log(`🗑️ 已清除sessionStorage: ${sessionKeys.length}个项目`)
  }

  // 清除可能的cookie
  const cookies = document.cookie.split(";")
  cookies.forEach(function(c) {
    const cookieName = c.replace(/^ +/, "").split('=')[0]
    if (cookieName) {
      document.cookie = `${cookieName}=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;`
      document.cookie = `${cookieName}=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;domain=${window.location.hostname};`
      document.cookie = `${cookieName}=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;domain=.${window.location.hostname};`
    }
  })
  if (cookies.length > 1) {
    console.log(`🗑️ 已清除cookies: ${cookies.length}个`)
  }

  // 清除IndexedDB（如果有的话）
  clearIndexedDB()

  // 清除缓存
  clearBrowserCache()

  console.log('✅ 所有认证数据清除完成')
}

/**
 * 生产环境专用的强制缓存清理
 * 解决部署后用户切换问题
 */
export async function clearProductionCache() {
  if (!import.meta.env.PROD) {
    return
  }

  console.log('🧹 开始清除生产环境缓存...')

  try {
    // 清除Service Worker
    if ('serviceWorker' in navigator) {
      const registrations = await navigator.serviceWorker.getRegistrations()
      await Promise.all(registrations.map(registration => registration.unregister()))
      console.log('🗑️ 已清除Service Worker')
    }

    // 清除Cache API
    if ('caches' in window) {
      const cacheNames = await caches.keys()
      await Promise.all(cacheNames.map(name => caches.delete(name)))
      console.log('🗑️ 已清除Cache API')
    }

    // 清除IndexedDB
    if ('indexedDB' in window) {
      try {
        const databases = await indexedDB.databases()
        await Promise.all(databases.map(db => {
          if (db.name) {
            const deleteReq = indexedDB.deleteDatabase(db.name)
            return new Promise((resolve, reject) => {
              deleteReq.onsuccess = () => resolve(undefined)
              deleteReq.onerror = () => reject(deleteReq.error)
            })
          }
        }))
        console.log('🗑️ 已清除IndexedDB')
      } catch (error) {
        console.warn('清除IndexedDB失败:', error)
      }
    }

    console.log('✅ 生产环境缓存清除完成')
  } catch (error) {
    console.error('清除生产环境缓存失败:', error)
  }
}

/**
 * 强制跳转到登录页
 * 使用replace避免用户通过后退按钮回到需要认证的页面
 */
export function forceRedirectToLogin() {
  console.log('🔄 强制跳转到登录页')
  clearAllAuthData()

  // 清除浏览器历史记录中的敏感页面
  clearBrowserHistory()

  // 使用replace而不是href，避免历史记录
  window.location.replace('/login')
}

/**
 * 清除浏览器历史记录中的敏感页面
 */
export function clearBrowserHistory() {
  try {
    // 如果当前不在登录页，先跳转到登录页
    if (window.location.pathname !== '/login') {
      // 使用replaceState清除当前历史记录
      window.history.replaceState(null, '', '/login')
    }

    // 清除可能的前进历史
    const currentLength = window.history.length
    for (let i = 0; i < currentLength - 1; i++) {
      window.history.back()
    }

    console.log('🗑️ 已清除浏览器历史记录')
  } catch (error) {
    console.warn('清除浏览器历史记录失败:', error)
  }
}

/**
 * 检查token是否过期
 */
export function isTokenExpired(token: string): boolean {
  if (!token) return true
  
  try {
    // 简单的JWT解析（不验证签名）
    const payload = JSON.parse(atob(token.split('.')[1]))
    const currentTime = Date.now() / 1000
    
    return payload.exp < currentTime
  } catch (error) {
    console.error('Token解析失败:', error)
    return true
  }
}

/**
 * 安全的用户切换
 * 确保完全清除之前用户的状态
 */
export function secureUserSwitch() {
  return new Promise<void>((resolve) => {
    console.log('🔄 执行安全用户切换')
    clearAllAuthData()
    clearBrowserHistory()

    // 等待一小段时间确保清除完成
    setTimeout(() => {
      window.location.replace('/login')
      resolve()
    }, 100)
  })
}

/**
 * 设置浏览器返回按钮监听
 * 防止用户通过返回按钮回到需要认证的页面
 */
export function setupBrowserBackHandler() {
  // 监听浏览器返回事件
  window.addEventListener('popstate', (event) => {
    const currentPath = window.location.pathname
    console.log('🔙 检测到浏览器返回操作:', currentPath)

    // 如果返回到需要认证的页面但没有有效token
    const token = localStorage.getItem('token')
    const isAuthRequired = currentPath.startsWith('/admin') || currentPath.startsWith('/user')

    if (isAuthRequired && !token) {
      console.log('❌ 返回到需要认证的页面但无有效token，重定向到登录页')
      event.preventDefault()
      forceRedirectToLogin()
      return
    }

    // 如果返回到登录页，确保清除所有状态
    if (currentPath === '/login' || currentPath === '/') {
      console.log('🧹 返回到登录页，清除认证状态')
      clearAllAuthData()
    }
  })

  console.log('👂 已设置浏览器返回按钮监听')
}

/**
 * 移除浏览器返回按钮监听
 */
export function removeBrowserBackHandler() {
  // 这里可以移除特定的监听器，但由于我们使用的是匿名函数，
  // 实际使用中可能需要保存监听器引用
  console.log('🔇 移除浏览器返回按钮监听')
}

/**
 * 清除IndexedDB数据
 */
export function clearIndexedDB() {
  try {
    // 获取所有数据库名称并删除
    if ('indexedDB' in window) {
      // 常见的可能存储用户数据的数据库名称
      const dbNames = ['pinia', 'vuex', 'auth', 'user-data', 'app-data']

      dbNames.forEach(dbName => {
        const deleteReq = indexedDB.deleteDatabase(dbName)
        deleteReq.onsuccess = () => {
          console.log(`🗑️ 已清除IndexedDB: ${dbName}`)
        }
        deleteReq.onerror = () => {
          console.log(`⚠️ 清除IndexedDB失败: ${dbName}`)
        }
      })
    }
  } catch (error) {
    console.warn('清除IndexedDB时出错:', error)
  }
}

/**
 * 清除浏览器缓存
 */
export function clearBrowserCache() {
  try {
    // 如果支持Service Worker，清除缓存
    if ('serviceWorker' in navigator && 'caches' in window) {
      caches.keys().then(cacheNames => {
        cacheNames.forEach(cacheName => {
          caches.delete(cacheName).then(() => {
            console.log(`🗑️ 已清除缓存: ${cacheName}`)
          })
        })
      })
    }

    // 强制重新加载页面资源（清除HTTP缓存）
    if ('performance' in window && 'navigation' in performance) {
      // 标记需要清除缓存
      sessionStorage.setItem('force-reload', 'true')
    }
  } catch (error) {
    console.warn('清除浏览器缓存时出错:', error)
  }
}
