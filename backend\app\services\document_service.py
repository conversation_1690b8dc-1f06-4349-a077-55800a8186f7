"""
完整版文档处理服务
支持多种文档格式的解析和分块处理，使用LangChain进行高级文档处理
"""
import logging
import os
import tempfile
from typing import List, Dict, Any, Optional, BinaryIO
from pathlib import Path
import asyncio
import aiohttp

# 文档处理相关导入
try:
    from pypdf import PdfReader
except ImportError:
    PdfReader = None

try:
    from docx import Document as DocxDocument
except ImportError:
    DocxDocument = None

try:
    from openpyxl import load_workbook
except ImportError:
    load_workbook = None

logger = logging.getLogger(__name__)

class DocumentServiceError(Exception):
    """文档服务异常"""
    pass

class AdvancedTextSplitter:
    """高级文本分块器"""
    
    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 200):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.separators = ["\n\n", "\n", "。", "！", "？", ". ", "! ", "? ", " ", ""]
    
    def split_text(self, text: str) -> List[str]:
        """智能文本分块"""
        if not text or len(text) <= self.chunk_size:
            return [text] if text else []
        
        chunks = []
        current_chunk = ""
        
        # 按段落分割
        paragraphs = text.split('\n\n')
        
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue
            
            # 如果当前段落加上现有块超过大小限制
            if len(current_chunk) + len(paragraph) > self.chunk_size:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                    current_chunk = ""
                
                # 如果单个段落太长，需要进一步分割
                if len(paragraph) > self.chunk_size:
                    sub_chunks = self._split_long_paragraph(paragraph)
                    chunks.extend(sub_chunks[:-1])  # 除了最后一个
                    current_chunk = sub_chunks[-1] if sub_chunks else ""
                else:
                    current_chunk = paragraph
            else:
                if current_chunk:
                    current_chunk += "\n\n" + paragraph
                else:
                    current_chunk = paragraph
        
        # 添加最后一个块
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        return self._add_overlap(chunks)
    
    def _split_long_paragraph(self, paragraph: str) -> List[str]:
        """分割长段落"""
        chunks = []
        sentences = self._split_by_sentences(paragraph)
        
        current_chunk = ""
        for sentence in sentences:
            if len(current_chunk) + len(sentence) > self.chunk_size:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                    current_chunk = sentence
                else:
                    # 单个句子太长，强制分割
                    chunks.extend(self._force_split(sentence))
            else:
                current_chunk += sentence
        
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def _split_by_sentences(self, text: str) -> List[str]:
        """按句子分割"""
        sentences = []
        current = ""
        
        for char in text:
            current += char
            if char in "。！？.!?":
                sentences.append(current)
                current = ""
        
        if current:
            sentences.append(current)
        
        return sentences
    
    def _force_split(self, text: str) -> List[str]:
        """强制分割长文本"""
        chunks = []
        for i in range(0, len(text), self.chunk_size):
            chunks.append(text[i:i + self.chunk_size])
        return chunks
    
    def _add_overlap(self, chunks: List[str]) -> List[str]:
        """添加重叠内容"""
        if len(chunks) <= 1:
            return chunks
        
        overlapped_chunks = [chunks[0]]
        
        for i in range(1, len(chunks)):
            prev_chunk = chunks[i-1]
            current_chunk = chunks[i]
            
            # 从前一个块的末尾取重叠内容
            overlap_text = prev_chunk[-self.chunk_overlap:] if len(prev_chunk) > self.chunk_overlap else prev_chunk
            
            # 添加重叠内容到当前块
            overlapped_chunk = overlap_text + "\n" + current_chunk
            overlapped_chunks.append(overlapped_chunk)
        
        return overlapped_chunks

class DocumentProcessor:
    """文档处理器基类"""
    
    def __init__(self):
        self.text_splitter = AdvancedTextSplitter()
    
    async def process_file(self, file_path: str, file_type: str) -> List[Dict[str, Any]]:
        """处理文件并返回分块"""
        raise NotImplementedError

class PDFProcessor(DocumentProcessor):
    """PDF文档处理器"""
    
    async def process_file(self, file_path: str, file_type: str) -> List[Dict[str, Any]]:
        """处理PDF文件"""
        try:
            if not PdfReader:
                raise DocumentServiceError("PDF处理库未安装")
            
            reader = PdfReader(file_path)
            full_text = ""
            page_contents = []

            print(f"[文档处理] 开始处理PDF文档: {file_path}")
            print(f"[文档处理] PDF总页数: {len(reader.pages)}")

            # 提取所有页面的文本
            for page_num, page in enumerate(reader.pages):
                page_text = page.extract_text()

                # 如果提取的文本为空或只有空白字符，尝试其他方法
                if not page_text or not page_text.strip():
                    print(f"[文档处理] 第{page_num + 1}页文本提取为空，尝试其他方法")
                    # 尝试使用不同的提取方法
                    try:
                        # 尝试获取页面的文本对象
                        if hasattr(page, 'get_text'):
                            page_text = page.get_text()
                        elif hasattr(page, 'extractText'):
                            page_text = page.extractText()
                        else:
                            page_text = ""
                    except:
                        page_text = ""

                    # 如果仍然为空，跳过这一页
                    if not page_text or not page_text.strip():
                        print(f"[文档处理] 第{page_num + 1}页无法提取文本，跳过")
                        continue

                # 清理文本
                page_text = page_text.strip()
                if len(page_text) < 10:  # 如果文本太短，可能不是有效内容
                    print(f"[文档处理] 第{page_num + 1}页文本太短({len(page_text)}字符)，跳过")
                    continue

                page_contents.append({
                    "page": page_num + 1,
                    "content": page_text
                })

                full_text += f"\n\n--- 第{page_num + 1}页 ---\n\n" + page_text
                print(f"[文档处理] 第{page_num + 1}页提取了 {len(page_text)} 个字符")

            # 检查是否提取到任何有效文本
            if not full_text.strip() or len(page_contents) == 0:
                import os
                filename = os.path.basename(file_path)
                error_msg = (
                    f"PDF文档 '{filename}' 无法提取到任何文本内容。"
                    f"可能原因：1) PDF是扫描版图片格式 2) PDF已加密保护 3) PDF文档损坏。"
                    f"建议：请使用文本版PDF或先进行OCR处理后重新上传。"
                )
                print(f"[文档处理] {error_msg}")
                raise DocumentServiceError(error_msg)

            # 智能分块处理
            splitter = AdvancedTextSplitter(chunk_size=1000, chunk_overlap=200)
            chunks = splitter.split_text(full_text)
            
            result = []
            for i, chunk in enumerate(chunks):
                # 尝试确定分块来源页面
                source_pages = self._identify_source_pages(chunk, page_contents)
                
                result.append({
                    "content": chunk,
                    "metadata": {
                        "chunk_index": i,
                        "source": file_path,
                        "file_type": file_type,
                        "total_chunks": len(chunks),
                        "total_pages": len(reader.pages),
                        "source_pages": source_pages,
                        "processing_method": "advanced_pdf"
                    }
                })
            
            return result
            
        except Exception as e:
            logger.error(f"PDF处理失败: {e}")
            raise DocumentServiceError(f"PDF处理失败: {str(e)}")
    
    def _identify_source_pages(self, chunk: str, page_contents: List[Dict]) -> List[int]:
        """识别分块来源页面"""
        source_pages = []
        chunk_words = set(chunk.lower().split())
        
        for page_info in page_contents:
            page_words = set(page_info["content"].lower().split())
            # 计算词汇重叠度
            overlap = len(chunk_words.intersection(page_words))
            if overlap > len(chunk_words) * 0.3:  # 30%重叠度阈值
                source_pages.append(page_info["page"])
        
        return source_pages

class WordProcessor(DocumentProcessor):
    """Word文档处理器"""
    
    async def process_file(self, file_path: str, file_type: str) -> List[Dict[str, Any]]:
        """处理Word文件"""
        try:
            if not DocxDocument:
                raise DocumentServiceError("Word处理库未安装")
            
            doc = DocxDocument(file_path)
            
            # 提取文档结构信息
            paragraphs_info = []
            full_text = ""
            
            for i, paragraph in enumerate(doc.paragraphs):
                para_text = paragraph.text.strip()
                if para_text:
                    paragraphs_info.append({
                        "index": i,
                        "text": para_text,
                        "style": paragraph.style.name if paragraph.style else "Normal"
                    })
                    full_text += para_text + "\n\n"
            
            # 提取表格内容
            tables_info = []
            for table_idx, table in enumerate(doc.tables):
                table_text = self._extract_table_text(table)
                if table_text:
                    tables_info.append({
                        "index": table_idx,
                        "content": table_text
                    })
                    full_text += f"\n\n--- 表格 {table_idx + 1} ---\n\n" + table_text
            
            # 智能分块处理
            splitter = AdvancedTextSplitter(chunk_size=1000, chunk_overlap=200)
            chunks = splitter.split_text(full_text)
            
            result = []
            for i, chunk in enumerate(chunks):
                result.append({
                    "content": chunk,
                    "metadata": {
                        "chunk_index": i,
                        "source": file_path,
                        "file_type": file_type,
                        "total_chunks": len(chunks),
                        "total_paragraphs": len(paragraphs_info),
                        "total_tables": len(tables_info),
                        "processing_method": "advanced_word"
                    }
                })
            
            return result
            
        except Exception as e:
            logger.error(f"Word处理失败: {e}")
            raise DocumentServiceError(f"Word处理失败: {str(e)}")
    
    def _extract_table_text(self, table) -> str:
        """提取表格文本"""
        table_text = ""
        for row in table.rows:
            row_text = []
            for cell in row.cells:
                row_text.append(cell.text.strip())
            table_text += " | ".join(row_text) + "\n"
        return table_text

class ExcelProcessor(DocumentProcessor):
    """Excel文档处理器"""
    
    async def process_file(self, file_path: str, file_type: str) -> List[Dict[str, Any]]:
        """处理Excel文件"""
        try:
            if not load_workbook:
                raise DocumentServiceError("Excel处理库未安装")
            
            workbook = load_workbook(file_path, data_only=True)
            full_text = ""
            sheets_info = []
            
            # 处理每个工作表
            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                sheet_text = f"工作表: {sheet_name}\n"
                
                # 获取有数据的区域
                if sheet.max_row > 0 and sheet.max_column > 0:
                    for row in sheet.iter_rows(min_row=1, max_row=sheet.max_row, 
                                             min_col=1, max_col=sheet.max_column, 
                                             values_only=True):
                        row_text = []
                        for cell in row:
                            if cell is not None:
                                row_text.append(str(cell))
                            else:
                                row_text.append("")
                        
                        if any(cell.strip() for cell in row_text):  # 跳过空行
                            sheet_text += " | ".join(row_text) + "\n"
                
                sheets_info.append({
                    "name": sheet_name,
                    "rows": sheet.max_row,
                    "columns": sheet.max_column
                })
                
                full_text += sheet_text + "\n\n"
            
            # 智能分块处理
            splitter = AdvancedTextSplitter(chunk_size=1000, chunk_overlap=200)
            chunks = splitter.split_text(full_text)
            
            result = []
            for i, chunk in enumerate(chunks):
                result.append({
                    "content": chunk,
                    "metadata": {
                        "chunk_index": i,
                        "source": file_path,
                        "file_type": file_type,
                        "total_chunks": len(chunks),
                        "sheets_info": sheets_info,
                        "processing_method": "advanced_excel"
                    }
                })
            
            return result
            
        except Exception as e:
            logger.error(f"Excel处理失败: {e}")
            raise DocumentServiceError(f"Excel处理失败: {str(e)}")

class TextProcessor(DocumentProcessor):
    """文本处理器"""
    
    async def process_file(self, file_path: str, file_type: str) -> List[Dict[str, Any]]:
        """处理文本文件"""
        # 检查文件是否存在
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='replace') as file:
                content = file.read()
            
            # 文本分块
            splitter = AdvancedTextSplitter(chunk_size=1000, chunk_overlap=200)
            chunks = splitter.split_text(content)
            
            # 构建结果
            result = []
            for i, chunk_text in enumerate(chunks):
                result.append({
                    'content': chunk_text,  # 使用 content 字段而不是 text
                    'metadata': {
                        'chunk_index': i,
                        'source': file_path,
                        'file_type': file_type,
                        'total_chunks': len(chunks),
                        'processing_method': 'text_processor'
                    }
                })
            
            return result
        except Exception as e:
            logger.error(f"处理文本文件失败: {str(e)}")
            raise DocumentServiceError(f"处理文本文件失败: {str(e)}")


class PowerPointProcessor(DocumentProcessor):
    """PowerPoint处理器"""
    
    async def process_file(self, file_path: str, file_type: str) -> List[Dict[str, Any]]:
        """处理PowerPoint文件"""
        # 检查文件是否存在
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        try:
            # 尝试导入pptx库
            try:
                from pptx import Presentation
            except ImportError:
                raise ImportError("处理PowerPoint需要python-pptx库，请安装后再试")
            
            presentation = Presentation(file_path)
            content = []
            
            # 提取每张幻灯片的内容
            for slide_number, slide in enumerate(presentation.slides, 1):
                slide_content = f"--- 幻灯片 {slide_number} ---\n"
                
                # 提取标题
                if slide.shapes.title:
                    slide_content += f"标题: {slide.shapes.title.text}\n"
                
                # 提取文本框内容
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text:
                        text = shape.text.strip()
                        if text and shape != slide.shapes.title:
                            slide_content += f"{text}\n"
                
                # 提取表格内容
                for shape in slide.shapes:
                    if shape.has_table:
                        table = shape.table
                        for row_idx, row in enumerate(table.rows):
                            row_texts = []
                            for cell in row.cells:
                                if cell.text.strip():
                                    row_texts.append(cell.text.strip())
                            if row_texts:
                                slide_content += " | ".join(row_texts) + "\n"
                
                if slide_content:
                    content.append(slide_content)
            
            # 合并所有幻灯片内容
            all_content = "\n\n".join(content)
            
            # 文本分块
            splitter = AdvancedTextSplitter(chunk_size=1000, chunk_overlap=200)
            chunks = splitter.split_text(all_content)
            
            # 构建结果
            result = []
            for i, chunk_text in enumerate(chunks):
                result.append({
                    'content': chunk_text,  # 使用 content 字段而不是 text
                    'metadata': {
                        'chunk_index': i,
                        'source': file_path,
                        'file_type': file_type,
                        'total_chunks': len(chunks),
                        'processing_method': 'powerpoint_processor'
                    }
                })
            
            return result
        except ImportError as e:
            logger.error(f"缺少处理PowerPoint的依赖库: {str(e)}")
            raise DocumentServiceError(f"缺少处理PowerPoint的依赖库: {str(e)}")
        except Exception as e:
            logger.error(f"处理PowerPoint文件失败: {str(e)}")
            raise DocumentServiceError(f"处理PowerPoint文件失败: {str(e)}")


class DocumentService:
    """完整版文档处理服务"""
    
    def __init__(self, upload_dir: str = "uploads"):
        self.upload_dir = Path(upload_dir)
        self.upload_dir.mkdir(exist_ok=True)
        
        # 注册处理器（使用简化的文件类型）
        self.processors = {
            "pdf": PDFProcessor(),
            "docx": WordProcessor(),
            "doc": WordProcessor(),
            "xlsx": ExcelProcessor(),
            "xls": ExcelProcessor(),
            "txt": TextProcessor(),
            "md": TextProcessor(),  # Markdown也使用文本处理器
            "pptx": PowerPointProcessor(),  # 新增PowerPoint处理器
            "ppt": PowerPointProcessor(),   # 新增PowerPoint处理器
        }
        
        # 文件扩展名映射（简化版本，适应数据库50字符限制）
        self.extension_map = {
            ".pdf": "pdf",
            ".docx": "docx",
            ".doc": "doc",
            ".xlsx": "xlsx",
            ".xls": "xls",
            ".txt": "txt",
            ".md": "md",
            ".pptx": "pptx",
            ".ppt": "ppt",
        }

        # MIME类型映射（用于文档处理）
        self.mime_type_map = {
            ".pdf": "application/pdf",
            ".docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            ".doc": "application/msword",
            ".xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            ".xls": "application/vnd.ms-excel",
            ".txt": "text/plain",
            ".md": "text/markdown",
            ".pptx": "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            ".ppt": "application/vnd.ms-powerpoint",
        }
    
    def get_file_type(self, filename: str) -> Optional[str]:
        """根据文件名获取文件类型（简化版本）"""
        ext = Path(filename).suffix.lower()
        return self.extension_map.get(ext)

    def get_mime_type(self, filename: str) -> Optional[str]:
        """根据文件名获取MIME类型"""
        ext = Path(filename).suffix.lower()
        return self.mime_type_map.get(ext)
    
    def is_supported_file(self, filename: str) -> bool:
        """检查是否支持该文件类型"""
        return self.get_file_type(filename) is not None
    
    async def save_uploaded_file(self, file_content: bytes, filename: str) -> str:
        """保存上传的文件"""
        try:
            # 生成唯一文件名
            timestamp = str(int(asyncio.get_event_loop().time()))
            safe_filename = f"{timestamp}_{filename}"
            file_path = self.upload_dir / safe_filename

            # 确保目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)

            # 保存文件
            with open(file_path, "wb") as f:
                f.write(file_content)

            return str(file_path)

        except Exception as e:
            logger.error(f"保存文件失败: {e}")
            raise DocumentServiceError(f"保存文件失败: {str(e)}")

    async def process_document_in_batches(self, file_path: str, filename: str, batch_size: int = 50) -> List[Dict[str, Any]]:
        """分批处理大文档，避免超时"""
        try:
            file_type = self.get_file_type(filename)
            if not file_type:
                raise DocumentServiceError(f"不支持的文件类型: {filename}")

            logger.info(f"开始分批处理文档: {file_path}, 批次大小: {batch_size}")

            if file_type == 'pdf':
                return await self._process_pdf_in_batches(file_path, batch_size)
            else:
                # 非PDF文件直接使用普通处理
                return await self.process_document(file_path, filename)

        except Exception as e:
            logger.error(f"分批处理文档失败: {e}")
            raise DocumentServiceError(f"分批处理文档失败: {str(e)}")

    async def _process_pdf_in_batches(self, file_path: str, batch_size: int) -> List[Dict[str, Any]]:
        """分批处理PDF文档"""
        from pypdf import PdfReader
        import asyncio

        try:
            reader = PdfReader(file_path)
            total_pages = len(reader.pages)

            logger.info(f"PDF总页数: {total_pages}, 分批大小: {batch_size}")

            all_chunks = []
            full_text = ""

            # 分批处理页面
            for batch_start in range(0, total_pages, batch_size):
                batch_end = min(batch_start + batch_size, total_pages)
                logger.info(f"处理页面 {batch_start + 1} 到 {batch_end}")

                batch_text = ""
                for page_num in range(batch_start, batch_end):
                    try:
                        page = reader.pages[page_num]
                        page_text = page.extract_text()

                        if not page_text.strip():
                            # 尝试其他提取方法
                            try:
                                page_text = page.extract_text(extraction_mode="layout")
                            except:
                                pass

                        if page_text.strip():
                            batch_text += f"\n\n--- 第{page_num + 1}页 ---\n\n" + page_text
                            logger.info(f"第{page_num + 1}页提取了 {len(page_text)} 个字符")
                        else:
                            logger.warning(f"第{page_num + 1}页无法提取文本，跳过")

                    except Exception as page_error:
                        logger.warning(f"处理第{page_num + 1}页时出错: {page_error}")
                        continue

                full_text += batch_text

                # 每处理一批后稍作休息，避免阻塞，并输出进度
                progress = (batch_end / total_pages) * 100
                logger.info(f"处理进度: {progress:.1f}% ({batch_end}/{total_pages})")
                await asyncio.sleep(0.05)  # 减少休息时间

            # 检查是否提取到有效文本
            if not full_text.strip():
                import os
                filename = os.path.basename(file_path)
                error_msg = (
                    f"PDF文档 '{filename}' 无法提取到任何文本内容。"
                    f"可能原因：1) PDF是扫描版图片格式 2) PDF已加密保护 3) PDF文档损坏。"
                    f"建议：请使用文本版PDF或先进行OCR处理后重新上传。"
                )
                logger.error(error_msg)
                raise DocumentServiceError(error_msg)

            # 智能分块处理
            splitter = AdvancedTextSplitter(chunk_size=1000, chunk_overlap=200)
            chunks = splitter.split_text(full_text)

            # 构建结果
            result = []
            for i, chunk_text in enumerate(chunks):
                result.append({
                    'content': chunk_text,
                    'metadata': {
                        'chunk_index': i,
                        'source': file_path,
                        'file_type': 'pdf',
                        'total_chunks': len(chunks),
                        'total_pages': total_pages,
                        'processing_method': 'batch_pdf_processor'
                    }
                })

            logger.info(f"PDF分批处理完成，生成 {len(result)} 个分块")
            return result

        except Exception as e:
            logger.error(f"PDF分批处理失败: {e}")
            raise DocumentServiceError(f"PDF分批处理失败: {str(e)}")

    async def process_document(self, file_path: str, filename: str) -> List[Dict[str, Any]]:
        """处理文档并返回分块"""
        try:
            file_type = self.get_file_type(filename)
            if not file_type:
                raise DocumentServiceError(f"不支持的文件类型: {filename}")
            
            processor = self.processors.get(file_type)
            if not processor:
                raise DocumentServiceError(f"没有找到对应的处理器: {file_type}")
            
            # 处理文档
            chunks = await processor.process_file(file_path, file_type)
            
            logger.info(f"成功处理文档 {filename}，生成 {len(chunks)} 个分块")
            return chunks
            
        except Exception as e:
            logger.error(f"文档处理失败: {e}")
            raise DocumentServiceError(f"文档处理失败: {str(e)}")
    
    async def delete_file(self, file_path: str):
        """删除文件"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"成功删除文件: {file_path}")
        except Exception as e:
            logger.error(f"删除文件失败: {e}")

# 全局文档服务实例
document_service = DocumentService()

def get_document_service() -> DocumentService:
    """获取文档服务实例"""
    return document_service
