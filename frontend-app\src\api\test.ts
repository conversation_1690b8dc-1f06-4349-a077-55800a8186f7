// API测试文件
import { authAPI } from './auth'
import { knowledgeBaseAPI } from './knowledgeBase'
import { documentAPI } from './document'
import { chatAPI } from './chat'
import { aiModelAPI } from './aiModel'
import { statsAPI } from './stats'
import { adminAPI } from './admin'

// 测试API连接
export const testAPI = {
  // 测试认证API
  async testAuth() {
    try {
      console.log('🔐 测试认证API...')
      
      // 测试登录
      const loginResult = await authAPI.login({
        username: 'testuser',
        password: 'test123'
      })
      console.log('✅ 登录成功:', loginResult)
      
      // 保存token
      localStorage.setItem('token', loginResult.access_token)
      
      // 测试获取用户信息
      const userInfo = await authAPI.getCurrentUser()
      console.log('✅ 获取用户信息成功:', userInfo)
      
      return true
    } catch (error) {
      console.error('❌ 认证API测试失败:', error)
      return false
    }
  },

  // 测试AI模型API
  async testAIModels() {
    try {
      console.log('🤖 测试AI模型API...')
      
      const models = await aiModelAPI.getModels()
      console.log('✅ 获取AI模型列表成功:', models)
      
      return true
    } catch (error) {
      console.error('❌ AI模型API测试失败:', error)
      return false
    }
  },

  // 测试知识库API
  async testKnowledgeBase() {
    try {
      console.log('📚 测试知识库API...')
      
      // 获取知识库列表
      const kbList = await knowledgeBaseAPI.getList()
      console.log('✅ 获取知识库列表成功:', kbList)
      
      // 创建知识库
      const newKB = await knowledgeBaseAPI.create({
        name: '前端测试知识库',
        description: '用于前端API测试'
      })
      console.log('✅ 创建知识库成功:', newKB)
      
      // 获取知识库详情
      const kbDetail = await knowledgeBaseAPI.getDetail(newKB.id)
      console.log('✅ 获取知识库详情成功:', kbDetail)
      
      // 删除测试知识库
      await knowledgeBaseAPI.delete(newKB.id)
      console.log('✅ 删除知识库成功')
      
      return true
    } catch (error) {
      console.error('❌ 知识库API测试失败:', error)
      return false
    }
  },

  // 测试聊天API
  async testChat() {
    try {
      console.log('💬 测试聊天API...')
      
      // 获取聊天会话列表
      const sessions = await chatAPI.getSessions()
      console.log('✅ 获取聊天会话列表成功:', sessions)
      
      // 创建聊天会话
      const newSession = await chatAPI.createSession({
        title: '前端API测试聊天'
      })
      console.log('✅ 创建聊天会话成功:', newSession)
      
      // 获取聊天历史
      const history = await chatAPI.getChatHistory(newSession.id)
      console.log('✅ 获取聊天历史成功:', history)
      
      // 删除测试会话
      await chatAPI.deleteSession(newSession.id)
      console.log('✅ 删除聊天会话成功')
      
      return true
    } catch (error) {
      console.error('❌ 聊天API测试失败:', error)
      return false
    }
  },

  // 测试统计API
  async testStats() {
    try {
      console.log('📊 测试统计API...')
      
      // 获取基础统计
      const dashboardStats = await statsAPI.getDashboardStats()
      console.log('✅ 获取基础统计成功:', dashboardStats)
      
      // 获取详细统计
      const detailedStats = await statsAPI.getDetailedStats()
      console.log('✅ 获取详细统计成功:', detailedStats)
      
      // 获取活动统计
      const activityStats = await statsAPI.getActivityStats({ days: 7 })
      console.log('✅ 获取活动统计成功:', activityStats)
      
      return true
    } catch (error) {
      console.error('❌ 统计API测试失败:', error)
      return false
    }
  },

  // 运行所有测试
  async runAllTests() {
    console.log('🚀 开始前端API测试...')
    
    const results = {
      auth: false,
      aiModels: false,
      knowledgeBase: false,
      chat: false,
      stats: false
    }
    
    // 首先测试认证
    results.auth = await this.testAuth()
    
    if (results.auth) {
      // 认证成功后测试其他API
      results.aiModels = await this.testAIModels()
      results.knowledgeBase = await this.testKnowledgeBase()
      results.chat = await this.testChat()
      results.stats = await this.testStats()
    }
    
    console.log('📋 测试结果汇总:', results)
    
    const successCount = Object.values(results).filter(Boolean).length
    const totalCount = Object.keys(results).length
    
    console.log(`🎯 测试完成: ${successCount}/${totalCount} 个API测试通过`)
    
    return results
  }
}
