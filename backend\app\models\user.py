"""
用户相关模型
"""
from datetime import datetime
from typing import Optional
from sqlmodel import SQLModel, Field
from .base import BaseModel


class User(BaseModel, table=True):
    """用户表"""
    __tablename__ = "users"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    username: str = Field(max_length=50, unique=True, index=True)
    email: str = Field(max_length=100, unique=True, index=True)
    password_hash: str = Field(max_length=255)
    display_name: Optional[str] = Field(default=None, max_length=100)
    avatar_url: Optional[str] = Field(default=None, max_length=255)
    bio: Optional[str] = Field(default=None)
    is_admin: bool = Field(default=False)
    status: str = Field(default="active", max_length=20)  # active, disabled, pending
    last_login_at: Optional[datetime] = Field(default=None)


class Session(BaseModel, table=True):
    """会话表"""
    __tablename__ = "sessions"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="users.id")
    token: str = Field(max_length=255, unique=True, index=True)
    ip_address: Optional[str] = Field(default=None, max_length=45)
    user_agent: Optional[str] = Field(default=None)
    expires_at: datetime = Field()


class UserQuota(SQLModel, table=True):
    """用户配额表"""
    __tablename__ = "user_quotas"

    user_id: int = Field(primary_key=True, foreign_key="users.id")
    max_kbs: int = Field(default=5)
    max_docs_per_kb: int = Field(default=100)
    max_storage_mb: int = Field(default=1024)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


class UserSettings(BaseModel, table=True):
    """用户设置表"""
    __tablename__ = "user_settings"

    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="users.id", unique=True, index=True)
    default_model_id: Optional[int] = Field(default=None, foreign_key="ai_models.id")
    default_knowledge_bases: Optional[str] = Field(default=None)  # JSON字符串存储知识库ID列表
    chat_retention_days: int = Field(default=30)
    theme: str = Field(default="auto", max_length=20)  # auto, light, dark
    font_size: str = Field(default="medium", max_length=20)  # small, medium, large
    enable_high_contrast: bool = Field(default=False)
    enable_reduced_motion: bool = Field(default=False)
    provider_preferences: Optional[str] = Field(default=None)  # JSON字符串存储供应商偏好设置
