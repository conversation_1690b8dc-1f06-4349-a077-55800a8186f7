<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学习效果改善雷达图</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 30px;
        }
        
        .title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            letter-spacing: -0.5px;
        }
        
        .subtitle {
            font-size: 16px;
            opacity: 0.9;
            font-weight: 400;
        }
        
        .content {
            padding: 40px;
            background: white;
        }
        
        .radar-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }
        
        .radar-container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
            border: 2px solid #f1f5f9;
        }
        
        .radar-title {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .radar-chart {
            position: relative;
            height: 400px;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .metric-card {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            border-left: 4px solid #3b82f6;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }
        
        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(59, 130, 246, 0.15);
        }
        
        .metric-icon {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .metric-title {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .metric-value {
            font-size: 20px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 5px;
        }
        
        .metric-desc {
            font-size: 12px;
            color: #6b7280;
        }
        
        .improvements-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-top: 40px;
        }
        
        .improvement-card {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            padding: 25px;
            border-radius: 12px;
            border-left: 4px solid #667eea;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        .improvement-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .improvement-content {
            color: #64748b;
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .feature-list {
            list-style: none;
        }
        
        .feature-list li {
            padding: 6px 0;
            color: #475569;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .feature-list li::before {
            content: '✓';
            color: #10b981;
            font-weight: bold;
            font-size: 14px;
        }
        
        .legend-section {
            background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
            padding: 25px;
            border-radius: 15px;
            margin-top: 40px;
        }
        
        .legend-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .legend-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .legend-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin: 0 auto 8px;
        }
        
        .before-color {
            background: rgba(239, 68, 68, 0.8);
        }
        
        .after-color {
            background: rgba(16, 185, 129, 0.8);
        }
        
        .legend-text {
            font-size: 14px;
            font-weight: 500;
            color: #1e293b;
        }
        
        @media (max-width: 768px) {
            .radar-section {
                grid-template-columns: 1fr;
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
            }
            
            .improvements-section {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">📊 学习效果改善雷达图</div>
            <div class="subtitle">Learning Effectiveness Improvement Radar Chart</div>
        </div>
        
        <div class="content">
            <!-- 核心学习指标 -->
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-icon">🎯</div>
                    <div class="metric-title">问题解答准确率</div>
                    <div class="metric-value">>95%</div>
                    <div class="metric-desc">基于RAG技术保证</div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-icon">⏰</div>
                    <div class="metric-title">响应时间</div>
                    <div class="metric-value">24小时</div>
                    <div class="metric-desc">全天候即时响应</div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-icon">👤</div>
                    <div class="metric-title">个性化指导</div>
                    <div class="metric-value">100%</div>
                    <div class="metric-desc">适配不同学习节奏</div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-icon">📚</div>
                    <div class="metric-title">多格式支持</div>
                    <div class="metric-value">全覆盖</div>
                    <div class="metric-desc">增强学习体验</div>
                </div>
            </div>
            
            <!-- 雷达图对比 -->
            <div class="radar-section">
                <div class="radar-container">
                    <div class="radar-title">📈 学习效果综合对比</div>
                    <div class="radar-chart">
                        <canvas id="learningRadar"></canvas>
                    </div>
                </div>
                
                <div class="radar-container">
                    <div class="radar-title">🎓 学习体验提升分析</div>
                    <div class="radar-chart">
                        <canvas id="experienceRadar"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- 图例说明 -->
            <div class="legend-section">
                <div class="legend-title">📋 图例说明</div>
                <div class="legend-grid">
                    <div class="legend-item">
                        <div class="legend-color before-color"></div>
                        <div class="legend-text">使用前</div>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color after-color"></div>
                        <div class="legend-text">使用后</div>
                    </div>
                </div>
            </div>

            <!-- 学习效果改善详情 -->
            <div class="improvements-section">
                <div class="improvement-card">
                    <div class="improvement-title">
                        🎯 专业问题解答
                    </div>
                    <div class="improvement-content">
                        基于RAG技术的智能问答系统，专业问题解答准确率超过95%，
                        为学生提供准确、及时的学习支持。
                    </div>
                    <ul class="feature-list">
                        <li>准确率超过95%</li>
                        <li>专业知识覆盖全面</li>
                        <li>多轮对话支持</li>
                        <li>上下文理解准确</li>
                    </ul>
                </div>

                <div class="improvement-card">
                    <div class="improvement-title">
                        ⏰ 24小时即时响应
                    </div>
                    <div class="improvement-content">
                        突破传统教学时间限制，提供24小时全天候学习支持，
                        学生可以随时获得问题解答和学习指导。
                    </div>
                    <ul class="feature-list">
                        <li>全天候在线服务</li>
                        <li>即时响应无延迟</li>
                        <li>跨时区学习支持</li>
                        <li>自主学习节奏</li>
                    </ul>
                </div>

                <div class="improvement-card">
                    <div class="improvement-title">
                        👤 个性化学习指导
                    </div>
                    <div class="improvement-content">
                        系统能够根据学生的学习进度、能力水平和学习偏好，
                        提供个性化的学习建议和指导方案。
                    </div>
                    <ul class="feature-list">
                        <li>学习路径个性化</li>
                        <li>难度自适应调节</li>
                        <li>学习节奏匹配</li>
                        <li>薄弱环节针对性辅导</li>
                    </ul>
                </div>

                <div class="improvement-card">
                    <div class="improvement-title">
                        📚 多格式学习体验
                    </div>
                    <div class="improvement-content">
                        支持文本、图片、表格、PPT等多种格式内容，
                        为学生提供丰富多样的学习资源和体验。
                    </div>
                    <ul class="feature-list">
                        <li>多媒体内容支持</li>
                        <li>交互式学习体验</li>
                        <li>可视化知识展示</li>
                        <li>多样化学习方式</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 学习效果综合对比雷达图
        const learningCtx = document.getElementById('learningRadar').getContext('2d');
        new Chart(learningCtx, {
            type: 'radar',
            data: {
                labels: [
                    '问题解答准确率',
                    '响应速度',
                    '学习效率',
                    '知识掌握度',
                    '学习兴趣',
                    '自主学习能力'
                ],
                datasets: [
                    {
                        label: '使用前',
                        data: [60, 30, 50, 65, 55, 45],
                        backgroundColor: 'rgba(239, 68, 68, 0.2)',
                        borderColor: 'rgba(239, 68, 68, 0.8)',
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(239, 68, 68, 0.8)',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 5
                    },
                    {
                        label: '使用后',
                        data: [95, 90, 85, 88, 82, 80],
                        backgroundColor: 'rgba(16, 185, 129, 0.2)',
                        borderColor: 'rgba(16, 185, 129, 0.8)',
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(16, 185, 129, 0.8)',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 5
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            font: {
                                size: 12,
                                weight: '500'
                            },
                            color: '#1e293b',
                            usePointStyle: true,
                            pointStyle: 'circle'
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: '#667eea',
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ' + context.parsed.r + '%';
                            }
                        }
                    }
                },
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            stepSize: 20,
                            font: {
                                size: 10
                            },
                            color: '#64748b'
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        angleLines: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        pointLabels: {
                            font: {
                                size: 11,
                                weight: '500'
                            },
                            color: '#1e293b'
                        }
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeInOutQuart'
                }
            }
        });

        // 学习体验提升分析雷达图
        const experienceCtx = document.getElementById('experienceRadar').getContext('2d');
        new Chart(experienceCtx, {
            type: 'radar',
            data: {
                labels: [
                    '学习便利性',
                    '内容丰富度',
                    '交互体验',
                    '个性化程度',
                    '学习动机',
                    '满意度'
                ],
                datasets: [
                    {
                        label: '使用前',
                        data: [45, 55, 40, 35, 50, 60],
                        backgroundColor: 'rgba(239, 68, 68, 0.2)',
                        borderColor: 'rgba(239, 68, 68, 0.8)',
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(239, 68, 68, 0.8)',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 5
                    },
                    {
                        label: '使用后',
                        data: [92, 88, 85, 90, 83, 89],
                        backgroundColor: 'rgba(16, 185, 129, 0.2)',
                        borderColor: 'rgba(16, 185, 129, 0.8)',
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(16, 185, 129, 0.8)',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 5
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            font: {
                                size: 12,
                                weight: '500'
                            },
                            color: '#1e293b',
                            usePointStyle: true,
                            pointStyle: 'circle'
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: '#667eea',
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ' + context.parsed.r + '%';
                            }
                        }
                    }
                },
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            stepSize: 20,
                            font: {
                                size: 10
                            },
                            color: '#64748b'
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        angleLines: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        pointLabels: {
                            font: {
                                size: 11,
                                weight: '500'
                            },
                            color: '#1e293b'
                        }
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeInOutQuart'
                }
            }
        });
    </script>
</body>
</html>
