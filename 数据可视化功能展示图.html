<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据可视化功能展示图</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 30px;
        }
        
        .title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            letter-spacing: -0.5px;
        }
        
        .subtitle {
            font-size: 16px;
            opacity: 0.9;
            font-weight: 400;
        }
        
        .content {
            padding: 40px;
            background: white;
        }
        
        .section {
            margin-bottom: 50px;
        }
        
        .section-title {
            font-size: 24px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #667eea;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .engine-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .engine-card {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 25px;
            border-radius: 15px;
            border: 2px solid #e2e8f0;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }
        
        .engine-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
            border-color: #667eea;
        }
        
        .engine-title {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .engine-desc {
            color: #64748b;
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .feature-list {
            list-style: none;
        }
        
        .feature-list li {
            padding: 8px 0;
            color: #475569;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .feature-list li::before {
            content: '✓';
            color: #10b981;
            font-weight: bold;
            font-size: 14px;
        }
        
        .chart-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        
        .chart-type-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }
        
        .chart-type-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.15);
        }
        
        .chart-type-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .chart-type-desc {
            color: #64748b;
            font-size: 13px;
            line-height: 1.5;
            margin-bottom: 15px;
        }
        
        .chart-demo {
            width: 100%;
            height: 200px;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .applications {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
        }
        
        .app-card {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            padding: 25px;
            border-radius: 12px;
            border-left: 4px solid #667eea;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        .app-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 12px;
        }
        
        .app-desc {
            color: #64748b;
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .app-examples {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .example-tag {
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }
        
        .tech-stack {
            background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
            padding: 30px;
            border-radius: 15px;
            margin-top: 40px;
        }
        
        .tech-title {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .tech-item {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .tech-icon {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .tech-name {
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 5px;
        }
        
        .tech-desc-small {
            font-size: 12px;
            color: #64748b;
        }
        
        @media (max-width: 768px) {
            .engine-comparison {
                grid-template-columns: 1fr;
            }
            
            .chart-types {
                grid-template-columns: 1fr;
            }
            
            .applications {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">📊 数据可视化功能展示</div>
            <div class="subtitle">Data Visualization Features Showcase</div>
        </div>
        
        <div class="content">
            <!-- 双引擎架构 -->
            <div class="section">
                <div class="section-title">
                    🏗️ 双引擎可视化架构
                </div>
                <div class="engine-comparison">
                    <div class="engine-card">
                        <div class="engine-title">
                            📈 Chart.js 引擎
                        </div>
                        <div class="engine-desc">
                            基于Canvas的高性能图表渲染引擎，专注于交互性和响应式设计。
                        </div>
                        <ul class="feature-list">
                            <li>Canvas渲染，性能优异</li>
                            <li>响应式设计，自适应布局</li>
                            <li>丰富的交互功能</li>
                            <li>支持动画效果</li>
                            <li>轻量级，加载快速</li>
                        </ul>
                    </div>
                    
                    <div class="engine-card">
                        <div class="engine-title">
                            📊 ECharts 引擎
                        </div>
                        <div class="engine-desc">
                            功能强大的企业级可视化库，支持更丰富的图表类型和复杂交互。
                        </div>
                        <ul class="feature-list">
                            <li>图表类型丰富多样</li>
                            <li>支持3D和地理可视化</li>
                            <li>强大的数据处理能力</li>
                            <li>主题系统完善</li>
                            <li>企业级功能支持</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 支持的图表类型 -->
            <div class="section">
                <div class="section-title">
                    📈 支持的图表类型
                </div>
                <div class="chart-types">
                    <div class="chart-type-card">
                        <div class="chart-type-title">
                            📊 柱状图 (Bar Chart)
                        </div>
                        <div class="chart-type-desc">
                            适用于分类数据对比，支持垂直和水平布局，可展示多个数据系列。
                        </div>
                        <div class="chart-demo">
                            <canvas id="barChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-type-card">
                        <div class="chart-type-title">
                            📈 折线图 (Line Chart)
                        </div>
                        <div class="chart-type-desc">
                            展示数据随时间变化趋势，支持多条线对比和平滑曲线效果。
                        </div>
                        <div class="chart-demo">
                            <canvas id="lineChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-type-card">
                        <div class="chart-type-title">
                            🥧 饼图 (Pie Chart)
                        </div>
                        <div class="chart-type-desc">
                            显示数据占比关系，支持环形图和极坐标图变体。
                        </div>
                        <div class="chart-demo">
                            <canvas id="pieChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-type-card">
                        <div class="chart-type-title">
                            🕸️ 雷达图 (Radar Chart)
                        </div>
                        <div class="chart-type-desc">
                            多维数据对比分析，适用于能力评估和性能对比场景。
                        </div>
                        <div class="chart-demo" id="radarChart"></div>
                    </div>

                    <div class="chart-type-card">
                        <div class="chart-type-title">
                            🎯 散点图 (Scatter Chart)
                        </div>
                        <div class="chart-type-desc">
                            展示两个变量之间的相关性，支持气泡图和分组显示。
                        </div>
                        <div class="chart-demo">
                            <canvas id="scatterChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-type-card">
                        <div class="chart-type-title">
                            🍩 环形图 (Doughnut Chart)
                        </div>
                        <div class="chart-type-desc">
                            饼图的变体，中心可显示总计信息，视觉效果更现代。
                        </div>
                        <div class="chart-demo">
                            <canvas id="doughnutChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 智能解析功能 -->
            <div class="section">
                <div class="section-title">
                    🤖 智能解析功能
                </div>
                <div class="engine-comparison">
                    <div class="engine-card">
                        <div class="engine-title">
                            📝 多格式支持
                        </div>
                        <div class="engine-desc">
                            支持多种图表配置格式的智能解析和转换。
                        </div>
                        <ul class="feature-list">
                            <li>JSON格式配置</li>
                            <li>JavaScript对象</li>
                            <li>ECharts配置</li>
                            <li>简化数据格式</li>
                        </ul>
                    </div>

                    <div class="engine-card">
                        <div class="engine-title">
                            🐍 Python代码解析
                        </div>
                        <div class="engine-desc">
                            支持matplotlib代码的安全解析和转换。
                        </div>
                        <ul class="feature-list">
                            <li>matplotlib代码识别</li>
                            <li>安全代码执行</li>
                            <li>自动格式转换</li>
                            <li>错误处理机制</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 应用场景 -->
            <div class="section">
                <div class="section-title">
                    🎯 应用场景展示
                </div>
                <div class="applications">
                    <div class="app-card">
                        <div class="app-title">📊 管理员仪表板</div>
                        <div class="app-desc">
                            为管理员提供系统运行状态的可视化监控，包括用户活动、系统性能等关键指标。
                        </div>
                        <div class="app-examples">
                            <span class="example-tag">用户增长趋势</span>
                            <span class="example-tag">系统活动统计</span>
                            <span class="example-tag">资源使用情况</span>
                        </div>
                    </div>

                    <div class="app-card">
                        <div class="app-title">📈 数据分析报告</div>
                        <div class="app-desc">
                            支持知识库覆盖度分析、模型性能对比等专业数据分析场景。
                        </div>
                        <div class="app-examples">
                            <span class="example-tag">知识库覆盖度</span>
                            <span class="example-tag">模型性能对比</span>
                            <span class="example-tag">检索精度分析</span>
                        </div>
                    </div>

                    <div class="app-card">
                        <div class="app-title">🎓 教学辅助工具</div>
                        <div class="app-desc">
                            为教育场景提供学习进度追踪、成绩分布展示等可视化功能。
                        </div>
                        <div class="app-examples">
                            <span class="example-tag">学习进度追踪</span>
                            <span class="example-tag">成绩分布展示</span>
                            <span class="example-tag">知识点掌握度</span>
                        </div>
                    </div>

                    <div class="app-card">
                        <div class="app-title">🔍 智能问答增强</div>
                        <div class="app-desc">
                            在AI对话中动态生成图表，增强信息表达的直观性和准确性。
                        </div>
                        <div class="app-examples">
                            <span class="example-tag">自然语言驱动</span>
                            <span class="example-tag">实时图表生成</span>
                            <span class="example-tag">交互式展示</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 技术栈 -->
            <div class="tech-stack">
                <div class="tech-title">🛠️ 核心技术栈</div>
                <div class="tech-grid">
                    <div class="tech-item">
                        <div class="tech-icon">📊</div>
                        <div class="tech-name">Chart.js</div>
                        <div class="tech-desc-small">Canvas图表渲染</div>
                    </div>
                    <div class="tech-item">
                        <div class="tech-icon">📈</div>
                        <div class="tech-name">ECharts</div>
                        <div class="tech-desc-small">企业级可视化</div>
                    </div>
                    <div class="tech-item">
                        <div class="tech-icon">🎨</div>
                        <div class="tech-name">Vue.js</div>
                        <div class="tech-desc-small">组件化开发</div>
                    </div>
                    <div class="tech-item">
                        <div class="tech-icon">🔧</div>
                        <div class="tech-name">TypeScript</div>
                        <div class="tech-desc-small">类型安全</div>
                    </div>
                    <div class="tech-item">
                        <div class="tech-icon">🤖</div>
                        <div class="tech-name">AI解析</div>
                        <div class="tech-desc-small">智能代码解析</div>
                    </div>
                    <div class="tech-item">
                        <div class="tech-icon">🐍</div>
                        <div class="tech-name">Python支持</div>
                        <div class="tech-desc-small">matplotlib转换</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Chart.js 图表示例

        // 柱状图
        const barCtx = document.getElementById('barChart').getContext('2d');
        new Chart(barCtx, {
            type: 'bar',
            data: {
                labels: ['知识库', '文档', '用户', '会话'],
                datasets: [{
                    data: [25, 45, 30, 40],
                    backgroundColor: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444'],
                    borderRadius: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { display: false } },
                scales: { y: { beginAtZero: true } }
            }
        });

        // 折线图
        const lineCtx = document.getElementById('lineChart').getContext('2d');
        new Chart(lineCtx, {
            type: 'line',
            data: {
                labels: ['1月', '2月', '3月', '4月', '5月'],
                datasets: [{
                    data: [12, 19, 15, 25, 22],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { display: false } },
                scales: { y: { beginAtZero: true } }
            }
        });

        // 饼图
        const pieCtx = document.getElementById('pieChart').getContext('2d');
        new Chart(pieCtx, {
            type: 'pie',
            data: {
                labels: ['理工', '人文', '艺术'],
                datasets: [{
                    data: [40, 35, 25],
                    backgroundColor: ['#3b82f6', '#10b981', '#f59e0b']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { position: 'bottom' } }
            }
        });

        // 散点图
        const scatterCtx = document.getElementById('scatterChart').getContext('2d');
        new Chart(scatterCtx, {
            type: 'scatter',
            data: {
                datasets: [{
                    data: [{x: 10, y: 20}, {x: 15, y: 25}, {x: 20, y: 30}, {x: 25, y: 35}],
                    backgroundColor: '#667eea'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { display: false } },
                scales: { x: { beginAtZero: true }, y: { beginAtZero: true } }
            }
        });

        // 环形图
        const doughnutCtx = document.getElementById('doughnutChart').getContext('2d');
        new Chart(doughnutCtx, {
            type: 'doughnut',
            data: {
                labels: ['已用', '可用'],
                datasets: [{
                    data: [70, 30],
                    backgroundColor: ['#ef4444', '#e5e7eb']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { position: 'bottom' } }
            }
        });

        // ECharts 雷达图
        const radarChart = echarts.init(document.getElementById('radarChart'));
        radarChart.setOption({
            radar: {
                indicator: [
                    { name: '性能', max: 100 },
                    { name: '准确率', max: 100 },
                    { name: '响应速度', max: 100 },
                    { name: '稳定性', max: 100 }
                ],
                radius: '60%'
            },
            series: [{
                type: 'radar',
                data: [{
                    value: [85, 90, 88, 92],
                    areaStyle: { color: 'rgba(102, 126, 234, 0.3)' },
                    lineStyle: { color: '#667eea' }
                }]
            }]
        });
    </script>
</body>
</html>
