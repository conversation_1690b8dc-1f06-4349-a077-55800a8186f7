{"name": "frontend-app", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@types/marked": "^5.0.2", "@vueuse/core": "^13.5.0", "axios": "^1.11.0", "chart.js": "^4.5.0", "dompurify": "^3.2.6", "echarts": "^5.6.0", "element-plus": "^2.10.4", "highlight.js": "^11.11.1", "marked": "^16.1.1", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-chartjs": "^5.3.2", "vue-router": "^4.5.1"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/node": "^22.15.32", "@vitejs/plugin-vue": "^6.0.0", "@vue/eslint-config-typescript": "^14.5.1", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-oxlint": "~1.1.0", "eslint-plugin-vue": "~10.2.0", "jiti": "^2.4.2", "npm-run-all2": "^8.0.4", "oxlint": "~1.1.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "terser": "^5.43.1", "typescript": "~5.8.0", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vite": "npm:rolldown-vite@latest", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "^2.2.10"}}