"""
认证相关功能
"""
from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlmodel import Session, select
from app.core.database import get_session
from app.core.security import verify_token
from app.models.user import User

# HTTP Bearer 认证方案
security = HTTPBearer()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    session: Session = Depends(get_session)
) -> User:
    """获取当前用户"""
    import logging
    logger = logging.getLogger(__name__)

    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无效的认证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )

    logger.info(f"认证请求 - Token: {credentials.credentials[:20]}...")

    # 验证令牌
    payload = verify_token(credentials.credentials)
    if payload is None:
        logger.error("Token验证失败")
        raise credentials_exception

    logger.info(f"Token验证成功 - Payload: {payload}")

    # 获取用户ID
    user_id_str = payload.get("sub")
    if user_id_str is None:
        logger.error("Token中没有用户ID")
        raise credentials_exception

    try:
        user_id = int(user_id_str)
    except (ValueError, TypeError):
        logger.error(f"Token中的用户ID格式错误: {user_id_str}")
        raise credentials_exception

    logger.info(f"用户ID: {user_id}")

    # 查询用户
    try:
        statement = select(User).where(User.id == user_id)
        logger.info(f"执行用户查询: User.id == {user_id}")
        user = session.exec(statement).first()

        if user is None:
            logger.error(f"未找到用户ID: {user_id}")
            raise credentials_exception

        logger.info(f"找到用户: {user.username}, 状态: {user.status}, is_admin: {user.is_admin}")

        # 检查用户状态
        if user.status != "active":
            logger.error(f"用户状态不是active: {user.status}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="用户账户已被禁用"
            )

        logger.info(f"用户认证成功: {user.username} (ID: {user.id})")
        return user

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"查询用户时发生异常: {e}")
        import traceback
        logger.error(traceback.format_exc())
        raise credentials_exception


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """获取当前活跃用户"""
    import logging
    logger = logging.getLogger(__name__)

    logger.info(f"检查活跃用户: {current_user.username}, 状态: {current_user.status}")

    if current_user.status != "active":
        logger.error(f"用户状态不是active: {current_user.status}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户账户未激活"
        )

    logger.info("用户活跃检查通过")
    return current_user


async def get_current_admin_user(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """获取当前管理员用户"""
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    return current_user
