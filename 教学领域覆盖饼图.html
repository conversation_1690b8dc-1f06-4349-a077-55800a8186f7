<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教学领域覆盖饼图</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 40px;
        }
        
        .title {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 15px;
            letter-spacing: -0.5px;
        }
        
        .subtitle {
            font-size: 18px;
            opacity: 0.9;
            font-weight: 400;
            margin-bottom: 20px;
        }
        
        .coverage-badge {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
        }
        
        .content {
            padding: 50px;
            background: white;
        }
        
        .coverage-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 25px;
            margin-bottom: 50px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            border-left: 4px solid #667eea;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 30px rgba(102, 126, 234, 0.15);
        }
        
        .stat-icon {
            font-size: 32px;
            margin-bottom: 15px;
        }
        
        .stat-title {
            font-size: 16px;
            color: #64748b;
            margin-bottom: 10px;
            font-weight: 500;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
        }
        
        .stat-desc {
            font-size: 13px;
            color: #6b7280;
        }
        
        .charts-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 50px;
        }
        
        .chart-card {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
            border: 2px solid #f1f5f9;
            transition: all 0.3s ease;
        }
        
        .chart-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 12px 30px rgba(102, 126, 234, 0.15);
        }
        
        .chart-title {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .chart-container {
            position: relative;
            height: 400px;
        }
        
        .summary-section {
            background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
            padding: 40px;
            border-radius: 20px;
            margin-top: 50px;
            text-align: center;
        }
        
        .summary-title {
            font-size: 24px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 20px;
        }
        
        .summary-content {
            color: #64748b;
            font-size: 16px;
            line-height: 1.8;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .highlight {
            color: #667eea;
            font-weight: 600;
        }
        
        .highlight-green {
            color: #10b981;
            font-weight: 600;
        }
        
        .highlight-orange {
            color: #f59e0b;
            font-weight: 600;
        }
        
        @media (max-width: 768px) {
            .content {
                padding: 30px;
            }
            
            .coverage-stats {
                grid-template-columns: 1fr;
            }
            
            .charts-section {
                grid-template-columns: 1fr;
            }
            
            .chart-container {
                height: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">📚 教学领域覆盖饼图</div>
            <div class="subtitle">Educational Field Coverage Pie Charts</div>
            <div class="coverage-badge">
                🌍 全领域覆盖
            </div>
        </div>
        
        <div class="content">
            <!-- 覆盖统计概览 -->
            <div class="coverage-stats">
                <div class="stat-card">
                    <div class="stat-icon">🎓</div>
                    <div class="stat-title">教育层次</div>
                    <div class="stat-value">4大层次</div>
                    <div class="stat-desc">基础/高等/继续/企业</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">📚</div>
                    <div class="stat-title">学科类别</div>
                    <div class="stat-value">4大类别</div>
                    <div class="stat-desc">理工/人文/艺术/语言</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">🌟</div>
                    <div class="stat-title">具体学科</div>
                    <div class="stat-value">15+学科</div>
                    <div class="stat-desc">全面专业覆盖</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">👥</div>
                    <div class="stat-title">适用人群</div>
                    <div class="stat-value">全年龄段</div>
                    <div class="stat-desc">个性化适配</div>
                </div>
            </div>
            
            <!-- 饼图展示 -->
            <div class="charts-section">
                <div class="chart-card">
                    <div class="chart-title">教育层次分布</div>
                    <div class="chart-container">
                        <canvas id="levelChart"></canvas>
                    </div>
                </div>
                
                <div class="chart-card">
                    <div class="chart-title">学科领域分布</div>
                    <div class="chart-container">
                        <canvas id="subjectChart"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- 总结 -->
            <div class="summary-section">
                <div class="summary-title">🎯 教学领域全覆盖优势</div>
                <div class="summary-content">
                    平台实现了<span class="highlight">4大教育层次</span>和<span class="highlight">4大学科类别</span>的全面覆盖，
                    从<span class="highlight-green">基础教育到企业培训</span>，从<span class="highlight-orange">理工科到人文艺术</span>，
                    涵盖<span class="highlight">15+具体学科</span>，为各个教育领域提供专业、精准的AI教学服务。
                    真正实现了教育数字化的<span class="highlight">全场景应用</span>，
                    满足<span class="highlight-green">全年龄段学习者</span>的多样化学习需求。
                </div>
            </div>
        </div>
    </div>

    <script>
        // 教育层次分布饼图
        const levelCtx = document.getElementById('levelChart').getContext('2d');
        new Chart(levelCtx, {
            type: 'pie',
            data: {
                labels: [
                    '基础教育 (小/初/高)',
                    '高等教育 (本/硕/博)',
                    '继续教育 (在职/技能)',
                    '企业培训 (认证/专业)'
                ],
                datasets: [{
                    data: [30, 35, 20, 15], // 分布比例
                    backgroundColor: [
                        '#3b82f6', // 蓝色 - 基础教育
                        '#10b981', // 绿色 - 高等教育
                        '#f59e0b', // 橙色 - 继续教育
                        '#ef4444'  // 红色 - 企业培训
                    ],
                    borderColor: '#ffffff',
                    borderWidth: 3,
                    hoverOffset: 10
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            font: {
                                size: 12,
                                weight: '500'
                            },
                            color: '#1e293b',
                            usePointStyle: true,
                            pointStyle: 'circle',
                            padding: 15,
                            boxWidth: 12,
                            boxHeight: 12
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: '#667eea',
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                const label = context.label;
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return label + ': ' + percentage + '%';
                            }
                        }
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeInOutQuart'
                }
            }
        });

        // 学科领域分布饼图
        const subjectCtx = document.getElementById('subjectChart').getContext('2d');
        new Chart(subjectCtx, {
            type: 'pie',
            data: {
                labels: [
                    '理工类 (数理化/计算机/工程)',
                    '人文社科 (语史地政)',
                    '艺术类 (美术/音乐/设计)',
                    '语言类 (英日法德等)'
                ],
                datasets: [{
                    data: [40, 30, 15, 15], // 分布比例
                    backgroundColor: [
                        '#8b5cf6', // 紫色 - 理工类
                        '#06b6d4', // 青色 - 人文社科
                        '#f97316', // 橙色 - 艺术类
                        '#84cc16'  // 绿色 - 语言类
                    ],
                    borderColor: '#ffffff',
                    borderWidth: 3,
                    hoverOffset: 10
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            font: {
                                size: 12,
                                weight: '500'
                            },
                            color: '#1e293b',
                            usePointStyle: true,
                            pointStyle: 'circle',
                            padding: 15,
                            boxWidth: 12,
                            boxHeight: 12
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: '#667eea',
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                const label = context.label;
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return label + ': ' + percentage + '%';
                            }
                        }
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeInOutQuart'
                }
            }
        });
    </script>
</body>
</html>
