<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>成本对比柱状图</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 30px;
        }
        
        .title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            letter-spacing: -0.5px;
        }
        
        .subtitle {
            font-size: 16px;
            opacity: 0.9;
            font-weight: 400;
        }
        
        .content {
            padding: 40px;
            background: white;
        }
        
        .chart-section {
            margin-bottom: 40px;
        }
        
        .section-title {
            font-size: 22px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            margin-bottom: 30px;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .metric-card {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 25px;
            border-radius: 12px;
            border-left: 4px solid #667eea;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            text-align: center;
        }
        
        .metric-title {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 10px;
            font-weight: 500;
        }
        
        .metric-value {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
        }
        
        .metric-change {
            font-size: 12px;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 12px;
        }
        
        .metric-decrease {
            background: #dcfce7;
            color: #166534;
        }
        
        .metric-increase {
            background: #fee2e2;
            color: #dc2626;
        }
        
        .comparison-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .comparison-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }
        
        .comparison-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.15);
        }
        
        .comparison-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .comparison-desc {
            color: #64748b;
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .feature-list {
            list-style: none;
        }
        
        .feature-list li {
            padding: 8px 0;
            color: #475569;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .feature-list li::before {
            content: '•';
            color: #667eea;
            font-weight: bold;
            font-size: 16px;
        }
        
        .traditional {
            border-left-color: #ef4444;
        }
        
        .rag {
            border-left-color: #10b981;
        }
        
        .benefits-section {
            background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
            padding: 30px;
            border-radius: 15px;
            margin-top: 40px;
        }
        
        .benefits-title {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .benefit-item {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .benefit-icon {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .benefit-title {
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
        }
        
        .benefit-desc {
            font-size: 12px;
            color: #64748b;
            line-height: 1.4;
        }
        
        @media (max-width: 768px) {
            .comparison-section {
                grid-template-columns: 1fr;
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
            }
            
            .chart-container {
                height: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">📊 成本对比柱状图</div>
            <div class="subtitle">Traditional AI vs RAG Cost Comparison</div>
        </div>
        
        <div class="content">
            <!-- 核心指标 -->
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-title">Token消耗降低</div>
                    <div class="metric-value">80%</div>
                    <div class="metric-change metric-decrease">↓ 大幅优化</div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-title">单次对话成本</div>
                    <div class="metric-value">20%</div>
                    <div class="metric-change metric-decrease">↓ 仅为原来的</div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-title">年度成本节省</div>
                    <div class="metric-value">数万元</div>
                    <div class="metric-change metric-decrease">↓ 显著节省</div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-title">部署模式</div>
                    <div class="metric-value">私有化</div>
                    <div class="metric-change metric-decrease">↓ 一次性投入</div>
                </div>
            </div>
            
            <!-- Token消耗对比图表 -->
            <div class="chart-section">
                <div class="section-title">Token消耗对比</div>
                <div class="chart-container">
                    <canvas id="tokenChart"></canvas>
                </div>
            </div>
            
            <!-- 成本对比图表 -->
            <div class="chart-section">
                <div class="section-title">单次对话成本对比</div>
                <div class="chart-container">
                    <canvas id="costChart"></canvas>
                </div>
            </div>

            <!-- 详细对比分析 -->
            <div class="comparison-section">
                <div class="comparison-card traditional">
                    <div class="comparison-title">
                        🔴 传统AI对话
                    </div>
                    <div class="comparison-desc">
                        需要将大量无关上下文信息输入模型，导致Token消耗过高，成本昂贵。
                    </div>
                    <ul class="feature-list">
                        <li>单次对话消耗：3000-5000 Token</li>
                        <li>包含大量无关上下文</li>
                        <li>计算资源浪费严重</li>
                        <li>持续的云服务费用</li>
                        <li>数据安全风险</li>
                    </ul>
                </div>

                <div class="comparison-card rag">
                    <div class="comparison-title">
                        🟢 RAG智能检索
                    </div>
                    <div class="comparison-desc">
                        通过语义检索只提取最相关的知识片段，大幅降低Token消耗和成本。
                    </div>
                    <ul class="feature-list">
                        <li>单次对话消耗：500-800 Token</li>
                        <li>精准检索相关内容</li>
                        <li>计算资源高效利用</li>
                        <li>私有化部署，一次性投入</li>
                        <li>数据完全本地化处理</li>
                    </ul>
                </div>
            </div>

            <!-- 年度成本对比图表 -->
            <div class="chart-section">
                <div class="section-title">年度运营成本对比</div>
                <div class="chart-container">
                    <canvas id="annualCostChart"></canvas>
                </div>
            </div>

            <!-- 成本效益优势 -->
            <div class="benefits-section">
                <div class="benefits-title">🎯 RAG方案成本效益优势</div>
                <div class="benefits-grid">
                    <div class="benefit-item">
                        <div class="benefit-icon">💰</div>
                        <div class="benefit-title">成本大幅降低</div>
                        <div class="benefit-desc">Token消耗降低80%，单次对话成本仅为传统方式的20%</div>
                    </div>

                    <div class="benefit-item">
                        <div class="benefit-icon">🏠</div>
                        <div class="benefit-title">私有化部署</div>
                        <div class="benefit-desc">一次性投入，避免持续云服务费用，数据完全自主可控</div>
                    </div>

                    <div class="benefit-item">
                        <div class="benefit-icon">🔒</div>
                        <div class="benefit-title">数据安全</div>
                        <div class="benefit-desc">本地化处理，确保教育机构数据安全和隐私保护</div>
                    </div>

                    <div class="benefit-item">
                        <div class="benefit-icon">🎓</div>
                        <div class="benefit-title">教育适配</div>
                        <div class="benefit-desc">为预算有限的教育机构提供高性价比的AI解决方案</div>
                    </div>

                    <div class="benefit-item">
                        <div class="benefit-icon">⚡</div>
                        <div class="benefit-title">精准检索</div>
                        <div class="benefit-desc">语义检索技术，只提取最相关知识片段，提高效率</div>
                    </div>

                    <div class="benefit-item">
                        <div class="benefit-icon">📈</div>
                        <div class="benefit-title">可持续发展</div>
                        <div class="benefit-desc">长期使用成本更低，投资回报率高，适合长期规划</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Token消耗对比图表
        const tokenCtx = document.getElementById('tokenChart').getContext('2d');
        new Chart(tokenCtx, {
            type: 'bar',
            data: {
                labels: ['传统AI对话', 'RAG智能检索'],
                datasets: [{
                    label: 'Token消耗量',
                    data: [4000, 650], // 传统AI平均4000，RAG平均650
                    backgroundColor: [
                        'rgba(239, 68, 68, 0.8)',   // 红色 - 传统AI
                        'rgba(16, 185, 129, 0.8)'   // 绿色 - RAG
                    ],
                    borderColor: [
                        'rgba(239, 68, 68, 1)',
                        'rgba(16, 185, 129, 1)'
                    ],
                    borderWidth: 2,
                    borderRadius: 8,
                    borderSkipped: false,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '单次对话Token消耗对比',
                        font: {
                            size: 16,
                            weight: 'bold'
                        },
                        color: '#1e293b',
                        padding: 20
                    },
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: '#667eea',
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ' + context.parsed.y + ' Token';
                            },
                            afterLabel: function(context) {
                                if (context.dataIndex === 0) {
                                    return '范围: 3000-5000 Token';
                                } else {
                                    return '范围: 500-800 Token';
                                }
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 5000,
                        ticks: {
                            callback: function(value) {
                                return value + ' Token';
                            },
                            font: {
                                size: 12
                            },
                            color: '#64748b'
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)',
                            drawBorder: false
                        },
                        title: {
                            display: true,
                            text: 'Token消耗量',
                            font: {
                                size: 14,
                                weight: 'bold'
                            },
                            color: '#1e293b'
                        }
                    },
                    x: {
                        ticks: {
                            font: {
                                size: 12,
                                weight: '500'
                            },
                            color: '#1e293b'
                        },
                        grid: {
                            display: false
                        }
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeInOutQuart'
                }
            }
        });

        // 成本对比图表（假设每1000 Token成本为0.002美元）
        const costCtx = document.getElementById('costChart').getContext('2d');
        new Chart(costCtx, {
            type: 'bar',
            data: {
                labels: ['传统AI对话', 'RAG智能检索'],
                datasets: [{
                    label: '单次对话成本',
                    data: [0.008, 0.0013], // 传统AI: 4000*0.002/1000, RAG: 650*0.002/1000
                    backgroundColor: [
                        'rgba(239, 68, 68, 0.8)',
                        'rgba(16, 185, 129, 0.8)'
                    ],
                    borderColor: [
                        'rgba(239, 68, 68, 1)',
                        'rgba(16, 185, 129, 1)'
                    ],
                    borderWidth: 2,
                    borderRadius: 8,
                    borderSkipped: false,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '单次对话成本对比（美元）',
                        font: {
                            size: 16,
                            weight: 'bold'
                        },
                        color: '#1e293b',
                        padding: 20
                    },
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: '#667eea',
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': $' + context.parsed.y.toFixed(4);
                            },
                            afterLabel: function(context) {
                                if (context.dataIndex === 1) {
                                    return '节省: ' + (((0.008 - 0.0013) / 0.008) * 100).toFixed(1) + '%';
                                }
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 0.01,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toFixed(4);
                            },
                            font: {
                                size: 12
                            },
                            color: '#64748b'
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)',
                            drawBorder: false
                        },
                        title: {
                            display: true,
                            text: '成本（美元）',
                            font: {
                                size: 14,
                                weight: 'bold'
                            },
                            color: '#1e293b'
                        }
                    },
                    x: {
                        ticks: {
                            font: {
                                size: 12,
                                weight: '500'
                            },
                            color: '#1e293b'
                        },
                        grid: {
                            display: false
                        }
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeInOutQuart'
                }
            }
        });

        // 年度成本对比图表（假设每天100次对话）
        const annualCtx = document.getElementById('annualCostChart').getContext('2d');
        new Chart(annualCtx, {
            type: 'bar',
            data: {
                labels: ['传统AI方案', 'RAG方案'],
                datasets: [{
                    label: '年度运营成本',
                    data: [292, 47.45], // 传统: 0.008*100*365, RAG: 0.0013*100*365
                    backgroundColor: [
                        'rgba(239, 68, 68, 0.8)',
                        'rgba(16, 185, 129, 0.8)'
                    ],
                    borderColor: [
                        'rgba(239, 68, 68, 1)',
                        'rgba(16, 185, 129, 1)'
                    ],
                    borderWidth: 2,
                    borderRadius: 8,
                    borderSkipped: false,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '年度运营成本对比（美元/年，基于每天100次对话）',
                        font: {
                            size: 16,
                            weight: 'bold'
                        },
                        color: '#1e293b',
                        padding: 20
                    },
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: '#667eea',
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': $' + context.parsed.y.toFixed(2);
                            },
                            afterLabel: function(context) {
                                if (context.dataIndex === 1) {
                                    const savings = 292 - 47.45;
                                    return '年度节省: $' + savings.toFixed(2) + ' (' + ((savings/292)*100).toFixed(1) + '%)';
                                }
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 350,
                        ticks: {
                            callback: function(value) {
                                return '$' + value;
                            },
                            font: {
                                size: 12
                            },
                            color: '#64748b'
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)',
                            drawBorder: false
                        },
                        title: {
                            display: true,
                            text: '年度成本（美元）',
                            font: {
                                size: 14,
                                weight: 'bold'
                            },
                            color: '#1e293b'
                        }
                    },
                    x: {
                        ticks: {
                            font: {
                                size: 12,
                                weight: '500'
                            },
                            color: '#1e293b'
                        },
                        grid: {
                            display: false
                        }
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeInOutQuart'
                }
            }
        });
    </script>
</body>
</html>
