-- AI知识库平台测试数据
-- 注意：运行此脚本前请确保数据库表已创建

-- 清理现有数据（按依赖关系顺序）
DELETE FROM chat_messages;
DELETE FROM chat_sessions;
DELETE FROM user_api_keys;
DELETE FROM document_chunks;
DELETE FROM documents;
DELETE FROM knowledge_bases;
DELETE FROM user_quotas;
DELETE FROM sessions;
DELETE FROM operation_logs;
DELETE FROM system_settings;
DELETE FROM ai_providers;
DELETE FROM users;

-- 重置序列（PostgreSQL）
ALTER SEQUENCE users_id_seq RESTART WITH 1;
ALTER SEQUENCE knowledge_bases_id_seq RESTART WITH 1;
ALTER SEQUENCE documents_id_seq RESTART WITH 1;
ALTER SEQUENCE document_chunks_id_seq RESTART WITH 1;
ALTER SEQUENCE ai_providers_id_seq RESTART WITH 1;
ALTER SEQUENCE user_api_keys_id_seq RESTART WITH 1;
ALTER SEQUENCE chat_sessions_id_seq RESTART WITH 1;
ALTER SEQUENCE chat_messages_id_seq RESTART WITH 1;
ALTER SEQUENCE sessions_id_seq RESTART WITH 1;
ALTER SEQUENCE operation_logs_id_seq RESTART WITH 1;

-- 插入AI模型提供商数据
INSERT INTO ai_providers (provider_name, model_name, is_active, system_api_key, allow_system_key_use, created_at, updated_at) VALUES
('SiliconFlow', 'deepseek-chat', true, 'sk-ltvyukqqyhwhedkxyqhsqnrtqehlfdzpflskkfkqwetoiixm', true, NOW(), NOW()),
('SiliconFlow', 'qwen-turbo', true, 'sk-ltvyukqqyhwhedkxyqhsqnrtqehlfdzpflskkfkqwetoiixm', true, NOW(), NOW()),
('OpenAI', 'gpt-3.5-turbo', true, null, false, NOW(), NOW()),
('OpenAI', 'gpt-4', true, null, false, NOW(), NOW()),
('Anthropic', 'claude-3-sonnet', true, null, false, NOW(), NOW());

-- 插入测试用户数据
-- 密码都是: testpass123 (已加密)
INSERT INTO users (username, email, password_hash, display_name, bio, is_admin, status, created_at, updated_at) VALUES
('admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5u', '系统管理员', '系统管理员账户', true, 'active', NOW(), NOW()),
('testuser', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5u', '测试用户', '这是一个测试用户账户', false, 'active', NOW(), NOW()),
('alice', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5u', 'Alice Wang', 'AI研究员，专注于自然语言处理', false, 'active', NOW(), NOW()),
('bob', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5u', 'Bob Chen', '数据科学家，喜欢机器学习', false, 'active', NOW(), NOW());

-- 插入用户配额数据
INSERT INTO user_quotas (user_id, max_kbs, max_docs_per_kb, max_storage_mb, updated_at) VALUES
(1, 100, 1000, 10240, NOW()),  -- 管理员：更高配额
(2, 5, 100, 1024, NOW()),      -- 测试用户：标准配额
(3, 10, 200, 2048, NOW()),     -- Alice：中等配额
(4, 5, 100, 1024, NOW());      -- Bob：标准配额

-- 插入知识库数据
INSERT INTO knowledge_bases (owner_id, name, description, created_at, updated_at) VALUES
(2, 'Python编程指南', '包含Python基础到高级的编程知识', NOW(), NOW()),
(2, '机器学习笔记', '机器学习算法和实践经验总结', NOW(), NOW()),
(3, 'NLP研究资料', '自然语言处理相关的研究论文和资料', NOW(), NOW()),
(3, '深度学习基础', '深度学习的基础理论和实践', NOW(), NOW()),
(4, '数据分析工具', '各种数据分析工具的使用指南', NOW(), NOW());

-- 插入文档数据
INSERT INTO documents (kb_id, uploader_id, filename, storage_path, file_type, file_size, status, created_at, updated_at) VALUES
(1, 2, 'Python基础语法.pdf', '/uploads/kb_1/python_basics.pdf', 'pdf', 1024000, 'completed', NOW(), NOW()),
(1, 2, 'Python高级特性.md', '/uploads/kb_1/python_advanced.md', 'markdown', 512000, 'completed', NOW(), NOW()),
(2, 2, '线性回归算法.pdf', '/uploads/kb_2/linear_regression.pdf', 'pdf', 2048000, 'completed', NOW(), NOW()),
(2, 2, '决策树实现.py', '/uploads/kb_2/decision_tree.py', 'txt', 256000, 'completed', NOW(), NOW()),
(3, 3, 'Transformer论文.pdf', '/uploads/kb_3/transformer_paper.pdf', 'pdf', 3072000, 'completed', NOW(), NOW()),
(4, 3, '神经网络基础.docx', '/uploads/kb_4/neural_networks.docx', 'docx', 1536000, 'completed', NOW(), NOW()),
(5, 4, 'Pandas使用指南.md', '/uploads/kb_5/pandas_guide.md', 'markdown', 768000, 'completed', NOW(), NOW());

-- 插入文档分块数据（示例）
INSERT INTO document_chunks (doc_id, content, chunk_metadata, created_at, updated_at) VALUES
(1, 'Python是一种高级编程语言，具有简洁的语法和强大的功能。它广泛应用于Web开发、数据科学、人工智能等领域。', '{"page": 1, "chunk_index": 0}', NOW(), NOW()),
(1, 'Python的基本数据类型包括整数(int)、浮点数(float)、字符串(str)、布尔值(bool)等。', '{"page": 1, "chunk_index": 1}', NOW(), NOW()),
(2, '线性回归是机器学习中最基础的算法之一，用于预测连续数值。其基本思想是找到一条直线来最好地拟合数据点。', '{"page": 1, "chunk_index": 0}', NOW(), NOW()),
(3, 'Transformer是一种基于注意力机制的神经网络架构，在自然语言处理任务中取得了突破性进展。', '{"page": 1, "chunk_index": 0}', NOW(), NOW());

-- 插入用户API密钥数据
INSERT INTO user_api_keys (user_id, model_id, api_key, use_system_key, created_at, updated_at) VALUES
(2, 1, '', true, NOW(), NOW()),  -- 测试用户使用系统密钥
(3, 1, '', true, NOW(), NOW()),  -- Alice使用系统密钥
(4, 2, '', true, NOW(), NOW());  -- Bob使用系统密钥

-- 插入聊天会话数据
INSERT INTO chat_sessions (user_id, title, created_at, updated_at) VALUES
(2, 'Python学习讨论', NOW(), NOW()),
(2, '机器学习问答', NOW(), NOW()),
(3, 'NLP技术交流', NOW(), NOW()),
(4, '数据分析咨询', NOW(), NOW());

-- 插入聊天消息数据
INSERT INTO chat_messages (session_id, role, content, model_id_used, referenced_kbs, created_at) VALUES
(1, 'user', '请解释一下Python的列表推导式', 1, '[1]', NOW()),
(1, 'assistant', 'Python的列表推导式是一种简洁的创建列表的方法。语法格式为：[expression for item in iterable if condition]。例如：[x*2 for x in range(5)] 会创建 [0, 2, 4, 6, 8]。', 1, '[1]', NOW()),
(2, 'user', '什么是过拟合？如何避免？', 1, '[2]', NOW()),
(2, 'assistant', '过拟合是指模型在训练数据上表现很好，但在新数据上表现较差的现象。避免过拟合的方法包括：1)增加训练数据 2)使用正则化 3)简化模型 4)交叉验证等。', 1, '[2]', NOW()),
(3, 'user', 'Transformer的注意力机制是如何工作的？', 1, '[3]', NOW()),
(3, 'assistant', 'Transformer的注意力机制通过计算查询(Query)、键(Key)和值(Value)之间的相关性来工作。它能够让模型关注输入序列中的不同位置，从而更好地理解上下文关系。', 1, '[3]', NOW());

-- 插入系统设置数据
INSERT INTO system_settings (key, value, description, updated_at) VALUES
('allow_registration', 'true', '是否允许用户注册', NOW()),
('max_file_size_mb', '50', '最大文件上传大小(MB)', NOW()),
('default_model_id', '1', '默认AI模型ID', NOW()),
('maintenance_mode', 'false', '维护模式开关', NOW());

-- 插入操作日志数据
INSERT INTO operation_logs (user_id, action, target_type, target_id, details, ip_address, created_at, updated_at) VALUES
(1, 'create_user', 'user', 2, '{"username": "testuser"}', '127.0.0.1', NOW(), NOW()),
(2, 'create_kb', 'knowledge_base', 1, '{"name": "Python编程指南"}', '127.0.0.1', NOW(), NOW()),
(2, 'upload_document', 'document', 1, '{"filename": "Python基础语法.pdf"}', '127.0.0.1', NOW(), NOW()),
(3, 'create_kb', 'knowledge_base', 3, '{"name": "NLP研究资料"}', '*************', NOW(), NOW());

-- 显示插入结果统计
SELECT 'users' as table_name, COUNT(*) as count FROM users
UNION ALL
SELECT 'ai_providers', COUNT(*) FROM ai_providers
UNION ALL
SELECT 'knowledge_bases', COUNT(*) FROM knowledge_bases
UNION ALL
SELECT 'documents', COUNT(*) FROM documents
UNION ALL
SELECT 'document_chunks', COUNT(*) FROM document_chunks
UNION ALL
SELECT 'chat_sessions', COUNT(*) FROM chat_sessions
UNION ALL
SELECT 'chat_messages', COUNT(*) FROM chat_messages
UNION ALL
SELECT 'system_settings', COUNT(*) FROM system_settings
UNION ALL
SELECT 'operation_logs', COUNT(*) FROM operation_logs;
