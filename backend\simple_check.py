#!/usr/bin/env python3
"""
简单的文档状态检查脚本
"""
import sys
import os
from sqlalchemy import create_engine, text

# 数据库连接
DATABASE_URL = "postgresql://postgres:111222@localhost:5432/aiknowledgebase"

def check_document_status(doc_id: int = None):
    """检查文档状态"""
    engine = create_engine(DATABASE_URL)
    
    with engine.connect() as conn:
        if doc_id:
            # 检查特定文档
            result = conn.execute(text("""
                SELECT id, filename, status, file_size, error_message, created_at, updated_at
                FROM documents 
                WHERE id = :doc_id
            """), {"doc_id": doc_id})
            
            row = result.fetchone()
            if row:
                print(f"文档ID: {row[0]}")
                print(f"文件名: {row[1]}")
                print(f"状态: {row[2]}")
                print(f"文件大小: {row[3]} bytes ({row[3] / (1024*1024):.1f} MB)")
                print(f"错误信息: {row[4] or '无'}")
                print(f"创建时间: {row[5]}")
                print(f"更新时间: {row[6]}")
            else:
                print(f"文档ID {doc_id} 不存在")
        else:
            # 检查最近的文档
            result = conn.execute(text("""
                SELECT id, filename, status, file_size, error_message, created_at
                FROM documents
                WHERE id >= 75
                ORDER BY id DESC
                LIMIT 20
            """))
            
            print("最近10个文档:")
            print("-" * 80)
            for row in result:
                size_mb = row[3] / (1024*1024) if row[3] else 0
                print(f"ID: {row[0]:3d} | 状态: {row[2]:10s} | 大小: {size_mb:6.1f}MB | {row[1]}")
                if row[4]:
                    print(f"     错误: {row[4]}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        try:
            doc_id = int(sys.argv[1])
            check_document_status(doc_id)
        except ValueError:
            print("请提供有效的文档ID")
    else:
        check_document_status()
