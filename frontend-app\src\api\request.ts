import axios from 'axios'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'

// 获取API基础URL
const getApiBaseUrl = () => {
  // 优先使用环境变量
  if (import.meta.env.VITE_API_BASE_URL) {
    return import.meta.env.VITE_API_BASE_URL
  }

  // 生产环境使用相对路径
  if (import.meta.env.PROD) {
    return '/api'
  }

  // 开发环境使用localhost
  return 'http://localhost:8000/api'
}

// 创建axios实例
const request = axios.create({
  baseURL: getApiBaseUrl(),
  timeout: 30000, // 30秒超时（普通请求）
  headers: {
    'Content-Type': 'application/json',
    // 生产环境添加强制不缓存的头部
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0'
  }
})

// 创建文件上传专用的axios实例（更长超时时间）
const uploadRequest = axios.create({
  baseURL: getApiBaseUrl(),
  timeout: 300000, // 5分钟超时（文件上传）
  headers: {
    // 文件上传不设置Content-Type，让浏览器自动设置
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    console.log('🚀 发送请求:', config.method?.toUpperCase(), config.url)

    // 添加认证token
    const authStore = useAuthStore()
    const token = authStore.token || localStorage.getItem('token')

    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // 生产环境添加时间戳防止缓存
    if (import.meta.env.PROD) {
      if (config.method?.toLowerCase() === 'get') {
        config.params = {
          ...config.params,
          _t: Date.now()
        }
      }
    }

    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    console.log('✅ 收到响应:', response.status, response.config.url)
    // 直接返回响应数据
    return response.data
  },
  (error) => {
    console.error('响应拦截器错误:', error)

    // 详细的错误信息记录
    if (error.response) {
      console.error('错误响应详情:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data,
        headers: error.response.headers,
        url: error.config?.url,
        method: error.config?.method
      })
    }

    // 统一错误处理
    if (error.response) {
      const { status, data } = error.response

      switch (status) {
        case 401:
          // 未授权，清除token并跳转到登录页
          const authStore = useAuthStore()
          authStore.logout()
          ElMessage.error('登录已过期，请重新登录')
          // 如果不在登录页，则跳转到登录页
          if (window.location.pathname !== '/login') {
            window.location.href = '/login'
          }
          break
        case 403:
          ElMessage.error('没有权限访问该资源')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 422:
          // 表单验证错误
          if (data.detail) {
            if (Array.isArray(data.detail)) {
              ElMessage.error(data.detail[0].msg || '请求参数错误')
            } else {
              ElMessage.error(data.detail || '请求参数错误')
            }
          } else {
            ElMessage.error('请求参数错误')
          }
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(data.detail || data.message || '请求失败')
      }
    } else if (error.request) {
      ElMessage.error('网络连接失败，请检查网络')
    } else {
      ElMessage.error('请求配置错误')
    }

    return Promise.reject(error)
  }
)

// 文件上传请求拦截器
uploadRequest.interceptors.request.use(
  (config) => {
    console.log('📤 发送文件上传请求:', config.method?.toUpperCase(), config.url)

    // 添加认证token
    const authStore = useAuthStore()
    const token = authStore.token || localStorage.getItem('token')

    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    return config
  },
  (error) => {
    console.error('文件上传请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 文件上传响应拦截器
uploadRequest.interceptors.response.use(
  (response) => {
    console.log('✅ 文件上传响应:', response.status, response.config.url)
    return response
  },
  (error) => {
    console.error('❌ 文件上传响应错误:', error)

    if (error.response) {
      const { status, data } = error.response
      switch (status) {
        case 413:
          ElMessage.error('文件太大，请选择较小的文件')
          break
        case 408:
        case 504:
          ElMessage.error('文件上传超时，请重试或选择较小的文件')
          break
        case 500:
          ElMessage.error('服务器处理文件时出错，请重试')
          break
        default:
          ElMessage.error(data?.detail || data?.message || '文件上传失败')
      }
    } else if (error.code === 'ECONNABORTED') {
      ElMessage.error('文件上传超时，请重试或选择较小的文件')
    } else {
      ElMessage.error('网络连接失败，请检查网络')
    }

    return Promise.reject(error)
  }
)

export default request
export { uploadRequest }
