<template>
  <!-- 页面加载状态 -->
  <div v-if="pageLoading" class="h-full flex items-center justify-center bg-white dark:bg-[#121212]">
    <div class="text-center">
      <el-icon :size="48" class="text-emerald-500 animate-spin mb-4">
        <Loading />
      </el-icon>
      <p class="text-gray-600 dark:text-gray-400">正在加载知识库数据...</p>
    </div>
  </div>

  <!-- 主要内容 -->
  <div v-else class="space-y-6 bg-white dark:bg-[#121212] p-6 rounded-lg">
    <!-- 页面头部 -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">
          知识库管理
        </h1>
        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
          创建、管理和组织您的知识内容
        </p>
        <!-- 配额信息 -->
        <div v-if="quota" class="mt-2 flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
          <span>知识库: {{ quota.current_kbs || 0 }}/{{ quota.max_kbs }}</span>
          <span>存储: {{ formatStorage(quota.current_storage_mb || 0) }}/{{ formatStorage(quota.max_storage_mb) }}</span>
        </div>
      </div>
      <div class="mt-4 sm:mt-0">
        <el-button
          type="primary"
          @click="handleCreateKnowledgeBase"
          class="bg-emerald-600 text-white border-0 hover:bg-emerald-700 shadow-lg"
          :disabled="isQuotaExceeded"
        >
          <el-icon class="mr-2"><Plus /></el-icon>
          创建知识库
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="bg-white dark:bg-[#1e1e1e] p-6 rounded-lg border border-gray-200/80 dark:border-zinc-800/80">
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
        <!-- 搜索框 -->
        <div class="flex-1 max-w-md">
          <el-input
            v-model="searchQuery"
            placeholder="搜索知识库..."
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <!-- 筛选和排序 -->
        <div class="flex items-center space-x-2">
          <!-- 排序 -->
          <el-select v-model="sortBy" placeholder="排序方式" style="width: 160px" @change="handleSort">
            <el-option label="最近更新" value="lastUpdated" />
            <el-option label="创建时间" value="createdAt" />
            <el-option label="名称" value="name" />
          </el-select>

          <!-- 排序方向 -->
          <el-button-group class="tech-button-group">
            <el-button
              :type="sortOrder === 'desc' ? 'primary' : 'default'"
              @click="sortOrder = 'desc'; handleSort()"
            >
              <el-icon><SortDown /></el-icon>
            </el-button>
            <el-button
              :type="sortOrder === 'asc' ? 'primary' : 'default'"
              @click="sortOrder = 'asc'; handleSort()"
            >
              <el-icon><SortUp /></el-icon>
            </el-button>
          </el-button-group>

          <!-- 视图切换 -->
          <el-button-group class="tech-button-group">
            <el-button
              :type="viewMode === 'grid' ? 'primary' : 'default'"
              @click="viewMode = 'grid'"
            >
              <el-icon><Grid /></el-icon>
            </el-button>
            <el-button
              :type="viewMode === 'list' ? 'primary' : 'default'"
              @click="viewMode = 'list'"
            >
              <el-icon><List /></el-icon>
            </el-button>
          </el-button-group>
        </div>
      </div>
    </div>

    <!-- 知识库列表 -->
    <div v-loading="loading">
      <!-- 空状态 -->
      <div v-if="filteredKnowledgeBases.length === 0 && !loading" class="text-center py-12">
        <el-icon :size="64" class="text-gray-400 mb-4">
          <Box />
        </el-icon>
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
          {{ searchQuery ? '未找到匹配的知识库' : '还没有知识库' }}
        </h3>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          {{ searchQuery ? '尝试调整搜索条件' : '创建您的第一个知识库来开始管理知识内容' }}
        </p>
        <el-button v-if="!searchQuery" type="primary" @click="showCreateDialog = true" class="bg-emerald-600 text-white border-0 hover:bg-emerald-700 shadow-lg">
          <el-icon class="mr-2"><Plus /></el-icon>
          创建知识库
        </el-button>
      </div>

      <!-- 卡片视图 -->
      <div v-else-if="viewMode === 'grid'" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div
          v-for="kb in paginatedKnowledgeBases"
          :key="kb.id"
          class="bg-white dark:bg-[#2a2a2a] p-6 rounded-xl border border-gray-200/50 dark:border-zinc-800 hover:border-emerald-500/50 hover:ring-2 hover:ring-emerald-500/20 transition-all duration-300 cursor-pointer group"
          @click="enterKnowledgeBase(kb)"
        >
          <div class="flex items-start justify-between mb-4">
            <el-icon :size="32" class="text-emerald-500">
              <Collection />
            </el-icon>
            <div @click.stop>
              <el-dropdown @command="(command: string) => handleKnowledgeBaseAction(command, kb)" trigger="click">
                <el-button circle size="small" class="opacity-0 group-hover:opacity-100 transition-opacity">
                  <el-icon><MoreFilled /></el-icon>
                </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="edit">
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-dropdown-item>
                  <el-dropdown-item command="delete" divided>
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            </div>
          </div>

          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2 group-hover:text-emerald-500 dark:group-hover:text-emerald-400 transition-colors">
            {{ kb.name }}
          </h3>
          <p class="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-2">
            {{ kb.description || '暂无描述' }}
          </p>

          <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
            <div class="flex items-center space-x-4">
              <span class="flex items-center">
                <el-icon class="mr-1"><Document /></el-icon>
                知识库
              </span>
            </div>
            <span>{{ formatRelativeTime(kb.updated_at) }}</span>
          </div>
        </div>
      </div>

      <!-- 列表视图 -->
      <div v-else class="bg-white dark:bg-[#1e1e1e] rounded-lg border border-gray-200/80 dark:border-zinc-800/80 overflow-hidden">
        <el-table :data="paginatedKnowledgeBases" style="width: 100%">
          <el-table-column prop="name" label="名称" min-width="200">
            <template #default="{ row }">
              <div class="flex items-center space-x-3 cursor-pointer" @click="enterKnowledgeBase(row)">
                <div class="w-8 h-8 bg-zinc-800 rounded-lg flex items-center justify-center">
                  <el-icon :size="16" class="text-emerald-500">
                    <Collection />
                  </el-icon>
                </div>
                <div>
                  <div class="font-medium text-gray-900 dark:text-gray-100 hover:text-emerald-500 dark:hover:text-emerald-400 transition-colors">
                    {{ row.name }}
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="description" label="描述" min-width="300">
            <template #default="{ row }">
              <span class="text-gray-600 dark:text-gray-400">
                {{ row.description || '暂无描述' }}
              </span>
            </template>
          </el-table-column>

          <el-table-column prop="updated_at" label="最后更新" width="150">
            <template #default="{ row }">
              <span class="text-gray-500 dark:text-gray-400 text-sm">
                {{ formatRelativeTime(row.updated_at) }}
              </span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="120" align="center">
            <template #default="{ row }">
              <el-dropdown @command="(command: string) => handleKnowledgeBaseAction(command, row)" trigger="click">
                <el-button circle size="small">
                  <el-icon><MoreFilled /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="edit">
                      <el-icon><Edit /></el-icon>
                      编辑
                    </el-dropdown-item>
                    <el-dropdown-item command="delete" divided>
                      <el-icon><Delete /></el-icon>
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 分页 -->
    <div v-if="filteredKnowledgeBases.length > pageSize" class="flex justify-center">
      <el-pagination
        v-model:current-page="currentPage"
        :page-size="pageSize"
        :total="filteredKnowledgeBases.length"
        layout="prev, pager, next, jumper, total"
        @current-change="handlePageChange"
      />
    </div>

    <!-- 创建/编辑知识库对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingKnowledgeBase ? '编辑知识库' : '创建知识库'"
      width="500px"
      @close="resetForm"
      class="custom-dialog"
    >
      <el-form :model="knowledgeBaseForm" :rules="formRules" ref="formRef" label-width="80px">
        <el-form-item label="名称" prop="name">
          <el-input
            v-model="knowledgeBaseForm.name"
            placeholder="请输入知识库名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="knowledgeBaseForm.description"
            type="textarea"
            placeholder="请输入知识库描述（可选）"
            :rows="4"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            {{ editingKnowledgeBase ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useKnowledgeBaseStore } from '@/stores/knowledgeBase'
import { authAPI, type UserQuota } from '@/api/auth'
import type { KnowledgeBase, CreateKnowledgeBaseForm } from '@/types'
import {
  Plus,
  Search,
  SortDown,
  SortUp,
  Grid,
  List,
  Box,
  Collection,
  Document,
  MoreFilled,
  Edit,
  Delete,
  Loading
} from '@element-plus/icons-vue'

const router = useRouter()
const knowledgeBaseStore = useKnowledgeBaseStore()

// 响应式数据
const pageLoading = ref(false) // 页面加载状态
const loading = ref(false)
const submitting = ref(false)
const searchQuery = ref('')
const sortBy = ref('lastUpdated')
const sortOrder = ref<'asc' | 'desc'>('desc')
const viewMode = ref<'grid' | 'list'>('grid')
const currentPage = ref(1)
const pageSize = ref(12)

// 配额相关
const quota = ref<UserQuota | null>(null)

// 对话框和表单
const showCreateDialog = ref(false)
const editingKnowledgeBase = ref<KnowledgeBase | null>(null)
const formRef = ref()

const knowledgeBaseForm = ref<CreateKnowledgeBaseForm>({
  name: '',
  description: ''
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入知识库名称', trigger: 'blur' },
    { min: 2, max: 50, message: '名称长度在 2 到 50 个字符', trigger: 'blur' }
  ]
}

// 使用store中的真实数据
const knowledgeBases = computed(() => knowledgeBaseStore.knowledgeBases)

// 计算属性
const filteredKnowledgeBases = computed(() => {
  let filtered = knowledgeBases.value || []

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(kb =>
      kb.name.toLowerCase().includes(query) ||
      (kb.description && kb.description.toLowerCase().includes(query))
    )
  }

  // 排序
  filtered.sort((a, b) => {
    let aValue: any, bValue: any

    switch (sortBy.value) {
      case 'name':
        aValue = a.name.toLowerCase()
        bValue = b.name.toLowerCase()
        break
      case 'createdAt':
        aValue = new Date(a.created_at).getTime()
        bValue = new Date(b.created_at).getTime()
        break
      case 'lastUpdated':
      default:
        aValue = new Date(a.updated_at).getTime()
        bValue = new Date(b.updated_at).getTime()
        break
    }

    if (sortOrder.value === 'asc') {
      return aValue > bValue ? 1 : -1
    } else {
      return aValue < bValue ? 1 : -1
    }
  })

  return filtered
})

const paginatedKnowledgeBases = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredKnowledgeBases.value.slice(start, end)
})

// 配额相关计算属性
const isQuotaExceeded = computed(() => {
  if (!quota.value) return false
  return (quota.value.current_kbs || 0) >= quota.value.max_kbs
})

// 方法
const formatStorage = (mb: number) => {
  if (mb < 1024) {
    return `${mb.toFixed(1)} MB`
  } else {
    return `${(mb / 1024).toFixed(1)} GB`
  }
}

const formatRelativeTime = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 60) {
    return `${minutes} 分钟前`
  } else if (hours < 24) {
    return `${hours} 小时前`
  } else {
    return `${days} 天前`
  }
}

const handleSearch = () => {
  currentPage.value = 1
}

const handleSort = () => {
  currentPage.value = 1
}

const handlePageChange = (page: number) => {
  currentPage.value = page
}

const enterKnowledgeBase = (kb: KnowledgeBase) => {
  router.push(`/user/knowledge-base/${kb.id}/documents`)
}

const handleKnowledgeBaseAction = async (command: string, kb: KnowledgeBase) => {
  switch (command) {
    case 'edit':
      editKnowledgeBase(kb)
      break
    case 'delete':
      await deleteKnowledgeBase(kb)
      break
  }
}

const editKnowledgeBase = (kb: KnowledgeBase) => {
  editingKnowledgeBase.value = kb
  knowledgeBaseForm.value = {
    name: kb.name,
    description: kb.description || ''
  }
  showCreateDialog.value = true
}

const deleteKnowledgeBase = async (kb: KnowledgeBase) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除知识库"${kb.name}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    const result = await knowledgeBaseStore.deleteKnowledgeBase(kb.id)

    if (result.success) {
      ElMessage.success('知识库删除成功')
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    // 用户取消删除
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    if (editingKnowledgeBase.value) {
      // 更新知识库
      const result = await knowledgeBaseStore.updateKnowledgeBase(editingKnowledgeBase.value.id, {
        name: knowledgeBaseForm.value.name,
        description: knowledgeBaseForm.value.description
      })

      if (result.success) {
        ElMessage.success('知识库更新成功')
        showCreateDialog.value = false
        resetForm()
      } else {
        ElMessage.error(result.message || '更新失败')
      }
    } else {
      // 创建新知识库
      const result = await knowledgeBaseStore.createKnowledgeBase({
        name: knowledgeBaseForm.value.name,
        description: knowledgeBaseForm.value.description || ''
      })

      if (result.success) {
        ElMessage.success('知识库创建成功')
        showCreateDialog.value = false
        resetForm()
      } else {
        ElMessage.error(result.message || '创建失败')
      }
    }
  } catch (error) {
    ElMessage.error('操作失败，请重试')
  } finally {
    submitting.value = false
  }
}

const resetForm = () => {
  editingKnowledgeBase.value = null
  knowledgeBaseForm.value = {
    name: '',
    description: ''
  }
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 监听搜索查询变化，重置页码
watch(searchQuery, () => {
  currentPage.value = 1
})

// 配额相关函数
const loadQuota = async () => {
  try {
    quota.value = await authAPI.getUserQuota()
  } catch (error) {
    console.error('获取配额信息失败:', error)
  }
}

const handleCreateKnowledgeBase = () => {
  if (isQuotaExceeded.value) {
    ElMessage.warning(`已达到知识库数量限制，最多可创建 ${quota.value?.max_kbs} 个知识库`)
    return
  }
  showCreateDialog.value = true
}

onMounted(async () => {
  loading.value = true
  try {
    await Promise.all([
      knowledgeBaseStore.fetchKnowledgeBases(),
      loadQuota()
    ])
  } catch (error) {
    ElMessage.error('加载知识库失败')
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
/* Dialog Styles */
:deep(.custom-dialog .el-dialog) {
  border-radius: 16px;
  background: #020202;
  border: 1px solid #3f3f46;
  box-shadow: none;
}

:deep(.custom-dialog .el-dialog__header) {
  background:#10b981;
  border-radius: 16px 16px 0 0;
  padding: 20px 24px;
  border-bottom: 1px solid #3f3f46;
}

:deep(.custom-dialog .el-dialog__title) {
  font-weight: 600;
  font-size: 18px;
  color: #e5e7eb;
}

:deep(.custom-dialog .el-dialog__body) {
  padding: 24px;
  color: #d1d5db;
}

:deep(.custom-dialog .el-form-item__label) {
  font-weight: 500;
  color: #a1a1aa;
}

:deep(.custom-dialog .el-input__wrapper),
:deep(.custom-dialog .el-textarea__inner) {
  border-radius: 8px;
  border: 1px solid #fafafa;
  background: #ffffff;
  box-shadow: none;
  color: #000000;
  transition: all 0.2s ease;
}

:deep(.custom-dialog .el-input__wrapper:hover),
:deep(.custom-dialog .el-textarea__inner:hover) {
  border-color: #000000;
}

:deep(.custom-dialog .el-input__wrapper.is-focus),
:deep(.custom-dialog .el-textarea__inner:focus) {
  border-color: #10b981;
  box-shadow: none;
}

:deep(.custom-dialog .el-button--primary) {
  background-color: #059669;
  border-color: #059669;
}
:deep(.custom-dialog .el-button--primary:hover) {
  background-color: #047857;
  border-color: #047857;
}

/* Table Dark Theme */
:deep(.el-table) {
  background-color: transparent;
}
:deep(.el-table th),
:deep(.el-table tr),
:deep(.el-table td) {
  background-color: transparent;
  border-color: #3f3f46;
  color: #a1a1aa;
}
:deep(.el-table thead) {
  color: #e5e7eb;
}
:deep(.el-table--enable-row-hover .el-table__body tr:hover > td) {
  background-color: #2a2a2a !important;
}

/* Button Group Styles */
:deep(.tech-button-group .el-button) {
  background-color: transparent;
  color: #a1a1aa;
  border: 1px solid #3f3f46;
  box-shadow: none;
}
:deep(.tech-button-group .el-button:hover) {
  background-color: #27272a;
  color: #e4e4e7;
  border-color: #52525b;
}

:deep(.tech-button-group .el-button.is-active) {
  border-color: #10b981 !important;
  color: #10b981;
  background-color: #10b9811a;
  box-shadow: none;
}
</style>
