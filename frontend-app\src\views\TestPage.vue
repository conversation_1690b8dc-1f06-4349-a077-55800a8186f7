<template>
  <div class="p-6 space-y-6">
    <div class="text-center mb-8">
      <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">功能测试页面</h1>
      <p class="text-gray-600 dark:text-gray-400">测试各个API功能是否正常工作</p>
    </div>

    <!-- AI模型管理测试 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">AI模型管理测试</h2>
      <div class="space-y-4">
        <div class="flex items-center space-x-4">
          <el-button @click="testAIProviders" :loading="loading.providers">
            测试获取AI提供商
          </el-button>
          <span v-if="results.providers" class="text-green-600">
            ✅ 成功获取 {{ results.providers.length }} 个提供商
          </span>
        </div>
        
        <div class="flex items-center space-x-4">
          <el-button @click="testAIModels" :loading="loading.models">
            测试获取AI模型
          </el-button>
          <span v-if="results.models" class="text-green-600">
            ✅ 成功获取 {{ results.models.length }} 个模型
          </span>
        </div>
      </div>
    </div>

    <!-- 系统设置测试 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">系统设置测试</h2>
      <div class="space-y-4">
        <div class="flex items-center space-x-4">
          <el-button @click="testSystemSettings" :loading="loading.settings">
            测试获取系统设置
          </el-button>
          <span v-if="results.settings" class="text-green-600">
            ✅ 成功获取 {{ results.settings.length }} 个设置
          </span>
        </div>
        
        <div class="flex items-center space-x-4">
          <el-button @click="testUpdateSetting" :loading="loading.updateSetting">
            测试更新设置
          </el-button>
          <span v-if="results.updateSetting" class="text-green-600">
            ✅ 设置更新成功
          </span>
        </div>

        <div class="flex items-center space-x-4">
          <el-button @click="testProviderCRUD" type="warning">
            测试供应商创建/删除
          </el-button>
        </div>
      </div>
    </div>

    <!-- 系统日志测试 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">系统日志测试</h2>
      <div class="space-y-4">
        <div class="flex items-center space-x-4">
          <el-button @click="testLogs" :loading="loading.logs">
            测试获取操作日志
          </el-button>
          <span v-if="results.logs" class="text-green-600">
            ✅ 成功获取 {{ results.logs.length }} 条日志
          </span>
        </div>
      </div>
    </div>

    <!-- 测试结果显示 -->
    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">测试结果</h3>
      <pre class="text-sm text-gray-600 dark:text-gray-300 overflow-auto">{{ JSON.stringify(results, null, 2) }}</pre>
    </div>

    <!-- 一键测试所有功能 -->
    <div class="text-center">
      <el-button type="primary" size="large" @click="testAllFeatures" :loading="loading.all">
        一键测试所有功能
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { adminAPI } from '@/api/admin'

// 加载状态
const loading = ref({
  providers: false,
  models: false,
  settings: false,
  updateSetting: false,
  logs: false,
  all: false
})

// 测试结果
const results = ref<any>({})

// 测试AI提供商
const testAIProviders = async () => {
  try {
    loading.value.providers = true
    const providers = await adminAPI.getAIProviders()
    results.value.providers = providers
    ElMessage.success(`成功获取 ${providers.length} 个AI提供商`)
  } catch (error) {
    console.error('测试AI提供商失败:', error)
    ElMessage.error('测试AI提供商失败')
  } finally {
    loading.value.providers = false
  }
}

// 测试AI模型
const testAIModels = async () => {
  try {
    loading.value.models = true
    const models = await adminAPI.getAIModels()
    results.value.models = models
    ElMessage.success(`成功获取 ${models.length} 个AI模型`)
  } catch (error) {
    console.error('测试AI模型失败:', error)
    ElMessage.error('测试AI模型失败')
  } finally {
    loading.value.models = false
  }
}

// 测试系统设置
const testSystemSettings = async () => {
  try {
    loading.value.settings = true
    const settings = await adminAPI.getSettings()
    results.value.settings = settings
    ElMessage.success(`成功获取 ${settings.length} 个系统设置`)
  } catch (error) {
    console.error('测试系统设置失败:', error)
    ElMessage.error('测试系统设置失败')
  } finally {
    loading.value.settings = false
  }
}

// 测试更新设置
const testUpdateSetting = async () => {
  try {
    loading.value.updateSetting = true
    const result = await adminAPI.updateSetting('test_frontend_setting', {
      value: 'test_value_from_frontend',
      description: '前端测试设置'
    })
    results.value.updateSetting = result
    ElMessage.success('设置更新成功')
  } catch (error) {
    console.error('测试更新设置失败:', error)
    ElMessage.error('测试更新设置失败')
  } finally {
    loading.value.updateSetting = false
  }
}

// 测试创建和删除供应商
const testProviderCRUD = async () => {
  try {
    // 创建测试供应商
    const timestamp = Date.now()
    const testProvider = {
      name: `test_provider_${timestamp}`,
      display_name: `测试供应商_${timestamp}`,
      base_url: 'https://api.test.com',
      description: '前端测试用供应商',
      is_active: true
    }

    const createdProvider = await adminAPI.createAIProvider(testProvider)
    ElMessage.success(`供应商创建成功: ${createdProvider.display_name}`)

    // 删除测试供应商
    await adminAPI.deleteAIProvider(createdProvider.id)
    ElMessage.success('供应商删除成功')

  } catch (error: any) {
    console.error('测试供应商CRUD失败:', error)
    ElMessage.error(error.response?.data?.detail || '测试供应商CRUD失败')
  }
}

// 测试操作日志
const testLogs = async () => {
  try {
    loading.value.logs = true
    const logs = await adminAPI.getLogs({ limit: 10 })
    results.value.logs = logs
    ElMessage.success(`成功获取 ${logs.length} 条操作日志`)
  } catch (error) {
    console.error('测试操作日志失败:', error)
    ElMessage.error('测试操作日志失败')
  } finally {
    loading.value.logs = false
  }
}

// 测试所有功能
const testAllFeatures = async () => {
  try {
    loading.value.all = true
    await Promise.all([
      testAIProviders(),
      testAIModels(),
      testSystemSettings(),
      testLogs()
    ])
    await testUpdateSetting()
    ElMessage.success('所有功能测试完成！')
  } catch (error) {
    console.error('测试失败:', error)
    ElMessage.error('测试失败')
  } finally {
    loading.value.all = false
  }
}
</script>
