"""
用户管理API
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlmodel import Session, select
from app.core.database import SessionDep
from app.core.auth import get_current_active_user
from app.core.security import verify_password, get_password_hash
from app.models.user import User, UserSettings
from app.schemas.user import UserResponse, UserUpdate, UserPasswordUpdate, UserSettingsResponse, UserSettingsUpdate
from typing import List
import json

router = APIRouter()


@router.get("/", response_model=List[UserResponse])
async def get_users(
    session: SessionDep,
    current_user: User = Depends(get_current_active_user)
):
    """获取用户列表（仅管理员）"""
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )

    statement = select(User)
    users = session.exec(statement).all()
    return users


@router.get("/profile", response_model=UserResponse)
async def get_user_profile(current_user: User = Depends(get_current_active_user)):
    """获取用户资料（别名）"""
    return current_user


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: User = Depends(get_current_active_user)):
    """获取当前用户信息"""
    return current_user


@router.put("/me", response_model=UserResponse)
async def update_current_user(
    user_update: UserUpdate,
    session: SessionDep,
    current_user: User = Depends(get_current_active_user)
):
    """更新当前用户信息"""
    # 更新用户信息
    if user_update.display_name is not None:
        current_user.display_name = user_update.display_name
    if user_update.bio is not None:
        current_user.bio = user_update.bio
    if user_update.avatar_url is not None:
        current_user.avatar_url = user_update.avatar_url
    
    session.add(current_user)
    session.commit()
    session.refresh(current_user)
    
    return current_user


@router.put("/me/password")
async def update_password(
    password_update: UserPasswordUpdate,
    session: SessionDep,
    current_user: User = Depends(get_current_active_user)
):
    """更新用户密码"""
    # 验证当前密码
    if not verify_password(password_update.current_password, current_user.password_hash):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="当前密码错误"
        )
    
    # 更新密码
    current_user.password_hash = get_password_hash(password_update.new_password)
    session.add(current_user)
    session.commit()
    
    return {"message": "密码更新成功"}


@router.get("/me/settings", response_model=UserSettingsResponse)
async def get_user_settings(
    session: SessionDep,
    current_user: User = Depends(get_current_active_user)
):
    """获取用户设置"""
    statement = select(UserSettings).where(UserSettings.user_id == current_user.id)
    settings = session.exec(statement).first()

    if not settings:
        # 创建默认设置
        settings = UserSettings(
            user_id=current_user.id,
            default_model_id=None,
            default_knowledge_bases=None,
            chat_retention_days=30,
            theme="auto",
            font_size="medium",
            enable_high_contrast=False,
            enable_reduced_motion=False,
            provider_preferences=None
        )
        session.add(settings)
        session.commit()
        session.refresh(settings)

    # 创建响应对象，解析JSON字段
    response_data = {
        "id": settings.id,
        "user_id": settings.user_id,
        "default_model_id": settings.default_model_id,
        "default_knowledge_bases": json.loads(settings.default_knowledge_bases) if settings.default_knowledge_bases else [],
        "chat_retention_days": settings.chat_retention_days,
        "theme": settings.theme,
        "font_size": settings.font_size,
        "enable_high_contrast": settings.enable_high_contrast,
        "enable_reduced_motion": settings.enable_reduced_motion,
        "provider_preferences": json.loads(settings.provider_preferences) if settings.provider_preferences else {},
        "created_at": settings.created_at,
        "updated_at": settings.updated_at
    }

    return response_data


@router.put("/me/settings", response_model=UserSettingsResponse)
async def update_user_settings(
    settings_update: UserSettingsUpdate,
    session: SessionDep,
    current_user: User = Depends(get_current_active_user)
):
    """更新用户设置"""
    statement = select(UserSettings).where(UserSettings.user_id == current_user.id)
    settings = session.exec(statement).first()

    if not settings:
        # 创建新设置
        settings = UserSettings(user_id=current_user.id)
        session.add(settings)

    # 更新设置
    if settings_update.default_model_id is not None:
        settings.default_model_id = settings_update.default_model_id
    if settings_update.default_knowledge_bases is not None:
        settings.default_knowledge_bases = json.dumps(settings_update.default_knowledge_bases)
    if settings_update.chat_retention_days is not None:
        settings.chat_retention_days = settings_update.chat_retention_days
    if settings_update.theme is not None:
        settings.theme = settings_update.theme
    if settings_update.font_size is not None:
        settings.font_size = settings_update.font_size
    if settings_update.enable_high_contrast is not None:
        settings.enable_high_contrast = settings_update.enable_high_contrast
    if settings_update.enable_reduced_motion is not None:
        settings.enable_reduced_motion = settings_update.enable_reduced_motion
    if settings_update.provider_preferences is not None:
        settings.provider_preferences = json.dumps(settings_update.provider_preferences)

    session.add(settings)
    session.commit()
    session.refresh(settings)

    # 创建响应对象，解析JSON字段
    response_data = {
        "id": settings.id,
        "user_id": settings.user_id,
        "default_model_id": settings.default_model_id,
        "default_knowledge_bases": json.loads(settings.default_knowledge_bases) if settings.default_knowledge_bases else [],
        "chat_retention_days": settings.chat_retention_days,
        "theme": settings.theme,
        "font_size": settings.font_size,
        "enable_high_contrast": settings.enable_high_contrast,
        "enable_reduced_motion": settings.enable_reduced_motion,
        "provider_preferences": json.loads(settings.provider_preferences) if settings.provider_preferences else {},
        "created_at": settings.created_at,
        "updated_at": settings.updated_at
    }

    return response_data
