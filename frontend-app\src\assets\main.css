@import './base.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义全局样式 */
@layer base {
  html {
    @apply scroll-smooth;
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  body {
    @apply bg-gray-50 dark:bg-dark-900 text-gray-900 dark:text-gray-100 transition-colors duration-300;
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  #app {
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }
}

@layer components {
  /* 科技风按钮 */
  .btn-tech {
    @apply px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg
           hover:from-blue-600 hover:to-purple-700 transform hover:scale-105
           transition-all duration-300 shadow-lg hover:shadow-xl;
  }

  /* 科技风卡片 */
  .card-tech {
    @apply bg-white dark:bg-dark-800 rounded-xl shadow-lg hover:shadow-xl
           transition-all duration-300 border border-gray-200 dark:border-dark-700
           hover:border-blue-300 dark:hover:border-blue-600;
  }

  /* 强制全宽布局 */
  .el-container,
  .el-main,
  .el-header,
  .el-footer {
    width: 100% !important;
    max-width: none !important;
  }

  /* 发光效果 */
  .glow-effect {
    @apply shadow-lg;
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  /* 渐变文字 */
  .text-gradient {
    @apply bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent;
  }
}

#app {
  @apply min-h-screen;
}

/* --- 全局缩放至90% (最终方案) --- */
/*
  通过设置根字体大小，可以无缝地缩放所有使用rem单位的UI元素，
  这是最稳定、无副作用的全局缩放方法。
*/
html {
  font-size: 90%;
}
