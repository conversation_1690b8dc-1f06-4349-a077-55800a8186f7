<template>
  <div class="h-screen flex bg-gray-50 dark:bg-gray-900 overflow-hidden">
    <!-- 左侧导航栏 -->
    <div
      :class="[
        'bg-white dark:bg-gray-800 shadow-lg transition-all duration-300 ease-in-out flex flex-col border-r border-gray-200 dark:border-gray-700',
        isCollapsed ? 'w-16' : 'w-64'
      ]"
    >
      <!-- 顶部Logo区域 -->
      <div class="flex items-center justify-between h-16 px-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
        <div v-if="!isCollapsed" class="flex items-center">
          <h1 class="text-xl font-bold text-gray-900 dark:text-white">
            管理后台
          </h1>
        </div>
        <el-button
          @click="toggleSidebar"
          size="small"
          text
          class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          <el-icon>
            <Expand v-if="isCollapsed" />
            <Fold v-else />
          </el-icon>
        </el-button>
      </div>

      <!-- 导航菜单 -->
      <nav class="flex-1 px-2 py-4 space-y-1 overflow-y-auto">
        <router-link
          v-for="item in navigation"
          :key="item.to"
          :to="item.to"
          class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 relative"
          :class="[
            $route.path === item.to
              ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200'
              : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white'
          ]"
        >
          <component
            :is="getIcon(item.icon)"
            class="flex-shrink-0 h-5 w-5"
            :class="[
              isCollapsed ? 'mx-auto' : 'mr-3',
              $route.path === item.to
                ? 'text-blue-500 dark:text-blue-300'
                : 'text-gray-400 group-hover:text-gray-500 dark:text-gray-400 dark:group-hover:text-gray-300'
            ]"
          />
          <span v-if="!isCollapsed" class="truncate">{{ item.name }}</span>

          <!-- 折叠时的提示 -->
          <el-tooltip
            v-if="isCollapsed"
            :content="item.name"
            placement="right"
            :show-after="500"
          >
            <div class="absolute inset-0"></div>
          </el-tooltip>
        </router-link>
      </nav>

      <!-- 底部用户信息 -->
      <div class="border-t border-gray-200 dark:border-gray-700 p-4 flex-shrink-0">
        <div class="flex items-center">
          <el-avatar :size="32" :src="userStore.user?.avatar_url">
            {{ userStore.user?.username?.charAt(0).toUpperCase() }}
          </el-avatar>
          <div v-if="!isCollapsed" class="ml-3 flex-1 min-w-0">
            <div class="text-sm font-medium text-gray-900 dark:text-white truncate">
              {{ userStore.user?.display_name || userStore.user?.username }}
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-400">
              管理员
            </div>
          </div>
          <el-dropdown v-if="!isCollapsed" @command="handleCommand">
            <el-button size="small" text>
              <el-icon><MoreFilled /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="user-view">
                  <el-icon><View /></el-icon>
                  用户视图
                </el-dropdown-item>
                <el-dropdown-item command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-tooltip v-else content="更多选项" placement="right">
            <el-dropdown @command="handleCommand">
              <el-button size="small" text>
                <el-icon><MoreFilled /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="user-view">
                    <el-icon><View /></el-icon>
                    用户视图
                  </el-dropdown-item>
                  <el-dropdown-item command="logout">
                    <el-icon><SwitchButton /></el-icon>
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </el-tooltip>
        </div>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="flex-1 flex flex-col min-w-0">
      <!-- 顶部栏 -->
      <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
        <div class="px-6 py-4">
          <div class="flex justify-between items-center">
            <div>
              <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {{ currentPageTitle }}
              </h2>
              <p class="text-sm text-gray-600 dark:text-gray-400">
                {{ currentPageDescription }}
              </p>
            </div>

            <div class="flex items-center space-x-4">
              <!-- 刷新按钮 -->
              <el-button @click="refreshPage" size="small">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>

              <!-- 主题切换 -->
              <el-button @click="toggleTheme" size="small" circle>
                <el-icon>
                  <Sunny v-if="isDark" />
                  <Moon v-else />
                </el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </header>

      <!-- 页面内容 -->
      <main class="flex-1 overflow-auto">
        <div class="p-6">
          <router-view />
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useSettingsStore } from '@/stores/settings'
import {
  DataBoard,
  User,
  Setting,
  Document,
  Sunny,
  Moon,
  Refresh,
  MoreFilled,
  View,
  SwitchButton,
  Expand,
  Fold
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const userStore = useAuthStore()
const settingsStore = useSettingsStore()

// 侧边栏折叠状态
const isCollapsed = ref(false)

// 切换侧边栏
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
}

// 主题相关
const isDark = computed(() => {
  return document.documentElement.classList.contains('dark')
})

const toggleTheme = async () => {
  const currentTheme = settingsStore.userSettings.theme
  let newTheme: 'light' | 'dark' | 'system'

  if (currentTheme === 'light') {
    newTheme = 'dark'
  } else if (currentTheme === 'dark') {
    newTheme = 'system'
  } else {
    newTheme = 'light'
  }

  await settingsStore.applyTheme(newTheme)
}

// 图标映射
const iconMap: Record<string, any> = {
  DataBoard,
  User,
  Setting,
  Document
}

// 获取图标组件
const getIcon = (iconName: string) => {
  return iconMap[iconName] || Document
}

// 导航菜单
const navigation = [
  {
    name: '仪表盘',
    to: '/admin/dashboard',
    icon: 'DataBoard',
    title: '系统概览',
    description: '查看系统整体运行状态和关键指标'
  },
  {
    name: '用户管理',
    to: '/admin/users',
    icon: 'User',
    title: '用户管理',
    description: '管理平台用户，调整权限和配额'
  },
  {
    name: 'AI模型管理',
    to: '/admin/ai-models',
    icon: 'Setting',
    title: 'AI模型管理',
    description: '配置AI模型参数和系统密钥'
  },
  {
    name: '操作日志',
    to: '/admin/operation-logs',
    icon: 'Document',
    title: '操作日志',
    description: '查看管理员操作记录和审计日志'
  },
  {
    name: '系统设置',
    to: '/admin/settings',
    icon: 'Setting',
    title: '系统设置',
    description: '配置系统参数和全局设置'
  },
  {
    name: '系统日志',
    to: '/admin/logs',
    icon: 'Document',
    title: '系统日志',
    description: '查看系统运行日志和错误记录'
  },
]

// 当前页面信息
const currentPageInfo = computed(() => {
  return navigation.find(item => item.to === route.path) || {
    title: '管理后台',
    description: '系统管理控制台'
  }
})

const currentPageTitle = computed(() => currentPageInfo.value.title)
const currentPageDescription = computed(() => currentPageInfo.value.description)

// 用户菜单处理
const handleCommand = (command: string) => {
  switch (command) {
    case 'user-view':
      router.push('/user/home')
      break
    case 'logout':
      handleLogout()
      break
  }
}

// 退出登录
const handleLogout = async () => {
  await userStore.logout()
  router.push('/login')
}

// 刷新页面
const refreshPage = () => {
  window.location.reload()
}
</script>

<style scoped>
/* 侧边栏滚动条样式 */
nav::-webkit-scrollbar {
  width: 4px;
}

nav::-webkit-scrollbar-track {
  background: transparent;
}

nav::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.3);
  border-radius: 2px;
}

nav::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.5);
}

/* 暗色模式下的滚动条 */
.dark nav::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.3);
}

.dark nav::-webkit-scrollbar-thumb:hover {
  background: rgba(75, 85, 99, 0.5);
}
</style>
