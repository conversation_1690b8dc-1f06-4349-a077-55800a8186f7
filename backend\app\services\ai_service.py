"""
AI服务模块
处理与各种AI提供商的集成，包括硅基流动、OpenAI、DeepSeek等
"""
import asyncio
import json
import logging
from typing import AsyncGenerator, Dict, List, Optional, Any
import aiohttp
from openai import AsyncOpenAI

logger = logging.getLogger(__name__)

class AIServiceError(Exception):
    """AI服务异常"""
    pass

class SiliconFlowClient:
    """硅基流动客户端"""
    
    def __init__(self, api_key: str, base_url: str = "https://api.siliconflow.cn/v1"):
        self.api_key = api_key
        self.base_url = base_url
        self.client = AsyncOpenAI(
            api_key=api_key,
            base_url=base_url
        )
    
    async def stream_chat(
        self, 
        messages: List[Dict[str, str]], 
        model: str = "Qwen/Qwen2.5-7B-Instruct",
        temperature: float = 0.7,
        max_tokens: int = 2000
    ) -> AsyncGenerator[str, None]:
        """流式聊天"""
        try:
            stream = await self.client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=True
            )
            
            async for chunk in stream:
                if chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            logger.error(f"硅基流动流式聊天错误: {e}")
            raise AIServiceError(f"硅基流动API调用失败: {str(e)}")

class OpenAIClient:
    """OpenAI客户端"""
    
    def __init__(self, api_key: str):
        self.client = AsyncOpenAI(api_key=api_key)
    
    async def stream_chat(
        self, 
        messages: List[Dict[str, str]], 
        model: str = "gpt-3.5-turbo",
        temperature: float = 0.7,
        max_tokens: int = 2000
    ) -> AsyncGenerator[str, None]:
        """流式聊天"""
        try:
            stream = await self.client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=True
            )
            
            async for chunk in stream:
                if chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            logger.error(f"OpenAI流式聊天错误: {e}")
            raise AIServiceError(f"OpenAI API调用失败: {str(e)}")

class DeepSeekClient:
    """DeepSeek客户端"""
    
    def __init__(self, api_key: str, base_url: str = "https://api.deepseek.com/v1"):
        self.api_key = api_key
        self.base_url = base_url
        self.client = AsyncOpenAI(
            api_key=api_key,
            base_url=base_url
        )
    
    async def stream_chat(
        self, 
        messages: List[Dict[str, str]], 
        model: str = "deepseek-chat",
        temperature: float = 0.7,
        max_tokens: int = 2000
    ) -> AsyncGenerator[str, None]:
        """流式聊天"""
        try:
            stream = await self.client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=True
            )
            
            async for chunk in stream:
                if chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            logger.error(f"DeepSeek流式聊天错误: {e}")
            raise AIServiceError(f"DeepSeek API调用失败: {str(e)}")

class AIService:
    """AI服务统一接口"""
    
    def __init__(self):
        self.clients = {}
    
    def register_client(self, provider: str, client):
        """注册AI客户端"""
        self.clients[provider] = client
    
    async def stream_chat(
        self,
        provider: str,
        messages: List[Dict[str, str]],
        model: str,
        temperature: float = 0.7,
        max_tokens: int = 2000,
        knowledge_context: Optional[str] = None
    ) -> AsyncGenerator[str, None]:
        """统一的流式聊天接口"""
        if provider not in self.clients:
            raise AIServiceError(f"不支持的AI提供商: {provider}")
        
        client = self.clients[provider]
        
        # 构建系统消息
        system_content = """你是一个智能助手，具有以下能力：

## � 表格输出规范
当用户要求表格形式的输出时，请直接使用Markdown表格格式，不要包裹在代码块中：

正确示例：
| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |
| 数据4 | 数据5 | 数据6 |

错误示例（不要这样做）：
```markdown
| 列1 | 列2 |
|-----|-----|
| 数据1 | 数据2 |
```

记住：表格就是表格，不是代码，请直接输出表格格式。

## �📊 图表绘制功能
你可以为用户生成可视化图表。当用户需要图表时，请使用以下格式：

### 方法1：Chart.js JSON格式（推荐）
```json
{
  "type": "bar",
  "title": "图表标题",
  "data": {
    "labels": ["标签1", "标签2", "标签3"],
    "datasets": [{
      "label": "数据系列名称",
      "data": [数值1, 数值2, 数值3]
    }]
  }
}
```

### 方法2：ECharts配置格式（支持更丰富的图表）
```javascript
option = {
  title: {
    text: '图表标题'
  },
  tooltip: {},
  legend: {
    data: ['系列名称']
  },
  xAxis: {
    data: ['标签1', '标签2', '标签3']
  },
  yAxis: {},
  series: [{
    name: '系列名称',
    type: 'bar',  // 支持: bar, line, pie, scatter, radar等
    data: [数值1, 数值2, 数值3]
  }]
}
```

### 方法3：Python matplotlib代码（自动转换）
```python
import matplotlib.pyplot as plt

# 数据
labels = ["标签1", "标签2", "标签3"]
data = [数值1, 数值2, 数值3]

# 创建图表
plt.figure(figsize=(10, 6))
plt.bar(labels, data)  # 或 plt.plot() 用于折线图
plt.title("图表标题")
plt.xlabel("X轴标题")
plt.ylabel("Y轴标题")
plt.show()
```

支持的图表类型：柱状图(bar)、折线图(line)、饼图(pie)、散点图(scatter)、雷达图(radar)等。

## 🌐 HTML预览功能
你可以为用户生成HTML内容并实时预览。当用户需要HTML演示时，请使用以下格式：

### 完整HTML文档
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面标题</title>
    <style>
        /* CSS样式 */
        body { font-family: Arial, sans-serif; }
        .container { max-width: 800px; margin: 0 auto; padding: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>欢迎</h1>
        <p>这是一个示例页面</p>
    </div>
</body>
</html>
```

### HTML片段（自动包装）
```html
<div class="card">
    <h2>卡片标题</h2>
    <p>卡片内容</p>
    <button onclick="alert('点击了按钮')">点击我</button>
</div>
<style>
.card {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin: 10px 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>
```

注意：为了安全考虑，JavaScript代码会被过滤，但CSS样式完全支持。

## 🧪 测试示例
当用户要求测试图表功能时，你可以提供以下示例：

### ECharts柱状图示例：
```javascript
option = {
  title: { text: '销售数据统计' },
  xAxis: { data: ['1月', '2月', '3月', '4月'] },
  yAxis: {},
  series: [{
    name: '销售额',
    type: 'bar',
    data: [120, 200, 150, 80]
  }]
}
```

### Chart.js饼图示例：
```json
{
  "type": "pie",
  "title": "市场份额分布",
  "data": {
    "labels": ["产品A", "产品B", "产品C"],
    "datasets": [{
      "data": [30, 40, 30],
      "backgroundColor": ["#FF6384", "#36A2EB", "#FFCE56"]
    }]
  }
}
```

## 💡 使用建议
- 当用户提到"图表"、"可视化"、"统计"、"趋势"等词汇时，主动提供图表
- 优先使用Chart.js JSON格式，更简洁高效
- 确保数据准确，标题和标签清晰易懂"""

        # 如果有知识库上下文，添加到系统消息中
        if knowledge_context:
            print(f"[AI服务] 接收到知识库上下文，长度: {len(knowledge_context)}")
            print(f"[AI服务] 知识库上下文前200字符: {knowledge_context[:200]}...")
            system_content += f"\n\n## 📚 知识库内容\n请基于以下知识库内容回答用户问题：\n\n{knowledge_context}\n\n如果知识库中没有相关信息，请说明并基于你的知识回答。"
        else:
            print("[AI服务] 没有接收到知识库上下文")

        system_message = {
            "role": "system",
            "content": system_content
        }
        messages = [system_message] + messages
        
        try:
            full_response = ""
            async for chunk in client.stream_chat(
                messages=messages,
                model=model,
                temperature=temperature,
                max_tokens=max_tokens
            ):
                if chunk:
                    full_response += chunk
                    # 检查是否包含表格相关内容
                    if '|' in chunk or 'markdown' in chunk.lower() or '```' in chunk:
                        print(f"[AI输出检测] 发现可能的表格内容: {chunk[:100]}")
                yield chunk

            # 输出完整响应用于调试
            if '|' in full_response and ('```' in full_response or 'markdown' in full_response.lower()):
                print(f"[AI完整输出] 包含表格的响应: {full_response[:500]}...")
        except Exception as e:
            logger.error(f"AI服务流式聊天错误: {e}")
            raise AIServiceError(f"AI服务调用失败: {str(e)}")

# 全局AI服务实例
ai_service = AIService()

def get_ai_service() -> AIService:
    """获取AI服务实例"""
    return ai_service

async def initialize_ai_clients(providers_config: Dict[str, Dict[str, Any]]):
    """初始化AI客户端"""
    for provider, config in providers_config.items():
        try:
            if provider == "siliconflow":
                client = SiliconFlowClient(
                    api_key=config["api_key"],
                    base_url=config.get("base_url", "https://api.siliconflow.cn/v1")
                )
            elif provider == "openai":
                client = OpenAIClient(api_key=config["api_key"])
            elif provider == "deepseek":
                client = DeepSeekClient(
                    api_key=config["api_key"],
                    base_url=config.get("base_url", "https://api.deepseek.com/v1")
                )
            elif provider in ["google", "gemini"]:
                # Google/Gemini 使用 OpenAI 兼容的客户端
                client = SiliconFlowClient(
                    api_key=config["api_key"],
                    base_url=config.get("base_url", "https://generativelanguage.googleapis.com/v1beta")
                )
            else:
                logger.warning(f"未知的AI提供商: {provider}")
                continue
            
            ai_service.register_client(provider, client)
            logger.info(f"成功初始化AI客户端: {provider}")
            
        except Exception as e:
            logger.error(f"初始化AI客户端失败 {provider}: {e}")

async def test_ai_connection(
    provider_name: str,
    model_name: str,
    api_key: str,
    base_url: Optional[str] = None
) -> bool:
    """测试AI连接"""
    try:
        # 根据供应商类型创建临时客户端
        if provider_name.lower() in ["siliconflow", "硅基流动"]:
            client = SiliconFlowClient(
                api_key=api_key,
                base_url=base_url or "https://api.siliconflow.cn/v1"
            )
        elif provider_name.lower() == "openai":
            client = OpenAIClient(api_key=api_key)
        elif provider_name.lower() == "deepseek":
            client = DeepSeekClient(
                api_key=api_key,
                base_url=base_url or "https://api.deepseek.com/v1"
            )
        elif provider_name.lower() in ["google", "gemini"]:
            # Google/Gemini 使用 OpenAI 兼容的客户端
            client = SiliconFlowClient(
                api_key=api_key,
                base_url=base_url or "https://generativelanguage.googleapis.com/v1beta"
            )
        else:
            # 对于其他供应商，尝试使用通用的OpenAI兼容客户端
            client = SiliconFlowClient(
                api_key=api_key,
                base_url=base_url or "https://api.openai.com/v1"
            )

        messages = [{"role": "user", "content": "Hello"}]

        # 获取第一个响应块来测试连接
        async for _ in client.stream_chat(
            messages=messages,
            model=model_name,
            max_tokens=10
        ):
            return True  # 如果能获取到响应，说明连接正常

    except Exception as e:
        logger.error(f"AI连接测试失败 {provider_name}/{model_name}: {e}")
        return False

    return False


def get_provider_from_model_id(model_id: int, session) -> tuple[str, str]:
    """根据模型ID获取供应商信息和模型名称"""
    from sqlmodel import select
    from app.models.ai import AIModel, AIProvider

    statement = select(AIModel, AIProvider).join(AIProvider).where(AIModel.id == model_id)
    result = session.exec(statement).first()

    if result:
        model, provider = result
        return provider.name, model.model_name

    raise ValueError(f"未找到模型ID: {model_id}")


async def get_ai_client_for_model(model_id: int, user_id: int, session):
    """为指定模型和用户获取AI客户端"""
    from sqlmodel import select
    from app.models.ai import AIModel, AIProvider, UserAPIKey

    # 获取模型和供应商信息
    model_statement = select(AIModel, AIProvider).join(AIProvider).where(AIModel.id == model_id)
    model_result = session.exec(model_statement).first()

    if not model_result:
        raise ValueError(f"未找到模型ID: {model_id}")

    model, provider = model_result

    # 获取API密钥
    api_key = None

    # 首先尝试获取用户的个人密钥
    user_key_statement = select(UserAPIKey).where(
        UserAPIKey.user_id == user_id,
        UserAPIKey.provider_id == provider.id
    )
    user_key = session.exec(user_key_statement).first()

    if user_key:
        api_key = user_key.api_key
    elif model.allow_system_key_use and model.system_api_key:
        api_key = model.system_api_key
    else:
        raise ValueError(f"未找到可用的API密钥，模型: {model.display_name}")

    # 创建客户端
    if provider.name.lower() in ["siliconflow", "硅基流动"]:
        return SiliconFlowClient(
            api_key=api_key,
            base_url=provider.base_url or "https://api.siliconflow.cn/v1"
        )
    elif provider.name.lower() == "openai":
        return OpenAIClient(api_key=api_key)
    elif provider.name.lower() == "deepseek":
        return DeepSeekClient(
            api_key=api_key,
            base_url=provider.base_url or "https://api.deepseek.com/v1"
        )
    elif provider.name.lower() in ["google", "gemini"]:
        # Google/Gemini 使用 OpenAI 兼容的客户端
        return SiliconFlowClient(
            api_key=api_key,
            base_url=provider.base_url or "https://generativelanguage.googleapis.com/v1beta"
        )
    else:
        # 对于其他供应商，使用通用的OpenAI兼容客户端
        return SiliconFlowClient(
            api_key=api_key,
            base_url=provider.base_url or "https://api.openai.com/v1"
        )
