<template>
  <div class="flex h-full bg-gray-50 dark:bg-dark-900">
    <!-- 侧边栏导航 -->
    <div class="w-64 bg-white dark:bg-dark-800 border-r border-gray-200 dark:border-dark-700">
      <div class="p-6">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">
          设置
        </h2>

        <nav class="space-y-2">
          <button
            v-for="item in settingsMenu"
            :key="item.key"
            @click="activeTab = item.key"
            :class="[
              'w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors',
              activeTab === item.key
                ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300'
                : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-700'
            ]"
          >
            <el-icon class="mr-3" :size="18">
              <component :is="iconMap[item.icon]" />
            </el-icon>
            {{ item.label }}
          </button>
        </nav>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="flex-1 overflow-y-auto">
      <div class="max-w-4xl mx-auto p-6">
        <!-- 个人资料 -->
        <div v-if="activeTab === 'profile'" class="space-y-6">
          <div class="card-tech p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">
              个人资料
            </h3>

            <el-form :model="profileForm" :rules="profileRules" ref="profileFormRef" label-width="120px">
              <!-- 头像 -->
              <el-form-item label="头像">
                <div class="flex items-center space-x-4">
                  <el-avatar :size="80" :src="profileForm.avatar">
                    {{ profileForm.username?.charAt(0).toUpperCase() }}
                  </el-avatar>
                  <div>
                    <el-button size="small" @click="uploadAvatar">
                      <el-icon class="mr-1"><Upload /></el-icon>
                      更换头像
                    </el-button>
                    <p class="text-xs text-gray-500 mt-1">
                      支持 JPG、PNG 格式，建议尺寸 200x200px
                    </p>
                  </div>
                </div>
              </el-form-item>

              <el-form-item label="用户名" prop="username">
                <el-input
                  v-model="profileForm.username"
                  placeholder="请输入用户名"
                  maxlength="20"
                  show-word-limit
                  disabled
                />
                <div class="text-xs text-gray-500 mt-1">
                  用户名不可修改
                </div>
              </el-form-item>

              <el-form-item label="显示名称" prop="displayName">
                <el-input
                  v-model="profileForm.displayName"
                  placeholder="请输入显示名称"
                  maxlength="30"
                  show-word-limit
                />
              </el-form-item>

              <el-form-item label="邮箱地址" prop="email">
                <el-input
                  v-model="profileForm.email"
                  placeholder="请输入邮箱地址"
                  type="email"
                />
              </el-form-item>

              <el-form-item label="个人简介" prop="bio">
                <el-input
                  v-model="profileForm.bio"
                  type="textarea"
                  placeholder="请输入个人简介（可选）"
                  :rows="4"
                  maxlength="200"
                  show-word-limit
                />
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="saveProfile" :loading="saving">
                  保存更改
                </el-button>
                <el-button @click="resetProfile">
                  重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 修改密码 -->
          <div class="card-tech p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">
              修改密码
            </h3>

            <el-form :model="passwordForm" :rules="passwordRules" ref="passwordFormRef" label-width="120px">
              <el-form-item label="当前密码" prop="oldPassword">
                <el-input
                  v-model="passwordForm.oldPassword"
                  type="password"
                  placeholder="请输入当前密码"
                  show-password
                />
              </el-form-item>

              <el-form-item label="新密码" prop="newPassword">
                <el-input
                  v-model="passwordForm.newPassword"
                  type="password"
                  placeholder="请输入新密码"
                  show-password
                />
              </el-form-item>

              <el-form-item label="确认密码" prop="confirmPassword">
                <el-input
                  v-model="passwordForm.confirmPassword"
                  type="password"
                  placeholder="请再次输入新密码"
                  show-password
                />
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="changePassword" :loading="changingPassword">
                  修改密码
                </el-button>
                <el-button @click="resetPasswordForm">
                  重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>

        <!-- AI模型偏好 -->
        <div v-else-if="activeTab === 'ai'" class="space-y-6">
          <div class="card-tech p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">
              AI模型偏好
            </h3>

            <el-form :model="aiSettings" label-width="150px">
              <el-form-item label="默认AI模型">
                <el-select v-model="aiSettings.defaultModel" placeholder="选择默认AI模型" style="width: 300px">
                  <el-option
                    v-for="model in availableModels"
                    :key="model.id"
                    :label="model.display_name"
                    :value="model.id"
                  >
                    <div class="flex items-center justify-between">
                      <span>{{ model.display_name }}</span>
                      <el-tag size="small" type="info">{{ model.provider?.display_name }}</el-tag>
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="默认知识库">
                <el-select
                  v-model="aiSettings.defaultKnowledgeBases"
                  multiple
                  placeholder="选择默认使用的知识库"
                  style="width: 400px"
                >
                  <el-option
                    v-for="kb in availableKnowledgeBases"
                    :key="kb.id"
                    :label="kb.name"
                    :value="kb.id"
                  />
                </el-select>
                <div class="text-xs text-gray-500 mt-1">
                  新建对话时将自动选择这些知识库
                </div>
              </el-form-item>

              <el-form-item label="对话保留天数">
                <el-select v-model="aiSettings.chatRetentionDays" style="width: 200px">
                  <el-option label="7天" :value="7" />
                  <el-option label="30天" :value="30" />
                  <el-option label="90天" :value="90" />
                  <el-option label="永久保留" :value="0" />
                </el-select>
                <div class="text-xs text-gray-500 mt-1">
                  超过保留期的对话将被自动删除
                </div>
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="saveAISettings" :loading="saving">
                  保存设置
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- API密钥管理 -->
          <div class="card-tech p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">
              API密钥管理
            </h3>

            <div class="space-y-4">
              <div
                v-for="provider in aiProviders"
                :key="provider.id"
                class="border border-gray-200 dark:border-dark-700 rounded-lg p-4"
              >
                <div class="flex items-center justify-between mb-3">
                  <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 rounded-lg flex items-center justify-center"
                         :class="getProviderIconClass(provider.name)">
                      <el-icon :size="16">
                        <component :is="Robot" />
                      </el-icon>
                    </div>
                    <div>
                      <div class="flex items-center space-x-2">
                        <h4 class="font-medium text-gray-900 dark:text-gray-100">
                          {{ provider.display_name }}
                        </h4>
                        <el-tag v-if="hasSystemKey(provider)" type="info" size="small">
                          系统提供
                        </el-tag>
                      </div>
                      <p class="text-sm text-gray-500">
                        {{ provider.description || `${provider.display_name} AI模型服务` }}
                      </p>
                    </div>
                  </div>
                  <el-switch
                    v-model="provider.enabled"
                    @change="toggleProvider(provider)"
                  />
                </div>

                <div v-if="provider.enabled" class="space-y-3">
                  <!-- 选择模型 -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      选择模型
                    </label>
                    <el-select
                      v-model="provider.selectedModel"
                      placeholder="请选择要测试的模型"
                      style="width: 100%"
                      @change="onModelChange(provider)"
                    >
                      <el-option
                        v-for="model in getProviderModels(provider.id)"
                        :key="model.id"
                        :label="model.display_name"
                        :value="model.id"
                      >
                        <div class="flex items-center justify-between">
                          <span>{{ model.display_name }}</span>
                          <el-tag v-if="model.allow_system_key_use" size="small" type="success">
                            支持系统密钥
                          </el-tag>
                        </div>
                      </el-option>
                    </el-select>
                  </div>

                  <!-- 系统密钥提示 -->
                  <div v-if="provider.useSystemKey && hasSystemKey(provider)"
                       class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
                    <div class="flex items-center justify-between">
                      <div>
                        <p class="text-sm font-medium text-blue-900 dark:text-blue-100">
                          使用系统提供的API密钥
                        </p>
                        <p class="text-xs text-blue-600 dark:text-blue-300 mt-1">
                          管理员已为此服务配置了API密钥，您可以直接使用
                        </p>
                      </div>
                      <el-button size="small" type="primary" @click="useCustomKey(provider)">
                        使用自定义密钥
                      </el-button>
                    </div>
                  </div>

                  <!-- 自定义密钥输入 -->
                  <div v-else>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      API密钥
                    </label>
                    <el-input
                      v-model="provider.apiKey"
                      type="password"
                      placeholder="请输入API密钥"
                      show-password
                    >
                      <template #prepend>API Key</template>
                    </el-input>

                    <div v-if="hasSystemKey(provider)" class="mt-2">
                      <el-button size="small" type="info" @click="useSystemKey(provider)">
                        使用系统密钥
                      </el-button>
                    </div>
                  </div>

                  <!-- 操作按钮 -->
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                      <span class="text-sm text-gray-500">状态:</span>
                      <el-tag :type="getStatusType(provider.status)" size="small">
                        {{ getStatusText(provider.status) }}
                      </el-tag>
                      <span v-if="provider.lastTestTime" class="text-xs text-gray-400">
                        {{ formatTime(provider.lastTestTime) }}
                      </span>
                    </div>
                    <div class="flex items-center space-x-2">
                      <el-button
                        size="small"
                        @click="testConnection(provider)"
                        :loading="provider.testing"
                        :disabled="!canTest(provider)"
                      >
                        测试连接
                      </el-button>
                      <el-button
                        v-if="!provider.useSystemKey && provider.apiKey"
                        size="small"
                        type="success"
                        @click="saveApiKey(provider)"
                        :loading="provider.saving"
                      >
                        保存密钥
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 空状态 -->
              <div v-if="aiProviders.length === 0" class="text-center py-8">
                <el-icon :size="48" class="text-gray-400 mb-4">
                  <Robot />
                </el-icon>
                <p class="text-gray-500">暂无可用的AI供应商</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 界面偏好 -->
        <div v-else-if="activeTab === 'appearance'" class="space-y-6">
          <div class="card-tech p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">
              界面偏好
            </h3>

            <el-form :model="appearanceSettings" label-width="120px">
              <el-form-item label="主题模式">
                <el-radio-group v-model="appearanceSettings.theme" @change="applyTheme">
                  <el-radio value="light">
                    <div class="flex items-center space-x-2">
                      <el-icon><Sunny /></el-icon>
                      <span>浅色模式</span>
                    </div>
                  </el-radio>
                  <el-radio value="dark">
                    <div class="flex items-center space-x-2">
                      <el-icon><Moon /></el-icon>
                      <span>深色模式</span>
                    </div>
                  </el-radio>
                  <el-radio value="system">
                    <div class="flex items-center space-x-2">
                      <el-icon><Monitor /></el-icon>
                      <span>跟随系统</span>
                    </div>
                  </el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="字体大小">
                <el-radio-group v-model="appearanceSettings.fontSize" @change="applyFontSize">
                  <el-radio value="small">小</el-radio>
                  <el-radio value="medium">中</el-radio>
                  <el-radio value="large">大</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="高对比度">
                <el-switch
                  v-model="appearanceSettings.enableHighContrast"
                  @change="applyAccessibility"
                />
                <div class="text-xs text-gray-500 mt-1">
                  提高界面对比度，便于视力不佳的用户使用
                </div>
              </el-form-item>

              <el-form-item label="减少动画">
                <el-switch
                  v-model="appearanceSettings.enableReducedMotion"
                  @change="applyAccessibility"
                />
                <div class="text-xs text-gray-500 mt-1">
                  减少界面动画效果，提高性能并减少干扰
                </div>
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="saveAppearanceSettings" :loading="saving">
                  保存设置
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>

        <!-- 通知设置 -->
        <div v-else-if="activeTab === 'notifications'" class="space-y-6">
          <div class="card-tech p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">
              通知设置
            </h3>

            <el-form :model="notificationSettings" label-width="150px">
              <el-form-item label="邮件通知">
                <div class="space-y-3">
                  <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-700 dark:text-gray-300">文档处理完成</span>
                    <el-switch v-model="notificationSettings.email.documentProcessed" />
                  </div>
                  <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-700 dark:text-gray-300">系统维护通知</span>
                    <el-switch v-model="notificationSettings.email.systemMaintenance" />
                  </div>
                  <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-700 dark:text-gray-300">安全警告</span>
                    <el-switch v-model="notificationSettings.email.securityAlert" />
                  </div>
                </div>
              </el-form-item>

              <el-form-item label="浏览器通知">
                <div class="space-y-3">
                  <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-700 dark:text-gray-300">新消息提醒</span>
                    <el-switch v-model="notificationSettings.browser.newMessage" />
                  </div>
                  <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-700 dark:text-gray-300">任务完成提醒</span>
                    <el-switch v-model="notificationSettings.browser.taskCompleted" />
                  </div>
                </div>
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="saveNotificationSettings" :loading="saving">
                  保存设置
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>

        <!-- 数据管理 -->
        <div v-else-if="activeTab === 'data'" class="space-y-6">
          <div class="card-tech p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">
              数据管理
            </h3>

            <div class="space-y-6">
              <!-- 存储使用情况 -->
              <div>
                <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-3">
                  存储使用情况
                </h4>
                <div class="bg-gray-50 dark:bg-dark-700 rounded-lg p-4">
                  <div class="flex items-center justify-between mb-2">
                    <span class="text-sm text-gray-600 dark:text-gray-400">已使用</span>
                    <span class="text-sm font-medium">{{ formatStorage(storageUsed) }} / {{ formatStorage(storageLimit) }}</span>
                  </div>
                  <el-progress
                    :percentage="storagePercentage"
                    :color="storagePercentage > 80 ? '#f56565' : '#48bb78'"
                    :stroke-width="8"
                  />
                  <div class="grid grid-cols-2 gap-4 mt-4 text-sm">
                    <div class="flex items-center justify-between">
                      <span class="text-gray-600 dark:text-gray-400">文档</span>
                      <span>{{ formatStorage(storageBreakdown.documents) }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                      <span class="text-gray-600 dark:text-gray-400">对话记录</span>
                      <span>{{ formatStorage(storageBreakdown.chats) }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 数据导出 -->
              <div>
                <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-3">
                  数据导出
                </h4>
                <div class="space-y-3">
                  <el-button @click="exportData('profile')" :loading="exporting">
                    <el-icon class="mr-2"><Download /></el-icon>
                    导出个人资料
                  </el-button>
                  <el-button @click="exportData('chats')" :loading="exporting">
                    <el-icon class="mr-2"><Download /></el-icon>
                    导出对话记录
                  </el-button>
                  <el-button @click="exportData('all')" :loading="exporting">
                    <el-icon class="mr-2"><Download /></el-icon>
                    导出所有数据
                  </el-button>
                </div>
              </div>

              <!-- 数据清理 -->
              <div>
                <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-3">
                  数据清理
                </h4>
                <div class="space-y-3">
                  <el-button type="warning" @click="clearData('cache')">
                    <el-icon class="mr-2"><Delete /></el-icon>
                    清理缓存
                  </el-button>
                  <el-button type="warning" @click="clearData('oldChats')">
                    <el-icon class="mr-2"><Delete /></el-icon>
                    清理过期对话
                  </el-button>
                  <el-button type="danger" @click="clearData('all')">
                    <el-icon class="mr-2"><Delete /></el-icon>
                    清空所有数据
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 账户安全 -->
        <div v-else-if="activeTab === 'security'" class="space-y-6">
          <div class="card-tech p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">
              账户安全
            </h3>

            <div class="space-y-6">
              <!-- 登录活动 -->
              <div>
                <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-3">
                  最近登录活动
                </h4>
                <div class="space-y-3">
                  <div
                    v-for="activity in loginActivities"
                    :key="activity.id"
                    class="flex items-center justify-between p-3 bg-gray-50 dark:bg-dark-700 rounded-lg"
                  >
                    <div>
                      <div class="font-medium text-gray-900 dark:text-gray-100">
                        {{ activity.device }}
                      </div>
                      <div class="text-sm text-gray-500">
                        {{ activity.location }} • {{ formatTime(activity.time) }}
                      </div>
                    </div>
                    <el-tag :type="activity.current ? 'success' : 'info'" size="small">
                      {{ activity.current ? '当前会话' : '历史登录' }}
                    </el-tag>
                  </div>
                </div>
              </div>

              <!-- 安全设置 -->
              <div>
                <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-3">
                  安全设置
                </h4>
                <div class="space-y-3">
                  <div class="flex items-center justify-between">
                    <div>
                      <div class="font-medium text-gray-900 dark:text-gray-100">
                        双因素认证
                      </div>
                      <div class="text-sm text-gray-500">
                        为您的账户添加额外的安全保护
                      </div>
                    </div>
                    <el-button size="small" type="primary">
                      启用
                    </el-button>
                  </div>

                  <div class="flex items-center justify-between">
                    <div>
                      <div class="font-medium text-gray-900 dark:text-gray-100">
                        登录通知
                      </div>
                      <div class="text-sm text-gray-500">
                        新设备登录时发送邮件通知
                      </div>
                    </div>
                    <el-switch v-model="securitySettings.loginNotification" />
                  </div>
                </div>
              </div>

              <!-- 危险操作 -->
              <div>
                <h4 class="font-medium text-red-600 dark:text-red-400 mb-3">
                  危险操作
                </h4>
                <div class="space-y-3">
                  <el-button type="danger" @click="deleteAccount">
                    <el-icon class="mr-2"><Delete /></el-icon>
                    删除账户
                  </el-button>
                  <div class="text-xs text-gray-500">
                    删除账户将永久删除您的所有数据，此操作不可恢复
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { useSettingsStore } from '@/stores/settings'
import { useAIModelsStore } from '@/stores/aiModels'
import type { User, UserSettings, AIProvider, AIModel, UserAPIKey } from '@/types'
import {
  User as UserIcon,
  ChatDotRound as Robot,
  Brush as Palette,
  Bell,
  Document as Files,
  Lock,
  Upload,
  Sunny,
  Moon,
  Monitor,
  Download,
  Delete
} from '@element-plus/icons-vue'
import { api } from '@/utils/api'

const authStore = useAuthStore()
const settingsStore = useSettingsStore()
const aiModelsStore = useAIModelsStore()

// 扩展的 AI Provider 接口，包含前端特有的属性
interface ExtendedAIProvider extends AIProvider {
  enabled: boolean
  apiKey: string
  status: 'untested' | 'testing' | 'valid' | 'invalid'
  testing: boolean
  saving: boolean
  useSystemKey: boolean
  selectedModel: number | null
  userApiKey: UserAPIKey | null
  lastTestTime: string | null
}

// 响应式数据
const activeTab = ref('profile')
const saving = ref(false)
const changingPassword = ref(false)
const exporting = ref(false)

// 表单引用
const profileFormRef = ref()
const passwordFormRef = ref()

// 图标映射
const iconMap: Record<string, any> = {
  UserIcon,
  Robot,
  Palette,
  Bell,
  Files,
  Lock
}

// 设置菜单
const settingsMenu = [
  { key: 'profile', label: '个人资料', icon: 'UserIcon' },
  { key: 'ai', label: 'AI模型', icon: 'Robot' },
  { key: 'appearance', label: '界面偏好', icon: 'Palette' },
  { key: 'notifications', label: '通知设置', icon: 'Bell' },
  { key: 'data', label: '数据管理', icon: 'Files' },
  { key: 'security', label: '账户安全', icon: 'Lock' }
]

// 个人资料表单
const profileForm = reactive({
  username: authStore.user?.username || '',
  displayName: authStore.user?.display_name || '',
  email: authStore.user?.email || '',
  bio: authStore.user?.bio || '',
  avatar: authStore.user?.avatar_url || ''
})

const profileRules = {
  displayName: [
    { max: 30, message: '显示名称不能超过30个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  bio: [
    { max: 200, message: '个人简介不能超过200个字符', trigger: 'blur' }
  ]
}

// 密码修改表单
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// AI设置
const aiSettings = reactive({
  defaultModel: null as number | null,
  defaultKnowledgeBases: [] as number[],
  chatRetentionDays: 30
})

// 外观设置
const appearanceSettings = reactive({
  theme: 'light' as 'light' | 'dark' | 'system',
  fontSize: 'medium' as 'small' | 'medium' | 'large',
  enableHighContrast: false,
  enableReducedMotion: false
})

// 通知设置
const notificationSettings = reactive({
  email: {
    documentProcessed: true,
    systemMaintenance: true,
    securityAlert: true
  },
  browser: {
    newMessage: true,
    taskCompleted: true
  }
})

// 安全设置
const securitySettings = reactive({
  loginNotification: true
})

// AI相关数据
const aiProviders = ref<ExtendedAIProvider[]>([])
const aiModels = ref<AIModel[]>([])
const userApiKeys = ref<UserAPIKey[]>([])
const knowledgeBases = ref<any[]>([])

// 可用知识库（从后端加载）
const availableKnowledgeBases = computed(() => knowledgeBases.value)

// 存储数据
const storageUsed = ref(2.5 * 1024 * 1024 * 1024) // 2.5GB
const storageLimit = ref(10 * 1024 * 1024 * 1024) // 10GB
const storageBreakdown = ref({
  documents: 2.1 * 1024 * 1024 * 1024,
  chats: 0.4 * 1024 * 1024 * 1024
})

// 登录活动
const loginActivities = ref([
  {
    id: 1,
    device: 'Chrome on Windows',
    location: '北京, 中国',
    time: '2024-01-20T10:30:00Z',
    current: true
  },
  {
    id: 2,
    device: 'Safari on iPhone',
    location: '上海, 中国',
    time: '2024-01-19T15:20:00Z',
    current: false
  }
])

// 计算属性
const storagePercentage = computed(() => {
  return Math.round((storageUsed.value / storageLimit.value) * 100)
})

const availableModels = computed(() => {
  return aiModels.value.filter(model => {
    if (!model.is_active) return false

    // 查找该模型对应的供应商
    const provider = aiProviders.value.find(p => p.id === model.provider_id)
    if (!provider) return false

    // 检查供应商是否被用户启用
    if (!provider.enabled) return false

    // 检查用户是否有该供应商的API密钥，或者该模型允许使用系统密钥
    const hasUserKey = userApiKeys.value.some(key => key.provider_id === provider.id)
    const hasSystemKey = model.allow_system_key_use && model.system_api_key

    return hasUserKey || hasSystemKey
  })
})

// 全局可用模型（供其他组件使用）
const getGlobalAvailableModels = () => {
  // 构建供应商偏好对象
  const providerPreferences: Record<string, any> = {}
  aiProviders.value.forEach(provider => {
    providerPreferences[provider.id] = {
      enabled: provider.enabled,
      useSystemKey: provider.useSystemKey,
      selectedModel: provider.selectedModel
    }
  })

  return aiModelsStore.getAvailableModels(providerPreferences)
}

// 工具方法
const formatStorage = (bytes: number) => {
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  if (bytes === 0) return '0 B'
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}



// 个人资料相关方法
const uploadAvatar = () => {
  ElMessage.info('头像上传功能开发中...')
}

const saveProfile = async () => {
  if (!profileFormRef.value) return

  try {
    await profileFormRef.value.validate()
    saving.value = true

    // 调用API更新个人资料
    const result = await authStore.updateProfile({
      display_name: profileForm.displayName,
      email: profileForm.email,
      bio: profileForm.bio
    })

    if (result.success) {
      ElMessage.success('个人资料更新成功')
    } else {
      ElMessage.error(result.message || '更新失败')
    }
  } catch (error) {
    ElMessage.error('保存失败，请检查输入信息')
  } finally {
    saving.value = false
  }
}

const resetProfile = () => {
  if (authStore.user) {
    profileForm.username = authStore.user.username
    profileForm.displayName = authStore.user.display_name || ''
    profileForm.email = authStore.user.email
    profileForm.bio = authStore.user.bio || ''
    profileForm.avatar = authStore.user.avatar_url || ''
  }
}

const changePassword = async () => {
  if (!passwordFormRef.value) return

  try {
    await passwordFormRef.value.validate()
    changingPassword.value = true

    // 调用API修改密码
    const result = await authStore.changePassword(
      passwordForm.oldPassword,
      passwordForm.newPassword
    )

    if (result.success) {
      ElMessage.success('密码修改成功')
      resetPasswordForm()
    } else {
      ElMessage.error(result.message || '密码修改失败')
    }
  } catch (error) {
    ElMessage.error('密码修改失败')
  } finally {
    changingPassword.value = false
  }
}

const resetPasswordForm = () => {
  passwordForm.oldPassword = ''
  passwordForm.newPassword = ''
  passwordForm.confirmPassword = ''
  if (passwordFormRef.value) {
    passwordFormRef.value.resetFields()
  }
}

// 数据加载方法
const loadAIProviders = async () => {
  try {
    console.log('开始加载AI供应商...')
    console.log('当前token:', localStorage.getItem('token'))
    console.log('用户认证状态:', authStore.isAuthenticated)
    console.log('用户信息:', authStore.user)

    const response = await api.get('/ai/providers')
    console.log('AI供应商响应:', response)
    const providers = response.data

    // 转换为前端需要的格式
    aiProviders.value = providers.map((provider: any) => ({
      ...provider,
      enabled: true, // 默认勾选所有供应商
      apiKey: '',
      status: 'untested',
      testing: false,
      saving: false,
      useSystemKey: true,
      selectedModel: null,
      userApiKey: null,
      lastTestTime: null
    }))
    console.log('AI供应商加载成功:', aiProviders.value)
  } catch (error: any) {
    console.error('加载AI供应商失败:', error)
    console.error('错误响应:', error.response)
    console.error('错误详情:', error.response?.data)
    ElMessage.error(`加载AI供应商失败: ${error.response?.status} ${error.response?.statusText}`)
  }
}

const loadAIModels = async () => {
  try {
    const response = await api.get('/ai/models')
    aiModels.value = response.data
  } catch (error) {
    ElMessage.error('加载AI模型失败')
  }
}

const loadUserApiKeys = async () => {
  try {
    const response = await api.get('/ai/api-keys')
    userApiKeys.value = response.data

    // 将用户密钥关联到供应商
    userApiKeys.value.forEach((key: any) => {
      const provider = aiProviders.value.find((p: any) => p.id === key.provider_id)
      if (provider) {
        provider.userApiKey = key
        provider.apiKey = key.api_key || ''
        provider.useSystemKey = false
        provider.enabled = true
        provider.status = 'valid'
      }
    })
  } catch (error) {
    ElMessage.error('加载用户API密钥失败')
  }
}

const loadKnowledgeBases = async () => {
  try {
    const response = await api.get('/knowledge-bases/')
    knowledgeBases.value = response.data
  } catch (error) {
    console.error('加载知识库失败:', error)
    // 不显示错误消息，因为知识库是可选的
  }
}

// AI设置相关方法
const saveAISettings = async () => {
  try {
    saving.value = true

    // 保存供应商偏好设置
    const providerPreferences: Record<string, any> = {}
    aiProviders.value.forEach(provider => {
      providerPreferences[provider.id] = {
        enabled: provider.enabled,
        useSystemKey: provider.useSystemKey,
        selectedModel: provider.selectedModel
      }
    })

    // 调用API保存AI设置
    const result = await settingsStore.updateUserSettings({
      defaultModel: aiSettings.defaultModel,
      defaultKnowledgeBases: aiSettings.defaultKnowledgeBases,
      chatRetentionDays: aiSettings.chatRetentionDays,
      providerPreferences: providerPreferences
    })

    if (result.success) {
      ElMessage.success('AI设置保存成功')
    } else {
      ElMessage.error(result.message || '保存失败')
    }
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 工具方法
const getProviderIconClass = (providerName: string) => {
  switch (providerName?.toLowerCase()) {
    case 'openai':
      return 'bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400'
    case 'anthropic':
      return 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
    case 'google':
      return 'bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-400'
    case 'siliconflow':
    case '硅基流动':
      return 'bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400'
    case 'deepseek':
      return 'bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-400'
    default:
      return 'bg-gray-100 dark:bg-gray-900 text-gray-600 dark:text-gray-400'
  }
}

const getProviderModels = (providerId: number) => {
  return aiModels.value.filter((model: any) => model.provider_id === providerId && model.is_active)
}

const hasSystemKey = (provider: any) => {
  const models = getProviderModels(provider.id)
  return models.some((model: any) => model.allow_system_key_use && model.system_api_key)
}

const canTest = (provider: any) => {
  return provider.selectedModel && (provider.apiKey || (provider.useSystemKey && hasSystemKey(provider)))
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'valid': return 'success'
    case 'invalid': return 'danger'
    case 'testing': return 'warning'
    default: return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'valid': return '有效'
    case 'invalid': return '无效'
    case 'testing': return '测试中'
    default: return '未测试'
  }
}

const formatTime = (timeString: string) => {
  const date = new Date(timeString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  if (diff < 60000) { // 小于1分钟
    return '刚刚'
  } else if (diff < 3600000) { // 小于1小时
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) { // 小于1天
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return date.toLocaleDateString()
  }
}

// 供应商相关方法
const toggleProvider = (provider: any) => {
  if (!provider.enabled) {
    provider.selectedModel = null
    provider.status = 'untested'
  }
  // 自动保存供应商偏好
  saveProviderPreferences()
}

// 保存供应商偏好设置
const saveProviderPreferences = async () => {
  try {
    const providerPreferences: Record<string, any> = {}
    aiProviders.value.forEach(provider => {
      providerPreferences[provider.id] = {
        enabled: provider.enabled,
        useSystemKey: provider.useSystemKey,
        selectedModel: provider.selectedModel
      }
    })

    await settingsStore.updateUserSettings({
      providerPreferences: providerPreferences
    })
  } catch (error) {
    console.error('保存供应商偏好失败:', error)
  }
}

const onModelChange = (provider: any) => {
  provider.status = 'untested'
}

const testConnection = async (provider: any) => {
  if (!canTest(provider)) {
    ElMessage.warning('请先选择模型并配置API密钥')
    return
  }

  provider.testing = true
  provider.status = 'testing'

  try {
    const selectedModel = aiModels.value.find((m: any) => m.id === provider.selectedModel)
    if (!selectedModel) {
      throw new Error('未找到选择的模型')
    }

    const testData = {
      provider_id: provider.id,
      model_name: selectedModel.model_name,
      api_key: provider.useSystemKey ? undefined : provider.apiKey
    }

    const response = await api.post('/ai/test-connection', testData)
    const result = response.data

    if (result.success) {
      provider.status = 'valid'
      provider.lastTestTime = new Date().toISOString()
      ElMessage.success(`${provider.display_name} 连接测试成功`)
    } else {
      provider.status = 'invalid'
      // 静默处理失败，不显示弹窗提示
    }
  } catch (error) {
    provider.status = 'invalid'
    // 静默处理错误，不显示弹窗提示
  } finally {
    provider.testing = false
  }
}

const saveApiKey = async (provider: any) => {
  if (!provider.apiKey) {
    ElMessage.warning('请输入API密钥')
    return
  }

  provider.saving = true

  try {
    if (provider.userApiKey) {
      // 更新现有密钥
      await api.put(`/ai/api-keys/${provider.userApiKey.id}`, {
        api_key: provider.apiKey,
        description: `${provider.display_name} API密钥`
      })
    } else {
      // 创建新密钥
      const response = await api.post('/ai/api-keys', {
        provider_id: provider.id,
        api_key: provider.apiKey,
        description: `${provider.display_name} API密钥`
      })
      provider.userApiKey = response.data
    }

    ElMessage.success('API密钥保存成功')
    await loadUserApiKeys() // 重新加载用户密钥
  } catch (error) {
    ElMessage.error('API密钥保存失败')
  } finally {
    provider.saving = false
  }
}

// 使用自定义密钥
const useCustomKey = (provider: any) => {
  provider.useSystemKey = false
  provider.apiKey = provider.userApiKey?.api_key || ''
  provider.status = 'untested'
  ElMessage.info('已切换到自定义API密钥模式，请输入您的密钥')
  saveProviderPreferences()
}

// 恢复使用系统密钥
const useSystemKey = (provider: any) => {
  if (hasSystemKey(provider)) {
    provider.useSystemKey = true
    provider.apiKey = ''
    provider.status = 'untested'
    ElMessage.success('已切换到系统提供的API密钥')
    saveProviderPreferences()
  } else {
    ElMessage.warning('该服务没有系统提供的API密钥')
  }
}

// 外观设置相关方法
const applyTheme = async (theme: string) => {
  await settingsStore.applyTheme(theme as 'light' | 'dark' | 'system')
}

const applyFontSize = (fontSize: string) => {
  settingsStore.applyFontSize(fontSize as 'small' | 'medium' | 'large')
}

const applyAccessibility = () => {
  settingsStore.applyAccessibilitySettings()
}

const saveAppearanceSettings = async () => {
  try {
    saving.value = true

    const result = await settingsStore.updateUserSettings({
      theme: appearanceSettings.theme,
      fontSize: appearanceSettings.fontSize,
      enableHighContrast: appearanceSettings.enableHighContrast,
      enableReducedMotion: appearanceSettings.enableReducedMotion
    })

    if (result.success) {
      ElMessage.success('界面设置保存成功')
    } else {
      ElMessage.error(result.message || '保存失败')
    }
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 通知设置相关方法
const saveNotificationSettings = async () => {
  try {
    saving.value = true

    // 这里应该调用API保存通知设置
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('通知设置保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 数据管理相关方法
const exportData = async (type: string) => {
  try {
    exporting.value = true

    let data: any = {}
    let filename = ''

    switch (type) {
      case 'profile':
        data = { profile: profileForm }
        filename = 'profile.json'
        break
      case 'chats':
        data = { chats: [] } // 这里应该获取实际的对话数据
        filename = 'chats.json'
        break
      case 'all':
        data = { profile: profileForm, chats: [], settings: aiSettings }
        filename = 'all_data.json'
        break
    }

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    a.click()
    URL.revokeObjectURL(url)

    ElMessage.success('数据导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  } finally {
    exporting.value = false
  }
}

const clearData = async (type: string) => {
  let confirmText = ''
  let successText = ''

  switch (type) {
    case 'cache':
      confirmText = '确定要清理缓存吗？'
      successText = '缓存清理成功'
      break
    case 'oldChats':
      confirmText = '确定要清理过期对话吗？'
      successText = '过期对话清理成功'
      break
    case 'all':
      confirmText = '确定要清空所有数据吗？此操作不可恢复！'
      successText = '所有数据已清空'
      break
  }

  try {
    await ElMessageBox.confirm(confirmText, '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      confirmButtonClass: type === 'all' ? 'el-button--danger' : ''
    })

    // 这里应该调用相应的清理API
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success(successText)
  } catch (error) {
    // 用户取消操作
  }
}

// 安全相关方法
const deleteAccount = async () => {
  try {
    await ElMessageBox.confirm(
      '删除账户将永久删除您的所有数据，包括知识库、对话记录等。此操作不可恢复！',
      '确认删除账户',
      {
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        type: 'error',
        confirmButtonClass: 'el-button--danger'
      }
    )

    // 这里应该调用删除账户API
    ElMessage.success('账户删除请求已提交，请查收邮件确认')
  } catch (error) {
    // 用户取消删除
  }
}

// 初始化
onMounted(async () => {
  // 检查用户认证状态
  if (!authStore.isAuthenticated) {
    ElMessage.error('请先登录')
    return
  }

  // 加载AI相关数据
  await loadAIProviders()
  await loadAIModels()
  await loadUserApiKeys()
  await loadKnowledgeBases()

  // 加载用户设置
  await settingsStore.fetchUserSettings()

  // 同步设置到本地状态
  const userSettings = settingsStore.userSettings
  console.log('加载的用户设置:', userSettings)
  aiSettings.defaultModel = userSettings.defaultModel
  aiSettings.defaultKnowledgeBases = userSettings.defaultKnowledgeBases
  aiSettings.chatRetentionDays = userSettings.chatRetentionDays

  appearanceSettings.theme = userSettings.theme
  appearanceSettings.fontSize = userSettings.fontSize
  appearanceSettings.enableHighContrast = userSettings.enableHighContrast
  appearanceSettings.enableReducedMotion = userSettings.enableReducedMotion

  // 应用供应商偏好设置
  if (userSettings.providerPreferences) {
    aiProviders.value.forEach(provider => {
      const prefs = userSettings.providerPreferences?.[provider.id]
      if (prefs) {
        provider.enabled = prefs.enabled !== undefined ? prefs.enabled : true // 如果没有设置，默认启用
        provider.useSystemKey = prefs.useSystemKey !== undefined ? prefs.useSystemKey : true
        provider.selectedModel = prefs.selectedModel || null
      } else {
        // 如果没有该供应商的偏好设置，默认启用
        provider.enabled = true
      }
    })
  } else {
    // 如果完全没有偏好设置，默认启用所有供应商
    aiProviders.value.forEach(provider => {
      provider.enabled = true
    })
  }
})
</script>
