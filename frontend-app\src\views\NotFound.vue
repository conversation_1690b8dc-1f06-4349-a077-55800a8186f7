<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-dark-900">
    <div class="text-center">
      <div class="mb-8">
        <h1 class="text-9xl font-bold text-gradient">404</h1>
      </div>
      <h2 class="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
        页面未找到
      </h2>
      <p class="text-gray-600 dark:text-gray-400 mb-8 max-w-md">
        抱歉，您访问的页面不存在。可能是链接错误或页面已被移除。
      </p>
      <div class="space-x-4">
        <el-button type="primary" @click="goBack">
          返回上页
        </el-button>
        <el-button @click="goHome">
          回到首页
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const goBack = () => {
  router.go(-1)
}

const goHome = () => {
  if (authStore.isAuthenticated) {
    if (authStore.user?.is_admin) {
      router.push('/admin/dashboard')
    } else {
      router.push('/user/home')
    }
  } else {
    router.push('/login')
  }
}
</script>
