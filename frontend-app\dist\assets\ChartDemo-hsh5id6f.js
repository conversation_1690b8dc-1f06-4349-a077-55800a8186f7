import{E as a}from"./elementPlus-Di4PDIm8.js";import{dB as t,dN as e,dU as l,dc as d,dd as s,dg as r,dj as o,dk as m,dl as i,ed as g}from"./vendor-BJ-uKP15.js";import{b as n}from"./_plugin-vue_export-helper-CjD0mXop.js";import{b as x,c}from"./LineChart-Bgmbl9FC.js";import{b as u}from"./BarChart-r5W9w9bw.js";const h={class:"p-6 space-y-6"},v={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},b={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},y={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},p={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},f={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},k={class:"text-center"};var _=n(i({__name:"ChartDemo",setup(i){const n=d(()=>document.documentElement.classList.contains("dark")),_=()=>{document.documentElement.classList.toggle("dark")},j=l([{name:"已使用",value:320,color:"#3b82f6"},{name:"可用空间",value:680,color:"#e5e7eb"}]),w=l([{name:"管理员",value:5,color:"#ef4444"},{name:"普通用户",value:45,color:"#10b981"},{name:"访客",value:20,color:"#f59e0b"}]),C=l(["1月","2月","3月","4月","5月","6月","7月"]),E=l([{name:"新用户",data:[120,132,101,134,90,230,210],color:"#3b82f6"},{name:"活跃用户",data:[220,182,191,234,290,330,310],color:"#10b981"},{name:"留存用户",data:[150,232,201,154,190,330,410],color:"#f59e0b"}]),L=l(["知识库","文档","聊天","用户"]),B=l([{name:"本月",data:[120,200,150,80],color:"#3b82f6"},{name:"上月",data:[100,180,120,70],color:"#10b981"}]);return(l,d)=>{const i=a;return t(),r("div",h,[d[7]||(d[7]=s("div",{class:"text-center mb-8"},[s("h1",{class:"text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2"},"图表演示"),s("p",{class:"text-gray-600 dark:text-gray-400"},"展示各种图表组件的效果")],-1)),s("div",v,[d[2]||(d[2]=s("h2",{class:"text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4"},"饼图示例",-1)),s("div",b,[s("div",null,[d[0]||(d[0]=s("h3",{class:"text-lg font-medium text-gray-700 dark:text-gray-300 mb-2"},"存储使用分布",-1)),m(c,{data:j.value,height:"300px",theme:n.value?"dark":"light"},null,8,["data","theme"])]),s("div",null,[d[1]||(d[1]=s("h3",{class:"text-lg font-medium text-gray-700 dark:text-gray-300 mb-2"},"用户类型分布",-1)),m(c,{data:w.value,height:"300px",theme:n.value?"dark":"light",radius:["30%","60%"]},null,8,["data","theme"])])])]),s("div",y,[d[3]||(d[3]=s("h2",{class:"text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4"},"折线图示例",-1)),m(x,{data:E.value,"x-axis-data":C.value,height:"350px",theme:n.value?"dark":"light","y-axis-name":"数量",title:"用户活动趋势"},null,8,["data","x-axis-data","theme"])]),s("div",p,[d[6]||(d[6]=s("h2",{class:"text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4"},"柱状图示例",-1)),s("div",f,[s("div",null,[d[4]||(d[4]=s("h3",{class:"text-lg font-medium text-gray-700 dark:text-gray-300 mb-2"},"垂直柱状图",-1)),m(u,{data:B.value,"x-axis-data":L.value,height:"300px",theme:n.value?"dark":"light","y-axis-name":"数量"},null,8,["data","x-axis-data","theme"])]),s("div",null,[d[5]||(d[5]=s("h3",{class:"text-lg font-medium text-gray-700 dark:text-gray-300 mb-2"},"水平柱状图",-1)),m(u,{data:B.value,"x-axis-data":L.value,height:"300px",theme:n.value?"dark":"light","y-axis-name":"数量",horizontal:!0},null,8,["data","x-axis-data","theme"])])])]),s("div",k,[m(i,{onClick:_,type:"primary"},{default:e(()=>[o(" 切换主题 (当前: "+g(n.value?"深色":"浅色")+") ",1)]),_:1})])])}}}),[["__scopeId","data-v-d321a346"]]);export{_ as default};