<template>
  <div class="api-test-page">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>API连接测试</span>
          <el-button type="primary" @click="runAllTests" :loading="testing">
            {{ testing ? '测试中...' : '运行所有测试' }}
          </el-button>
        </div>
      </template>

      <div class="test-results">
        <el-alert
          v-if="testResults"
          :title="getTestSummary()"
          :type="getTestType()"
          :closable="false"
          show-icon
          class="mb-4"
        />

        <el-collapse v-model="activeNames">
          <el-collapse-item title="认证API测试" name="auth">
            <div class="test-item">
              <el-tag :type="getTagType(testResults?.auth)">
                {{ testResults?.auth ? '✅ 通过' : '❌ 失败' }}
              </el-tag>
              <span class="test-desc">登录、获取用户信息</span>
            </div>
          </el-collapse-item>

          <el-collapse-item title="AI模型API测试" name="aiModels">
            <div class="test-item">
              <el-tag :type="getTagType(testResults?.aiModels)">
                {{ testResults?.aiModels ? '✅ 通过' : '❌ 失败' }}
              </el-tag>
              <span class="test-desc">获取AI模型列表</span>
            </div>
          </el-collapse-item>

          <el-collapse-item title="知识库API测试" name="knowledgeBase">
            <div class="test-item">
              <el-tag :type="getTagType(testResults?.knowledgeBase)">
                {{ testResults?.knowledgeBase ? '✅ 通过' : '❌ 失败' }}
              </el-tag>
              <span class="test-desc">创建、获取、删除知识库</span>
            </div>
          </el-collapse-item>

          <el-collapse-item title="聊天API测试" name="chat">
            <div class="test-item">
              <el-tag :type="getTagType(testResults?.chat)">
                {{ testResults?.chat ? '✅ 通过' : '❌ 失败' }}
              </el-tag>
              <span class="test-desc">创建会话、获取历史记录</span>
            </div>
          </el-collapse-item>

          <el-collapse-item title="统计API测试" name="stats">
            <div class="test-item">
              <el-tag :type="getTagType(testResults?.stats)">
                {{ testResults?.stats ? '✅ 通过' : '❌ 失败' }}
              </el-tag>
              <span class="test-desc">获取统计数据</span>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>

      <div class="console-output" v-if="consoleOutput.length > 0">
        <h4>控制台输出:</h4>
        <div class="console-content">
          <div 
            v-for="(log, index) in consoleOutput" 
            :key="index" 
            :class="['console-line', log.type]"
          >
            {{ log.message }}
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { testAPI } from '@/api/test'

// 响应式数据
const testing = ref(false)
const testResults = ref<any>(null)
const activeNames = ref(['auth', 'aiModels', 'knowledgeBase', 'chat', 'stats'])
const consoleOutput = ref<Array<{ type: string; message: string }>>([])

// 重写console方法来捕获输出
const originalConsoleLog = console.log
const originalConsoleError = console.error

const captureConsole = () => {
  console.log = (...args) => {
    consoleOutput.value.push({
      type: 'log',
      message: args.join(' ')
    })
    originalConsoleLog(...args)
  }

  console.error = (...args) => {
    consoleOutput.value.push({
      type: 'error',
      message: args.join(' ')
    })
    originalConsoleError(...args)
  }
}

const restoreConsole = () => {
  console.log = originalConsoleLog
  console.error = originalConsoleError
}

// 运行所有测试
const runAllTests = async () => {
  testing.value = true
  consoleOutput.value = []
  
  try {
    captureConsole()
    const results = await testAPI.runAllTests()
    testResults.value = results
  } catch (error) {
    console.error('测试运行失败:', error)
  } finally {
    restoreConsole()
    testing.value = false
  }
}

// 获取测试摘要
const getTestSummary = () => {
  if (!testResults.value) return ''
  
  const successCount = Object.values(testResults.value).filter(Boolean).length
  const totalCount = Object.keys(testResults.value).length
  
  return `测试完成: ${successCount}/${totalCount} 个API测试通过`
}

// 获取测试类型
const getTestType = () => {
  if (!testResults.value) return 'info'
  
  const successCount = Object.values(testResults.value).filter(Boolean).length
  const totalCount = Object.keys(testResults.value).length
  
  if (successCount === totalCount) return 'success'
  if (successCount === 0) return 'error'
  return 'warning'
}

// 获取标签类型
const getTagType = (success: boolean | undefined) => {
  if (success === undefined) return 'info'
  return success ? 'success' : 'danger'
}

onMounted(() => {
  // 页面加载时自动运行测试
  runAllTests()
})
</script>

<style scoped>
.api-test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-results {
  margin-bottom: 20px;
}

.test-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 0;
}

.test-desc {
  color: #666;
}

.console-output {
  margin-top: 20px;
  border-top: 1px solid #eee;
  padding-top: 20px;
}

.console-content {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.console-line {
  margin-bottom: 5px;
  white-space: pre-wrap;
}

.console-line.log {
  color: #333;
}

.console-line.error {
  color: #f56c6c;
}

.mb-4 {
  margin-bottom: 16px;
}
</style>
