fastapi==0.104.1
uvicorn[standard]==0.24.0
sqlmodel==0.0.14
asyncpg==0.30.0
psycopg2-binary==2.9.10
pydantic-settings==2.1.0
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
bcrypt==4.0.1
python-multipart==0.0.6
alembic==1.13.1
python-dotenv==1.0.0
email-validator==2.1.0

# AI和向量化相关
openai==1.86.0
redis==5.0.1
httpx==0.25.2
numpy==1.24.3
scikit-learn==1.3.2

# 文档处理相关（简化版本）
pypdf==3.17.4
python-docx==1.1.0
openpyxl==3.1.2
python-pptx==0.6.21

# 异步HTTP客户端（已在Dockerfile中使用 --prefer-binary 参数）
aiohttp==3.9.1

# 加密和安全相关
cryptography==41.0.7

# 其他工具库
coloredlogs==15.0.1
humanfriendly==10.0
sympy==1.12
markdown-it-py==3.0.0
pygments==2.17.2
typer==0.9.0
watchfiles==0.21.0
websockets==12.0

# 其他可能需要的依赖
# pydantic[email] 已通过 email-validator 单独安装
