import request from './request'

// 认证相关接口类型
export interface LoginRequest {
  username: string
  password: string
}

export interface RegisterRequest {
  username: string
  email: string
  password: string
  display_name?: string
}

export interface UpdateProfileRequest {
  display_name?: string
  bio?: string
  avatar_url?: string
}

export interface ChangePasswordRequest {
  current_password: string
  new_password: string
}

export interface LoginResponse {
  access_token: string
  token_type: string
}

export interface UserInfo {
  id: number
  username: string
  email: string
  display_name: string
  avatar_url?: string
  bio?: string
  is_admin: boolean
  status: string
  last_login_at?: string
  created_at: string
  updated_at: string
}

export interface UserQuota {
  max_kbs: number
  max_docs_per_kb: number
  max_storage_mb: number
  current_kbs?: number
  current_storage_mb?: number
}

// 单独定义每个API函数
const login = (data: LoginRequest): Promise<LoginResponse> => {
  return request.post('/auth/login', data)
}

const register = (data: RegisterRequest): Promise<UserInfo> => {
  return request.post('/auth/register', data)
}

const getCurrentUser = (): Promise<UserInfo> => {
  return request.get('/auth/me')
}

const updateProfile = (data: UpdateProfileRequest): Promise<UserInfo> => {
  return request.put('/auth/profile', data)
}

const changePassword = (data: ChangePasswordRequest): Promise<{ message: string }> => {
  return request.put('/auth/password', data)
}

const getUserQuota = (): Promise<UserQuota> => {
  return request.get('/auth/quota')
}

const logout = (): Promise<{ message: string }> => {
  return request.post('/auth/logout')
}

// 认证API
export const authAPI = {
  login,
  register,
  getCurrentUser,
  updateProfile,
  changePassword,
  getUserQuota,
  logout
}
