var e;(()=>{function a(e,a,t,s,l,r,n){try{var d=e[r](n),o=d.value}catch(e){return void t(e)}d.done?a(o):Promise.resolve(o).then(s,l)}e=function(e){return function(){var t=this,s=arguments;return new Promise(function(l,r){var n=e.apply(t,s);function d(e){a(n,l,r,d,o,"next",e)}function o(e){a(n,l,r,d,o,"throw",e)}d(void 0)})}}})();import{E as a,I as t,J as s,N as l,q as r,r as n,s as d,t as o}from"./elementPlus-Di4PDIm8.js";import{bM as i,bT as u,be as c,bj as m,bn as f,bt as v,c1 as g,c4 as x,c5 as p,cT as h,cU as b,ca as k,d4 as y,d8 as _,dB as w,dD as C,dF as j,dH as S,dN as z,dO as D,dU as U,d_ as I,dc as L,dd as A,de as H,df as M,dg as N,dj as P,dk as T,dl as V,ea as E,ed as O}from"./vendor-BJ-uKP15.js";import{b as R}from"./index-Byt5TjPh.js";import"./api-D-gMiCJf.js";import{b as q}from"./settings-46b1LTsi.js";import{b as B}from"./logo-D-wHJMD-.js";import{b as F}from"./ui-xhrfN-Sd.js";const J={class:"min-h-screen w-full bg-gray-50 dark:bg-dark-900"},W={class:"bg-white dark:bg-dark-800 shadow-sm border-b border-gray-200 dark:border-dark-700 transition-all duration-300"},$={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},G={class:"flex justify-between items-center h-16"},K={class:"flex items-center space-x-8"},Q={class:"hidden md:flex space-x-6"},X={class:"flex items-center space-x-4"},Y={class:"flex items-center space-x-2 cursor-pointer p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-700"},Z={class:"hidden md:block text-sm font-medium text-gray-700 dark:text-gray-300"},ee={class:"space-y-4"},ae={key:0,class:"text-center text-gray-500 py-8"},te={class:"flex justify-between items-start"},se={class:"font-medium text-gray-900 dark:text-gray-100"},le={class:"text-sm text-gray-600 dark:text-gray-400 mt-1"},re={class:"text-xs text-gray-400 mt-2 block"};var ne=V({__name:"UserLayout",setup(V){const ne=b(),de=h(),oe=R(),ie=q(),ue=F(),ce=L(()=>"Chat"===de.name),me=L(()=>document.documentElement.classList.contains("dark")),fe=(ve=e(function*(){const e=ie.userSettings.theme;let a;a="light"===e?"dark":"dark"===e?"system":"light",yield ie.applyTheme(a)}),function(){return ve.apply(this,arguments)});var ve;const ge={House:i,Collection:v,ChatDotRound:f,SettingIcon:g},xe=[{name:"首页",to:"/user/home",icon:"House"},{name:"知识库",to:"/user/knowledge-base",icon:"Collection"},{name:"智能对话",to:"/user/chat",icon:"ChatDotRound"},{name:"设置",to:"/user/settings",icon:"SettingIcon"}],pe=U(!1),he=U([{id:1,title:"文档处理完成",content:'您上传的文档"技术文档.pdf"已处理完成',createdAt:(new Date).toISOString(),read:!1}]),be=L(()=>he.value.filter(e=>!e.read).length),ke=e=>{switch(e){case"profile":case"settings":ne.push("/user/settings");break;case"logout":oe.logout(),ne.push("/login")}};return(e,i)=>{const f=l,v=j("router-link"),h=a,b=t,U=s,L=n,V=d,R=r,q=j("router-view"),F=o;return w(),N("div",J,[D(A("header",W,[A("div",$,[A("div",G,[A("div",K,[i[2]||(i[2]=A("div",{class:"flex-shrink-0"},[A("img",{src:B,alt:"CogniSynth Logo",class:"h-10"})],-1)),A("nav",Q,[(w(),N(_,null,C(xe,a=>T(v,{key:a.name,to:a.to,class:E(["px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200",[e.$route.path.startsWith(a.to)?"bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300":"text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400"]])},{default:z(()=>[T(f,{class:"mr-2"},{default:z(()=>[(w(),H(S(ge[a.icon])))]),_:2},1024),P(" "+O(a.name),1)]),_:2},1032,["to","class"])),64))])]),A("div",X,[T(b,{value:be.value,hidden:0===be.value},{default:z(()=>[T(h,{circle:"",size:"large",onClick:i[0]||(i[0]=e=>pe.value=!0)},{default:z(()=>[T(f,null,{default:z(()=>[T(I(m))]),_:1})]),_:1})]),_:1},8,["value","hidden"]),T(h,{circle:"",size:"large",onClick:fe},{default:z(()=>[T(f,null,{default:z(()=>[me.value?(w(),H(I(x),{key:0})):(w(),H(I(u),{key:1}))]),_:1})]),_:1}),T(R,{onCommand:ke},{dropdown:z(()=>[T(V,null,{default:z(()=>[T(L,{command:"profile"},{default:z(()=>[T(f,null,{default:z(()=>[T(I(k))]),_:1}),i[3]||(i[3]=P(" 个人资料 ",-1))]),_:1,__:[3]}),T(L,{command:"settings"},{default:z(()=>[T(f,null,{default:z(()=>[T(I(g))]),_:1}),i[4]||(i[4]=P(" 设置 ",-1))]),_:1,__:[4]}),T(L,{divided:"",command:"logout"},{default:z(()=>[T(f,null,{default:z(()=>[T(I(p))]),_:1}),i[5]||(i[5]=P(" 退出登录 ",-1))]),_:1,__:[5]})]),_:1})]),default:z(()=>{var e,a,t;return[A("div",Y,[T(U,{size:32,src:null===(e=I(oe).user)||void 0===e?void 0:e.avatar_url},{default:z(()=>{var e;return[P(O(null===(e=I(oe).user)||void 0===e||null===(e=e.username)||void 0===e?void 0:e.charAt(0).toUpperCase()),1)]}),_:1},8,["src"]),A("span",Z,O((null===(a=I(oe).user)||void 0===a?void 0:a.display_name)||(null===(t=I(oe).user)||void 0===t?void 0:t.username)),1),T(f,{class:"text-gray-400"},{default:z(()=>[T(I(c))]),_:1})])]}),_:1})])])])],512),[[y,!I(ue).focusMode]]),A("main",{class:E(ce.value?I(ue).focusMode?"h-screen w-full":"h-[calc(100vh-4rem)] w-full":"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8")},[T(q)],2),T(F,{modelValue:pe.value,"onUpdate:modelValue":i[1]||(i[1]=e=>pe.value=e),title:"通知中心",direction:"rtl",size:"400px"},{default:z(()=>[A("div",ee,[0===he.value.length?(w(),N("div",ae," 暂无通知 ")):M("",!0),(w(!0),N(_,null,C(he.value,e=>{return w(),N("div",{key:e.id,class:"p-4 bg-gray-50 dark:bg-dark-800 rounded-lg"},[A("div",te,[A("div",null,[A("h4",se,O(e.title),1),A("p",le,O(e.content),1),A("span",re,O((a=e.createdAt,new Date(a).toLocaleString("zh-CN"))),1)]),T(h,{size:"small",text:"",onClick:a=>(e=>{const a=he.value.find(a=>a.id===e);a&&(a.read=!0)})(e.id)},{default:z(()=>i[6]||(i[6]=[P(" 标记已读 ",-1)])),_:2,__:[6]},1032,["onClick"])])]);var a}),128))])]),_:1},8,["modelValue"])])}}});export{ne as default};