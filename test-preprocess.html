<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预处理测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-case { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .before, .after { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>预处理测试</h1>
    
    <div class="test-case">
        <h3>原始文本（包含代码块包裹的表格）</h3>
        <div class="before" id="before"></div>
        
        <h3>预处理后的文本</h3>
        <div class="after" id="after"></div>
        
        <h3>最终渲染结果</h3>
        <div id="result"></div>
    </div>

    <script>
        // 预处理函数
        function preprocessMarkdown(text) {
            const codeBlockRegex = /```(?:markdown|table|)\s*\n([\s\S]*?)\n```/g
            
            return text.replace(codeBlockRegex, (match, content) => {
                const trimmedContent = content.trim()
                
                // 检查是否确实是表格格式
                const lines = trimmedContent.split('\n')
                const hasTableStructure = lines.some(line => line.includes('|')) && 
                                          lines.some(line => /^\s*\|?[-\s\|:]+\|?\s*$/.test(line))
                
                if (hasTableStructure) {
                    console.log('预处理：提取被代码块包裹的表格')
                    return '\n\n' + trimmedContent + '\n\n'
                }
                
                // 如果不是表格，保持原样
                return match
            })
        }

        // 测试文本
        const testText = `当然，以下是用Markdown表格形式重新输出的步骤和建议：

\`\`\`markdown
| 步骤 | 详细操作 |
|------|----------|
| 访问官网 | 打开江苏第二师范学院官网：[https://www.jssnu.edu.cn/](https://www.jssnu.edu.cn/) |
| 查找联系方式 | 点击"联系我们"或"招生就业"页面，记录联系电话或联系邮箱 |
| 联系学院电话 | 拨打联系电话：025-83758102 |
\`\`\`

希望这个Markdown表格形式的输出对你有帮助。`

        // 显示原始文本
        document.getElementById('before').textContent = testText

        // 预处理
        const processedText = preprocessMarkdown(testText)
        document.getElementById('after').textContent = processedText

        // 使用marked渲染（如果可用）
        if (typeof marked !== 'undefined') {
            document.getElementById('result').innerHTML = marked.parse(processedText)
        } else {
            document.getElementById('result').innerHTML = '<p>需要marked.js库来渲染结果</p>'
        }
    </script>
    
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
</body>
</html>
