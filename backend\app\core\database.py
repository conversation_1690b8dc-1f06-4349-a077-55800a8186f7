"""
数据库连接和配置
"""
from typing import Generator, AsyncGenerator
from sqlmodel import SQLModel, create_engine, Session
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from app.config import settings


# 创建同步数据库引擎（用于Alembic迁移）
engine = create_engine(
    settings.database_url.replace("+asyncpg", ""),  # 移除异步驱动标识
    echo=settings.debug,  # 开发环境显示SQL语句
    pool_pre_ping=True,   # 连接池预检查
    pool_recycle=300,     # 连接回收时间
)

# 创建异步数据库引擎
async_engine = create_async_engine(
    settings.database_url,
    echo=settings.debug,
    pool_pre_ping=True,
    pool_recycle=300,
)


def create_db_and_tables():
    """创建数据库表"""
    SQLModel.metadata.create_all(engine)


async def create_db_and_tables_async():
    """异步创建数据库表"""
    async with async_engine.begin() as conn:
        await conn.run_sync(SQLModel.metadata.create_all)


def get_session() -> Generator[Session, None, None]:
    """获取同步数据库会话"""
    with Session(engine) as session:
        yield session


async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    """获取异步数据库会话"""
    async with AsyncSession(async_engine) as session:
        yield session


# 类型注解，用于依赖注入
from typing import Annotated
from fastapi import Depends

SessionDep = Annotated[Session, Depends(get_session)]
AsyncSessionDep = Annotated[AsyncSession, Depends(get_async_session)]
