var t;(()=>{function s(t,s,e,a,i,n,h){try{var l=t[n](h),o=l.value}catch(t){return void e(t)}l.done?s(o):Promise.resolve(o).then(a,i)}t=function(t){return function(){var e=this,a=arguments;return new Promise(function(i,n){var h=t.apply(e,a);function l(t){s(h,i,n,l,o,"next",t)}function o(t){s(h,i,n,l,o,"throw",t)}l(void 0)})}}})();import{N as s,c as e,u as a}from"./elementPlus-Di4PDIm8.js";import{bA as i,bI as n,bO as h,b_ as l,bs as o,c$ as u,ce as r,d as p,dB as c,dL as d,dN as m,dU as v,d_ as f,dc as g,dd as b,de as w,df as y,dg as x,di as C,dk as _,dl as T,ds as H,ea as k}from"./vendor-BJ-uKP15.js";import{b as L}from"./_plugin-vue_export-helper-CjD0mXop.js";const j={class:"modal-header"},M={class:"header-right"},E=["onClick"],O={class:"modal-content"},V={class:"iframe-container"},P=["srcdoc"],D={key:0,class:"loading-overlay"};var I=L(T({__name:"HtmlPreviewModal",props:{modelValue:{type:Boolean},htmlContent:{}},emits:["update:modelValue"],setup(n,{emit:u}){const r=n,C=u,T=v(),k=v(!1),L=g({get:()=>r.modelValue,set:t=>C("update:modelValue",t)}),I=g(()=>{if(!r.htmlContent)return"";let t=p.sanitize(r.htmlContent,{FORBID_ATTR:["onload","onerror","onclick","onmouseover"],ALLOWED_TAGS:["script","div","canvas","style","head","body","html","title","meta"],ALLOWED_ATTR:["src","type","id","class","style","width","height"]});if(t.includes("<!DOCTYPE html>")||t.includes("<html")){if(!t.includes("chart.js")&&!t.includes("echarts")){const s=window.location.origin,e=[];e.push('    <script src="'+s+'/libs/chart.js"><\/script>'),e.push('    <script src="'+s+'/libs/echarts.js"><\/script>'),e.push("</head>");const a=e.join("\n");t=t.replace("</head>",a)}}else{const s=window.location.origin,e=[];e.push("<!DOCTYPE html>"),e.push('<html lang="zh-CN">'),e.push("<head>"),e.push('    <meta charset="UTF-8">'),e.push('    <meta name="viewport" content="width=device-width, initial-scale=1.0">'),e.push("    <title>HTML预览</title>"),e.push('    <script src="'+s+'/libs/chart.js"><\/script>'),e.push('    <script src="'+s+'/libs/echarts.js"><\/script>'),e.push("    <style>"),e.push("        body {"),e.push("            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;"),e.push("            margin: 20px;"),e.push("            line-height: 1.6;"),e.push("            padding: 20px;"),e.push("        }"),e.push("        .chart-container {"),e.push("            width: 100%;"),e.push("            height: 500px;"),e.push("            margin: 20px auto;"),e.push("            position: relative;"),e.push("            background: white;"),e.push("            border-radius: 8px;"),e.push("            box-shadow: 0 2px 8px rgba(0,0,0,0.1);"),e.push("            padding: 20px;"),e.push("            box-sizing: border-box;"),e.push("        }"),e.push("        canvas {"),e.push("            width: 100% !important;"),e.push("            height: 100% !important;"),e.push("            max-width: 100% !important;"),e.push("            max-height: 100% !important;"),e.push("        }"),e.push("        /* 确保图表正确渲染 */"),e.push("        .chart-wrapper {"),e.push("            position: relative;"),e.push("            width: 100%;"),e.push("            height: 450px;"),e.push("        }"),e.push("        h1, h2, h3 {"),e.push("            text-align: center;"),e.push("            color: #333;"),e.push("            margin-bottom: 20px;"),e.push("        }"),e.push("    </style>"),e.push("    <script>"),e.push("        // 确保图表库加载完成后再执行图表代码"),e.push('        window.addEventListener("load", function() {'),e.push('            console.log("页面加载完成，Chart.js可用:", typeof Chart !== "undefined");'),e.push('            console.log("ECharts可用:", typeof echarts !== "undefined");'),e.push("            // 如果有图表代码，确保在这里执行"),e.push("            setTimeout(function() {"),e.push("                // 重新触发任何图表初始化代码"),e.push("                if (window.initCharts) {"),e.push("                    window.initCharts();"),e.push("                }"),e.push("            }, 100);"),e.push("        });"),e.push("    <\/script>"),e.push("</head>"),e.push("<body>"),e.push("    "+t),e.push("</body>"),e.push("</html>"),t=e.join("\n")}return t}),F=()=>{L.value=!1},S=(U=t(function*(){if(T.value){k.value=!0;try{const t=T.value.srcdoc;T.value.srcdoc="",yield H(),T.value.srcdoc=t}catch(t){e.error("刷新预览失败")}}}),function(){return U.apply(this,arguments)});var U;const z=(A=t(function*(){try{yield navigator.clipboard.writeText(r.htmlContent),e.success("HTML代码已复制到剪贴板")}catch(t){e.error("复制失败，请手动复制")}}),function(){return A.apply(this,arguments)});var A;const B=()=>{k.value=!1};return d(()=>r.htmlContent,()=>{L.value&&(k.value=!0)},{immediate:!0}),(t,e)=>{const n=s,u=a;return c(),w(u,{modelValue:L.value,"onUpdate:modelValue":e[0]||(e[0]=t=>L.value=t),title:"HTML 预览",width:"90%","before-close":F,class:"html-preview-modal","show-close":!1,"close-on-click-modal":!1,"close-on-press-escape":!0,top:"5vh",fullscreen:!1,"destroy-on-close":!0,"append-to-body":!0,"lock-scroll":!0},{header:m(({close:t})=>[b("div",j,[e[1]||(e[1]=b("div",{class:"header-left"},[b("div",{class:"traffic-lights"},[b("div",{class:"light red"}),b("div",{class:"light yellow"}),b("div",{class:"light green"})]),b("span",{class:"modal-title"},"HTML 预览 - 放大视图")],-1)),b("div",M,[b("button",{onClick:S,class:"action-btn",title:"刷新"},[_(n,null,{default:m(()=>[_(f(l))]),_:1})]),b("button",{onClick:z,class:"action-btn",title:"复制HTML"},[_(n,null,{default:m(()=>[_(f(i))]),_:1})]),b("button",{onClick:t,class:"action-btn close-btn",title:"关闭"},[_(n,null,{default:m(()=>[_(f(o))]),_:1})],8,E)])])]),default:m(()=>[b("div",O,[b("div",V,[b("iframe",{ref_key:"previewFrame",ref:T,srcdoc:I.value,class:"preview-iframe",onLoad:B},null,40,P),k.value?(c(),x("div",D,[_(n,{class:"loading-icon"},{default:m(()=>[_(f(h))]),_:1}),e[2]||(e[2]=b("span",null,"加载中...",-1))])):y("",!0)])])]),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-f5ed992d"]]);const F={class:"html-preview-container"},S={key:0,class:"html-wrapper"},U={class:"preview-header"},z={class:"header-right"},A=["srcdoc"],B={key:0,class:"loading-overlay"},R={key:1,class:"error-message"};var N=L(T({__name:"HtmlPreview",props:{htmlContent:{},height:{default:"auto"},allowScripts:{type:Boolean,default:!0},minHeight:{default:"300px"},maxHeight:{default:"600px"}},setup(a){u(t=>({"1839aac6":t.minHeight,"2ba2d750":t.maxHeight,ff7ae8cc:t.height}));const o=a,p=v(),w=v(!0),T=v(!1),H=()=>{const t=window.location.origin;return{chartJs:t+"/libs/chart.js",echarts:t+"/libs/echarts.js"}},L=g(()=>{if(!o.htmlContent)return"";let t=o.htmlContent.trim();if(t.includes("<!DOCTYPE")||t.includes("<html")?t.includes("chart.js")||t.includes("echarts")||(t=function(t,s){const e=[];e.push('    <script src="'+s.chartJs+'"><\/script>'),e.push('    <script src="'+s.echarts+'"><\/script>'),e.push("</head>");const a=e.join("\n");return t.replace("</head>",a)}(t,H())):t=function(t,s){const e=[];return e.push("<!DOCTYPE html>"),e.push('<html lang="zh-CN">'),e.push("<head>"),e.push('    <meta charset="UTF-8">'),e.push('    <meta name="viewport" content="width=device-width, initial-scale=1.0">'),e.push("    <title>HTML预览</title>"),e.push('    <script src="'+s.chartJs+'"><\/script>'),e.push('    <script src="'+s.echarts+'"><\/script>'),e.push("    <style>"),e.push("        body {"),e.push('            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;'),e.push("            margin: 20px;"),e.push("            line-height: 1.6;"),e.push("            color: #333;"),e.push("            padding: 20px;"),e.push("        }"),e.push("        * {"),e.push("            box-sizing: border-box;"),e.push("        }"),e.push("        .chart-container {"),e.push("            width: 100%;"),e.push("            height: 500px;"),e.push("            margin: 20px auto;"),e.push("            position: relative;"),e.push("            background: white;"),e.push("            border-radius: 8px;"),e.push("            box-shadow: 0 2px 8px rgba(0,0,0,0.1);"),e.push("            padding: 20px;"),e.push("            box-sizing: border-box;"),e.push("        }"),e.push("        canvas {"),e.push("            width: 100% !important;"),e.push("            height: 100% !important;"),e.push("            max-width: 100% !important;"),e.push("            max-height: 100% !important;"),e.push("        }"),e.push("        /* 确保图表正确渲染 */"),e.push("        .chart-wrapper {"),e.push("            position: relative;"),e.push("            width: 100%;"),e.push("            height: 450px;"),e.push("        }"),e.push("        h1, h2, h3 {"),e.push("            text-align: center;"),e.push("            color: #333;"),e.push("            margin-bottom: 20px;"),e.push("        }"),e.push("    </style>"),e.push("    <script>"),e.push("        // 确保图表库加载完成后再执行图表代码"),e.push('        window.addEventListener("load", function() {'),e.push('            console.log("页面加载完成，Chart.js可用:", typeof Chart !== "undefined");'),e.push('            console.log("ECharts可用:", typeof echarts !== "undefined");'),e.push("            // 如果有图表代码，确保在这里执行"),e.push("            setTimeout(function() {"),e.push("                // 重新触发任何图表初始化代码"),e.push("                if (window.initCharts) {"),e.push("                    window.initCharts();"),e.push("                }"),e.push("            }, 100);"),e.push("        });"),e.push("    <\/script>"),e.push("</head>"),e.push("<body>"),e.push("    "+t),e.push("</body>"),e.push("</html>"),e.join("\n")}(t,H()),!o.allowScripts){const s="<script",e="<\/script>";let a=t.indexOf(s);for(;-1!==a;){const i=t.indexOf(e,a);if(-1===i)break;t=t.substring(0,a)+t.substring(i+9),a=t.indexOf(s,a)}}return t}),j=()=>{if(w.value=!1,"auto"===o.height&&p.value)try{var t;const s=p.value,e=s.contentDocument||(null===(t=s.contentWindow)||void 0===t?void 0:t.document);e&&setTimeout(()=>{const t=e.body,a=e.documentElement;if(t&&a){const e=Math.max(t.scrollHeight,t.offsetHeight,a.clientHeight,a.scrollHeight,a.offsetHeight),i=parseInt(o.minHeight)||300,n=parseInt(o.maxHeight)||600,h=Math.min(Math.max(e+20,i),n);s.style.height=h+"px"}},100)}catch(s){}},M=()=>{w.value=!0,p.value&&(p.value.src=p.value.src)},E=()=>{T.value=!0},O=(V=t(function*(){try{yield navigator.clipboard.writeText(o.htmlContent),e.success("HTML内容已复制到剪贴板")}catch(t){e.error("复制失败")}}),function(){return V.apply(this,arguments)});var V;return d(()=>o.htmlContent,()=>{o.htmlContent&&(w.value=!0)},{immediate:!0}),(t,e)=>{const a=s;return c(),x("div",F,[t.htmlContent?(c(),x("div",S,[b("div",U,[e[1]||(e[1]=C('<div class="header-left" data-v-0b2c50ba><div class="traffic-lights" data-v-0b2c50ba><div class="light red" data-v-0b2c50ba></div><div class="light yellow" data-v-0b2c50ba></div><div class="light green" data-v-0b2c50ba></div></div><span class="preview-title" data-v-0b2c50ba>HTML 预览</span></div>',1)),b("div",z,[b("button",{onClick:E,class:"action-btn",title:"放大预览"},[_(a,null,{default:m(()=>[_(f(n))]),_:1})]),b("button",{onClick:M,class:"action-btn",title:"刷新"},[_(a,null,{default:m(()=>[_(f(l))]),_:1})]),b("button",{onClick:O,class:"action-btn",title:"复制HTML"},[_(a,null,{default:m(()=>[_(f(i))]),_:1})])])]),b("div",{class:k(["iframe-container",{"auto-height":"auto"===o.height}])},[b("iframe",{ref_key:"previewFrame",ref:p,srcdoc:L.value,class:"preview-iframe",onLoad:j},null,40,A),w.value?(c(),x("div",B,[_(a,{class:"loading-icon"},{default:m(()=>[_(f(h))]),_:1}),e[2]||(e[2]=b("span",null,"加载中...",-1))])):y("",!0)],2)])):(c(),x("div",R,[_(a,null,{default:m(()=>[_(f(r))]),_:1}),e[3]||(e[3]=b("span",null,"无效的HTML内容",-1))])),_(I,{modelValue:T.value,"onUpdate:modelValue":e[0]||(e[0]=t=>T.value=t),"html-content":t.htmlContent},null,8,["modelValue","html-content"])])}}}),[["__scopeId","data-v-0b2c50ba"]]);export{N as b};