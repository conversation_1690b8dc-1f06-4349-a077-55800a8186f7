<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基于RAG技术的个性化教学智能体系统架构</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* 全局强制所有SVG矩形都有背景色 */
        rect {
            fill: #f8f9fa !important;
            stroke: #6c757d !important;
            stroke-width: 1.5px !important;
        }
        
        body {
            font-family: 'Times New Roman', 'SimSun', serif;
            background: #ffffff;
            color: #2c3e50;
            line-height: 1.6;
            padding: 40px 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: #ffffff;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px 0;
            border-bottom: 3px solid #34495e;
        }
        
        .header h1 {
            font-size: 28px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 15px;
            letter-spacing: 1px;
        }
        
        .header p {
            font-size: 18px;
            color: #7f8c8d;
            font-style: italic;
        }
        
        .architecture-section {
            background: #ffffff;
            border-radius: 0;
            box-shadow: none;
            padding: 0;
        }
        
        .section-title {
            font-size: 22px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
            padding-bottom: 10px;
            border-bottom: 2px solid #bdc3c7;
        }
        
        .mermaid {
            text-align: center;
            background: #ffffff;
            padding: 15px 10px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            margin: 10px 0;
            height: 420px;
            overflow: visible;
        }
        
        .description {
            margin-top: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-left: 5px solid #3498db;
            border-radius: 0 8px 8px 0;
        }

        .description h3 {
            color: #2c3e50;
            font-size: 18px;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .layer-description {
            margin-bottom: 15px;
        }

        .layer-description h4 {
            color: #34495e;
            font-size: 15px;
            font-weight: 600;
            margin-bottom: 8px;
            padding-left: 12px;
            border-left: 3px solid #3498db;
        }

        .layer-description p {
            color: #5d6d7e;
            font-size: 13px;
            line-height: 1.6;
            text-align: justify;
            padding-left: 15px;
        }
        
        .tech-stack {
            margin-top: 25px;
            padding: 20px;
            background: #ffffff;
            border: 1px solid #e8e8e8;
            border-radius: 8px;
        }

        .tech-stack h3 {
            color: #2c3e50;
            font-size: 18px;
            margin-bottom: 15px;
            text-align: center;
            font-weight: 600;
        }
        
        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .tech-item {
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #3498db;
        }
        
        .tech-item h5 {
            color: #2c3e50;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .tech-item p {
            color: #7f8c8d;
            font-size: 12px;
            line-height: 1.5;
        }
        
        @media print {
            body {
                background: white;
                font-size: 12px;
            }
            
            .container {
                max-width: none;
                margin: 0;
                padding: 0;
            }
            
            .header {
                page-break-after: avoid;
            }
            
            .mermaid {
                page-break-inside: avoid;
            }
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 24px;
            }
            
            .header p {
                font-size: 16px;
            }
            
            .section-title {
                font-size: 20px;
            }
            
            .mermaid {
                padding: 20px 10px;
            }
            
            .tech-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>基于RAG技术的个性化教学智能体系统架构</h1>
            <p>Architecture of Personalized Teaching Intelligent Agent System Based on RAG Technology</p>
        </div>
        
        <div class="architecture-section">
            <h2 class="section-title">系统整体架构 System Architecture</h2>
            <div class="mermaid">
                graph TB
                    subgraph "用户界面层 User Interface Layer"
                        A1["用户端<br/>Vue.js 3 + TypeScript + Element Plus UI<br/>知识库管理 | 智能问答 | 文档上传"]
                        A2["管理员端<br/>用户管理 + AI模型配置 + 系统监控<br/>Chart.js/ECharts数据可视化"]
                    end

                    subgraph "网关服务层 Gateway Service Layer"
                        B1["Nginx反向代理<br/>静态文件服务 + 负载均衡"]
                        B2["CORS跨域处理<br/>请求路由转发 + 安全策略"]
                        B3["JWT身份认证<br/>权限验证 + 会话管理"]
                    end

                    subgraph "应用服务层 Application Service Layer"
                        C1["用户认证服务<br/>FastAPI + SQLModel + 权限控制"]
                        C2["知识库管理<br/>文档上传解析 + 批量处理 + 版本控制"]
                        C3["对话管理<br/>多轮会话维护 + 上下文管理 + 历史记录"]
                        C4["AI模型管理<br/>多模型手动切换 + 参数配置 + 性能监控"]
                    end

                    subgraph "AI核心层 AI Core Layer"
                        D1["多模型接入<br/>OpenAI GPT| Claude | DeepSeek |Qwen<br/>SiliconFlow | 通义千问"]
                        D2["RAG检索引擎<br/>FAISS向量检索 + 余弦相似度匹配<br/>语义相似度阈值动态调节"]
                        D3["文本向量化<br/>Sentence-BERT编码 + BGE-M3<br/>768维语义向量空间"]
                        D4["文档处理<br/>智能分块算法 + 语义边界识别<br/>多格式解析引擎"]
                        D5["流式输出<br/>SSE实时响应 + 增量推送"]
                    end

                    subgraph "数据持久层 Data Persistence Layer"
                        E1["PostgreSQL<br/>用户数据 + 知识库元数据 + 对话记录<br/>索引优化 + 事务管理"]
                        E2["FAISS向量库<br/>文档向量索引 + 高效检索<br/>IVF索引 + 量化压缩"]
                        E3["Redis缓存<br/>会话状态缓存 + 检索结果缓存<br/>过期策略 + 内存优化"]
                        E4["本地文件系统<br/>文档存储 + 媒体文件<br/>目录结构 + 权限管理"]
                    end

                    subgraph "部署运行层 Deployment Layer"
                        F1["Docker容器化<br/>前后端分离部署 + 微服务架构<br/>镜像管理 + 服务编排"]
                        F2["宝塔面板<br/>服务管理 + 监控告警<br/>日志管理 + 性能优化"]
                    end

                    A1 --> B1
                    A2 --> B1
                    B1 --> B2
                    B2 --> B3
                    B3 --> C1
                    B3 --> C2
                    B3 --> C3
                    B3 --> C4
                    C2 --> D3
                    C2 --> D4
                    C3 --> D1
                    C3 --> D2
                    C4 --> D1
                    D1 --> D5
                    D2 --> D3
                    D3 --> E2
                    D4 --> E2
                    C1 --> E1
                    C2 --> E1
                    C3 --> E3
                    D5 --> E3
                    C2 --> E4

                    F1 -.-> C1
                    F1 -.-> C2
                    F1 -.-> C3
                    F1 -.-> C4
                    F2 -.-> F1
            </div>

                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化Mermaid，使用专业科研风格配置
        mermaid.initialize({
            startOnLoad: true,
            theme: 'base',
            themeVariables: {
                // 主题颜色配置 - 专业科研风格
                primaryColor: '#e8f4fd',
                primaryTextColor: '#2c3e50',
                primaryBorderColor: '#3498db',
                lineColor: '#34495e',

                // 子图配置 - 渐变色彩
                subgraphBkg: '#f8fbff',
                subgraphBorder: '#2980b9',
                subgraphTextColor: '#2c3e50',

                // 节点配置 - 圆润样式
                nodeBkg: '#ffffff',
                nodeBorder: '#3498db',
                nodeTextColor: '#2c3e50',

                // 箭头配置
                arrowheadColor: '#2980b9',

                // 字体配置
                fontFamily: 'Times New Roman, SimSun, serif',
                fontSize: '15px'
            },
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'cardinal',
                padding: 8,
                nodeSpacing: 25,
                rankSpacing: 35,
                diagramPadding: 8
            },
            subgraph: {
                padding: 6,
                margin: 4
            }
        });

        // 添加自定义CSS样式使节点更圆润和鲜艳
        const style = document.createElement('style');
        style.textContent = `
            .mermaid .node rect {
                rx: 8px !important;
                ry: 8px !important;
                stroke-width: 3px !important;
                min-width: 280px !important;
                width: 280px !important;
            }

            .mermaid .cluster rect {
                rx: 12px !important;
                ry: 12px !important;
                stroke-width: 3px !important;
            }

            .mermaid .edgePath path {
                stroke-width: 3px !important;
                stroke: #34495e !important;
            }

            .mermaid .edgePath .arrowheadPath {
                fill: #34495e !important;
                stroke: #34495e !important;
            }

            /* 强制应用节点颜色 - 使用更具体的选择器 */
            .mermaid svg g.root g.clusters g.cluster:nth-child(1) rect {
                fill: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%) !important;
                stroke: #4caf50 !important;
                stroke-width: 3px !important;
            }

            .mermaid svg g.root g.clusters g.cluster:nth-child(2) rect {
                fill: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%) !important;
                stroke: #ff9800 !important;
                stroke-width: 3px !important;
            }

            .mermaid svg g.root g.clusters g.cluster:nth-child(3) rect {
                fill: linear-gradient(135deg, #e1f5fe 0%, #b3e5fc 100%) !important;
                stroke: #2196f3 !important;
                stroke-width: 3px !important;
            }

            .mermaid svg g.root g.clusters g.cluster:nth-child(4) rect {
                fill: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%) !important;
                stroke: #9c27b0 !important;
                stroke-width: 3px !important;
            }

            .mermaid svg g.root g.clusters g.cluster:nth-child(5) rect {
                fill: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%) !important;
                stroke: #f44336 !important;
                stroke-width: 3px !important;
            }

            .mermaid svg g.root g.clusters g.cluster:nth-child(6) rect {
                fill: linear-gradient(135deg, #e0f2f1 0%, #b2dfdb 100%) !important;
                stroke: #009688 !important;
                stroke-width: 3px !important;
            }

            /* 用户界面层节点颜色 - 鲜绿色系 */
            .mermaid g[id*="A1"] rect,
            .mermaid g.node:nth-child(1) rect,
            .mermaid svg g g.node:nth-child(1) rect {
                fill: #8bc34a !important;
                background: linear-gradient(135deg, #8bc34a 0%, #689f38 100%) !important;
                stroke: #33691e !important;
                stroke-width: 3px !important;
            }

            .mermaid g[id*="A2"] rect,
            .mermaid g.node:nth-child(2) rect,
            .mermaid svg g g.node:nth-child(2) rect {
                fill: #4caf50 !important;
                background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%) !important;
                stroke: #1b5e20 !important;
                stroke-width: 3px !important;
            }

            /* 网关服务层节点颜色 - 鲜黄橙色系 */
            .mermaid g[id*="B1"] rect {
                fill: linear-gradient(135deg, #ffeb3b 0%, #fbc02d 100%) !important;
                stroke: #f57f17 !important;
                stroke-width: 3px !important;
            }

            .mermaid g[id*="B2"] rect {
                fill: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%) !important;
                stroke: #e65100 !important;
                stroke-width: 3px !important;
            }

            .mermaid g[id*="B3"] rect {
                fill: linear-gradient(135deg, #ff9800 0%, #f57c00 100%) !important;
                stroke: #bf360c !important;
                stroke-width: 3px !important;
            }

            /* 应用服务层节点颜色 - 鲜蓝青色系 */
            .mermaid g[id*="C1"] rect {
                fill: linear-gradient(135deg, #2196f3 0%, #1976d2 100%) !important;
                stroke: #0d47a1 !important;
                stroke-width: 3px !important;
            }

            .mermaid g[id*="C2"] rect {
                fill: linear-gradient(135deg, #03a9f4 0%, #0288d1 100%) !important;
                stroke: #01579b !important;
                stroke-width: 3px !important;
            }

            .mermaid g[id*="C3"] rect {
                fill: linear-gradient(135deg, #00bcd4 0%, #0097a7 100%) !important;
                stroke: #006064 !important;
                stroke-width: 3px !important;
            }

            .mermaid g[id*="C4"] rect {
                fill: linear-gradient(135deg, #009688 0%, #00695c 100%) !important;
                stroke: #004d40 !important;
                stroke-width: 3px !important;
            }

            /* AI核心层节点颜色 - 鲜紫粉色系 */
            .mermaid g[id*="D1"] rect {
                fill: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%) !important;
                stroke: #4a148c !important;
                stroke-width: 3px !important;
            }

            .mermaid g[id*="D2"] rect {
                fill: linear-gradient(135deg, #e91e63 0%, #c2185b 100%) !important;
                stroke: #880e4f !important;
                stroke-width: 3px !important;
            }

            .mermaid g[id*="D3"] rect {
                fill: linear-gradient(135deg, #673ab7 0%, #512da8 100%) !important;
                stroke: #311b92 !important;
                stroke-width: 3px !important;
            }

            .mermaid g[id*="D4"] rect {
                fill: linear-gradient(135deg, #3f51b5 0%, #303f9f 100%) !important;
                stroke: #1a237e !important;
                stroke-width: 3px !important;
            }

            .mermaid g[id*="D5"] rect {
                fill: linear-gradient(135deg, #f44336 0%, #d32f2f 100%) !important;
                stroke: #b71c1c !important;
                stroke-width: 3px !important;
            }

            /* 数据持久层节点颜色 - 鲜红橙色系 */
            .mermaid g[id*="E1"] rect {
                fill: linear-gradient(135deg, #f44336 0%, #d32f2f 100%) !important;
                stroke: #b71c1c !important;
                stroke-width: 3px !important;
            }

            .mermaid g[id*="E2"] rect {
                fill: linear-gradient(135deg, #ff5722 0%, #e64a19 100%) !important;
                stroke: #bf360c !important;
                stroke-width: 3px !important;
            }

            .mermaid g[id*="E3"] rect {
                fill: linear-gradient(135deg, #ff9800 0%, #f57c00 100%) !important;
                stroke: #e65100 !important;
                stroke-width: 3px !important;
            }

            .mermaid g[id*="E4"] rect {
                fill: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%) !important;
                stroke: #ff6f00 !important;
                stroke-width: 3px !important;
            }

            /* 部署运行层节点颜色 - 鲜青绿色系 */
            .mermaid g[id*="F1"] rect {
                fill: linear-gradient(135deg, #00bcd4 0%, #0097a7 100%) !important;
                stroke: #006064 !important;
                stroke-width: 3px !important;
            }

            .mermaid g[id*="F2"] rect {
                fill: linear-gradient(135deg, #4caf50 0%, #388e3c 100%) !important;
                stroke: #1b5e20 !important;
                stroke-width: 3px !important;
            }

            /* 子图背景颜色 - 鲜艳配色方案 */
            .mermaid g[id*="用户界面层"] rect {
                fill: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%) !important;
                stroke: #4caf50 !important;
                stroke-width: 3px !important;
            }

            .mermaid g[id*="网关服务层"] rect {
                fill: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%) !important;
                stroke: #ff9800 !important;
                stroke-width: 3px !important;
            }

            .mermaid g[id*="应用服务层"] rect {
                fill: linear-gradient(135deg, #e1f5fe 0%, #b3e5fc 100%) !important;
                stroke: #2196f3 !important;
                stroke-width: 3px !important;
            }

            .mermaid g[id*="AI核心层"] rect {
                fill: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%) !important;
                stroke: #9c27b0 !important;
                stroke-width: 3px !important;
            }

            .mermaid g[id*="数据持久层"] rect {
                fill: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%) !important;
                stroke: #f44336 !important;
                stroke-width: 3px !important;
            }

            .mermaid g[id*="部署运行层"] rect {
                fill: linear-gradient(135deg, #e0f2f1 0%, #b2dfdb 100%) !important;
                stroke: #009688 !important;
                stroke-width: 3px !important;
            }

            /* 节点文字样式 - 清晰深色文字 */
            .mermaid .node text {
                font-size: 14px !important;
                font-weight: 600 !important;
                fill: #2c3e50 !important;
                text-shadow: none !important;
                font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif !important;
            }

            /* 子图标题样式 */
            .mermaid .cluster text {
                font-size: 16px !important;
                font-weight: 700 !important;
                fill: #2c3e50 !important;
                text-shadow: none !important;
                font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif !important;
            }

            /* 确保所有文字都清晰 */
            .mermaid text {
                fill: #2c3e50 !important;
                text-shadow: none !important;
            }

            /* 强制所有矩形都有背景色 - 最高优先级 */
            .mermaid rect,
            rect {
                fill: #f8f9fa !important;
                stroke: #6c757d !important;
                stroke-width: 1.5px !important;
            }

            /* 特别针对可能透明的矩形 */
            .mermaid rect[fill="transparent"],
            .mermaid rect[fill="none"],
            .mermaid rect[fill=""],
            rect[fill="transparent"],
            rect[fill="none"],
            rect[fill=""] {
                fill: #f8f9fa !important;
                stroke: #6c757d !important;
                stroke-width: 1.5px !important;
            }

            /* 节点矩形 */
            .mermaid .node rect,
            .mermaid g[id*="flowchart"] rect {
                fill: #f8f9fa !important;
                stroke: #6c757d !important;
                stroke-width: 1.5px !important;
            }

            /* 子图背景 */
            .mermaid .cluster rect,
            .mermaid g[id*="subGraph"] rect {
                fill: #fafafa !important;
                stroke: #e0e0e0 !important;
                stroke-width: 1px !important;
            }

            /* 覆盖任何可能的透明背景 */
            .mermaid rect[fill="transparent"],
            .mermaid rect[fill="none"],
            .mermaid rect:not([fill]) {
                fill: #f8f9fa !important;
                stroke: #6c757d !important;
                stroke-width: 1.5px !important;
            }

            /* 连接线样式 */
            .mermaid .edgePath path {
                stroke-width: 3px !important;
                stroke: #34495e !important;
            }

            /* 箭头样式 */
            .mermaid .edgePath .arrowheadPath {
                fill: #34495e !important;
                stroke: #34495e !important;
                stroke-width: 2px !important;
            }
        `;
        document.head.appendChild(style);

        // 强制应用背景色的函数
        function forceApplyColors() {
            // 查找所有可能的矩形元素
            const selectors = [
                '.mermaid rect',
                '.mermaid g rect',
                '.mermaid .node rect',
                '.mermaid [id*="flowchart"] rect',
                'rect'
            ];

            let allRects = [];
            selectors.forEach(selector => {
                const rects = document.querySelectorAll(selector);
                rects.forEach(rect => {
                    if (!allRects.includes(rect)) {
                        allRects.push(rect);
                    }
                });
            });

            console.log('找到的所有矩形元素数量:', allRects.length);

            // 定义非常柔和的颜色方案
            const softColors = [
                { fill: '#f8f9fa', stroke: '#6c757d' }, // 浅灰
                { fill: '#f1f8e9', stroke: '#689f38' }, // 极浅绿
                { fill: '#fff8e1', stroke: '#ffa000' }, // 极浅橙
                { fill: '#fce4ec', stroke: '#e91e63' }, // 极浅粉
                { fill: '#e8f5e8', stroke: '#4caf50' }, // 极浅绿2
                { fill: '#e3f2fd', stroke: '#2196f3' }, // 极浅蓝
                { fill: '#f3e5f5', stroke: '#9c27b0' }, // 极浅紫
                { fill: '#fff3e0', stroke: '#ff9800' }, // 极浅橙2
                { fill: '#e0f2f1', stroke: '#009688' }, // 极浅青
                { fill: '#fafafa', stroke: '#757575' }  // 极浅灰
            ];

            allRects.forEach((rect, index) => {
                // 强制移除任何可能导致透明的属性
                rect.removeAttribute('fill-opacity');
                rect.style.fillOpacity = '';

                // 检查是否是子图背景
                const rectWidth = parseFloat(rect.getAttribute('width') || '0');
                const rectHeight = parseFloat(rect.getAttribute('height') || '0');
                const parentElement = rect.parentElement;
                const isSubgraph = parentElement && (
                    parentElement.classList.contains('cluster') ||
                    parentElement.id.includes('subGraph') ||
                    rectWidth > 400 || rectHeight > 200
                );

                if (isSubgraph) {
                    // 子图背景
                    rect.setAttribute('fill', '#fafafa');
                    rect.setAttribute('stroke', '#e0e0e0');
                    rect.setAttribute('stroke-width', '1');
                    rect.style.fill = '#fafafa';
                    rect.style.stroke = '#e0e0e0';
                } else {
                    // 节点矩形 - 强制应用颜色
                    const colorIndex = index % softColors.length;
                    const color = softColors[colorIndex];

                    rect.setAttribute('fill', color.fill);
                    rect.setAttribute('stroke', color.stroke);
                    rect.setAttribute('stroke-width', '1.5');

                    // 同时设置style属性确保生效
                    rect.style.fill = color.fill;
                    rect.style.stroke = color.stroke;
                    rect.style.strokeWidth = '1.5px';
                }

                console.log(`矩形 ${index}: fill=${rect.getAttribute('fill')}, stroke=${rect.getAttribute('stroke')}`);
            });
        }

        // 多次尝试应用颜色
        setTimeout(forceApplyColors, 100);
        setTimeout(forceApplyColors, 500);
        setTimeout(forceApplyColors, 1000);
        setTimeout(forceApplyColors, 2000);

        // 添加DOM观察器，监控新添加的元素
        const observer = new MutationObserver((mutations) => {
            let hasNewRects = false;
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === 1) { // Element node
                            if (node.tagName === 'rect' || node.querySelector('rect')) {
                                hasNewRects = true;
                            }
                        }
                    });
                }
            });

            if (hasNewRects) {
                setTimeout(forceApplyColors, 100);
            }
        });

        // 开始观察
        const mermaidContainer = document.querySelector('.mermaid');
        if (mermaidContainer) {
            observer.observe(mermaidContainer, {
                childList: true,
                subtree: true
            });
        }

            // 确保所有文字都是深色且清晰
            setTimeout(() => {
                const allTexts = document.querySelectorAll('.mermaid text');
                allTexts.forEach(text => {
                    text.setAttribute('fill', '#2c3e50');
                    text.style.textShadow = 'none';
                    text.style.color = '#2c3e50';
                });
            }, 3000);

        // 响应式处理
        window.addEventListener('resize', function() {
            // Mermaid图表会自动适应容器大小
            console.log('Window resized, Mermaid will auto-adjust');
        });

        // 打印优化
        window.addEventListener('beforeprint', function() {
            document.body.style.fontSize = '12px';
        });

        window.addEventListener('afterprint', function() {
            document.body.style.fontSize = '';
        });
    </script>
</body>
</html>
