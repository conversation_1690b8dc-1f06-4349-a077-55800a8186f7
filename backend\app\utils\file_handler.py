"""
文件处理工具
"""
import os
import uuid
from typing import Optional
from fastapi import UploadFile
from app.config import settings


def get_file_extension(filename: str) -> str:
    """获取文件扩展名"""
    return os.path.splitext(filename)[1].lower()


def generate_unique_filename(original_filename: str) -> str:
    """生成唯一文件名"""
    ext = get_file_extension(original_filename)
    unique_id = str(uuid.uuid4())
    return f"{unique_id}{ext}"


def get_file_type(filename: str) -> str:
    """根据文件扩展名确定文件类型"""
    ext = get_file_extension(filename)
    
    type_mapping = {
        '.pdf': 'pdf',
        '.doc': 'doc',
        '.docx': 'docx',
        '.txt': 'txt',
        '.md': 'markdown',
        '.html': 'html',
        '.htm': 'html',
    }
    
    return type_mapping.get(ext, 'unknown')


async def save_upload_file(upload_file: UploadFile, kb_id: int) -> tuple[str, int]:
    """
    保存上传的文件
    返回: (文件路径, 文件大小)
    """
    # 创建知识库目录
    kb_dir = os.path.join(settings.upload_dir, f"kb_{kb_id}")
    os.makedirs(kb_dir, exist_ok=True)
    
    # 生成唯一文件名
    unique_filename = generate_unique_filename(upload_file.filename)
    file_path = os.path.join(kb_dir, unique_filename)
    
    # 保存文件
    content = await upload_file.read()
    with open(file_path, "wb") as f:
        f.write(content)
    
    return file_path, len(content)


def delete_file(file_path: str) -> bool:
    """删除文件"""
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            return True
        return False
    except Exception:
        return False
