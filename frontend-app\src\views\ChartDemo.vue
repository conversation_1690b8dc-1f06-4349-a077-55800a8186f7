<template>
  <div class="p-6 space-y-6">
    <div class="text-center mb-8">
      <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">图表演示</h1>
      <p class="text-gray-600 dark:text-gray-400">展示各种图表组件的效果</p>
    </div>

    <!-- 饼图演示 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">饼图示例</h2>
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">存储使用分布</h3>
          <PieChart 
            :data="pieData1" 
            height="300px"
            :theme="isDark ? 'dark' : 'light'"
          />
        </div>
        <div>
          <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">用户类型分布</h3>
          <PieChart 
            :data="pieData2" 
            height="300px"
            :theme="isDark ? 'dark' : 'light'"
            :radius="['30%', '60%']"
          />
        </div>
      </div>
    </div>

    <!-- 折线图演示 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">折线图示例</h2>
      <LineChart 
        :data="lineData"
        :x-axis-data="xAxisData"
        height="350px"
        :theme="isDark ? 'dark' : 'light'"
        y-axis-name="数量"
        title="用户活动趋势"
      />
    </div>

    <!-- 柱状图演示 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">柱状图示例</h2>
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">垂直柱状图</h3>
          <BarChart 
            :data="barData"
            :x-axis-data="categories"
            height="300px"
            :theme="isDark ? 'dark' : 'light'"
            y-axis-name="数量"
          />
        </div>
        <div>
          <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">水平柱状图</h3>
          <BarChart 
            :data="barData"
            :x-axis-data="categories"
            height="300px"
            :theme="isDark ? 'dark' : 'light'"
            y-axis-name="数量"
            :horizontal="true"
          />
        </div>
      </div>
    </div>

    <!-- 主题切换 -->
    <div class="text-center">
      <el-button @click="toggleTheme" type="primary">
        切换主题 (当前: {{ isDark ? '深色' : '浅色' }})
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import PieChart from '@/components/charts/PieChart.vue'
import LineChart from '@/components/charts/LineChart.vue'
import BarChart from '@/components/charts/BarChart.vue'

// 主题检测
const isDark = computed(() => {
  return document.documentElement.classList.contains('dark')
})

// 切换主题
const toggleTheme = () => {
  document.documentElement.classList.toggle('dark')
}

// 饼图数据
const pieData1 = ref([
  { name: '已使用', value: 320, color: '#3b82f6' },
  { name: '可用空间', value: 680, color: '#e5e7eb' }
])

const pieData2 = ref([
  { name: '管理员', value: 5, color: '#ef4444' },
  { name: '普通用户', value: 45, color: '#10b981' },
  { name: '访客', value: 20, color: '#f59e0b' }
])

// 折线图数据
const xAxisData = ref(['1月', '2月', '3月', '4月', '5月', '6月', '7月'])
const lineData = ref([
  { name: '新用户', data: [120, 132, 101, 134, 90, 230, 210], color: '#3b82f6' },
  { name: '活跃用户', data: [220, 182, 191, 234, 290, 330, 310], color: '#10b981' },
  { name: '留存用户', data: [150, 232, 201, 154, 190, 330, 410], color: '#f59e0b' }
])

// 柱状图数据
const categories = ref(['知识库', '文档', '聊天', '用户'])
const barData = ref([
  { name: '本月', data: [120, 200, 150, 80], color: '#3b82f6' },
  { name: '上月', data: [100, 180, 120, 70], color: '#10b981' }
])
</script>

<style scoped>
/* 自定义样式 */
</style>
