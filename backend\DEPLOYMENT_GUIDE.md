# 宝塔环境部署指南

## 问题说明

您遇到的大文件处理超时问题是由于宝塔环境中gunicorn的Worker超时时间过短导致的。

## 解决方案

### 方案1：使用新的生产环境启动脚本（推荐）

1. **替换启动脚本**
   在宝塔面板中，将Python项目的启动文件从 `start_server.py` 改为 `start_production.py`

2. **启动命令**
   ```bash
   python start_production.py
   ```

### 方案2：配置gunicorn超时时间

如果必须使用gunicorn，请在宝塔面板中添加以下配置：

1. **创建gunicorn配置文件**
   使用提供的 `gunicorn.conf.py` 文件

2. **修改启动命令**
   ```bash
   gunicorn -c gunicorn.conf.py app.main:app
   ```

### 方案3：环境变量配置

在宝塔面板的环境变量中添加：
```
WORKER_TIMEOUT=300
WORKER_CLASS=uvicorn.workers.UvicornWorker
```

## 优化说明

### 大文件处理优化

1. **分批处理**：大于10MB的文件自动启用分批处理模式
2. **超时控制**：每批处理30页，避免单次处理时间过长
3. **进度反馈**：实时显示处理进度
4. **错误恢复**：处理失败时提供详细错误信息

### 性能参数

- **Worker超时**：300秒（5分钟）
- **批次大小**：30页/批
- **连接超时**：300秒
- **最大请求数**：1000

## 验证部署

部署完成后，可以通过以下方式验证：

1. **检查服务状态**
   ```bash
   curl http://localhost:8000/health
   ```

2. **上传测试文件**
   上传一个大文件，观察是否能正常处理完成

3. **查看日志**
   观察是否还有Worker超时错误

## 故障排除

### 如果仍然超时

1. **增加超时时间**：将timeout从300秒增加到600秒
2. **减少批次大小**：将batch_size从30减少到20
3. **检查资源**：确保服务器有足够的内存和CPU

### 常见错误

1. **Worker timeout**：增加超时时间
2. **Memory error**：减少批次大小或增加服务器内存
3. **Connection reset**：检查nginx配置的超时时间

## 监控建议

1. **日志监控**：关注Worker超时和内存使用情况
2. **性能监控**：监控文档处理时间和成功率
3. **资源监控**：监控CPU和内存使用率

## 联系支持

如果问题仍然存在，请提供：
1. 宝塔面板的错误日志
2. 文档大小和页数
3. 服务器配置信息
