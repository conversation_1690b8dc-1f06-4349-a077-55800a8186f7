import{c as e}from"./elementPlus-Di4PDIm8.js";import{cQ as r}from"./vendor-BJ-uKP15.js";const o=r.create({baseURL:"/api",timeout:3e4,headers:{"Content-Type":"application/json"}});o.interceptors.request.use(e=>{const r=localStorage.getItem("token");return r&&(e.headers.Authorization=`Bearer ${r}`),e},e=>Promise.reject(e)),o.interceptors.response.use(r=>{const o=r.data;return!1===o.success?(e.error(o.message||"请求失败"),Promise.reject(new Error(o.message||"请求失败"))):r},r=>{if(r.response){const{status:o,data:s}=r.response;switch(o){case 401:localStorage.removeItem("token"),localStorage.removeItem("user"),localStorage.removeItem("userInfo"),sessionStorage.clear(),window.location.replace("/login"),e.error("登录已过期，请重新登录");break;case 403:"/login"!==window.location.pathname&&e.error("没有权限访问该资源");break;case 404:e.error("请求的资源不存在");break;case 422:if(s.errors){const r=Object.values(s.errors)[0];e.error(r[0])}else e.error(s.message||"请求参数错误");break;case 500:e.error("服务器内部错误");break;default:e.error(s.message||"请求失败")}}else e.error(r.request?"网络连接失败，请检查网络":"请求配置错误");return Promise.reject(r)});export{o as b};