var e,t;(()=>{function a(e,t,a,s,r,l,d){try{var n=e[l](d),o=n.value}catch(e){return void a(e)}n.done?t(o):Promise.resolve(o).then(s,r)}function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function r(e){var t=function(e){if("object"!=s(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var a=t.call(e,"string");if("object"!=s(a))return a;throw TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==s(t)?t:t+""}function l(e,t,a){return(t=r(t))in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function d(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),a.push.apply(a,s)}return a}e=function(e){return function(){var t=this,s=arguments;return new Promise(function(r,l){var d=e.apply(t,s);function n(e){a(d,r,l,n,o,"next",e)}function o(e){a(d,r,l,n,o,"throw",e)}n(void 0)})}},t=function(e){for(var t=1;t<arguments.length;t++){var a=null==arguments[t]?{}:arguments[t];t%2?d(Object(a),!0).forEach(function(t){l(e,t,a[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):d(Object(a)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))})}return e}})();import{E as a,N as s,c as r,j as l,y as d}from"./elementPlus-Di4PDIm8.js";import{bB as n,bH as o,bO as i,bQ as c,bY as u,bj as g,bk as m,bn as p,bo as x,bt as b,c1 as f,c7 as v,c8 as y,cU as h,ca as k,d8 as _,dB as w,dD as j,dH as O,dN as D,dU as P,d_ as S,dc as z,dd as C,de as M,dg as $,di as A,dj as B,dk as N,dl as T,dy as E,ea as F,ed as L}from"./vendor-BJ-uKP15.js";import{b as U,c as H}from"./index-Byt5TjPh.js";import"./_plugin-vue_export-helper-CjD0mXop.js";import{b as I}from"./knowledgeBase-yaqAZvLB.js";import{b as Q}from"./chat-Cv_kC_4-.js";import{b as R}from"./stats-DK53Rw0v.js";import{b as V,c as G}from"./LineChart-Bgmbl9FC.js";const Y={key:0,class:"h-full flex items-center justify-center"},q={class:"text-center"},J={key:1,class:"space-y-6"},K={class:"relative overflow-hidden card-tech p-8 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-blue-900/30 dark:via-indigo-900/30 dark:to-purple-900/30"},W={class:"relative z-10 flex items-center justify-between"},X={class:"flex-1"},Z={class:"flex items-center space-x-3 mb-3"},ee={class:"text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"},te={class:"text-lg text-gray-600 dark:text-gray-300 mb-6"},ae={class:"flex flex-wrap items-center gap-6 text-sm"},se={class:"flex items-center space-x-2 bg-white/50 dark:bg-gray-800/50 px-4 py-2 rounded-full backdrop-blur-sm"},re={class:"text-gray-700 dark:text-gray-300 font-medium"},le={class:"flex items-center space-x-2 bg-white/50 dark:bg-gray-800/50 px-4 py-2 rounded-full backdrop-blur-sm"},de={class:"text-gray-700 dark:text-gray-300 font-medium"},ne={class:"hidden lg:block ml-8"},oe={class:"relative"},ie={class:"w-24 h-24 bg-gradient-to-br from-blue-500 via-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center glow-effect transform rotate-3 hover:rotate-0 transition-transform duration-300"},ce={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"},ue={class:"group card-tech p-6 hover:scale-105 hover:shadow-xl transition-all duration-300 border-l-4 border-blue-500"},ge={class:"flex items-center justify-between"},me={class:"text-3xl font-bold text-gray-900 dark:text-gray-100"},pe={class:"w-14 h-14 bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900 dark:to-blue-800 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300"},xe={class:"mt-4 pt-4 border-t border-gray-100 dark:border-gray-700"},be={class:"flex items-center text-sm"},fe={class:"text-green-600 dark:text-green-400 font-semibold"},ve={class:"group card-tech p-6 hover:scale-105 hover:shadow-xl transition-all duration-300 border-l-4 border-green-500"},ye={class:"flex items-center justify-between"},he={class:"text-3xl font-bold text-gray-900 dark:text-gray-100"},ke={class:"w-14 h-14 bg-gradient-to-br from-green-100 to-green-200 dark:from-green-900 dark:to-green-800 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300"},_e={class:"mt-4 pt-4 border-t border-gray-100 dark:border-gray-700"},we={class:"flex items-center text-sm"},je={class:"text-green-600 dark:text-green-400 font-semibold"},Oe={class:"group card-tech p-6 hover:scale-105 hover:shadow-xl transition-all duration-300 border-l-4 border-purple-500"},De={class:"flex items-center justify-between"},Pe={class:"text-3xl font-bold text-gray-900 dark:text-gray-100"},Se={class:"w-14 h-14 bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-900 dark:to-purple-800 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300"},ze={class:"mt-4 pt-4 border-t border-gray-100 dark:border-gray-700"},Ce={class:"flex items-center text-sm"},Me={class:"text-green-600 dark:text-green-400 font-semibold"},$e={class:"group card-tech p-6 hover:scale-105 hover:shadow-xl transition-all duration-300 border-l-4 border-orange-500"},Ae={class:"flex items-center justify-between"},Be={class:"text-2xl font-bold text-gray-900 dark:text-gray-100"},Ne={class:"text-sm text-gray-500"},Te={class:"w-14 h-14 bg-gradient-to-br from-orange-100 to-orange-200 dark:from-orange-900 dark:to-orange-800 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300"},Ee={class:"mt-4 pt-4 border-t border-gray-100 dark:border-gray-700"},Fe={class:"flex justify-between text-xs text-gray-500"},Le={class:"font-medium"},Ue={class:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8"},He={class:"card-tech p-6"},Ie={class:"flex items-center justify-between mb-4"},Qe={class:"text-right"},Re={class:"text-lg font-bold text-gray-900 dark:text-gray-100"},Ve={class:"mt-4 grid grid-cols-2 gap-4 text-center"},Ge={class:"p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg"},Ye={class:"text-lg font-bold text-blue-700 dark:text-blue-300"},qe={class:"p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg"},Je={class:"text-lg font-bold text-gray-700 dark:text-gray-300"},Ke={class:"card-tech p-6"},We={class:"mt-4 grid grid-cols-3 gap-4 text-center"},Xe={class:"p-3 bg-green-50 dark:bg-green-900/20 rounded-lg"},Ze={class:"text-lg font-bold text-green-700 dark:text-green-300"},et={class:"p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg"},tt={class:"text-lg font-bold text-purple-700 dark:text-purple-300"},at={class:"p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg"},st={class:"text-lg font-bold text-orange-700 dark:text-orange-300"},rt={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},lt={class:"lg:col-span-2"},dt={class:"card-tech p-6"},nt={class:"flex items-center justify-between mb-4"},ot={key:0,class:"text-center py-8"},it={key:1,class:"space-y-3"},ct=["onClick"],ut={class:"flex items-center space-x-3"},gt={class:"font-medium text-gray-900 dark:text-gray-100"},mt={class:"text-sm text-gray-500 dark:text-gray-400"},pt={class:"text-right"},xt={class:"text-sm text-gray-500 dark:text-gray-400"},bt={class:"text-xs text-gray-400"},ft={class:"space-y-6"},vt={class:"card-tech p-6"},yt={class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},ht={class:"card-tech p-6"},kt={class:"space-y-4"},_t={class:"flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg"},wt={class:"flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg"},jt={class:"flex items-center justify-between p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg"};var Ot=T({__name:"Home",setup(T){const Ot=h(),Dt=U(),Pt=P(!1),St=P({knowledge_bases:0,documents:0,chat_sessions:0,total_messages:0,storage_used_bytes:0,storage_used_mb:0,storage_used_gb:0,storage_total_gb:10,storage_percent:0,recent_activity:{knowledge_bases_created:0,documents_uploaded:0,chat_sessions_created:0,messages_sent:0},unreadNotifications:0,pendingTasks:0}),zt=P(null),Ct=P(!1),Mt=P([]),$t={Collection:b,ChatDotRound:p,Document:n,Box:m,Plus:u,MessageBox:c,FolderOpened:o,Upload:y,Setting:f},At=[{name:"创建知识库",icon:$t.Plus,type:"primary",handler:()=>Ot.push("/user/knowledge-base")},{name:"上传文档",icon:$t.Upload,type:"success",handler:()=>Ot.push("/user/knowledge-base")},{name:"开始对话",icon:$t.MessageBox,type:"info",handler:()=>Ot.push("/user/chat")},{name:"系统设置",icon:$t.Setting,type:"default",handler:()=>Ot.push("/user/settings")}],Bt=z(()=>zt.value&&0!==zt.value.max_storage_mb?Math.round((zt.value.current_storage_mb||0)/zt.value.max_storage_mb*100):0),Nt=z(()=>document.documentElement.classList.contains("dark")),Tt=z(()=>{var e,t;const a=(null===(e=zt.value)||void 0===e?void 0:e.current_storage_mb)||0,s=(null===(t=zt.value)||void 0===t?void 0:t.max_storage_mb)||1024,r=Math.max(0,s-a);return[{name:"已使用",value:a,color:a>.8*s?"#ef4444":a>.6*s?"#f59e0b":"#3b82f6",actualValue:a,displayName:`已使用 (${Ht(a)})`},{name:"可用空间",value:r,color:"#e5e7eb",actualValue:r,displayName:`可用空间 (${Ht(r)})`}]}),Et=z(()=>{const e=[];for(let t=6;t>=0;t--){const a=new Date;a.setDate(a.getDate()-t),e.push(a.toLocaleDateString("zh-CN",{month:"numeric",day:"numeric"}))}return e}),Ft=P([]),Lt=z(()=>0===Ft.value.length?[{name:"文档上传",data:Array(7).fill(0),color:"#10b981"},{name:"聊天会话",data:Array(7).fill(0),color:"#8b5cf6"},{name:"知识库创建",data:Array(7).fill(0),color:"#f59e0b"}]:[{name:"文档上传",data:Ft.value.map(e=>e.documents_uploaded),color:"#10b981"},{name:"聊天会话",data:Ft.value.map(e=>e.chat_sessions_created),color:"#8b5cf6"},{name:"知识库创建",data:Ft.value.map(e=>e.knowledge_bases_created),color:"#f59e0b"}]),Ut=z(()=>{if(0===Ft.value.length)return{documents_uploaded:0,chat_sessions_created:0,knowledge_bases_created:0};const e=(new Date).toISOString().split("T")[0],t=Ft.value.find(t=>t.date===e);return{documents_uploaded:(null==t?void 0:t.documents_uploaded)||0,chat_sessions_created:(null==t?void 0:t.chat_sessions_created)||0,knowledge_bases_created:(null==t?void 0:t.knowledge_bases_created)||0}}),Ht=e=>e<1024?`${e.toFixed(1)} MB`:`${(e/1024).toFixed(1)} GB`,It=e=>{const t=(new Date).getTime()-e.getTime(),a=Math.floor(t/6e4),s=Math.floor(t/36e5),r=Math.floor(t/864e5);return a<60?`${a} 分钟前`:s<24?`${s} 小时前`:`${r} 天前`},Qt=e=>{switch(e){case"knowledge_base":return $t.Collection;case"chat_session":return $t.ChatDotRound;case"document":return $t.Document;default:return $t.Box}},Rt=e=>{switch(e){case"knowledge_base":return"bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400";case"chat_session":return"bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400";case"document":return"bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400";default:return"bg-gray-100 dark:bg-gray-900 text-gray-600 dark:text-gray-400"}},Vt=e=>{switch(e){case"knowledge_base":return"知识库";case"chat_session":return"对话会话";case"document":return"文档";default:return"未知类型"}},Gt=(Yt=e(function*(){Ct.value=!0;try{const[e,a]=yield Promise.all([R.getDetailedStats(),H.getUserQuota()]);St.value=t(t({},e),{},{storage_used_gb:e.storage_used_bytes/1073741824,storage_total_gb:10,unreadNotifications:Math.floor(5*Math.random()),pendingTasks:Math.floor(3*Math.random())}),zt.value=a}catch(e){r.error("获取统计数据失败")}finally{Ct.value=!1}}),function(){return Yt.apply(this,arguments)});var Yt;const qt=(Jt=e(function*(){try{const e=yield R.getActivityStats({days:7});Ft.value=e}catch(e){Ft.value=[]}}),function(){return Jt.apply(this,arguments)});var Jt;const Kt=(Wt=e(function*(){try{const e=[],t=yield I.getList({limit:3});for(const s of t.slice(0,2))e.push({id:s.id,name:s.name,description:s.description,type:"knowledge_base",lastAccessed:new Date(s.updated_at),documentCount:0});const a=yield Q.getSessions({limit:2});for(const s of a.slice(0,1))e.push({id:s.id,name:s.title||"未命名对话",description:"聊天会话",type:"chat_session",lastAccessed:new Date(s.updated_at),documentCount:0});e.sort((e,t)=>t.lastAccessed.getTime()-e.lastAccessed.getTime()),Mt.value=e.slice(0,3)}catch(e){}}),function(){return Wt.apply(this,arguments)});var Wt;return E(e(function*(){Promise.all([Gt(),qt(),Kt()]).catch(e=>{})})),(e,t)=>{var r,c,u,f,y,h,P,z,T,E;const U=s,H=l,I=a,Q=d;return Pt.value?(w(),$("div",Y,[C("div",q,[N(U,{size:48,class:"text-blue-500 animate-spin mb-4"},{default:D(()=>[N(S(i))]),_:1}),t[2]||(t[2]=C("p",{class:"text-gray-600 dark:text-gray-400"},"正在加载数据...",-1))])])):(w(),$("div",J,[C("div",K,[t[5]||(t[5]=C("div",{class:"absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-blue-200/20 to-purple-200/20 rounded-full -translate-y-32 translate-x-32 dark:from-blue-600/10 dark:to-purple-600/10"},null,-1)),t[6]||(t[6]=C("div",{class:"absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-indigo-200/20 to-pink-200/20 rounded-full translate-y-24 -translate-x-24 dark:from-indigo-600/10 dark:to-pink-600/10"},null,-1)),C("div",W,[C("div",X,[C("div",Z,[t[3]||(t[3]=C("div",{class:"w-2 h-8 bg-gradient-to-b from-blue-500 to-purple-600 rounded-full"},null,-1)),C("h1",ee," 欢迎回来，"+L((null===(r=S(Dt).user)||void 0===r?void 0:r.display_name)||(null===(c=S(Dt).user)||void 0===c?void 0:c.username))+"！ ",1)]),C("p",te," 今天是 "+L((R=new Date,R.toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric",weekday:"long"})))+"，开始您的知识探索之旅吧 ",1),C("div",ae,[C("div",se,[N(U,{class:"text-blue-500"},{default:D(()=>[N(S(g))]),_:1}),C("span",re,L(St.value.unreadNotifications)+" 条未读通知 ",1)]),C("div",le,[N(U,{class:"text-green-500"},{default:D(()=>[N(S(x))]),_:1}),C("span",de,L(St.value.pendingTasks)+" 个待办任务 ",1)])])]),C("div",ne,[C("div",oe,[C("div",ie,[N(U,{size:48,class:"text-white"},{default:D(()=>[N(S(k))]),_:1})]),t[4]||(t[4]=C("div",{class:"absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full border-2 border-white dark:border-gray-800 animate-pulse"},null,-1))])])])]),C("div",ce,[C("div",ue,[C("div",ge,[C("div",null,[t[7]||(t[7]=C("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400 mb-1"},"知识库数量",-1)),C("p",me,L(St.value.knowledge_bases),1)]),C("div",pe,[N(U,{size:28,class:"text-blue-600 dark:text-blue-400"},{default:D(()=>[N(S(b))]),_:1})])]),C("div",xe,[C("div",be,[N(U,{class:"text-green-500 mr-1"},{default:D(()=>[N(S(v))]),_:1}),C("span",fe,"+"+L((null===(u=St.value.recent_activity)||void 0===u?void 0:u.knowledge_bases_created)||0),1),t[8]||(t[8]=C("span",{class:"text-gray-500 ml-1"},"最近7天",-1))])])]),C("div",ve,[C("div",ye,[C("div",null,[t[9]||(t[9]=C("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400 mb-1"},"文档总数",-1)),C("p",he,L(St.value.documents),1)]),C("div",ke,[N(U,{size:28,class:"text-green-600 dark:text-green-400"},{default:D(()=>[N(S(n))]),_:1})])]),C("div",_e,[C("div",we,[N(U,{class:"text-green-500 mr-1"},{default:D(()=>[N(S(v))]),_:1}),C("span",je,"+"+L((null===(f=St.value.recent_activity)||void 0===f?void 0:f.documents_uploaded)||0),1),t[10]||(t[10]=C("span",{class:"text-gray-500 ml-1"},"最近7天",-1))])])]),C("div",Oe,[C("div",De,[C("div",null,[t[11]||(t[11]=C("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400 mb-1"},"聊天会话",-1)),C("p",Pe,L(St.value.chat_sessions),1)]),C("div",Se,[N(U,{size:28,class:"text-purple-600 dark:text-purple-400"},{default:D(()=>[N(S(p))]),_:1})])]),C("div",ze,[C("div",Ce,[N(U,{class:"text-green-500 mr-1"},{default:D(()=>[N(S(v))]),_:1}),C("span",Me,"+"+L((null===(y=St.value.recent_activity)||void 0===y?void 0:y.chat_sessions_created)||0),1),t[12]||(t[12]=C("span",{class:"text-gray-500 ml-1"},"最近7天",-1))])])]),C("div",$e,[C("div",Ae,[C("div",null,[t[13]||(t[13]=C("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400 mb-1"},"存储使用",-1)),C("p",Be,L(Ht((null===(h=zt.value)||void 0===h?void 0:h.current_storage_mb)||0)),1),C("p",Ne," / "+L(Ht((null===(P=zt.value)||void 0===P?void 0:P.max_storage_mb)||1024)),1)]),C("div",Te,[N(U,{size:28,class:"text-orange-600 dark:text-orange-400"},{default:D(()=>[N(S(o))]),_:1})])]),C("div",Ee,[N(H,{percentage:Bt.value,color:Bt.value>80?"#f56565":Bt.value>60?"#f6ad55":"#48bb78","show-text":!1,"stroke-width":8,class:"mb-2"},null,8,["percentage","color"]),C("div",Fe,[C("span",Le,L(Ht((null===(z=zt.value)||void 0===z?void 0:z.current_storage_mb)||0)),1),C("span",null,L(Bt.value.toFixed(1))+"%",1)])])])]),C("div",Ue,[C("div",He,[C("div",Ie,[t[15]||(t[15]=C("h3",{class:"text-lg font-semibold text-gray-900 dark:text-gray-100"},"存储使用分布",-1)),C("div",Qe,[t[14]||(t[14]=C("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"总容量",-1)),C("div",Re,L(Ht((null===(T=zt.value)||void 0===T?void 0:T.max_storage_mb)||1024)),1)])]),N(G,{data:Tt.value,height:"280px",theme:Nt.value?"dark":"light",radius:["45%","75%"]},null,8,["data","theme"]),C("div",Ve,[C("div",Ge,[t[16]||(t[16]=C("div",{class:"text-xs text-blue-600 dark:text-blue-400 font-medium"},"已使用",-1)),C("div",Ye,L(Ht((null===(E=zt.value)||void 0===E?void 0:E.current_storage_mb)||0)),1)]),C("div",qe,[t[17]||(t[17]=C("div",{class:"text-xs text-gray-600 dark:text-gray-400 font-medium"},"使用率",-1)),C("div",Je,L(Bt.value)+"% ",1)])])]),C("div",Ke,[t[21]||(t[21]=A('<div class="flex items-center justify-between mb-4"><h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">最近7天活动</h3><div class="flex items-center space-x-4 text-sm"><div class="flex items-center space-x-1"><div class="w-3 h-3 bg-green-500 rounded-full"></div><span class="text-gray-600 dark:text-gray-400">文档上传</span></div><div class="flex items-center space-x-1"><div class="w-3 h-3 bg-purple-500 rounded-full"></div><span class="text-gray-600 dark:text-gray-400">聊天会话</span></div><div class="flex items-center space-x-1"><div class="w-3 h-3 bg-orange-500 rounded-full"></div><span class="text-gray-600 dark:text-gray-400">知识库创建</span></div></div></div>',1)),N(V,{data:Lt.value,"x-axis-data":Et.value,height:"280px",theme:Nt.value?"dark":"light","y-axis-name":"数量"},null,8,["data","x-axis-data","theme"]),C("div",We,[C("div",Xe,[t[18]||(t[18]=C("div",{class:"text-xs text-green-600 dark:text-green-400 font-medium"},"今日文档",-1)),C("div",Ze,L(Ut.value.documents_uploaded),1)]),C("div",et,[t[19]||(t[19]=C("div",{class:"text-xs text-purple-600 dark:text-purple-400 font-medium"},"今日聊天",-1)),C("div",tt,L(Ut.value.chat_sessions_created),1)]),C("div",at,[t[20]||(t[20]=C("div",{class:"text-xs text-orange-600 dark:text-orange-400 font-medium"},"今日知识库",-1)),C("div",st,L(Ut.value.knowledge_bases_created),1)])])])]),C("div",rt,[C("div",lt,[C("div",dt,[C("div",nt,[t[23]||(t[23]=C("h2",{class:"text-lg font-semibold text-gray-900 dark:text-gray-100"}," 最近访问 ",-1)),N(I,{text:"",type:"primary",onClick:t[0]||(t[0]=t=>e.$router.push("/user/knowledge-base"))},{default:D(()=>t[22]||(t[22]=[B(" 查看全部 ",-1)])),_:1,__:[22]})]),0===Mt.value.length?(w(),$("div",ot,[N(U,{size:48,class:"text-gray-400 mb-4"},{default:D(()=>[N(S(m))]),_:1}),t[25]||(t[25]=C("p",{class:"text-gray-500 dark:text-gray-400"},"暂无最近访问记录",-1)),N(I,{type:"primary",class:"mt-4",onClick:t[1]||(t[1]=t=>e.$router.push("/user/knowledge-base"))},{default:D(()=>t[24]||(t[24]=[B(" 创建第一个知识库 ",-1)])),_:1,__:[24]})])):(w(),$("div",it,[(w(!0),$(_,null,j(Mt.value,e=>(w(),$("div",{key:e.id,class:"flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-dark-700 transition-colors cursor-pointer",onClick:t=>(e=>{switch(e.type){case"knowledge_base":Ot.push(`/user/knowledge-base/${e.id}/documents`);break;case"chat_session":Ot.push(`/user/chat?session=${e.id}`)}})(e)},[C("div",ut,[C("div",{class:F(["w-10 h-10 rounded-lg flex items-center justify-center",Rt(e.type)])},[N(U,{size:20},{default:D(()=>[(w(),M(O(Qt(e.type))))]),_:2},1024)],2),C("div",null,[C("h3",gt,L(e.name),1),C("p",mt,L(e.description||Vt(e.type)),1)])]),C("div",pt,[C("p",xt,L(It(e.lastAccessed)),1),C("p",bt,L("knowledge_base"===e.type?`${e.documentCount} 个文档`:""),1)])],8,ct))),128))]))])]),C("div",ft,[C("div",vt,[t[26]||(t[26]=C("h2",{class:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6"}," 快速操作 ",-1)),C("div",yt,[(w(),$(_,null,j(At,e=>N(I,{key:e.name,type:e.type,class:"h-12 flex items-center justify-center text-sm font-medium hover:scale-105 transition-all duration-200",onClick:e.handler},{default:D(()=>[N(U,{class:"mr-2",size:18},{default:D(()=>[(w(),M(O(e.icon)))]),_:2},1024),B(" "+L(e.name),1)]),_:2},1032,["type","onClick"])),64))])]),C("div",ht,[t[33]||(t[33]=C("h2",{class:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6"}," 系统状态 ",-1)),C("div",kt,[C("div",_t,[t[28]||(t[28]=C("div",{class:"flex items-center space-x-3"},[C("div",{class:"w-3 h-3 bg-green-500 rounded-full animate-pulse"}),C("span",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"API 状态")],-1)),N(Q,{type:"success",size:"small",effect:"light"},{default:D(()=>t[27]||(t[27]=[B("正常",-1)])),_:1,__:[27]})]),C("div",wt,[t[30]||(t[30]=C("div",{class:"flex items-center space-x-3"},[C("div",{class:"w-3 h-3 bg-blue-500 rounded-full animate-pulse"}),C("span",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"文档处理")],-1)),N(Q,{type:"primary",size:"small",effect:"light"},{default:D(()=>t[29]||(t[29]=[B("运行中",-1)])),_:1,__:[29]})]),C("div",jt,[t[32]||(t[32]=C("div",{class:"flex items-center space-x-3"},[C("div",{class:"w-3 h-3 bg-purple-500 rounded-full animate-pulse"}),C("span",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"AI 模型")],-1)),N(Q,{type:"success",size:"small",effect:"light"},{default:D(()=>t[31]||(t[31]=[B("可用",-1)])),_:1,__:[31]})])])])])])]));var R}}});export{Ot as default};