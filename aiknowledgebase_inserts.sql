--
-- PostgreSQL database dump
--

-- Dumped from database version 17.5
-- Dumped by pg_dump version 17.5

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: ai_models; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.ai_models (
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL,
    id integer NOT NULL,
    provider_id integer NOT NULL,
    model_name character varying(100) NOT NULL,
    display_name character varying(100) NOT NULL,
    is_active boolean NOT NULL,
    system_api_key character varying(255),
    allow_system_key_use boolean NOT NULL,
    max_tokens integer,
    supports_streaming boolean NOT NULL,
    cost_per_1k_tokens double precision
);


ALTER TABLE public.ai_models OWNER TO postgres;

--
-- Name: ai_models_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.ai_models_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.ai_models_id_seq OWNER TO postgres;

--
-- Name: ai_models_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.ai_models_id_seq OWNED BY public.ai_models.id;


--
-- Name: ai_providers; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.ai_providers (
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL,
    id integer NOT NULL,
    name character varying(50) NOT NULL,
    display_name character varying(100) NOT NULL,
    base_url character varying(255),
    is_active boolean NOT NULL,
    description character varying(500)
);


ALTER TABLE public.ai_providers OWNER TO postgres;

--
-- Name: ai_providers_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.ai_providers_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.ai_providers_id_seq OWNER TO postgres;

--
-- Name: ai_providers_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.ai_providers_id_seq OWNED BY public.ai_providers.id;


--
-- Name: chat_messages; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.chat_messages (
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL,
    id integer NOT NULL,
    session_id integer NOT NULL,
    role character varying(20) NOT NULL,
    content character varying NOT NULL,
    model_id_used integer,
    referenced_kbs character varying
);


ALTER TABLE public.chat_messages OWNER TO postgres;

--
-- Name: chat_messages_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.chat_messages_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.chat_messages_id_seq OWNER TO postgres;

--
-- Name: chat_messages_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.chat_messages_id_seq OWNED BY public.chat_messages.id;


--
-- Name: chat_sessions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.chat_sessions (
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL,
    id integer NOT NULL,
    user_id integer NOT NULL,
    title character varying(255)
);


ALTER TABLE public.chat_sessions OWNER TO postgres;

--
-- Name: chat_sessions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.chat_sessions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.chat_sessions_id_seq OWNER TO postgres;

--
-- Name: chat_sessions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.chat_sessions_id_seq OWNED BY public.chat_sessions.id;


--
-- Name: document_chunks; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.document_chunks (
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL,
    id integer NOT NULL,
    doc_id integer NOT NULL,
    content character varying NOT NULL,
    chunk_metadata character varying
);


ALTER TABLE public.document_chunks OWNER TO postgres;

--
-- Name: document_chunks_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.document_chunks_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.document_chunks_id_seq OWNER TO postgres;

--
-- Name: document_chunks_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.document_chunks_id_seq OWNED BY public.document_chunks.id;


--
-- Name: documents; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.documents (
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL,
    id integer NOT NULL,
    kb_id integer NOT NULL,
    uploader_id integer NOT NULL,
    filename character varying(255) NOT NULL,
    storage_path character varying(255) NOT NULL,
    file_type character varying(50),
    file_size integer,
    status character varying(20) NOT NULL,
    error_message character varying
);


ALTER TABLE public.documents OWNER TO postgres;

--
-- Name: documents_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.documents_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.documents_id_seq OWNER TO postgres;

--
-- Name: documents_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.documents_id_seq OWNED BY public.documents.id;


--
-- Name: knowledge_bases; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.knowledge_bases (
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL,
    id integer NOT NULL,
    owner_id integer NOT NULL,
    name character varying(100) NOT NULL,
    description character varying
);


ALTER TABLE public.knowledge_bases OWNER TO postgres;

--
-- Name: knowledge_bases_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.knowledge_bases_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.knowledge_bases_id_seq OWNER TO postgres;

--
-- Name: knowledge_bases_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.knowledge_bases_id_seq OWNED BY public.knowledge_bases.id;


--
-- Name: operation_logs; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.operation_logs (
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL,
    id integer NOT NULL,
    user_id integer,
    action character varying(100) NOT NULL,
    target_type character varying(50),
    target_id integer,
    details character varying,
    ip_address character varying(45)
);


ALTER TABLE public.operation_logs OWNER TO postgres;

--
-- Name: operation_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.operation_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.operation_logs_id_seq OWNER TO postgres;

--
-- Name: operation_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.operation_logs_id_seq OWNED BY public.operation_logs.id;


--
-- Name: sessions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.sessions (
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL,
    id integer NOT NULL,
    user_id integer NOT NULL,
    token character varying(255) NOT NULL,
    ip_address character varying(45),
    user_agent character varying,
    expires_at timestamp without time zone NOT NULL
);


ALTER TABLE public.sessions OWNER TO postgres;

--
-- Name: sessions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.sessions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.sessions_id_seq OWNER TO postgres;

--
-- Name: sessions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.sessions_id_seq OWNED BY public.sessions.id;


--
-- Name: system_settings; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.system_settings (
    key character varying(50) NOT NULL,
    value character varying,
    description character varying,
    updated_at timestamp without time zone NOT NULL
);


ALTER TABLE public.system_settings OWNER TO postgres;

--
-- Name: user_api_keys; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_api_keys (
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL,
    id integer NOT NULL,
    user_id integer NOT NULL,
    provider_id integer NOT NULL,
    api_key character varying(255) NOT NULL,
    description character varying(200)
);


ALTER TABLE public.user_api_keys OWNER TO postgres;

--
-- Name: user_api_keys_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.user_api_keys_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.user_api_keys_id_seq OWNER TO postgres;

--
-- Name: user_api_keys_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.user_api_keys_id_seq OWNED BY public.user_api_keys.id;


--
-- Name: user_quotas; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_quotas (
    user_id integer NOT NULL,
    max_kbs integer NOT NULL,
    max_docs_per_kb integer NOT NULL,
    max_storage_mb integer NOT NULL,
    updated_at timestamp without time zone NOT NULL
);


ALTER TABLE public.user_quotas OWNER TO postgres;

--
-- Name: user_settings; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_settings (
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL,
    id integer NOT NULL,
    user_id integer NOT NULL,
    default_model_id integer,
    default_knowledge_bases character varying,
    chat_retention_days integer NOT NULL,
    theme character varying(20) NOT NULL,
    font_size character varying(20) NOT NULL,
    enable_high_contrast boolean NOT NULL,
    enable_reduced_motion boolean NOT NULL,
    provider_preferences text
);


ALTER TABLE public.user_settings OWNER TO postgres;

--
-- Name: user_settings_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.user_settings_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.user_settings_id_seq OWNER TO postgres;

--
-- Name: user_settings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.user_settings_id_seq OWNED BY public.user_settings.id;


--
-- Name: users; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.users (
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL,
    id integer NOT NULL,
    username character varying(50) NOT NULL,
    email character varying(100) NOT NULL,
    password_hash character varying(255) NOT NULL,
    display_name character varying(100),
    avatar_url character varying(255),
    bio character varying,
    is_admin boolean NOT NULL,
    status character varying(20) NOT NULL,
    last_login_at timestamp without time zone
);


ALTER TABLE public.users OWNER TO postgres;

--
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.users_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.users_id_seq OWNER TO postgres;

--
-- Name: users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;


--
-- Name: ai_models id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ai_models ALTER COLUMN id SET DEFAULT nextval('public.ai_models_id_seq'::regclass);


--
-- Name: ai_providers id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ai_providers ALTER COLUMN id SET DEFAULT nextval('public.ai_providers_id_seq'::regclass);


--
-- Name: chat_messages id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.chat_messages ALTER COLUMN id SET DEFAULT nextval('public.chat_messages_id_seq'::regclass);


--
-- Name: chat_sessions id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.chat_sessions ALTER COLUMN id SET DEFAULT nextval('public.chat_sessions_id_seq'::regclass);


--
-- Name: document_chunks id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.document_chunks ALTER COLUMN id SET DEFAULT nextval('public.document_chunks_id_seq'::regclass);


--
-- Name: documents id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.documents ALTER COLUMN id SET DEFAULT nextval('public.documents_id_seq'::regclass);


--
-- Name: knowledge_bases id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.knowledge_bases ALTER COLUMN id SET DEFAULT nextval('public.knowledge_bases_id_seq'::regclass);


--
-- Name: operation_logs id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.operation_logs ALTER COLUMN id SET DEFAULT nextval('public.operation_logs_id_seq'::regclass);


--
-- Name: sessions id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.sessions ALTER COLUMN id SET DEFAULT nextval('public.sessions_id_seq'::regclass);


--
-- Name: user_api_keys id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_api_keys ALTER COLUMN id SET DEFAULT nextval('public.user_api_keys_id_seq'::regclass);


--
-- Name: user_settings id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_settings ALTER COLUMN id SET DEFAULT nextval('public.user_settings_id_seq'::regclass);


--
-- Name: users id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);


--
-- Data for Name: ai_models; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.ai_models VALUES ('2025-07-27 06:43:40.674565', '2025-07-27 06:43:40.674622', 3, 1, 'Qwen/Qwen2.5-32B-Instruct', 'Qwen2.5-32B-Instruct', true, 'sk-ltvyukqqyhwhedkxyqhsqnrtqehlfdzpflskkfkqwetoiixm', true, NULL, true, 0.0024);
INSERT INTO public.ai_models VALUES ('2025-07-27 06:43:40.680116', '2025-07-27 06:43:40.680172', 5, 1, 'deepseek-ai/DeepSeek-V2.5', 'DeepSeek-V2.5', true, 'sk-ltvyukqqyhwhedkxyqhsqnrtqehlfdzpflskkfkqwetoiixm', true, 4000, true, 0.0014);
INSERT INTO public.ai_models VALUES ('2025-07-27 06:43:40.695971', '2025-07-27 06:43:40.696035', 10, 3, 'deepseek-chat', 'DeepSeek Chat', true, NULL, true, NULL, true, 0.0034);
INSERT INTO public.ai_models VALUES ('2025-07-28 10:49:46.622296', '2025-07-28 10:49:46.622482', 13, 5, 'gemini-2.5-flash', 'gemini-2.5-flash', true, 'AIzaSyD4mv0k7MOh4kGpdm9lsz4uQlFuE3StATI', true, 12000, true, NULL);
INSERT INTO public.ai_models VALUES ('2025-07-28 11:34:46.670123', '2025-07-28 11:34:46.670393', 14, 5, 'gemini-2.5-pro', 'gemini-2.5-pro', true, '', true, 12000, true, NULL);
INSERT INTO public.ai_models VALUES ('2025-07-27 06:43:40.689934', '2025-07-27 06:43:40.690003', 8, 2, 'gpt-4-turbo', 'GPT-4 Turbo', true, NULL, true, 8000, true, 0.01);
INSERT INTO public.ai_models VALUES ('2025-07-27 06:43:40.692121', '2025-07-27 06:43:40.692181', 9, 2, 'gpt-3.5-turbo', 'GPT-3.5 Turbo', true, NULL, true, 16000, true, 0.02);
INSERT INTO public.ai_models VALUES ('2025-07-27 06:43:40.687064', '2025-07-27 06:43:40.687123', 7, 2, 'gpt-4', 'GPT-4', true, NULL, true, 8003, true, 0.016);
INSERT INTO public.ai_models VALUES ('2025-07-27 06:43:40.698654', '2025-07-27 06:43:40.698717', 11, 3, 'deepseek-coder', 'DeepSeek Coder', true, NULL, true, 4096, true, 0.0014);
INSERT INTO public.ai_models VALUES ('2025-07-27 06:43:40.6716', '2025-07-27 06:43:40.671654', 2, 1, 'Qwen/Qwen2.5-14B-Instruct', 'Qwen2.5-14B-Instruct', true, 'sk-ltvyukqqyhwhedkxyqhsqnrtqehlfdzpflskkfkqwetoiixm', true, NULL, true, 0.0014);
INSERT INTO public.ai_models VALUES ('2025-07-27 06:43:40.666961', '2025-07-27 06:43:40.667011', 1, 1, 'Qwen/Qwen2.5-7B-Instruct', 'Qwen2.5-7B-Instruct', true, 'sk-ltvyukqqyhwhedkxyqhsqnrtqehlfdzpflskkfkqwetoiixm', true, NULL, true, 0.0007);
INSERT INTO public.ai_models VALUES ('2025-07-27 06:43:40.683005', '2025-07-27 06:43:40.683082', 6, 1, 'meta-llama/Meta-Llama-3.1-8B-Instruct', 'Llama-3.1-8B-Instruct', true, 'sk-ltvyukqqyhwhedkxyqhsqnrtqehlfdzpflskkfkqwetoiixm', true, NULL, true, 0.01);
INSERT INTO public.ai_models VALUES ('2025-07-27 06:43:40.676984', '2025-07-27 06:43:40.677038', 4, 1, 'Qwen/Qwen2.5-72B-Instruct', 'Qwen2.5-72B-Instruct', true, 'sk-ltvyukqqyhwhedkxyqhsqnrtqehlfdzpflskkfkqwetoiixm', true, NULL, true, 0.0056);


--
-- Data for Name: ai_providers; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.ai_providers VALUES ('2025-07-27 06:43:40.651829', '2025-07-27 06:43:40.65198', 4, 'anthropic', 'Anthropic', 'https://api.anthropic.com', true, 'Anthropic Claude系列模型');
INSERT INTO public.ai_providers VALUES ('2025-07-27 06:43:40.622268', '2025-07-27 06:43:40.622331', 1, 'siliconflow', '硅基流动', 'https://api.siliconflow.cn/v1', true, '硅基流动AI平台，提供多种开源大模型服务');
INSERT INTO public.ai_providers VALUES ('2025-07-27 06:43:40.638667', '2025-07-27 06:43:40.638724', 2, 'openai', 'OpenAI', 'https://api.openai.com/v1', true, 'OpenAI官方API服务');
INSERT INTO public.ai_providers VALUES ('2025-07-27 06:43:40.645014', '2025-07-27 06:43:40.645121', 3, 'deepseek', 'DeepSeek', 'https://api.deepseek.com/v1', true, 'DeepSeek AI平台');
INSERT INTO public.ai_providers VALUES ('2025-07-27 06:43:40.65771', '2025-07-27 06:43:40.657773', 5, 'google', 'Google', 'https://generativelanguage.googleapis.com/v1beta', true, '谷歌大模型');


--
-- Data for Name: chat_messages; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.chat_messages VALUES ('2025-07-27 09:43:44.460367', '2025-07-27 09:43:44.460469', 1, 26, 'user', '你好', 1, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-27 09:43:49.698757', '2025-07-27 09:43:49.698871', 2, 26, 'assistant', '你好！有什么问题或者需要我帮助的吗？', 1, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-27 09:45:04.611343', '2025-07-27 09:45:04.611433', 3, 26, 'user', '你是什么模型', 6, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-27 09:45:05.473412', '2025-07-27 09:45:05.473941', 4, 26, 'assistant', 'AI服务调用失败: 硅基流动API调用失败: Error code: 403 - {''code'': 30003, ''message'': ''Model disabled.'', ''data'': None}', 6, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-27 09:45:34.724344', '2025-07-27 09:45:34.724435', 5, 26, 'user', '你是什么模型', 5, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-27 09:45:39.802592', '2025-07-27 09:45:39.802707', 6, 26, 'assistant', '我是由中国的深度求索（DeepSeek）公司开发的智能助手DeepSeek Chat。我可以帮助回答问题、提供信息、执行任务等。如果你有任何疑问或需要帮助，请随时告诉我。', 5, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-27 09:50:23.308325', '2025-07-27 09:50:23.308513', 7, 26, 'user', '你是什么模型', 6, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-27 09:50:24.197029', '2025-07-27 09:50:24.197506', 8, 26, 'assistant', '当前选择的AI模型已被禁用，请在设置中选择其他可用的模型', 6, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-27 09:50:46.779672', '2025-07-27 09:50:46.779755', 9, 26, 'user', '你是什么模型', 6, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-27 09:50:47.662722', '2025-07-27 09:50:47.662994', 10, 26, 'assistant', '当前选择的AI模型已被禁用，请在设置中选择其他可用的模型', 6, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-27 09:50:58.483661', '2025-07-27 09:50:58.483755', 11, 26, 'user', '你是什么模型', 2, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-27 16:55:10.279113', '2025-07-27 16:55:10.279198', 15, 27, 'user', '你好你是什么模型', 4, '[]');
INSERT INTO public.chat_messages VALUES ('2025-07-27 16:55:13.289869', '2025-07-27 16:55:13.289985', 16, 27, 'assistant', '你好！我是Qwen，是阿里云推出的一种超大规模语言模型。我具有强大的语言理解与生成能力，能够进行流畅的对话、写故事、写公文、写邮件、写剧本等等，还能表达观点、撰写代码，玩文字游戏等。如果你有任何问题或需要帮助，欢迎随时和我说！', 4, '[]');
INSERT INTO public.chat_messages VALUES ('2025-07-27 17:35:23.931542', '2025-07-27 17:35:23.931711', 17, 28, 'user', '你好', 3, '[]');
INSERT INTO public.chat_messages VALUES ('2025-07-27 17:35:25.237721', '2025-07-27 17:35:25.237969', 18, 28, 'assistant', '你好！有什么可以帮助你的吗？', 3, '[]');
INSERT INTO public.chat_messages VALUES ('2025-07-27 18:11:58.654324', '2025-07-27 18:11:58.654743', 19, 25, 'user', 'option = {
  title: { text: ''销售数据'' },
  xAxis: { data: [''1月'', ''2月'', ''3月'', ''4月''] },
  yAxis: {},
  series: [{
    name: ''销售额'',
    type: ''bar'',
    data: [120, 200, 150, 80]
  }]
}', 3, '[]');
INSERT INTO public.chat_messages VALUES ('2025-07-27 18:24:13.850838', '2025-07-27 18:24:13.850964', 21, 25, 'user', 'option = {
  title: { text: ''销售数据'' },
  xAxis: { data: [''1月'', ''2月'', ''3月'', ''4月''] },
  yAxis: {},
  series: [{
    name: ''销售额'',
    type: ''bar'',
    data: [120, 200, 150, 80]
  }]
}', 3, '[]');
INSERT INTO public.chat_messages VALUES ('2025-07-27 18:24:41.269435', '2025-07-27 18:24:41.269531', 23, 25, 'user', '请帮我生成一个销售数据的柱状图，数据如下：
1月: 120
2月: 200  
3月: 150
4月: 80', 3, '[]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 11:20:37.280735', '2025-07-28 11:29:56.23839', 52, 26, 'assistant', '根据提供的知识库信息，22级重修作业主要集中在电子商务专业，以下是具体的专业课程和作业要求：

1. **电子商务法律概论 (课程号: CC010022)**
   - 负责老师: 邓文雨
   - 作业要求: 提交一份电子商务法律法规的案例分析，字数不少于1000字，word电子档命名：姓名学号➕重修科目。加Q发我。联系方式: Q：3209070030

2. **电子商务基础项目实战 (课程号: CC010010)**
   - 负责老师: 邓文雨
   - 作业要求: 提交一份电子商务基础项目实战的案例分析。字数不少于1000字。加Q发我，word电子档命名：姓名学号➕重修科目。联系方式: Q：3209070030

3. **项目实训3：视频制作 (课程号: CC071003)**
   - 负责老师: 孙海婷
   - 作业要求: 提交一篇视频剪辑脚本（word文档），题材任选，脚本字数不少于1000字，标注好【重修科目-姓名】提交word文档至qq邮箱*****************。联系方式：<EMAIL>

4. **新媒体界面设计 (课程号: CC020013)**
   - 负责老师: 覃琴
   - 作业要求: 分析淘宝和京东用户界面设计的不同、及其优缺点。不少于1000字。以word提交，word命名：【重修科目-姓名】。联系方式: QQ:1791071916

5. **新媒体文案创作与传播 (课程号: CC011016)**
   - 负责老师: 曾彦
   - 作业要求: 为自己选择的一个品牌写一篇软文文案，字数不少于500字。加QQ发送doc文档，文档命名：姓名+学号+重修科目。联系方式: QQ：77809441

6. **直播电商实操 (课程号: CC020028)**
   - 负责老师: 覃琴
   - 作业要求: 提交一份不少于20分钟的直播脚本，以word提交，word命名：【重修科目-姓名】。联系方式: QQ:1791071916

7. **电子商务概论 (课程号: CC010002)**
   - 负责老师: 孙海婷
   - 作业要求: 提交一篇电子商务营销相关论文（word文档），题目任选，字数不少于1500字，标注好【重修科目-姓名】提交word文档至qq邮箱*****************。联系方式: <EMAIL>

8. **管理学基础 (课程号: CC010023)**
   - 负责老师: 曾彦
   - 作业要求: 为自己选择的一个品牌，撰写一篇休眠客户促活电子商务营销活动策划方案，字数不少于1500字。加QQ发送doc文档，文档命名：姓名+学号+重修科目。联系方式: QQ：77809441

请根据具体课程的要求准备相应的作业。如果有其他课程或问题，可以继续提问。', 1, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 12:16:14.390294', '2025-07-28 12:19:07.14583', 60, 26, 'assistant', '好的，我能为您生成一个描述每个老师布置作业数量的条形图。

请注意，我只能提供Chart.js格式的JSON配置，无法直接生成HTML代码。您可以将此JSON配置集成到您的网页中以显示图表。

```json
{
  "type": "bar",
  "data": {
    "labels": ["邓文雨", "孙海婷", "覃琴", "曾彦", "黄沁玥", "任曦", "张越航", "胡海霞", "汪耀辉", "胥恭丽", "祝绍红", "廖斌", "耿莹"],
    "datasets": [{
      "label": "作业数量",
      "data": [2, 3, 3, 4, 3, 1, 1, 2, 1, 1, 2, 1, 1],
      "backgroundColor": [
        "#FF6384",
        "#36A2EB",
        "#FFCE56",
        "#4BC0C0",
        "#9966FF",
        "#FF9F40",
        "#C9CBCF",
        "#A3E4D7",
        "#F1948A",
        "#D7BDE2",
        "#82E0AA",
        "#FADBD8",
        "#D6DBDF"
      ]
    }]
  },
  "options": {
    "responsive": true,
    "plugins": {
      "title": {
        "display": true,
        "text": "各老师布置作业数量统计"
      }
    },
    "scales": {
      "y": {
        "beginAtZero": true
      }
    }
  }
}
```', 13, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-27 18:24:57.370327', '2025-07-27 18:51:21.822979', 24, 25, 'assistant', '### ECharts柱状图示例：
```javascript
option = {
  title: { text: ''销售数据统计'' },
  xAxis: { data: [''1月'', ''2月'', ''3月'', ''4月''] },
  yAxis: {},
  series: [{
    name: ''销售额'',
    type: ''bar'',
    data: [120, 200, 150, 80]
  }]
}
```

### Chart.js JSON格式（推荐）：
```json
{
  "type": "bar",
  "title": "销售数据统计",
  "data": {
    "labels": ["1月", "2月", "3月", "4月"],
    "datasets": [{
      "label": "销售额",
      "data": [120, 200, 150, 80]
    }]
  }
}
```

### Python matplotlib代码（自动转换）：
```python
import matplotlib.pyplot as plt

# 数据
labels = ["1月", "2月", "3月", "4月"]
data = [120, 200, 150, 80]

# 创建图表
plt.figure(figsize=(10, 6))
plt.bar(labels, data)  # 或 plt.plot() 用于折线图
plt.title("销售数据统计")
plt.xlabel("月份")
plt.ylabel("销售额")
plt.show()
```

请选择你需要的格式，我可以为你生成具体的代码。', 1, '[]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 12:33:26.654969', '2025-07-28 13:19:41.096223', 64, 26, 'assistant', '好的，没问题。

我已经根据您提供的知识库内容，统计了每位老师布置的作业数量，并为您生成了条形图。

```json
{
  "type": "bar",
  "data": {
    "labels": ["曾彦", "孙海婷", "覃琴", "黄沁玥", "邓文雨", "胡海霞", "祝绍红", "任曦", "张越航", "汪耀辉", "胥恭丽", "廖斌", "耿莹"],
    "datasets": [{
      "label": "作业数量",
      "data": [4, 3, 3, 3, 2, 2, 2, 1, 1, 1, 1, 1, 1],
      "backgroundColor": [
        "#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0", "#9966FF", "#FF9F40", 
        "#C9CBCF", "#E7E9ED", "#77DD77", "#FDFD96", "#836953", "#FFB347", "#B19CD9"
      ],
      "borderColor": "#fff",
      "borderWidth": 1
    }]
  },
  "options": {
    "responsive": true,
    "plugins": {
      "title": {
        "display": true,
        "text": "各老师布置作业数量统计",
        "font": {
          "size": 18
        }
      },
      "legend": {
        "display": false
      }
    },
    "scales": {
      "y": {
        "beginAtZero": true,
        "ticks": {
          "stepSize": 1
        }
      }
    }
  }
}
```

从图表中可以看出：
*   **曾彦**老师布置的作业最多，共 **4** 项。
*   **孙海婷**、**覃琴** 和 **黄沁玥** 老师并列第二，各布置了 **3** 项作业。
*   其他老师的作业数量在1到2项之间。

如果您需要其他类型的图表或者有其他问题，随时可以告诉我！', 14, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-27 18:24:24.59812', '2025-07-27 19:00:55.481891', 22, 25, 'assistant', '您已经提供了一个使用ECharts的配置对象，这是一个很好的柱状图配置示例。为了更好地展示这个配置并确保其正确生成图表，我们可以将这段配置转换成一个完整的HTML文档。这样，您可以直接在浏览器中预览效果。

下面是展示您提供的ECharts配置的完整HTML文档：

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>销售数据统计</title>
    <!-- 引入 ECharts 文件 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js"></script>
</head>
<body>
    <div id="main" style="width: 600px;height:400px;"></div>
    <script>
        // 初始化图表实例
        var myChart = echarts.init(document.getElementById(''main''));

        // 指定图表的配置项和数据
        var option = {
            title: { text: ''销售数据'' },
            xAxis: { data: [''1月'', ''2月'', ''3月'', ''4月''] },
            yAxis: {},
            series: [{
                name: ''销售额'',
                type: ''bar'',
                data: [120, 200, 150, 80]
            }]
        };

        // 使用配置项和数据显示图表
        myChart.setOption(option);
    </script>
</body>
</html>
```

### 解释
1. **引入ECharts**：`<script src="https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js"></script>` 这一行代码从CDN引入了ECharts库，确保ECharts库在页面中可用。
2. **初始化图表**：通过`echarts.init(document.getElementById(''main''))`初始化图表实例，其中`''main''`是HTML中`<div>`的id。
3. **配置图表**：定义了图表的配置选项，并通过`myChart.setOption(option)`设置了图表的数据和样式。
4. **HTML结构**：在`<body>`中定义了一个`<div>`元素，其ID为`main`，用于承载图表。

请将上述代码复制到一个`.html`文件中，然后在浏览器中打开该文件，即可看到生成的柱状图。如果您有任何进一步的问题或需要修改的地方，请随时告知！', 1, '[]');
INSERT INTO public.chat_messages VALUES ('2025-07-27 19:03:36.889011', '2025-07-27 19:03:36.889134', 25, 25, 'user', '用html画一张条形图', 1, '[]');
INSERT INTO public.chat_messages VALUES ('2025-07-27 19:03:54.546357', '2025-07-27 19:03:54.546581', 26, 25, 'assistant', '好的，我们将使用ECharts来生成一个条形图，并将其嵌入到一个HTML页面中。以下是完整的HTML代码：

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>销售数据统计</title>
    <!-- 引入 ECharts 文件 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js"></script>
</head>
<body>
    <div id="main" style="width: 600px;height:400px;"></div>
    <script>
        // 初始化图表实例
        var myChart = echarts.init(document.getElementById(''main''));

        // 指定图表的配置项和数据
        var option = {
            title: {
                text: ''销售数据统计''
            },
            xAxis: {
                data: [''1月'', ''2月'', ''3月'', ''4月'']
            },
            yAxis: {},
            series: [{
                name: ''销售额'',
                type: ''bar'',
                data: [120, 200, 150, 80]
            }]
        };

        // 使用配置项和数据显示图表
        myChart.setOption(option);
    </script>
</body>
</html>
```

### 解释
1. **引入ECharts**：`<script src="https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js"></script>` 这一行代码从CDN引入了ECharts库，确保ECharts库在页面中可用。
2. **初始化图表**：通过`echarts.init(document.getElementById(''main''))`初始化图表实例，其中`''main''`是HTML中`<div>`的id。
3. **配置图表**：定义了图表的配置选项，并通过`myChart.setOption(option)`设置了图表的数据和样式。
4. **HTML结构**：在`<body>`中定义了一个`<div>`元素，其ID为`main`，用于承载图表。

将上述代码保存到一个`.html`文件中，然后在浏览器中打开该文件，即可看到生成的条形图。

如果你有任何其他需求或问题，请随时告诉我！', 1, '[]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 06:48:57.316531', '2025-07-28 06:48:57.316832', 27, 25, 'user', '根据文档数据帮我绘制几张图表', 1, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 06:50:30.58922', '2025-07-28 06:50:30.589413', 28, 25, 'assistant', '好的，我们可以根据你提供的数据绘制几种不同的图表。假设数据如下：

- 1月: 120
- 2月: 200
- 3月: 150
- 4月: 80

我们将分别绘制条形图、折线图和饼图。以下是完整的HTML代码：

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>销售数据统计</title>
    <!-- 引入 ECharts 文件 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js"></script>
</head>
<body>
    <h1>销售数据统计</h1>
    <div id="barChart" style="width: 600px;height:400px;"></div>
    <div id="lineChart" style="width: 600px;height:400px;"></div>
    <div id="pieChart" style="width: 600px;height:400px;"></div>
    <script>
        // 条形图
        var barChart = echarts.init(document.getElementById(''barChart''));
        var barOption = {
            title: {
                text: ''条形图 - 销售数据''
            },
            xAxis: {
                data: [''1月'', ''2月'', ''3月'', ''4月'']
            },
            yAxis: {},
            series: [{
                name: ''销售额'',
                type: ''bar'',
                data: [120, 200, 150, 80]
            }]
        };
        barChart.setOption(barOption);

        // 折线图
        var lineChart = echarts.init(document.getElementById(''lineChart''));
        var lineOption = {
            title: {
                text: ''折线图 - 销售数据''
            },
            xAxis: {
                data: [''1月'', ''2月'', ''3月'', ''4月'']
            },
            yAxis: {},
            series: [{
                name: ''销售额'',
                type: ''line'',
                data: [120, 200, 150, 80]
            }]
        };
        lineChart.setOption(lineOption);

        // 饼图
        var pieChart = echarts.init(document.getElementById(''pieChart''));
        var pieOption = {
            title: {
                text: ''饼图 - 销售数据''
            },
            series: [{
                type: ''pie'',
                data: [
                    { value: 120, name: ''1月'' },
                    { value: 200, name: ''2月'' },
                    { value: 150, name: ''3月'' },
                    { value: 80, name: ''4月'' }
                ],
                radius: ''50%''
            }]
        };
        pieChart.setOption(pieOption);
    </script>
</body>
</html>
```

### 解释
1. **引入ECharts**：`<script src="https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js"></script>` 这一行代码从CDN引入了ECharts库。
2. **初始化图表**：分别通过`echarts.init()`初始化三个不同类型的图表实例。
3. **配置图表**：
   - **条形图**：`barOption`配置了条形图的样式和数据。
   - **折线图**：`lineOption`配置了折线图的样式和数据。
   - **饼图**：`pieOption`配置了饼图的样式和数据。
4. **HTML结构**：在`<body>`中定义了三个`<div>`元素，分别用于承载条形图、折线图和饼图。

将上述代码保存到一个`.html`文件中，然后在浏览器中打开该文件，即可看到三个不同的图表。

如果你有任何其他需求或问题，请随时告诉我！', 1, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 06:51:22.080266', '2025-07-28 06:51:22.080347', 29, 25, 'user', '这个知识库中有哪些内容', 1, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 06:51:40.7014', '2025-07-28 06:51:40.701571', 30, 25, 'assistant', '这个知识库主要包含以下内容：

1. **技术文档和教程**：
   - **编程语言**：包括Python、Java、JavaScript、Go等。
   - **框架和库**：包括Django、Flask、React、Vue、ECharts等。
   - **数据库**：包括MySQL、MongoDB、Redis等。
   - **云计算和服务器**：包括阿里云、AWS、Kubernetes等。

2. **数据分析和可视化**：
   - **数据处理**：包括数据清洗、数据转换等。
   - **可视化工具**：包括Matplotlib、Seaborn、Pandas、Plotly等。
   - **图表生成**：包括条形图、折线图、饼图等。

3. **机器学习和人工智能**：
   - **算法**：包括线性回归、决策树、支持向量机等。
   - **框架**：包括TensorFlow、PyTorch、Scikit-learn等。
   - **应用场景**：包括图像识别、自然语言处理、推荐系统等。

4. **操作系统和网络**：
   - **操作系统**：包括Linux、Windows、MacOS等。
   - **网络协议**：包括HTTP、TCP/IP、DNS等。
   - **网络工具**：包括curl、ping、traceroute等。

5. **系统管理**：
   - **配置管理**：包括Ansible、Puppet、Chef等。
   - **监控和日志管理**：包括Prometheus、Grafana、ELK Stack等。
   - **安全**：包括防火墙、SSL证书、漏洞管理等。

6. **前端开发**：
   - **框架和库**：包括React、Vue、Angular等。
   - **工具**：包括Webpack、Babel、ESLint等。
   - **页面布局和样式**：包括Bootstrap、Tailwind CSS等。

7. **后端开发**：
   - **框架**：包括Django、Flask、Express等。
   - **服务器管理**：包括Nginx、Apache等。
   - **数据库管理**：包括MySQL、PostgreSQL、MongoDB等。

8. **其他**：
   - **项目管理**：包括Git、Jira、Trello等。
   - **开发工具**：包括VS Code、PyCharm、WebStorm等。
   - **文档和资源**：包括官方文档、教程、实战案例等。

如果你有具体的需求或想了解更多详细信息，请告诉我！我可以为你提供更具体的文档和教程。', 1, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 06:51:57.992523', '2025-07-28 06:51:57.992601', 31, 25, 'user', '数据库重修作业是上面', 1, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 06:55:08.245412', '2025-07-28 06:55:08.245576', 32, 25, 'assistant', '好的，我们可以为你设计一个数据库重修作业，确保涵盖数据库的基本概念、操作和应用。以下是一个具体的作业示例，包括题目和参考答案。

### 数据库重修作业

#### 一、选择题（每题2分，共20分）

1. **下列哪个选项不是关系数据库的基本范式？**
   - A. 第一范式 (1NF)
   - B. 第二范式 (2NF)
   - C. 第三范式 (3NF)
   - D. 第四范式 (4NF)
   - **答案：D**

2. **下列哪个SQL语句用于创建数据库表？**
   - A. `CREATE TABLE`
   - B. `ALTER TABLE`
   - C. `DROP TABLE`
   - D. `SELECT TABLE`
   - **答案：A**

3. **下列哪个SQL语句用于插入数据？**
   - A. `INSERT INTO`
   - B. `UPDATE`
   - C. `DELETE`
   - D. `SELECT`
   - **答案：A**

4. **下列哪个SQL语句用于查询数据？**
   - A. `INSERT INTO`
   - B. `UPDATE`
   - C. `SELECT`
   - D. `CREATE TABLE`
   - **答案：C**

5. **下列哪个SQL语句用于更新数据？**
   - A. `INSERT INTO`
   - B. `UPDATE`
   - C. `DELETE`
   - D. `SELECT`
   - **答案：B**

6. **下列哪个SQL语句用于删除数据？**
   - A. `INSERT INTO`
   - B. `UPDATE`
   - C. `DELETE`
   - D. `SELECT`
   - **答案：C**

7. **下列哪个SQL语句用于删除数据库表？**
   - A. `CREATE TABLE`
   - B. `ALTER TABLE`
   - C. `DROP TABLE`
   - D. `SELECT TABLE`
   - **答案：C**

8. **下列哪个SQL语句用于添加索引？**
   - A. `CREATE INDEX`
   - B. `ALTER INDEX`
   - C. `DROP INDEX`
   - D. `SELECT INDEX`
   - **答案：A**

9. **下列哪个SQL语句用于事务控制？**
   - A. `BEGIN TRANSACTION`
   - B. `COMMIT`
   - C. `ROLLBACK`
   - D. 所有选项都正确
   - **答案：D**

10. **下列哪个选项不是数据库的约束类型？**
    - A. PRIMARY KEY
    - B. FOREIGN KEY
    - C. CHECK
    - D. UNIQUE
    - **答案：B**

#### 二、填空题（每题2分，共20分）

1. **`CREATE TABLE` 语句用于创建一个新的数据库表，其基本语法为：**
   - `CREATE TABLE 表名 (列1 数据类型, 列2 数据类型, ..., PRIMARY KEY (列名));`

2. **`INSERT INTO` 语句的基本语法为：**
   - `INSERT INTO 表名 (列名1, 列名2, ...) VALUES (值1, 值2, ...);`

3. **`SELECT` 语句的基本语法为：**
   - `SELECT 列名1, 列名2, ... FROM 表名;`

4. **`UPDATE` 语句的基本语法为：**
   - `UPDATE 表名 SET 列名1 = 值1, 列名2 = 值2, ... WHERE 条件;`

5. **`DELETE` 语句的基本语法为：**
   - `DELETE FROM 表名 WHERE 条件;`

6. **`DROP TABLE` 语句的基本语法为：**
   - `DROP TABLE 表名;`

7. **`CREATE INDEX` 语句的基本语法为：**
   - `CREATE INDEX 索引名 ON 表名 (列名);`

8. **事务控制中的 `BEGIN TRANSACTION` 语句用于开始一个新的事务。**

9. **事务控制中的 `COMMIT` 语句用于提交事务。**

10. **事务控制中的 `ROLLBACK` 语句用于回滚事务。**

#### 三、简答题（每题10分，共50分）

1. **解释什么是关系数据库的基本范式（1NF, 2NF, 3NF）。**

   - **答案**：
     - **1NF (第一范式)**：要求表中的每一列都是不可再分的原子值，即每个字段都应该是单一的值。
     - **2NF (第二范式)**：要求每个非键列完全依赖于主键，即不允许部分依赖。
     - **3NF (第三范式)**：要求每个非键列直接依赖于主键，即不允许传递依赖。

2. **解释 `CREATE INDEX` 和 `DROP INDEX` 的用途。**

   - **答案**：
     - `CREATE INDEX` 用于在表中创建索引，加快数据检索速度。索引可以提高查询效率，但会增加写入操作的复杂度。
     - `DROP INDEX` 用于删除表中的索引，减少存储空间并提高写入速度，但会降低查询效率。

3. **解释事务的 ACID 特性。**

   - **答案**：
     - **Atomicity (原子性)**：事务是一个不可分割的工作单元，要么全部执行成功，要么全部不执行。
     - **Consistency (一致性)**：事务执行前后，数据库必须从一个一致状态转换到另一个一致状态。
     - **Isolation (隔离性)**：并发执行的事务之间是隔离的，一个事务的执行不应影响其他事务的执行。
     - **Durability (持久性)**：事务一旦提交，其结果是永久性的，即使系统故障也不会丢失。

4. **解释 `INSERT INTO`, `UPDATE`, 和 `DELETE` 语句之间的区别。**

   - **答案**：
     - `INSERT INTO`：用于向表中插入新的数据记录。
     - `UPDATE`：用于修改已存在的数据记录。
     - `DELETE`：用于删除表中的数据记录。

5. **解释什么是数据库约束（Primary Key, Foreign Key, Check, Unique）。**

   - **答案**：
     - **Primary Key (主键)**：用于唯一标识表中的每一行数据，且不能重复。
     - **Foreign Key (外键)**：用于在两个表之间建立关联，确保外键值在关联表中存在。
     - **Check (检查约束)**：用于定义列的值必须满足的条件，确保数据的合法性。
     - **Unique (唯一约束)**：用于确保列中的值是唯一的，但可以为空值。

#### 四、编程题（共10分）

**题目**：编写一个SQL语句，创建一个包含学生信息的表 `students`，并插入一些示例数据。

1. **创建表 `students` 的SQL语句：**

   ```sql
   CREATE TABLE students (
       id INT PRIMARY KEY,
       name VARCHAR(100),
       age INT,
       major VARCHAR(100),
       enrollment_date DATE
   );
   ```

2. **插入示例数据：**

   ```sql
   INSERT INTO students (id, name, age, major, enrollment_date) VALUES
   (1, ''张三'', 20, ''计算机科学'', ''2023-09-01''),
   (2, ''李四'', 21, ''软件工程'', ''2023-09-01''),
   (3, ''王五'', 22, ''人工智能'', ''2023-09-01'');
   ```

希望这份作业能帮助你复习数据库的基本概念和操作！如果有任何问题或需要进一步的帮助，请随时告诉我！', 1, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 06:58:01.490259', '2025-07-28 06:58:01.490444', 33, 20, 'user', '数据库重修作业是什么', 4, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 06:58:08.026455', '2025-07-28 06:58:30.495346', 34, 20, 'assistant', '数据库重修作业通常指的是学生在数据库课程中，由于初次作业成绩不理想或未能通过，需要重新完成的一项或多项作业任务。这类作业的目的不仅在于帮助学生巩固和加深对数据库知识的理解，还在于培养学生的实践能力和问题解决能力。数据库重修作业的内容和要求可能会根据具体的课程目标、教学大纲以及教师的具体要求而有所不同。常见的数据库重修作业可能包括但不限于以下几个方面：

1. **概念理解**：要求学生重新复习数据库基础理论知识，如数据模型、关系代数、SQL语言等，并通过书面或口头形式展示自己的理解。

2. **设计任务**：要求学生根据给定的业务场景，设计数据库模式，包括但不限于实体-关系图（ER图）、表结构设计、主键和外键的设置等。

3. **编程实践**：要求学生使用SQL语言完成数据的插入、查询、更新和删除等操作，或者使用数据库管理系统（如MySQL、Oracle、SQL Server等）的高级特性，如存储过程、触发器等。

4. **性能优化**：对于更高级的课程，可能会要求学生分析和优化数据库查询的性能，包括索引的设计、查询优化等。

5. **项目报告**：要求学生撰写项目报告，总结项目过程中的学习点、遇到的问题及解决方案等，以加深对整个开发生命周期的理解。

如果你有具体的数据库重修作业要求或遇到了具体的难题，欢迎分享更多的细节，我可以提供更加针对性的帮助和建议。', 4, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 06:59:17.523734', '2025-07-28 06:59:17.523841', 35, 20, 'user', '说一下22级重修作业要求', 4, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 11:20:27.387421', '2025-07-28 11:20:27.387524', 51, 26, 'user', '22级重修作业有哪些', 13, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 06:59:51.863978', '2025-07-28 07:19:06.315089', 36, 20, 'assistant', '根据知识库中的信息，以下是22级电子商务专业学生重修作业的具体要求：

### 电子商务专业重修作业要求

1. **电子商务法律概论**
   - **负责老师**: 邓文雨
   - **作业要求**: 提交一份电子商务法律法规的案例分析，字数不少于1000字，word电子档命名：姓名学号➕重修科目。
   - **联系方式**: Q：3209070030

2. **电子商务基础项目实战**
   - **负责老师**: 邓文雨
   - **作业要求**: 提交一份电子商务基础项目实战的案例分析。字数不少于1000字。加Q发我，word电子档命名：姓名学号➕重修科目。
   - **联系方式**: Q：3209070030

3. **项目实训3：视频制作**
   - **负责老师**: 孙海婷
   - **作业要求**: 提交一篇视频剪辑脚本（word文档），题材任选，脚本字数不少于1000字，标注好【重修科目-姓名】提交word文档至qq邮箱2362705779@qq,com
   - **联系方式**: <EMAIL>

4. **新媒体界面设计**
   - **负责老师**: 覃琴
   - **作业要求**: 分析淘宝和京东用户界面设计的不同、及其优缺点。不少于1000字。以word提交，word命名：【重修科目-姓名】，做好之后，加Q发我。
   - **联系方式**: QQ:1791071916

5. **新媒体文案创作与传播**
   - **负责老师**: 曾彦
   - **作业要求**: 为自己选择的一个品牌写一篇软文文案，字数不少于500字。加QQ发送doc文档，文档命名：姓名+学号+重修科目
   - **联系方式**: QQ：77809441

6. **直播电商实操**
   - **负责老师**: 覃琴
   - **作业要求**: 提交一份不少于20分钟的直播脚本，以word提交，word命名：【重修科目-姓名】，做好之后，加Q发我。
   - **联系方式**: QQ:1791071916

7. **电子商务概论**
   - **负责老师**: 孙海婷
   - **作业要求**: 提交一篇电子商务营销相关论文（word文档），题目任选，字数不少于1500字，标注好【重修科目-姓名】提交word文档至qq邮箱2362705779@qq,com
   - **联系方式**: <EMAIL>

8. **管理学基础**
   - **负责老师**: 曾彦
   - **作业要求**: 为自己选择的一个品牌，撰写一篇休眠客户促活电子商务营销活动策划方案，字数不少于1500字。
   - **联系方式**: 加QQ发送doc文档，文档命名：姓名+学号+重修科目 | QQ：77809441

9. **图形图像处理(PhotoShop)**
   - **负责老师**: 黄沁玥
   - **作业要求**: 自选一款日用产品，完成主图与详情页文案策划。要求分点分模块完成，字数不少于1500字。打印提交，作业写上【姓名-学号-班级-重修科目】
   - **联系方式**: QQ:54505465

10. **项目实训I：海报设计**
    - **负责老师**: 黄沁玥
    - **作业要求**: 主题自选（如环保、节日、校园活动等），使用Word完成（插入形状、图片、艺术字等基础功能）。打印提交，作业写上【姓名-学号-班级-重修科目】
    - **内容要求**: 主标题、1-2句宣传语、至少2张相关图片、简单的排版设计
    - **联系方式**: QQ：54505465

11. **网页制作项目实战（H5）**
    - **负责老师**: 覃琴
    - **作业要求**: 提交一份静态网页的代码，网页风格不限，以word提交，word命名：【重修科目-姓名】，做好之后，加Q发我。
    - **联系方式**: QQ:1791071916

12. **电子商务物流管理**
    - **负责老师**: 曾彦
    - **作业要求**: 从京东、菜鸟、顺丰等电商物流平台中选择一个，分析其各个物流管理职能的优势、劣势，以及改进建议，字数不少于1500字。加QQ发送doc文档，文档命名：姓名+学号+重修科目
    - **联系方式**: QQ：77809441

13. **短视频制作与特效**
    - **负责老师**: 孙海婷
    - **作业要求**: 提交一篇视频剪辑脚本（word文档），题材任选，脚本字数不少于1000字，标注好【重修科目-姓名】提交word文档至qq邮箱2362705779@qq,com
    - **联系方式**: <EMAIL>

14. **PS核心技术项目实战**
    - **负责老师**: 黄沁玥
    - **作业要求**: 为一个新品牌（如奶茶店、潮牌服饰、美妆品牌等）设计完整的视觉方案。撰写品牌定位分析、LOGO设计技术方案、延展应用设计等方面，字数不少于1500字。打印提交，作业写上【姓名-学号-班级-重修科目】
    - **联系方式**: QQ：54505465

15. **视频后期效果处理**
    - **负责老师**: 曾彦
    - **作业要求**: 提交一篇视频剪辑脚本，并包括视频主题和创意说明，后期效果所使用的技巧和方法。主题任选，字数不少于1500字，加QQ发送doc文档，文档命名：姓名+学号+重修科目
    - **联系方式**: QQ：77809441

16. **UI交互设计**
    - **负责老师**: 覃琴
    - **作业要求**: 分析淘宝和京东用户界面设计的不同、及其优缺点。不少于1000字。以word提交，word命名：【重修科目-姓名】，做好之后，加Q发我。
    - **联系方式**: QQ:1791071916

17. **电商网店运营与管理**
    - **负责老师**: 邓文雨
    - **作业要求**: 提交一份电商网店运营与管理的案例分析，字数不少于1000字，加Q发我。word文档命名为：姓名学号及重修科目
    - **联系方式**: Q：3209070030

### 物联网应用技术专业重修作业要求

1. **C语言程序设计**
    - **负责老师**: 胡海霞
    - **作业要求**: 编写一个程序，要求用户输入一个正整数N，程序输出从1到N之间的所有偶数和奇数，并分别计数。提交程序源文件，文件后缀为.c以及运行结果截图，将文件和截图都以班级+姓名命名方式（如：23级软件2班+张三 .c / 23级软件2班+张三 . jpg）以附件方式发送至邮箱：<EMAIL>
    - **联系方式**: <EMAIL>

2. **电工电子技术**
    - **负责老师**: 杨代坤
    - **作业要求**: 使用嘉立创绘制stm32单片机的最小系统板原理图，提交项目工程文件（文件命令班级+姓名+学号）
    - **联系方式**: <EMAIL>

3. **物联网技术概论**
    - **负责老师**: 杨代坤
    - **作业要求**: 详细构思一个解决身边小问题的物联网应用方案（如环境监测系统）（文件命令班级+姓名+学号）
    - **联系方式**: <EMAIL>

4. **新技术讲座(人工智能)**
    - **负责老师**: 汪耀辉
    - **作业要求**: 完成一篇不少于1000字的关于不同AI模型擅长领域的对比分析报告，学号加姓名命名
    - **联系方式**: <EMAIL>

请根据以上要求准备并提交相应的作业。如果有任何疑问，可以联系相应的老师。', 4, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 10:51:04.915803', '2025-07-28 10:51:04.915893', 39, 26, 'user', '你是什么模型', 13, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 10:51:05.902913', '2025-07-28 10:51:05.903058', 40, 26, 'assistant', 'AI服务调用失败，请检查模型设置和API密钥配置。错误详情：硅基流动API调用失败: Error code: 404', 13, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 11:10:38.980473', '2025-07-28 11:10:38.980582', 41, 26, 'user', '你是什么模型', 13, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 11:10:43.034753', '2025-07-28 11:10:43.034883', 42, 26, 'assistant', '我是一个AI助手。

我的具体模型是由我所运行的平台或服务决定的。由于当前系统可能遇到一些配置问题，我可能无法准确地报出我当前正在使用的模型名称。

请问您有什么其他问题或需要我帮助的吗？', 13, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 11:11:09.087997', '2025-07-28 11:11:09.088103', 43, 26, 'user', '你能用html帮我设计一个B站首页吗', 13, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 11:11:22.55419', '2025-07-28 11:11:22.554346', 44, 26, 'assistant', '









好的', 13, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 11:14:27.959956', '2025-07-28 11:14:27.960043', 45, 26, 'user', '你是什么模型', 13, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 11:14:35.326807', '2025-07-28 11:14:35.327004', 46, 26, 'assistant', '我是一个AI助手。

我的具体模型是由我所运行的平台或服务决定的。由于当前系统可能遇到一些配置问题，我可能无法准确地报出我当前正在使用的模型名称。

请问您有什么其他问题或需要我帮助的吗？', 13, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 11:14:47.391445', '2025-07-28 11:14:47.391594', 47, 26, 'user', '总结文档内容', 13, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 11:14:54.883292', '2025-07-28 11:14:54.883402', 48, 26, 'assistant', '好的，请您将文档内容提供给我。

您可以选择以下方式：

1.  **直接粘贴文本：** 如果文档内容不长，可以直接复制粘贴到聊天框中。
2.  **上传文件：** 如果文档是PDF、Word、TXT等格式，您可以尝试上传文件（请注意，我的能力可能受限于平台对文件上传的支持）。
3.  **提供链接：** 如果文档是在网页上，请提供网页链接。

在您提供文档后，请告诉我您希望的总结类型，例如：
*   需要一份**简要概括**还是**详细总结**？
*   主要关注哪些**关键信息**或**核心观点**？
*   总结的**受众**是谁（比如，是给管理层看的，还是给普通读者看的）？

我将尽力为您提供准确的总结。', 13, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 11:16:40.901751', '2025-07-28 11:16:40.901841', 49, 26, 'user', '用html绘制一张图表给我', 2, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 11:16:48.663515', '2025-07-28 11:16:48.663674', 50, 26, 'assistant', '当然可以！使用HTML和JavaScript库（如Chart.js）可以很容易地创建图表。下面是一个简单的例子，展示如何使用HTML和Chart.js来绘制一个折线图。

首先，确保你已经引入了Chart.js库。你可以通过CDN链接来引入它，这样就不需要下载库文件了。

以下是一个完整的HTML文件示例，它会在页面上显示一个折线图：

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>折线图示例</title>
    <!-- 引入Chart.js库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        canvas {
            -moz-user-select: none;
            -webkit-user-select: none;
            -ms-user-select: none;
        }
    </style>
</head>
<body>
    <h1>折线图示例</h1>
    <canvas id="myChart" width="400" height="400"></canvas>
    <script>
        const labels = [''一月'', ''二月'', ''三月'', ''四月'', ''五月'', ''六月''];
        const data = {
            labels: labels,
            datasets: [{
                label: ''示例数据'',
                data: [20, 30, 50, 40, 60, 70],
                backgroundColor: ''rgba(255, 99, 132, 0.2)'',
                borderColor: ''rgba(255, 99, 132, 1)'',
                borderWidth: 1
            }]
        };

        const config = {
            type: ''line'',
            data: data,
            options: {}
        };

        const ctx = document.getElementById(''myChart'').getContext(''2d'');
        const myChart = new Chart(ctx, config);
    </script>
</body>
</html>
```

将上述代码保存为一个HTML文件（例如`index.html`），然后在浏览器中打开它，你将看到一个简单的折线图。

如果你需要其他类型的图表（如柱状图、饼图等），可以参考Chart.js的官方文档来调整代码。', 2, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 11:38:49.260378', '2025-07-28 11:38:49.260502', 53, 26, 'user', '22级重修作业有哪些', 13, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 11:38:56.99596', '2025-07-28 11:55:40.044591', 54, 26, 'assistant', '您好！关于“22级重修作业”的具体信息，我的知识库中没有直接标明“22级”的重修作业列表。

不过，我可以为您提供知识库中所有明确标注为“重修科目”的作业要求。这些作业主要集中在**电子商务专业**，请您根据自己的重修科目进行查找。

以下是电子商务专业的重修作业列表：

*   **课程名称：电子商务法律概论**
    *   **负责老师：** 邓文雨
    *   **作业要求：** 提交一份电子商务法律法规的案例分析，字数不少于1000字，word电子档命名：姓名学号➕重修科目。
    *   **联系方式：** Q：3209070030
*   **课程名称：电子商务基础项目实战**
    *   **负责老师：** 邓文雨
    *   **作业要求：** 提交一份电子商务基础项目实战的案例分析。字数不少于1000字。word电子档命名：姓名学号➕重修科目。
    *   **联系方式：** Q：3209070030
*   **课程名称：项目实训3：视频制作**
    *   **负责老师：** 孙海婷
    *   **作业要求：** 提交一篇视频剪辑脚本（word文档），题材任选，脚本字数不少于1000字，标注好【重修科目-姓名】提交word文档。
    *   **联系方式：** <EMAIL>
*   **课程名称：新媒体界面设计**
    *   **负责老师：** 覃琴
    *   **作业要求：** 分析淘宝和京东用户界面设计的不同、及其优缺点。不少于1000字。以word提交，word命名：【重修科目-姓名】。
    *   **联系方式：** QQ:1791071916
*   **课程名称：新媒体文案创作与传播**
    *   **负责老师：** 曾彦
    *   **作业要求：** 为自己选择的一个品牌写一篇软文文案，字数不少于500字。文档命名：姓名+学号+重修科目。
    *   **联系方式：** QQ：77809441
*   **课程名称：直播电商实操**
    *   **负责老师：** 覃琴
    *   **作业要求：** 提交一份不少于20分钟的直播脚本，以word提交，word命名：【重修科目-姓名】。
    *   **联系方式：** QQ:1791071916
*   **课程名称：电子商务概论**
    *   **负责老师：** 孙海婷
    *   **作业要求：** 提交一篇电子商务营销相关论文（word文档），题目任选，字数不少于1500字，标注好【重修科目-姓名】提交word文档。
    *   **联系方式：** <EMAIL>
*   **课程名称：管理学基础**
    *   **负责老师：** 曾彦
    *   **作业要求：** 为自己选择的一个品牌，撰写一篇休眠客户促活电子商务营销活动策划方案，字数不少于1500字。文档命名：姓名+学号+重修科目。
    *   **联系方式：** QQ：77809441
*   **课程名称：图形图像处理(PhotoShop)**
    *   **负责老师：** 黄沁玥
    *   **作业要求：** 自选一款日用产品，完成主图与详情页文案策划。要求分点分模块完成，字数不少于1500字。打印提交，作业写上【姓名-学号-班级-重修科目】。
    *   **联系方式：** QQ:54505465
*   **课程名称：项目实训I：海报设计**
    *   **负责老师：** 黄沁玥
    *   **作业要求：** 主题自选（如环保、节日、校园活动等），使用Word完成（插入形状、图片、艺术字等基础功能）。打印提交，作业写上【姓名-学号-班级-重修科目】。内容需包含：主标题、1-2句宣传语、至少2张相关图片、简单的排版设计。
    *   **联系方式：** QQ：54505465
*   **课程名称：网页制作项目实战（H5）**
    *   **负责老师：** 覃琴
    *   **作业要求：** 提交一份静态网页的代码，网页风格不限，以word提交，word命名：【重修科目-姓名】。
    *   **联系方式：** QQ:1791071916
*   **课程名称：电子商务物流管理**
    *   **负责老师：** 曾彦
    *   **作业要求：** 从京东、菜鸟、顺丰等电商物流平台中选择一个，分析其各个物流管理职能的优势、劣势，以及改进建议，字数不少于1500字。文档命名：姓名+学号+重修科目。
    *   **联系方式：** QQ：77809441
*   **课程名称：短视频制作与特效**
    *   **负责老师：** 孙海婷
    *   **作业要求：** 提交一篇视频剪辑脚本（word文档），题材任选，脚本字数不少于1000字，标注好【重修科目-姓名】提交word文档。
    *   **联系方式：** <EMAIL>
*   **课程名称：PS核心技术项目实战**
    *   **负责老师：** 黄沁玥
    *   **作业要求：** 为一个新品牌（如奶茶店、潮牌服饰、美妆品牌等）设计完整的视觉方案。撰写品牌定位分析、LOGO设计技术方案、延展应用设计等方面等方面，字数不少于1500字。打印提交，作业写上【姓名-学号-班级-重修科目】。
    *   **联系方式：** QQ：54505465
*   **课程名称：视频后期效果处理**
    *   **负责老师：** 曾彦
    *   **作业要求：** 提交一篇视频剪辑脚本，并包括视频主题和创意说明，后期效果所使用的技巧和方法。主题任选，字数不少于1500字。文档命名：姓名+学号+重修科目。
    *   **联系方式：** QQ：77809441
*   **课程名称：UI交互设计**
    *   **负责老师：** 覃琴
    *   **作业要求：** 分析淘宝和京东用户界面设计的不同、及其优缺点。不少于1000字。以word提交，word命名：【重修科目-姓名】。
    *   **联系方式：** QQ:1791071916
*   **课程名称：电商网店运营与管理**
    *   **负责老师：** 邓文雨
    *   **作业要求：** 提交一份电商网店运营与管理的案例分析，字数不少于1000字。word文档命名为：姓名学号及重修科目。
    *   **联系方式：** Q：3209070030

请您根据自己需要重修的课程名称，对照上述信息，并及时与负责老师联系确认具体要求和提交方式。', 13, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 11:56:38.456927', '2025-07-28 11:56:38.457023', 55, 26, 'user', '你能绘制一张条形图来设描述每个老师布置的作业数量吗', 13, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 12:20:46.514077', '2025-07-28 12:20:46.514189', 61, 26, 'user', '你能绘制一张条形图来设描述每个老师布置的作业数量吗，用html', 14, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 11:56:49.021067', '2025-07-28 12:09:02.974216', 56, 26, 'assistant', '```json
{
  "type": "bar",
  "data": {
    "labels": ["邓文雨", "孙海婷", "覃琴", "曾彦", "黄沁玥"],
    "datasets": [{
      "label": "作业数量",
      "data": [2, 3, 3, 4, 3],
      "backgroundColor": ["#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0", "#9966FF"]
    }]
  },
  "options": {
    "responsive": true,
    "plugins": {
      "title": {
        "display": true,
        "text": "各老师布置作业数量统计"
      }
    },
    "scales": {
      "y": {
        "beginAtZero": true
      }
    }
  }
}
```', 13, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 12:10:26.746694', '2025-07-28 12:10:26.746788', 57, 26, 'user', '你能绘制一张条形图来设描述每个老师布置的作业数量吗，用html', 13, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 12:10:37.23462', '2025-07-28 12:10:37.234733', 58, 26, 'assistant', '好的，为了用HTML绘制一张条形图来描述每个老师布置的作业数量，我们将使用Chart.js库。你需要将以下代码保存为一个`.html`文件，然后在浏览器中打开它。

这个HTML文件包含了Chart.js的引入、一个用于绘图的`canvas`元素，以及嵌入在`<script>`标签中的JavaScript代码，用于配置和渲染图表。

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>各老师布置作业数量统计</title>
    <!-- 引入Chart.js库的CDN链接 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
            background-color: #f4f4f4;
        }
        .chart-container {
            width: 80%; /* 控制图表的宽度 */
            max-width: 800px; /* 最大宽度 */
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <h1>各老师布置作业数量统计</h1>
    <div class="chart-container">
        <canvas id="assignmentChart"></canvas>
    </div>

    <script>
        // 从知识库中统计出的各老师作业数量
        const labels = ["邓文雨", "孙海婷", "覃琴", "曾彦", "黄沁玥"];
        const dataValues = [3, 3, 4, 4, 3]; // 邓文雨(3), 孙海婷(3), 覃琴(4), 曾彦(4), 黄沁玥(3)

        const ctx = document.getElementById(''assignmentChart'').getContext(''2d'');

        const chartConfig = {
            "type": "bar",
            "data": {
                "labels": labels,
                "datasets": [{
                    "label": "作业数量",
                    "data": dataValues,
                    "backgroundColor": [
                        "#FF6384", // 邓文雨
                        "#36A2EB", // 孙海婷
                        "#FFCE56", // 覃琴
                        "#4BC0C0", // 曾彦
                        "#9966FF"  // 黄沁玥
                    ],
                    "borderColor": [
                        "#FF6384",
                        "#36A2EB",
                        "#FFCE56",
                        "#4BC0C0",
                        "#9966FF"
                    ],
                    "borderWidth": 1
                }]
            },
            "options": {
                "responsive": true,
                "maintainAspectRatio": false, // 允许canvas根据容器大小调整
                "plugins": {
                    "title": {
                        "display": true,
                        "text": "各老师布置作业数量统计",
                        "font": {
                            "size": 18
                        }
                    },
                    "legend": {
                        "display": false // 通常条形图的图例可以省略，因为标签已经在X轴上
                    }
                },
                "scales": {
                    "y": {
                        "beginAtZero": true,
                        "title": {
                            "display": true,
                            "text": "作业数量"
                        },
                        "ticks": {
                            "stepSize": 1 // Y轴刻度步长为1
                        }
                    },
                    "x": {
                        "title": {
                            "display": true,
                            "text": "负责老师"
                        }
                    }
                }
            }
        };

        new Chart(ctx, chartConfig);
    </script>
</body>
</html>
```

**如何使用：**

1.  将上述代码复制粘贴到一个文本编辑器（如记事本、VS Code等）中。
2.  将文件保存为 `teachers_assignments.html` (或任何你喜欢的 `.html` 后缀名)。
3.  用你的网页浏览器（如Chrome, Firefox, Edge等）打开这个HTML文件。

你将看到一个清晰的条形图，展示了', 13, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 12:16:03.241659', '2025-07-28 12:16:03.241739', 59, 26, 'user', '你能绘制一张条形图来设描述每个老师布置的作业数量吗，用html', 13, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 12:21:08.212532', '2025-07-28 12:32:41.512725', 62, 26, 'assistant', '好的，没问题！

我已经根据您提供的知识库内容，统计了每位老师布置的作业数量，并为您生成了相应的条形图。

这是完整的HTML代码，您可以直接复制并保存为`.html`文件，在浏览器中打开即可查看。

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>各老师布置作业数量统计</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            margin: 20px;
            background-color: #f8f9fa;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .chart-container {
            width: 90%;
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="chart-container">
        <canvas id="myChart"></canvas>
    </div>

    <script>
        const ctx = document.getElementById(''myChart'').getContext(''2d'');
        const myChart = new Chart(ctx, {
            type: ''bar'',
            data: {
                labels: ["曾彦", "孙海婷", "覃琴", "黄沁玥", "邓文雨", "胡海霞", "祝绍红", "任曦", "张越航", "汪耀辉", "胥恭丽", "廖斌", "耿莹"],
                datasets: [{
                    label: ''作业数量'',
                    data: [4, 3, 3, 3, 2, 2, 2, 1, 1, 1, 1, 1, 1],
                    backgroundColor: [
                        ''rgba(255, 99, 132, 0.6)'',
                        ''rgba(54, 162, 235, 0.6)'',
                        ''rgba(255, 206, 86, 0.6)'',
                        ''rgba(75, 192, 192, 0.6)'',
                        ''rgba(153, 102, 255, 0.6)'',
                        ''rgba(255, 159, 64, 0.6)'',
                        ''rgba(199, 199, 199, 0.6)'',
                        ''rgba(83, 102, 255, 0.6)'',
                        ''rgba(40, 159, 64, 0.6)'',
                        ''rgba(210, 120, 20, 0.6)'',
                        ''rgba(180, 180, 80, 0.6)'',
                        ''rgba(60, 20, 180, 0.6)'',
                        ''rgba(130, 130, 130, 0.6)''
                    ],
                    borderColor: [
                        ''rgba(255, 99, 132, 1)'',
                        ''rgba(54, 162, 235, 1)'',
                        ''rgba(255, 206, 86, 1)'',
                        ''rgba(75, 192, 192, 1)'',
                        ''rgba(153, 102, 255, 1)'',
                        ''rgba(255, 159, 64, 1)'',
                        ''rgba(199, 199, 199, 1)'',
                        ''rgba(83, 102, 255, 1)'',
                        ''rgba(40, 159, 64, 1)'',
                        ''rgba(210, 120, 20, 1)'',
                        ''rgba(180, 180, 80, 1)'',
                        ''rgba(60, 20, 180, 1)'',
                        ''rgba(130, 130, 130, 1)''
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: ''各老师布置作业数量统计'',
                        font: {
                            size: 20
                        }
                    },
                    legend: {
                        display: false // 单一数据集时可以隐藏图例
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            // 确保Y轴只显示整数
                            stepSize: 1
                        },
                        title: {
                            display: true,
                            text: ''作业数量''
                        }
                    },
                    x: {
                         title: {
                            display: true,
                            text: ''负责老师''
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
```

如果您还有其他需要，比如想了解某位老师具体的作业要求，或者需要其他类型的图表，随时告诉我！', 14, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 12:33:04.803738', '2025-07-28 12:33:04.803849', 63, 26, 'user', '你能绘制一张条形图来设描述每个老师布置的作业数量吗', 14, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 13:19:50.289683', '2025-07-28 13:19:50.289776', 65, 26, 'user', '你能绘制一张条形图来设描述每个老师布置的作业数量吗，用html', 14, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 13:20:25.634131', '2025-07-28 13:20:25.634232', 66, 26, 'assistant', '好的，当然可以！

我已经根据知识库中的信息，统计了每位老师布置的作业数量，并为您生成了一个完整的HTML页面来展示这个条形图。

您只需将以下代码复制并保存为一个 `.html` 文件（例如 `chart.html`），然后用浏览器打开它即可查看。

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>各老师布置作业数量统计</title>
    <!-- 引入Chart.js库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 90vh;
        }
        .chart-container {
            width: 90%;
            max-width: 900px; /* 增加最大宽度以容纳更多标签 */
            margin: 0 auto;
            background: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="chart-container">
        <canvas id="myChart"></canvas>
    </div>

    <script>
        // 获取图表容器的2D上下文
        const ctx = document.getElementById(''myChart'').getContext(''2d'');
        
        // 创建并配置图表
        const myChart = new Chart(ctx, {
            type: ''bar'', // 图表类型：条形图
            data: {
                // X轴标签：所有老师的名字
                labels: ["曾彦", "孙海婷", "覃琴", "黄沁玥", "邓文雨", "胡海霞", "祝绍红", "任曦", "张越航", "汪耀辉", "胥恭丽", "廖斌", "耿莹"],
                datasets: [{
                    label: ''作业数量'',
                    // Y轴数据：根据知识库统计的每位老师的作业数量
                    data: [4, 3, 3, 3, 2, 2, 2, 1, 1, 1, 1, 1, 1],
                    // 条形的背景颜色
                    backgroundColor: [
                        ''rgba(255, 99, 132, 0.6)'',
                        ''rgba(54, 162, 235, 0.6)'',
                        ''rgba(255, 206, 86, 0.6)'',
                        ''rgba(75, 192, 192, 0.6)'',
                        ''rgba(153, 102, 255, 0.6)'',
                        ''rgba(255, 159, 64, 0.6)'',
                        ''rgba(199, 199, 199, 0.6)'',
                        ''rgba(83, 102, 255, 0.6)'',
                        ''rgba(40, 159, 64, 0.6)'',
                        ''rgba(210, 120, 20, 0.6)'',
                        ''rgba(180, 180, 80, 0.6)'',
                        ''rgba(60, 20, 180, 0.6)'',
                        ''rgba(130, 130, 130, 0.6)''
                    ],
                    // 条形的边框颜色
                    borderColor: [
                        ''rgba(255, 99, 132, 1)'',
                        ''rgba(54, 162, 235, 1)'',
                        ''rgba(255, 206, 86, 1)'',
                        ''rgba(75, 192, 192, 1)'',
                        ''rgba(153, 102, 255, 1)'',
                        ''rgba(255, 159, 64, 1)'',
                        ''rgba(199, 199, 199, 1)'',
                        ''rgba(83, 102, 255, 1)'',
                        ''rgba(40, 159, 64, 1)'',
                        ''rgba(210, 120, 20, 1)'',
                        ''rgba(180, 180, 80, 1)'',
                        ''rgba(60, 20, 180, 1)'',
                        ''rgba(130, 130, 130, 1)''
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true, // 图表自适应容器大小
                plugins: {
                    title: {
                        display: true,
                        text: ''各老师布置作业数量统计'',
                        font: {
                            size: 20
                        }
                    },
                    legend: {
                        display: false // 由于只有一个数据集，可以隐藏图例
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true, // Y轴从0开始
                        ticks: {
                            stepSize: 1 // Y轴刻度步长为1，确保显示整数
                        },
                        title: {
                            display: true,
                            text: ''作业数量''
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: ''负责老师''
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
```', 14, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 13:28:53.748517', '2025-07-28 13:28:53.748756', 67, 26, 'user', '换成折线图', 14, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 13:29:25.48941', '2025-07-28 13:29:25.4895', 68, 26, 'assistant', '好的，当然可以！

我已经将图表类型从 `bar` (条形图) 更改为 `line` (折线图)，并调整了颜色和线条样式以适应折线图的展示效果。

这是完整的HTML代码，您可以直接复制并保存为一个 `.html` 文件，然后在浏览器中打开即可查看。

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>各老师布置作业数量统计 (折线图)</title>
    <!-- 引入Chart.js库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 90vh;
        }
        .chart-container {
            width: 90%;
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="chart-container">
        <canvas id="myChart"></canvas>
    </div>

    <script>
        // 获取图表容器的2D上下文
        const ctx = document.getElementById(''myChart'').getContext(''2d'');
        
        // 创建并配置图表
        const myChart = new Chart(ctx, {
            type: ''line'', // 图表类型：折线图
            data: {
                // X轴标签：所有老师的名字
                labels: ["曾彦", "孙海婷", "覃琴", "黄沁玥", "邓文雨", "胡海霞", "祝绍红", "任曦", "张越航", "汪耀辉", "胥恭丽", "廖斌", "耿莹"],
                datasets: [{
                    label: ''作业数量'',
                    // Y轴数据：根据知识库统计的每位老师的作业数量
                    data: [4, 3, 3, 3, 2, 2, 2, 1, 1, 1, 1, 1, 1],
                    // 线的颜色
                    borderColor: ''rgba(54, 162, 235, 1)'',
                    // 线下填充区域的颜色
                    backgroundColor: ''rgba(54, 162, 235, 0.2)'',
                    // 是否填充线下区域
                    fill: true,
                    // 线条的平滑度，0为完全笔直
                    tension: 0.1,
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true, // 图表自适应容器大小
                plugins: {
                    title: {
                        display: true,
                        text: ''各老师布置作业数量统计'',
                        font: {
                            size: 20
                        }
                    },
                    legend: {
                        display: false // 由于只有一个数据集，可以隐藏图例
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true, // Y轴从0开始
                        ticks: {
                            stepSize: 1 // Y轴刻度步长为1，确保显示整数
                        },
                        title: {
                            display: true,
                            text: ''作业数量''
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: ''负责老师''
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
```', 14, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 13:29:45.770203', '2025-07-28 13:29:45.770377', 69, 26, 'user', '换成饼图', 13, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 13:29:58.668329', '2025-07-28 13:29:58.668525', 70, 26, 'assistant', '好的，没问题！

将“各老师布置作业数量”用饼图来展示。请注意，饼图更适合展示各部分占总体的比例，当分类较多时（如本例中有13位老师），饼图的视觉效果可能会比较拥挤，每个切片会比较小，识别度可能不如条形图或折线图清晰。

尽管如此，我仍然为您生成了完整的HTML代码，您可以直接复制并保存为 `.html` 文件，然后在浏览器中打开即可查看。

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>各老师布置作业数量统计 (饼图)</title>
    <!-- 引入Chart.js库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 90vh;
        }
        .chart-container {
            width: 90%;
            max-width: 600px; /* 饼图通常需要较小的最大宽度以保持圆形 */
            margin: 0 auto;
            background: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="chart-container">
        <canvas id="myChart"></canvas>
    </div>

    <script>
        // 获取图表容器的2D上下文
        const ctx = document.getElementById(''myChart'').getContext(''2d'');
        
        // 老师的名字和对应的作业数量
        const labels = ["曾彦", "孙海婷", "覃琴", "黄沁玥", "邓文雨", "胡海霞", "祝绍红", "任曦", "张越航", "汪耀辉", "胥恭丽", "廖斌", "耿莹"];
        const dataValues = [4, 3, 3, 3, 2, 2, 2, 1, 1, 1, 1, 1, 1];

        // 为每个切片定义颜色
        const backgroundColors = [
            ''rgba(255, 99, 132, 0.8)'', // 曾彦
            ''rgba(54, 162, 235, 0.8)'', // 孙海婷
            ''rgba(255, 206, 86, 0.8)'', // 覃琴
            ''rgba(75, 192, 192, 0.8)'', // 黄沁玥
            ''rgba(153, 102, 255, 0.8)'', // 邓文雨
            ''rgba(255, 159, 64, 0.8)'', // 胡海霞
            ''rgba(199, 199, 199, 0.8)'', // 祝绍红
            ''rgba(83, 102, 255, 0.8)'', // 任曦
            ''rgba(40, 159, 64, 0.8)'', // 张越航
            ''rgba(210, 120, 20, 0.8)'', // 汪耀辉
            ''rgba(180, 180, 80, 0.8)'', // 胥恭丽
            ''rgba(60, 20, 180, 0.8)'', // 廖斌
            ''rgba(130, 130, 130, 0.8)''  // 耿莹
        ];
        const borderColors = [
            ''rgba(255, 99, 132, 1)'',
            ''rgba(54, 162, 235, 1)'',
            ''rgba(255, 206, 86, 1)'',
            ''rgba(75, 192, 192, 1)'',
            ''rgba(153, 102, 255, 1)'',
            ''rgba(255, 159, 64, 1)'',
            ''rgba(199, 199, 199, 1)'',
            ''rgba(83, 102, 255, 1)'',
            ''rgba(40, 159, 64, 1)'',
            ''rgba(210, 120, 20, 1)'',
            ''rgba(180, 180, 80, 1)'',
            ''rgba(60, 20, 180, 1)'',
            ''rgba(130, 130, 130, 1)''
        ];

        // 创建并配置图表
        const myChart = new Chart(ctx, {
            type: ''pie'', // 图表类型：饼图
            data: {
                labels: labels, // 饼图的扇区标签
                datasets: [{
                    label: ''作业数量'',
                    data: dataValues, // 饼图的扇区大小数据
                    backgroundColor: backgroundColors, // 扇区的背景颜色
                    borderColor: borderColors, // 扇区的边框颜色
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true, // 图表自适应容器大小
                plugins: {
                    title: {
                        display: true,
                        text: ''各老师布置作业数量占比'', // 饼图标题通常强调“占比”
                        font: {
                            size: 20
                        }
                    },
                    legend: {
                        display: true, // 饼图通常需要图例来区分不同扇区
                        position: ''right'', // 图例位置
                        labels: {
                            font: {
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                // 自定义tooltip显示内容：老师名字，作业数量，以及占比
                                let label = context.label || '''';
                                if (label) {
                                    label += '': '';
                                }
                                if (context.parsed !== null) {
                                    const total = context.dataset.data.reduce((acc, current) => acc + current, 0);
                                    const percentage = ((context.parsed / total) * 100).toFixed(1) + ''%'';
                                    label += context.parsed + '' 项 ('' + percentage + '')'';
                                }
                                return label;
                            }
                        }
                    }
                }
                // 饼图没有x/y轴配置
            }
        });
    </script>
</body>
</html>
```', 13, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 13:30:22.997488', '2025-07-28 13:30:22.997707', 71, 26, 'user', '用echarts', 13, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 13:30:43.241794', '2025-07-28 13:30:43.242088', 72, 26, 'assistant', '好的，没问题！

这次我将使用 **ECharts** 库为您绘制一个饼图，来展示每位老师布置的作业数量。ECharts 是一个功能强大且高度可定制的JavaScript可视化库。

以下是完整的HTML代码，您可以直接复制并保存为 `.html` 文件（例如 `echarts_pie_chart.html`），然后在浏览器中打开即可查看。

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>各老师布置作业数量统计 (ECharts 饼图)</title>
    <!-- 引入 ECharts 库 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 90vh;
            flex-direction: column; /* 让标题和图表垂直排列 */
        }
        .chart-container {
            width: 90%;
            max-width: 800px; /* 调整最大宽度以适应图表和图例 */
            height: 600px; /* 设置一个固定高度，ECharts需要容器有尺寸 */
            margin: 20px auto;
            background: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>各老师布置作业数量占比</h1>
    <div id="main" class="chart-container"></div>

    <script type="text/javascript">
        // 基于准备好的dom，初始化echarts实例
        var myChart = echarts.init(document.getElementById(''main''));

        // 老师的名字和对应的作业数量
        const labels = ["曾彦", "孙海婷", "覃琴", "黄沁玥", "邓文雨", "胡海霞", "祝绍红", "任曦", "张越航", "汪耀辉", "胥恭丽", "廖斌", "耿莹"];
        const dataValues = [4, 3, 3, 3, 2, 2, 2, 1, 1, 1, 1, 1, 1];

        // 准备ECharts饼图所需的数据格式
        const pieData = labels.map((label, index) => ({
            name: label,
            value: dataValues[index]
        }));

        // 指定图表的配置项和数据
        var option = {
            title: {
                // 图表主标题（这里因为H1标签已经有了，所以可以省略或调整）
                // text: ''各老师布置作业数量占比'',
                // left: ''center'',
                // textStyle: {
                //     fontSize: 20
                // }
            },
            tooltip: {
                trigger: ''item'', // 触发类型：数据项
                formatter: ''{a} <br/>{b} : {c} ({d}%)'' // 提示框内容格式：系列名 <br/> 数据名 : 值 (百分比)
            },
            legend: {
                orient: ''vertical'', // 图例垂直排列
                left: ''left'',      // 图例在左侧
                data: labels       // 图例的数据（老师的名字）
            },
            series: [
                {
                    name: ''作业数量'', // 系列名称，在tooltip中显示
                    type: ''pie'',     // 图表类型：饼图
                    radius: ''55%'',   // 饼图的半径
                    center: [''50%'', ''60%''], // 饼图的中心位置 [x, y]，调整以给图例留空间
                    data: pieData,   // 饼图的数据

                    emphasis: { // 鼠标悬停时的样式
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: ''rgba(0, 0, 0, 0.5)''
                        }
                    },
                    label: { // 标签配置
                        show: true,
                        formatter: ''{b}: {c} ({d}%)'', // 显示老师名、作业数量和百分比
                        position: ''outside'', // 标签显示在饼图外部
                        textStyle: {
                            fontSize: 12
                        }
                    },
                    labelLine: { // 标签引导线
                        show: true
                    }
                }
            ]
        };

        // 使用刚指定的配置项和数据显示图表。
        myChart.setOption(option);

        // 监听窗口大小变化，使图表自适应
        window.addEventListener(''resize'', function() {
            myChart.resize();
        });
    </script>
</body>
</html>
```

**如何使用：**

1.  将上述代码复制粘贴到一个文本编辑器（如记事本、VS Code等）中。
2.  将文件保存为 `echarts_pie_chart.html` (或任何你喜欢的 `.html` 后缀名)。
3.  用你的网页浏览器（如Chrome, Firefox, Edge等）打开这个HTML文件。

您将看到一个交互式的饼图，展示了每位老师布置作业数量的占比。当您将鼠标悬停在饼图的任何一个切片上时，会显示该老师具体的作业数量和所占百分比。', 13, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 13:32:00.696359', '2025-07-28 13:32:00.696463', 73, 25, 'user', '数据库重修作业的具体内容是什么', 13, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 13:32:08.382993', '2025-07-28 13:32:08.383116', 74, 25, 'assistant', '根据您提供的知识库内容，"数据库基础应用 重修作业" 的具体内容如下：

---

### 数据库基础应用 重修作业

**完成下列题目，将SQL语句写在对应题目下方（使用蓝色字体）**

**创建数据库hospital，并使用SQL语句创建以下数据表**

---

**题1作答区：**
（此处应是创建 `hospital` 数据库的SQL语句，但题目描述中未给出具体要求，通常是 `CREATE DATABASE hospital;`）

---

**2、创建department(科室)表。department表的数据结构如表2-6所示。**

**department表的数据结构**
（此处应有表2-6的具体结构描述，例如字段名、数据类型、约束等，但知识库中未提供。）

**题2作答区：**
（此处应填写创建 department 表的SQL语句）

---

**3、创建checkitem(检查项目)表。checkitem表的数据结构如表2-7所示。**

**checkitem表的数据结构**
（此处应有表2-7的具体结构描述，例如字段名、数据类型、约束等，但知识库中未提供。）

**题3作答区：**
（此处应填写创建 checkitem 表的SQL语句）

---

**4、创建department_checkitem(科室与检查项目关系)表。Department_checkitem表的数据结构如表2-8所示。**

**department_checkitem表的数据结构**
（此处应有表2-8的具体结构描述，例如字段名、数据类型、约束等，但知识库中未提供。）

**题4作答区：**
（此处应填写创建 department_checkitem 表的SQL语句）

---

**5、创建prescription(处方)表。prescrition表的数据结构如表2-9所示。**

**prescription表的数据结构**
（此处应有表2-9的具体结构描述，例如字段名、数据类型、约束等，但知识库中未提供。）

**题5作答区：**
（此处应填写创建 prescription 表的SQL语句）

---

**6、通常，在软件系统中会使用一个日志表记录用户在系统中的重要操作，作为数据统计分析的依据。现在，在hospital数据库中创建表log。**

**log表数据结构**
（此处应有log表的具体结构描述，例如字段名、数据类型、约束等，但知识库中未提供。）

**题6作答区：**
（此处应填写创建 log 表的SQL语句）

---

**7、将log表改名为action_log**

**题7作答区：**
（此处应填写修改表名的SQL语句）

---

**8、为了让日志表的可读性更强，在hospital数据库中，将action_log表中的time字段名改为actionTime;字段类型原为date，发现存在时间精度无法满足需求的情况，因此，将数据类型修改为datetime。**

**题8作答区：**
（此处应填写修改字段名和字段类型的SQL语句）

---

**9、在hospital数据库中，向action_log表中添加comment（备注）字段**

**题9作答区：**
（此处应填写添加字段的SQL语句）

---

**10、在hospital数据库中，将action_log表中的comment字段删除。**

**题10作答区：**
（此处应填写删除字段的SQL语句）

---

**11、在hospital数据库中，给action_log表添加主键约束，将表中的id字段设置为主键。**

**题11作答区：**
（此处应填写添加主键约束的SQL语句）

---

**12、在hospital数据库中，给prescription表添加外键约束，将prescription表的depID字段与department表的depID字段建立主外键关联。**

**题12作答区：**
（此处应填写添加外键约束的SQL语句）

---

**13、为prescription表添加主键约束：病人编号、开处方的科室编号、检查项目编号、检查时间构成联合主键。**

**题13作答区：**
（此处应填写添加联合主键约束的SQL语句）

---

**总结**：这份重修作业主要围绕 **SQL语句** 的实践操作展开，涵盖了数据库和表的 **创建、修改（改名、改字段、增删字段）、以及添加各种约束（主键、外键、联合主键）** 等核心内容。需要注意的是，作业中多次提到了“数据结构如表X-X所示”，但这些表格的具体结构描述（字段名、数据类型等）在您提供的知识库片段中并未包含。', 13, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 13:32:30.301598', '2025-07-28 13:32:30.301706', 75, 25, 'user', '你能帮我给出这些题的答案吗，用markdown输出', 13, '[28]');
INSERT INTO public.chat_messages VALUES ('2025-07-28 13:32:47.272426', '2025-07-28 13:32:47.272549', 76, 25, 'assistant', '好的，我将根据您提供的作业内容，为您生成每道题目的SQL语句答案。

**重要提示：**
由于您提供的知识库中没有给出 `department`、`checkitem`、`department_checkitem`、`prescription` 和 `log` 表的具体字段结构（如表2-6至2-9），我将根据常见的数据库设计原则和题目要求，**自行假设这些表的字段和数据类型**。请您在实际提交作业时，务必根据您课程中提供的具体表结构进行调整。

以下是使用Markdown格式输出的答案：

---

# 数据库基础应用 重修作业

**完成下列题目，将SQL语句写在对应题目下方（使用蓝色字体）**

**创建数据库hospital，并使用SQL语句创建以下数据表**

---

### 题1作答区：

```sql
CREATE DATABASE IF NOT EXISTS hospital;
USE hospital;
```

---

### 2、创建department(科室)表。department表的数据结构如表2-6所示。

**（假设department表结构：`depID` INT PRIMARY KEY, `depName` VARCHAR(100)）**

### 题2作答区：

```sql
CREATE TABLE department (
    depID INT PRIMARY KEY,
    depName VARCHAR(100) NOT NULL
);
```

---

### 3、创建checkitem(检查项目)表。checkitem表的数据结构如表2-7所示。

**（假设checkitem表结构：`checkItemID` INT PRIMARY KEY, `checkItemName` VARCHAR(100)）**

### 题3作答区：

```sql
CREATE TABLE checkitem (
    checkItemID INT PRIMARY KEY,
    checkItemName VARCHAR(100) NOT NULL
);
```

---

### 4、创建department_checkitem(科室与检查项目关系)表。Department_checkitem表的数据结构如表2-8所示。

**（假设department_checkitem表结构：`depID` INT, `checkItemID` INT，联合主键，并作为外键引用department和checkitem表）**

### 题4作答区：

```sql
CREATE TABLE department_checkitem (
    depID INT,
    checkItemID INT,
    PRIMARY KEY (depID, checkItemID),
    FOREIGN KEY (depID) REFERENCES department(depID),
    FOREIGN KEY (checkItemID) REFERENCES checkitem(checkItemID)
);
```

---

### 5、创建prescription(处方)表。prescrition表的数据结构如表2-9所示。

**（根据题目13，假设prescription表包含 `patientID`, `depID`, `checkItemID`, `prescriptionTime` 作为未来联合主键的组成部分，并添加一个示例字段 `quantity`）**

### 题5作答区：

```sql
CREATE TABLE prescription (
    patientID INT,
    depID INT,
    checkItemID INT,
    prescriptionTime DATETIME,
    quantity INT
    -- 主键和外键将在后续题目中添加
);
```

---

### 6、通常，在软件系统中会使用一个日志表记录用户在系统中的重要操作，作为数据统计分析的依据。现在，在hospital数据库中创建表log。

**（根据题目8和11，假设log表结构：`id` INT, `actionType` VARCHAR(255), `userID` INT, `time` DATE）**

### 题6作答区：

```sql
CREATE TABLE log (
    id INT AUTO_INCREMENT, -- 假设ID自动递增，但主键约束将在题11添加
    actionType VARCHAR(255) NOT NULL,
    userID INT,
    time DATE -- 初始字段类型为DATE，将在题8修改
);
```

---

### 7、将log表改名为action_log

### 题7作答区：

```sql
ALTER TABLE log RENAME TO action_log;
```

---

### 8、为了让日志表的可读性更强，在hospital数据库中，将action_log表中的time字段名改为actionTime;字段类型原为date，发现存在时间精度无法满足需求的情况，因此，将数据类型修改为datetime。

### 题8作答区：

```sql
ALTER TABLE action_log CHANGE COLUMN time actionTime DATETIME;
```

---

### 9、在hospital数据库中，向action_log表中添加comment（备注）字段

### 题9作答区：

```sql
ALTER TABLE action_log ADD COLUMN comment VARCHAR(500);
```

---

### 10、在hospital数据库中，将action_log表中的comment字段删除。

### 题10作答区：

```sql
ALTER TABLE action_log DROP COLUMN comment;
```

---

### 11、在hospital数据库中，给action_log表添加主键约束，将表中的id字段设置为主键。

### 题11作答区：

```sql
ALTER TABLE action_log ADD PRIMARY KEY (id);
```

---

### 12、在hospital数据库中，给prescription表添加外键约束，将prescription表的depID字段与department表的depID字段建立主外键关联。

### 题12作答区：

```sql
ALTER TABLE prescription
ADD CONSTRAINT fk_prescription_depID
FOREIGN KEY (depID) REFERENCES department(depID);
```

---

### 13、为prescription表添加主键约束：病人编号、开处方的科室编号、检查项目编号、检查时间构成联合主键。

### 题13作答区：

```sql
ALTER TABLE prescription
ADD PRIMARY KEY (patientID, depID, checkItemID, prescriptionTime);
```', 13, '[28]');


--
-- Data for Name: chat_sessions; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.chat_sessions VALUES ('2025-07-25 14:48:48.95226', '2025-07-25 14:48:48.952397', 17, 4, '测试聊天会话1');
INSERT INTO public.chat_sessions VALUES ('2025-07-25 14:50:38.591688', '2025-07-25 14:50:38.59177', 18, 4, '测试聊天会话2');
INSERT INTO public.chat_sessions VALUES ('2025-07-25 14:53:13.577769', '2025-07-25 14:53:13.577877', 20, 4, '测试聊天会话4');
INSERT INTO public.chat_sessions VALUES ('2025-07-26 17:08:00.178808', '2025-07-26 17:08:00.17901', 25, 4, '知识库测试');
INSERT INTO public.chat_sessions VALUES ('2025-07-27 06:04:52.669635', '2025-07-27 06:04:52.669734', 26, 4, '新对话 5');
INSERT INTO public.chat_sessions VALUES ('2025-07-27 16:53:45.461629', '2025-07-27 16:53:45.461843', 27, 8, '1213');
INSERT INTO public.chat_sessions VALUES ('2025-07-27 17:22:25.535589', '2025-07-27 17:22:25.535671', 28, 11, 'hhh');
INSERT INTO public.chat_sessions VALUES ('2025-07-27 17:54:09.262123', '2025-07-27 17:54:09.262335', 29, 12, '214');


--
-- Data for Name: document_chunks; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: documents; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.documents VALUES ('2025-07-27 17:43:32.300296', '2025-07-27 17:43:32.300395', 51, 35, 11, '22级重修作业要求 (1).xlsx', 'uploads\348482_1753638212_a4d86c84.xlsx', 'xlsx', 25152, 'completed', NULL);
INSERT INTO public.documents VALUES ('2025-07-27 17:43:32.857895', '2025-07-27 17:43:32.857991', 52, 35, 11, '数据库重修作业 (1).docx', 'uploads\348483_1753638212_da1f636c.docx', 'docx', 20046, 'completed', NULL);
INSERT INTO public.documents VALUES ('2025-07-25 14:05:55.24324', '2025-07-25 14:05:55.243343', 4, 10, 4, 'test_document.txt', 'uploads\162620_dd6f82e3-88a3-4676-88ca-3c1e6a0665bc.txt', 'text/plain', 448, 'failed', NULL);
INSERT INTO public.documents VALUES ('2025-07-27 17:45:11.912284', '2025-07-27 17:45:11.912371', 53, 35, 11, '四川省教育厅关于举办首届教师人工智能应用能力大赛的通知.pdf', 'uploads\348582_1753638311_09401969.pdf', 'pdf', 615093, 'completed', NULL);
INSERT INTO public.documents VALUES ('2025-07-25 14:07:16.582996', '2025-07-25 14:07:16.583106', 5, 11, 4, 'ai_overview.txt', 'uploads\162701_0ad7c968-ccf3-4687-9b0d-dc4c99c1b110.txt', 'text/plain', 1041, 'failed', NULL);
INSERT INTO public.documents VALUES ('2025-07-25 14:09:52.806231', '2025-07-25 14:09:52.80634', 6, 12, 4, 'ai_overview.txt', 'uploads\162857_f52b71be-591c-4f7d-a719-02c35ab7769b.txt', 'text/plain', 1041, 'failed', NULL);
INSERT INTO public.documents VALUES ('2025-07-27 10:23:49.270668', '2025-07-27 10:23:49.27085', 47, 29, 4, '新建 文本文档.txt', 'uploads\322099_1753611829_3a496b90.txt', 'txt', 3837, 'completed', NULL);
INSERT INTO public.documents VALUES ('2025-07-25 17:50:12.547257', '2025-07-25 17:50:12.547368', 43, 28, 4, '22级重修作业要求 (1).xlsx', 'uploads\176077_1753465812_056c90d1.xlsx', 'xlsx', 25152, 'completed', NULL);
INSERT INTO public.documents VALUES ('2025-07-25 17:50:13.035779', '2025-07-25 17:50:13.035879', 44, 28, 4, '数据库重修作业 (1).docx', 'uploads\176078_1753465813_cb7c2744.docx', 'docx', 20046, 'completed', NULL);
INSERT INTO public.documents VALUES ('2025-07-25 17:50:13.199419', '2025-07-25 17:50:13.199598', 45, 28, 4, '基于MVC的Java Web程序开发重修作业.docx', 'uploads\176078_1753465813_e28251e9.docx', 'docx', 101955, 'completed', NULL);
INSERT INTO public.documents VALUES ('2025-07-25 17:50:13.429773', '2025-07-25 17:50:13.429894', 46, 28, 4, '面向对象程序与设计（Java）重修作业.docx', 'uploads\176078_1753465813_7a2dd028.docx', 'docx', 76310, 'completed', NULL);
INSERT INTO public.documents VALUES ('2025-07-27 10:23:49.689837', '2025-07-27 10:23:49.68995', 48, 29, 4, '新建 文本文档 (4).txt', 'uploads\322099_1753611829_8b2a4d8e.txt', 'txt', 62, 'completed', NULL);
INSERT INTO public.documents VALUES ('2025-07-27 10:23:49.792319', '2025-07-27 10:23:49.792512', 49, 29, 4, '新建 文本文档 (3).txt', 'uploads\322100_1753611829_2b9c72f3.txt', 'txt', 41, 'completed', NULL);


--
-- Data for Name: knowledge_bases; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.knowledge_bases VALUES ('2025-07-25 14:05:55.232246', '2025-07-25 14:05:55.232329', 10, 3, '增强功能测试知识库', '用于测试增强功能的知识库');
INSERT INTO public.knowledge_bases VALUES ('2025-07-25 14:07:16.561673', '2025-07-25 14:07:16.561774', 11, 3, '文档处理测试知识库', '测试文档处理功能');
INSERT INTO public.knowledge_bases VALUES ('2025-07-25 14:09:52.790631', '2025-07-25 14:09:52.790767', 12, 3, '文档处理测试知识库', '测试文档处理功能');
INSERT INTO public.knowledge_bases VALUES ('2025-07-25 17:49:48.916592', '2025-07-25 17:49:48.916705', 28, 4, '1234235', '24');
INSERT INTO public.knowledge_bases VALUES ('2025-07-27 10:23:38.163479', '2025-07-27 10:23:38.16358', 29, 4, 'hhh', '');
INSERT INTO public.knowledge_bases VALUES ('2025-07-27 11:14:00.591189', '2025-07-27 11:14:00.591415', 30, 4, '12', '');
INSERT INTO public.knowledge_bases VALUES ('2025-07-27 11:14:04.677494', '2025-07-27 11:14:04.67758', 31, 4, '22', '');
INSERT INTO public.knowledge_bases VALUES ('2025-07-27 11:14:08.484525', '2025-07-27 11:14:08.484825', 32, 4, '33', '');
INSERT INTO public.knowledge_bases VALUES ('2025-07-27 11:21:49.926643', '2025-07-27 11:21:49.926879', 33, 4, '3435', '');
INSERT INTO public.knowledge_bases VALUES ('2025-07-27 11:21:56.924805', '2025-07-27 11:21:56.925032', 34, 4, '6637', '');
INSERT INTO public.knowledge_bases VALUES ('2025-07-27 17:43:24.083557', '2025-07-27 17:43:24.083702', 35, 11, '332', '');


--
-- Data for Name: operation_logs; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.operation_logs VALUES ('2025-07-27 10:36:18.410447', '2025-07-27 10:36:18.41055', 1, 3, 'create_user', 'user', 5, '{"username": "testuser_1753612578", "email": "<EMAIL>", "is_admin": false}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 10:36:18.424558', '2025-07-27 10:36:18.424636', 2, 3, 'update_user', 'user', 5, '{"old_values": {"username": "testuser_1753612578", "email": "<EMAIL>", "display_name": "\u6d4b\u8bd5\u7528\u6237", "is_admin": false, "status": "active"}, "new_values": {"username": "testuser_1753612578", "email": "<EMAIL>", "display_name": "\u66f4\u65b0\u540e\u7684\u6d4b\u8bd5\u7528\u6237", "is_admin": false, "status": "active"}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 10:36:18.44696', '2025-07-27 10:36:18.447042', 3, 3, 'update_user_quota', 'user_quota', 5, '{"old_values": {"max_kbs": 5, "max_docs_per_kb": 100, "max_storage_mb": 1024}, "new_values": {"max_kbs": 10, "max_docs_per_kb": 200, "max_storage_mb": 2048}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 10:36:20.506702', '2025-07-27 10:36:20.506798', 4, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "test_setting", "old_value": null, "new_value": "test_value_123"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 10:36:20.525839', '2025-07-27 10:36:20.525917', 5, 3, 'delete_user', 'user', 5, '{"username": "testuser_1753612578", "email": "<EMAIL>", "is_admin": false}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 10:36:39.502451', '2025-07-27 10:36:39.50254', 6, 3, 'create_user', 'user', 6, '{"username": "testuser_1753612599", "email": "<EMAIL>", "is_admin": false}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 10:36:39.517718', '2025-07-27 10:36:39.517819', 7, 3, 'update_user', 'user', 6, '{"old_values": {"username": "testuser_1753612599", "email": "<EMAIL>", "display_name": "\u6d4b\u8bd5\u7528\u6237", "is_admin": false, "status": "active"}, "new_values": {"username": "testuser_1753612599", "email": "<EMAIL>", "display_name": "\u66f4\u65b0\u540e\u7684\u6d4b\u8bd5\u7528\u6237", "is_admin": false, "status": "active"}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 10:36:39.535429', '2025-07-27 10:36:39.535531', 8, 3, 'update_user_quota', 'user_quota', 6, '{"old_values": {"max_kbs": 5, "max_docs_per_kb": 100, "max_storage_mb": 1024}, "new_values": {"max_kbs": 10, "max_docs_per_kb": 200, "max_storage_mb": 2048}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 10:36:41.58198', '2025-07-27 10:36:41.582085', 9, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "test_setting", "old_value": "test_value_123", "new_value": "test_value_123"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 10:36:41.596573', '2025-07-27 10:36:41.596679', 10, 3, 'delete_user', 'user', 6, '{"username": "testuser_1753612599", "email": "<EMAIL>", "is_admin": false}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 10:39:44.088432', '2025-07-27 10:39:44.088545', 11, 3, 'create_user', 'user', 7, '{"username": "testuser_1753612783", "email": "<EMAIL>", "is_admin": false}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 10:39:44.101486', '2025-07-27 10:39:44.101565', 12, 3, 'update_user', 'user', 7, '{"old_values": {"username": "testuser_1753612783", "email": "<EMAIL>", "display_name": "\u6d4b\u8bd5\u7528\u6237", "is_admin": false, "status": "active"}, "new_values": {"username": "testuser_1753612783", "email": "<EMAIL>", "display_name": "\u66f4\u65b0\u540e\u7684\u6d4b\u8bd5\u7528\u6237", "is_admin": false, "status": "active"}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 10:39:44.124889', '2025-07-27 10:39:44.124992', 13, 3, 'update_user_quota', 'user_quota', 7, '{"old_values": {"max_kbs": 5, "max_docs_per_kb": 100, "max_storage_mb": 1024}, "new_values": {"max_kbs": 10, "max_docs_per_kb": 200, "max_storage_mb": 2048}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 10:39:44.155034', '2025-07-27 10:39:44.155118', 14, 3, 'update_ai_model', 'ai_model', 1, '{"model_name": "Qwen/Qwen2.5-7B-Instruct", "old_values": {"display_name": "Qwen2.5-7B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8192, "supports_streaming": true, "cost_per_1k_tokens": 0.0007}, "new_values": {"display_name": "Qwen2.5-7B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8192, "supports_streaming": true, "cost_per_1k_tokens": 0.0007}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 10:39:44.179998', '2025-07-27 10:39:44.180097', 15, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "test_setting", "old_value": "test_value_123", "new_value": "test_value_123"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 10:39:44.198938', '2025-07-27 10:39:44.199025', 16, 3, 'delete_user', 'user', 7, '{"username": "testuser_1753612783", "email": "<EMAIL>", "is_admin": false}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 10:54:19.173313', '2025-07-27 10:54:19.173469', 17, 3, 'update_ai_model', 'ai_model', 1, '{"model_name": "Qwen/Qwen2.5-7B-Instruct", "old_values": {"display_name": "Qwen2.5-7B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8192, "supports_streaming": true, "cost_per_1k_tokens": 0.0007}, "new_values": {"display_name": "Qwen2.5-7B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8195, "supports_streaming": true, "cost_per_1k_tokens": 0.0007}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 10:54:32.266638', '2025-07-27 10:54:32.26679', 18, 3, 'update_ai_model', 'ai_model', 2, '{"model_name": "Qwen/Qwen2.5-14B-Instruct", "old_values": {"display_name": "Qwen2.5-14B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8192, "supports_streaming": true, "cost_per_1k_tokens": 0.0014}, "new_values": {"display_name": "Qwen2.5-14B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8199, "supports_streaming": true, "cost_per_1k_tokens": 0.0014}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 11:12:06.683441', '2025-07-27 11:12:06.683532', 19, 3, 'update_user_quota', 'user_quota', 4, '{"old_values": {"max_kbs": 5, "max_docs_per_kb": 100, "max_storage_mb": 1024}, "new_values": {"max_kbs": 5, "max_docs_per_kb": 100, "max_storage_mb": 1024}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 11:12:12.450257', '2025-07-27 11:12:12.450481', 20, 3, 'update_user_quota', 'user_quota', 4, '{"old_values": {"max_kbs": 5, "max_docs_per_kb": 100, "max_storage_mb": 1024}, "new_values": {"max_kbs": 5, "max_docs_per_kb": 101, "max_storage_mb": 1024}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 11:16:26.007568', '2025-07-27 11:16:26.007673', 21, 3, 'update_user', 'user', 4, '{"old_values": {"username": "testuser", "email": "<EMAIL>", "display_name": "\u6d4b\u8bd5\u7528\u6237", "is_admin": false, "status": "active"}, "new_values": {"username": "testuser", "email": "<EMAIL>", "display_name": "testuser", "is_admin": false, "status": "active"}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 11:16:37.353657', '2025-07-27 11:16:37.353979', 22, 3, 'update_user_status', 'user', 4, '{"old_status": "active", "new_status": "disabled"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 11:16:41.73667', '2025-07-27 11:16:41.736954', 23, 3, 'update_user_status', 'user', 4, '{"old_status": "disabled", "new_status": "active"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 11:20:49.360185', '2025-07-27 11:20:49.360409', 24, 3, 'update_user_quota', 'user_quota', 4, '{"old_values": {"max_kbs": 5, "max_docs_per_kb": 101, "max_storage_mb": 1024}, "new_values": {"max_kbs": 7, "max_docs_per_kb": 101, "max_storage_mb": 1024}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 11:45:42.038543', '2025-07-27 11:45:42.038721', 25, 3, 'update_user_quota', 'user_quota', 4, '{"old_values": {"max_kbs": 7, "max_docs_per_kb": 101, "max_storage_mb": 1024}, "new_values": {"max_kbs": 20, "max_docs_per_kb": 101, "max_storage_mb": 1024}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 15:28:38.634942', '2025-07-27 15:28:38.635128', 26, 3, 'update_ai_model', 'ai_model', 3, '{"model_name": "Qwen/Qwen2.5-32B-Instruct", "old_values": {"display_name": "Qwen2.5-32B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8192, "supports_streaming": true, "cost_per_1k_tokens": 0.0024}, "new_values": {"display_name": "Qwen2.5-32B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8194, "supports_streaming": true, "cost_per_1k_tokens": 0.0024}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:07:15.278349', '2025-07-27 16:07:15.278502', 60, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "default_storage_quota_gb", "old_value": "1", "new_value": "1"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:07:15.294153', '2025-07-27 16:07:15.294284', 61, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "allow_user_registration", "old_value": "true", "new_value": "false"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 15:29:38.341209', '2025-07-27 15:29:38.341422', 27, 3, 'update_ai_model', 'ai_model', 4, '{"model_name": "Qwen/Qwen2.5-72B-Instruct", "old_values": {"display_name": "Qwen2.5-72B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8192, "supports_streaming": true, "cost_per_1k_tokens": 0.0056}, "new_values": {"display_name": "Qwen2.5-72B-Instruct", "is_active": false, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8192, "supports_streaming": true, "cost_per_1k_tokens": 0.0056}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 15:36:17.783477', '2025-07-27 15:36:17.783567', 28, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "test_setting", "old_value": "test_value_123", "new_value": "test_value_123"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 15:37:06.64282', '2025-07-27 15:37:06.64304', 29, 3, 'update_ai_model', 'ai_model', 4, '{"model_name": "Qwen/Qwen2.5-72B-Instruct", "old_values": {"display_name": "Qwen2.5-72B-Instruct", "is_active": false, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8192, "supports_streaming": true, "cost_per_1k_tokens": 0.0056}, "new_values": {"display_name": "Qwen2.5-72B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8192, "supports_streaming": true, "cost_per_1k_tokens": 0.0056}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 15:53:04.050057', '2025-07-27 15:53:04.050175', 30, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "test_setting", "old_value": "test_value_123", "new_value": "test_value_123"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 15:53:55.19441', '2025-07-27 15:53:55.194482', 31, 3, 'update_ai_model', 'ai_model', 6, '{"model_name": "meta-llama/Meta-Llama-3.1-8B-Instruct", "old_values": {"display_name": "Llama-3.1-8B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8192, "supports_streaming": true, "cost_per_1k_tokens": 0.0007}, "new_values": {"display_name": "Llama-3.1-8B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8000, "supports_streaming": true, "cost_per_1k_tokens": 0.01}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 15:53:59.288069', '2025-07-27 15:53:59.288161', 32, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "test_setting", "old_value": "test_value_123", "new_value": "test_value_123"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 15:54:39.878976', '2025-07-27 15:54:39.879072', 33, 3, 'create_ai_provider', 'ai_provider', 7, '{"name": "test_provider_1753631677", "display_name": "\u6d4b\u8bd5\u4f9b\u5e94\u5546_1753631677"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 15:54:41.972883', '2025-07-27 15:54:41.972962', 34, 3, 'delete_ai_provider', 'ai_provider', 7, '{"name": "test_provider_1753631677", "display_name": "\u6d4b\u8bd5\u4f9b\u5e94\u5546_1753631677"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 15:54:46.067805', '2025-07-27 15:54:46.068061', 35, 3, 'update_ai_model', 'ai_model', 7, '{"model_name": "gpt-4", "old_values": {"display_name": "GPT-4", "is_active": true, "system_api_key": null, "allow_system_key_use": true, "max_tokens": 8192, "supports_streaming": true, "cost_per_1k_tokens": 0.03}, "new_values": {"display_name": "GPT-4", "is_active": true, "system_api_key": null, "allow_system_key_use": true, "max_tokens": 8000, "supports_streaming": true, "cost_per_1k_tokens": 0.01}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 15:54:50.158406', '2025-07-27 15:54:50.158483', 36, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "test_setting", "old_value": "test_value_123", "new_value": "test_value_123"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:03:49.745422', '2025-07-27 16:03:49.745504', 37, 3, 'create_ai_provider', 'ai_provider', 8, '{"name": "test_provider_1753632227", "display_name": "\u6d4b\u8bd5\u4f9b\u5e94\u5546_1753632227"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:03:51.780299', '2025-07-27 16:03:51.780374', 38, 3, 'delete_ai_provider', 'ai_provider', 8, '{"name": "test_provider_1753632227", "display_name": "\u6d4b\u8bd5\u4f9b\u5e94\u5546_1753632227"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:03:55.845195', '2025-07-27 16:03:55.845284', 39, 3, 'update_ai_model', 'ai_model', 8, '{"model_name": "gpt-4-turbo", "old_values": {"display_name": "GPT-4 Turbo", "is_active": true, "system_api_key": null, "allow_system_key_use": true, "max_tokens": 128000, "supports_streaming": true, "cost_per_1k_tokens": 0.01}, "new_values": {"display_name": "GPT-4 Turbo", "is_active": true, "system_api_key": null, "allow_system_key_use": true, "max_tokens": 8000, "supports_streaming": true, "cost_per_1k_tokens": 0.01}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:03:59.914851', '2025-07-27 16:03:59.914959', 40, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "test_setting", "old_value": "test_value_123", "new_value": "test_value_123"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:04:06.009653', '2025-07-27 16:04:06.009745', 41, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "max_file_size_mb", "old_value": null, "new_value": "100"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:04:08.042293', '2025-07-27 16:04:08.042382', 42, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "allow_user_registration", "old_value": null, "new_value": "true"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:04:10.090982', '2025-07-27 16:04:10.09108', 43, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "default_storage_quota_gb", "old_value": null, "new_value": "20"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:04:16.224438', '2025-07-27 16:04:16.224519', 44, 3, 'update_ai_model', 'ai_model', 9, '{"model_name": "gpt-3.5-turbo", "old_values": {"display_name": "GPT-3.5 Turbo", "is_active": true, "system_api_key": null, "allow_system_key_use": true, "max_tokens": 4096, "supports_streaming": true, "cost_per_1k_tokens": 0.002}, "new_values": {"display_name": "GPT-3.5 Turbo", "is_active": true, "system_api_key": null, "allow_system_key_use": true, "max_tokens": 16000, "supports_streaming": true, "cost_per_1k_tokens": 0.02}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:05:22.16287', '2025-07-27 16:05:22.163052', 45, 3, 'delete_ai_model', 'ai_model', 12, '{"model_name": "test-model-v1", "display_name": "\u6d4b\u8bd5\u6a21\u578b V1 (\u5df2\u66f4\u65b0)", "provider_id": 6}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:06:11.52574', '2025-07-27 16:06:11.52583', 46, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "default_model_id", "old_value": null, "new_value": "10"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:06:11.543792', '2025-07-27 16:06:11.543887', 47, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "max_file_size_mb", "old_value": "100", "new_value": "100"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:06:11.562307', '2025-07-27 16:06:11.562577', 48, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "default_storage_quota_gb", "old_value": "20", "new_value": "1"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:06:11.58394', '2025-07-27 16:06:11.584146', 49, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "allow_user_registration", "old_value": "true", "new_value": "true"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:06:11.60607', '2025-07-27 16:06:11.60618', 50, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "document_process_timeout", "old_value": null, "new_value": "300"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:06:11.624688', '2025-07-27 16:06:11.624795', 51, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "chat_retention_days", "old_value": null, "new_value": "0"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:06:26.811225', '2025-07-27 16:06:26.811331', 52, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "default_model_id", "old_value": "10", "new_value": "10"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:06:26.833198', '2025-07-27 16:06:26.833316', 53, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "max_file_size_mb", "old_value": "100", "new_value": "100"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:06:26.853726', '2025-07-27 16:06:26.853982', 54, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "default_storage_quota_gb", "old_value": "1", "new_value": "1"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:06:26.866339', '2025-07-27 16:06:26.866442', 55, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "allow_user_registration", "old_value": "true", "new_value": "true"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:06:26.876542', '2025-07-27 16:06:26.876651', 56, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "document_process_timeout", "old_value": "300", "new_value": "300"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:06:26.887128', '2025-07-27 16:06:26.887244', 57, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "chat_retention_days", "old_value": "0", "new_value": "90"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:07:15.244815', '2025-07-27 16:07:15.24494', 58, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "default_model_id", "old_value": "10", "new_value": "10"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:07:15.262295', '2025-07-27 16:07:15.262402', 59, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "max_file_size_mb", "old_value": "100", "new_value": "100"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:07:15.304824', '2025-07-27 16:07:15.304937', 62, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "document_process_timeout", "old_value": "300", "new_value": "300"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:07:15.315893', '2025-07-27 16:07:15.316005', 63, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "chat_retention_days", "old_value": "90", "new_value": "90"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:07:35.63585', '2025-07-27 16:07:35.636063', 64, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "default_model_id", "old_value": "10", "new_value": "10"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:07:35.665159', '2025-07-27 16:07:35.665297', 65, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "max_file_size_mb", "old_value": "100", "new_value": "100"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:07:35.685943', '2025-07-27 16:07:35.686111', 66, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "default_storage_quota_gb", "old_value": "1", "new_value": "1"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:07:35.701457', '2025-07-27 16:07:35.701572', 67, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "allow_user_registration", "old_value": "false", "new_value": "true"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:07:35.712607', '2025-07-27 16:07:35.712713', 68, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "document_process_timeout", "old_value": "300", "new_value": "300"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:07:35.729845', '2025-07-27 16:07:35.730157', 69, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "chat_retention_days", "old_value": "90", "new_value": "0"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:13:03.965843', '2025-07-27 16:13:03.965941', 70, 3, 'create_ai_provider', 'ai_provider', 9, '{"name": "test_provider_1753632781", "display_name": "\u6d4b\u8bd5\u4f9b\u5e94\u5546_1753632781"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:13:06.014239', '2025-07-27 16:13:06.01432', 71, 3, 'delete_ai_provider', 'ai_provider', 9, '{"name": "test_provider_1753632781", "display_name": "\u6d4b\u8bd5\u4f9b\u5e94\u5546_1753632781"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:17:28.769802', '2025-07-27 16:17:28.76991', 72, 3, 'delete_ai_provider', 'ai_provider', 6, '{"name": "test_provider", "display_name": "\u6d4b\u8bd5\u4f9b\u5e94\u5546"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:18:52.183537', '2025-07-27 16:18:52.18364', 73, 3, 'create_ai_provider', 'ai_provider', 10, '{"name": "test_provider_1753633130", "display_name": "\u6d4b\u8bd5\u4f9b\u5e94\u5546_1753633130"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:18:54.237131', '2025-07-27 16:18:54.237216', 74, 3, 'delete_ai_provider', 'ai_provider', 10, '{"name": "test_provider_1753633130", "display_name": "\u6d4b\u8bd5\u4f9b\u5e94\u5546_1753633130"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:19:38.744489', '2025-07-27 16:19:38.744722', 75, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "default_model_id", "old_value": "10", "new_value": "1"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:19:38.75808', '2025-07-27 16:19:38.758153', 76, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "max_file_size_mb", "old_value": "100", "new_value": "100"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:19:38.771308', '2025-07-27 16:19:38.77141', 77, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "default_storage_quota_gb", "old_value": "1", "new_value": "1"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:19:38.783718', '2025-07-27 16:19:38.783821', 78, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "allow_user_registration", "old_value": "true", "new_value": "true"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:19:38.797293', '2025-07-27 16:19:38.797392', 79, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "document_process_timeout", "old_value": "300", "new_value": "300"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:19:38.809465', '2025-07-27 16:19:38.809564', 80, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "chat_retention_days", "old_value": "0", "new_value": "0"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:24:40.983928', '2025-07-27 16:24:40.984042', 81, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "chat_retention_days", "old_value": "0", "new_value": "0"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:25:09.7971', '2025-07-27 16:25:09.797359', 82, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "default_model_id", "old_value": "1", "new_value": "1"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:25:09.818216', '2025-07-27 16:25:09.818371', 83, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "max_file_size_mb", "old_value": "100", "new_value": "100"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:25:09.839692', '2025-07-27 16:25:09.839826', 84, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "default_storage_quota_gb", "old_value": "1", "new_value": "1"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:25:09.857997', '2025-07-27 16:25:09.858119', 85, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "allow_user_registration", "old_value": "true", "new_value": "true"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:25:09.872963', '2025-07-27 16:25:09.873077', 86, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "document_process_timeout", "old_value": "300", "new_value": "300"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:25:09.886601', '2025-07-27 16:25:09.886715', 87, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "chat_retention_days", "old_value": "0", "new_value": "90"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:25:22.899805', '2025-07-27 16:25:22.900001', 88, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "default_model_id", "old_value": "1", "new_value": "11"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:25:22.919948', '2025-07-27 16:25:22.920094', 89, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "max_file_size_mb", "old_value": "100", "new_value": "100"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:25:22.936039', '2025-07-27 16:25:22.93618', 90, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "default_storage_quota_gb", "old_value": "1", "new_value": "1"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:25:22.948163', '2025-07-27 16:25:22.948276', 91, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "allow_user_registration", "old_value": "true", "new_value": "true"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:25:22.958209', '2025-07-27 16:25:22.958322', 92, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "document_process_timeout", "old_value": "300", "new_value": "300"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:25:22.968309', '2025-07-27 16:25:22.968417', 93, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "chat_retention_days", "old_value": "90", "new_value": "90"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:25:25.462355', '2025-07-27 16:25:25.462532', 94, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "default_model_id", "old_value": "11", "new_value": "11"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:25:25.486107', '2025-07-27 16:25:25.486248', 95, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "max_file_size_mb", "old_value": "100", "new_value": "100"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:25:25.508099', '2025-07-27 16:25:25.508353', 96, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "default_storage_quota_gb", "old_value": "1", "new_value": "2"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:25:25.525964', '2025-07-27 16:25:25.526237', 97, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "allow_user_registration", "old_value": "true", "new_value": "true"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:25:25.542292', '2025-07-27 16:25:25.542454', 98, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "document_process_timeout", "old_value": "300", "new_value": "300"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:25:25.558865', '2025-07-27 16:25:25.559007', 99, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "chat_retention_days", "old_value": "90", "new_value": "90"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:26:59.254297', '2025-07-27 16:26:59.254445', 100, 3, 'update_ai_model', 'ai_model', 10, '{"model_name": "deepseek-chat", "old_values": {"display_name": "DeepSeek Chat", "is_active": true, "system_api_key": null, "allow_system_key_use": true, "max_tokens": 4096, "supports_streaming": true, "cost_per_1k_tokens": 0.0014}, "new_values": {"display_name": "DeepSeek Chat", "is_active": true, "system_api_key": null, "allow_system_key_use": true, "max_tokens": 4096, "supports_streaming": true, "cost_per_1k_tokens": 0.0014}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:27:21.148889', '2025-07-27 16:27:21.148991', 101, 3, 'update_system_setting', 'system_setting', NULL, '{"key": "chat_retention_days", "old_value": "90", "new_value": "0"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:28:45.756129', '2025-07-27 16:28:45.756273', 102, 3, 'update_ai_model', 'ai_model', 10, '{"model_name": "deepseek-chat", "old_values": {"display_name": "DeepSeek Chat", "is_active": true, "system_api_key": null, "allow_system_key_use": true, "max_tokens": 4096, "supports_streaming": true, "cost_per_1k_tokens": 0.0014}, "new_values": {"display_name": "DeepSeek Chat", "is_active": true, "system_api_key": null, "allow_system_key_use": true, "max_tokens": 4096, "supports_streaming": true, "cost_per_1k_tokens": 0.0014}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:29:05.430579', '2025-07-27 16:29:05.430694', 103, 3, 'update_ai_model', 'ai_model', 5, '{"model_name": "deepseek-ai/DeepSeek-V2.5", "old_values": {"display_name": "DeepSeek-V2.5", "is_active": false, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8192, "supports_streaming": true, "cost_per_1k_tokens": 0.0014}, "new_values": {"display_name": "DeepSeek-V2.5", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8192, "supports_streaming": true, "cost_per_1k_tokens": 0.0014}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:30:46.406323', '2025-07-27 16:30:46.406407', 104, 3, 'update_ai_model', 'ai_model', 10, '{"model_name": "deepseek-chat", "old_values": {"display_name": "DeepSeek Chat", "is_active": true, "system_api_key": null, "allow_system_key_use": true, "max_tokens": 4096, "supports_streaming": true, "cost_per_1k_tokens": 0.0014}, "new_values": {"display_name": "DeepSeek Chat", "is_active": true, "system_api_key": null, "allow_system_key_use": true, "max_tokens": 4096, "supports_streaming": true, "cost_per_1k_tokens": 0.0034}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:30:57.867853', '2025-07-27 16:30:57.867935', 105, 3, 'update_ai_model', 'ai_model', 11, '{"model_name": "deepseek-coder", "old_values": {"display_name": "DeepSeek Coder", "is_active": true, "system_api_key": null, "allow_system_key_use": true, "max_tokens": 4096, "supports_streaming": true, "cost_per_1k_tokens": 0.0014}, "new_values": {"display_name": "DeepSeek Coder", "is_active": true, "system_api_key": null, "allow_system_key_use": false, "max_tokens": 4096, "supports_streaming": true, "cost_per_1k_tokens": 0.0014}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:31:03.270414', '2025-07-27 16:31:03.270524', 106, 3, 'update_ai_model', 'ai_model', 1, '{"model_name": "Qwen/Qwen2.5-7B-Instruct", "old_values": {"display_name": "Qwen2.5-7B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8195, "supports_streaming": true, "cost_per_1k_tokens": 0.0007}, "new_values": {"display_name": "Qwen2.5-7B-Instruct", "is_active": false, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8195, "supports_streaming": true, "cost_per_1k_tokens": 0.0007}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:31:19.501335', '2025-07-27 16:31:19.501422', 107, 3, 'update_ai_model', 'ai_model', 1, '{"model_name": "Qwen/Qwen2.5-7B-Instruct", "old_values": {"display_name": "Qwen2.5-7B-Instruct", "is_active": false, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8195, "supports_streaming": true, "cost_per_1k_tokens": 0.0007}, "new_values": {"display_name": "Qwen2.5-7B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8195, "supports_streaming": true, "cost_per_1k_tokens": 0.0007}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:31:28.388856', '2025-07-27 16:31:28.388937', 108, 3, 'update_ai_model', 'ai_model', 7, '{"model_name": "gpt-4", "old_values": {"display_name": "GPT-4", "is_active": true, "system_api_key": null, "allow_system_key_use": true, "max_tokens": 8000, "supports_streaming": true, "cost_per_1k_tokens": 0.01}, "new_values": {"display_name": "GPT-4", "is_active": false, "system_api_key": null, "allow_system_key_use": true, "max_tokens": 8000, "supports_streaming": true, "cost_per_1k_tokens": 0.01}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:31:33.96575', '2025-07-27 16:31:33.965839', 109, 3, 'update_ai_model', 'ai_model', 7, '{"model_name": "gpt-4", "old_values": {"display_name": "GPT-4", "is_active": false, "system_api_key": null, "allow_system_key_use": true, "max_tokens": 8000, "supports_streaming": true, "cost_per_1k_tokens": 0.01}, "new_values": {"display_name": "GPT-4", "is_active": false, "system_api_key": null, "allow_system_key_use": false, "max_tokens": 8000, "supports_streaming": true, "cost_per_1k_tokens": 0.01}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:31:38.987891', '2025-07-27 16:31:38.987976', 110, 3, 'update_ai_model', 'ai_model', 7, '{"model_name": "gpt-4", "old_values": {"display_name": "GPT-4", "is_active": false, "system_api_key": null, "allow_system_key_use": false, "max_tokens": 8000, "supports_streaming": true, "cost_per_1k_tokens": 0.01}, "new_values": {"display_name": "GPT-4", "is_active": false, "system_api_key": null, "allow_system_key_use": false, "max_tokens": 8003, "supports_streaming": true, "cost_per_1k_tokens": 0.01}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:31:42.71031', '2025-07-27 16:31:42.710417', 111, 3, 'update_ai_model', 'ai_model', 7, '{"model_name": "gpt-4", "old_values": {"display_name": "GPT-4", "is_active": false, "system_api_key": null, "allow_system_key_use": false, "max_tokens": 8003, "supports_streaming": true, "cost_per_1k_tokens": 0.01}, "new_values": {"display_name": "GPT-4", "is_active": false, "system_api_key": null, "allow_system_key_use": false, "max_tokens": 8003, "supports_streaming": true, "cost_per_1k_tokens": 0.012}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:31:46.203221', '2025-07-27 16:31:46.203317', 112, 3, 'update_ai_model', 'ai_model', 7, '{"model_name": "gpt-4", "old_values": {"display_name": "GPT-4", "is_active": false, "system_api_key": null, "allow_system_key_use": false, "max_tokens": 8003, "supports_streaming": true, "cost_per_1k_tokens": 0.012}, "new_values": {"display_name": "GPT-4", "is_active": false, "system_api_key": null, "allow_system_key_use": false, "max_tokens": 8003, "supports_streaming": true, "cost_per_1k_tokens": 0.014}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:32:14.975164', '2025-07-27 16:32:14.975421', 113, 3, 'update_ai_model', 'ai_model', 7, '{"model_name": "gpt-4", "old_values": {"display_name": "GPT-4", "is_active": false, "system_api_key": null, "allow_system_key_use": false, "max_tokens": 8003, "supports_streaming": true, "cost_per_1k_tokens": 0.014}, "new_values": {"display_name": "GPT-4", "is_active": false, "system_api_key": null, "allow_system_key_use": false, "max_tokens": 8003, "supports_streaming": true, "cost_per_1k_tokens": 0.016}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:32:49.66823', '2025-07-27 16:32:49.668333', 114, 3, 'update_ai_model', 'ai_model', 7, '{"model_name": "gpt-4", "old_values": {"display_name": "GPT-4", "is_active": false, "system_api_key": null, "allow_system_key_use": false, "max_tokens": 8003, "supports_streaming": true, "cost_per_1k_tokens": 0.016}, "new_values": {"display_name": "GPT-4", "is_active": true, "system_api_key": null, "allow_system_key_use": true, "max_tokens": 8003, "supports_streaming": true, "cost_per_1k_tokens": 0.016}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:32:56.090256', '2025-07-27 16:32:56.090361', 115, 3, 'update_ai_model', 'ai_model', 11, '{"model_name": "deepseek-coder", "old_values": {"display_name": "DeepSeek Coder", "is_active": true, "system_api_key": null, "allow_system_key_use": false, "max_tokens": 4096, "supports_streaming": true, "cost_per_1k_tokens": 0.0014}, "new_values": {"display_name": "DeepSeek Coder", "is_active": true, "system_api_key": null, "allow_system_key_use": true, "max_tokens": 4096, "supports_streaming": true, "cost_per_1k_tokens": 0.0014}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:36:16.368543', '2025-07-27 16:36:16.368623', 116, 3, 'update_ai_model', 'ai_model', 2, '{"model_name": "Qwen/Qwen2.5-14B-Instruct", "old_values": {"display_name": "Qwen2.5-14B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8199, "supports_streaming": true, "cost_per_1k_tokens": 0.0014}, "new_values": {"display_name": "Qwen2.5-14B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8199, "supports_streaming": true, "cost_per_1k_tokens": 0.0014}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:36:20.456244', '2025-07-27 16:36:20.45632', 117, 3, 'update_ai_model', 'ai_model', 2, '{"model_name": "Qwen/Qwen2.5-14B-Instruct", "old_values": {"display_name": "Qwen2.5-14B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8199, "supports_streaming": true, "cost_per_1k_tokens": 0.0014}, "new_values": {"display_name": "Qwen2.5-14B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8000, "supports_streaming": true, "cost_per_1k_tokens": 0.0014}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:37:20.524284', '2025-07-27 16:37:20.524365', 118, 3, 'update_ai_model', 'ai_model', 3, '{"model_name": "Qwen/Qwen2.5-32B-Instruct", "old_values": {"display_name": "Qwen2.5-32B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8194, "supports_streaming": true, "cost_per_1k_tokens": 0.0024}, "new_values": {"display_name": "Qwen2.5-32B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": null, "supports_streaming": true, "cost_per_1k_tokens": 0.0024}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:37:24.606365', '2025-07-27 16:37:24.606444', 119, 3, 'update_ai_model', 'ai_model', 3, '{"model_name": "Qwen/Qwen2.5-32B-Instruct", "old_values": {"display_name": "Qwen2.5-32B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": null, "supports_streaming": true, "cost_per_1k_tokens": 0.0024}, "new_values": {"display_name": "Qwen2.5-32B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8000, "supports_streaming": true, "cost_per_1k_tokens": 0.0024}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:37:46.475369', '2025-07-27 16:37:46.475504', 120, 3, 'update_ai_model', 'ai_model', 4, '{"model_name": "Qwen/Qwen2.5-72B-Instruct", "old_values": {"display_name": "Qwen2.5-72B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8192, "supports_streaming": true, "cost_per_1k_tokens": 0.0056}, "new_values": {"display_name": "Qwen2.5-72B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8192, "supports_streaming": true, "cost_per_1k_tokens": 0.0056}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:38:28.873215', '2025-07-27 16:38:28.873401', 121, 3, 'update_ai_model', 'ai_model', 4, '{"model_name": "Qwen/Qwen2.5-72B-Instruct", "old_values": {"display_name": "Qwen2.5-72B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8192, "supports_streaming": true, "cost_per_1k_tokens": 0.0056}, "new_values": {"display_name": "Qwen2.5-72B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": null, "supports_streaming": true, "cost_per_1k_tokens": 0.0056}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:38:32.976132', '2025-07-27 16:38:32.976223', 122, 3, 'update_ai_model', 'ai_model', 4, '{"model_name": "Qwen/Qwen2.5-72B-Instruct", "old_values": {"display_name": "Qwen2.5-72B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": null, "supports_streaming": true, "cost_per_1k_tokens": 0.0056}, "new_values": {"display_name": "Qwen2.5-72B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 4000, "supports_streaming": true, "cost_per_1k_tokens": 0.0056}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:38:48.27654', '2025-07-27 16:38:48.276631', 123, 3, 'update_ai_model', 'ai_model', 4, '{"model_name": "Qwen/Qwen2.5-72B-Instruct", "old_values": {"display_name": "Qwen2.5-72B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 4000, "supports_streaming": true, "cost_per_1k_tokens": 0.0056}, "new_values": {"display_name": "Qwen2.5-72B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8195, "supports_streaming": true, "cost_per_1k_tokens": 0.0056}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:38:56.793333', '2025-07-27 16:38:56.793434', 124, 3, 'update_ai_model', 'ai_model', 4, '{"model_name": "Qwen/Qwen2.5-72B-Instruct", "old_values": {"display_name": "Qwen2.5-72B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8195, "supports_streaming": true, "cost_per_1k_tokens": 0.0056}, "new_values": {"display_name": "Qwen2.5-72B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8195, "supports_streaming": true, "cost_per_1k_tokens": 0.0056}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:42:16.357788', '2025-07-27 16:42:16.357861', 125, 3, 'update_ai_model', 'ai_model', 6, '{"model_name": "meta-llama/Meta-Llama-3.1-8B-Instruct", "old_values": {"display_name": "Llama-3.1-8B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8000, "supports_streaming": true, "cost_per_1k_tokens": 0.01}, "new_values": {"display_name": "Llama-3.1-8B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": null, "supports_streaming": true, "cost_per_1k_tokens": 0.01}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:42:20.433028', '2025-07-27 16:42:20.433106', 126, 3, 'update_ai_model', 'ai_model', 6, '{"model_name": "meta-llama/Meta-Llama-3.1-8B-Instruct", "old_values": {"display_name": "Llama-3.1-8B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": null, "supports_streaming": true, "cost_per_1k_tokens": 0.01}, "new_values": {"display_name": "Llama-3.1-8B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8000, "supports_streaming": true, "cost_per_1k_tokens": 0.01}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:42:42.495839', '2025-07-27 16:42:42.495949', 127, 3, 'update_ai_model', 'ai_model', 6, '{"model_name": "meta-llama/Meta-Llama-3.1-8B-Instruct", "old_values": {"display_name": "Llama-3.1-8B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8000, "supports_streaming": true, "cost_per_1k_tokens": 0.01}, "new_values": {"display_name": "Llama-3.1-8B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": null, "supports_streaming": true, "cost_per_1k_tokens": 0.01}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:42:55.776937', '2025-07-27 16:42:55.77732', 128, 3, 'update_ai_model', 'ai_model', 6, '{"model_name": "meta-llama/Meta-Llama-3.1-8B-Instruct", "old_values": {"display_name": "Llama-3.1-8B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": null, "supports_streaming": true, "cost_per_1k_tokens": 0.01}, "new_values": {"display_name": "Llama-3.1-8B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 4000, "supports_streaming": true, "cost_per_1k_tokens": 0.01}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:43:19.773323', '2025-07-27 16:43:19.773422', 129, 3, 'update_ai_model', 'ai_model', 2, '{"model_name": "Qwen/Qwen2.5-14B-Instruct", "old_values": {"display_name": "Qwen2.5-14B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8000, "supports_streaming": true, "cost_per_1k_tokens": 0.0014}, "new_values": {"display_name": "Qwen2.5-14B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": null, "supports_streaming": true, "cost_per_1k_tokens": 0.0014}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:43:26.072297', '2025-07-27 16:43:26.072459', 130, 3, 'update_ai_model', 'ai_model', 1, '{"model_name": "Qwen/Qwen2.5-7B-Instruct", "old_values": {"display_name": "Qwen2.5-7B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8195, "supports_streaming": true, "cost_per_1k_tokens": 0.0007}, "new_values": {"display_name": "Qwen2.5-7B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": null, "supports_streaming": true, "cost_per_1k_tokens": 0.0007}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:43:39.541754', '2025-07-27 16:43:39.541864', 131, 3, 'update_ai_model', 'ai_model', 6, '{"model_name": "meta-llama/Meta-Llama-3.1-8B-Instruct", "old_values": {"display_name": "Llama-3.1-8B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 4000, "supports_streaming": true, "cost_per_1k_tokens": 0.01}, "new_values": {"display_name": "Llama-3.1-8B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": null, "supports_streaming": true, "cost_per_1k_tokens": 0.01}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:43:45.403242', '2025-07-27 16:43:45.403381', 132, 3, 'update_ai_model', 'ai_model', 4, '{"model_name": "Qwen/Qwen2.5-72B-Instruct", "old_values": {"display_name": "Qwen2.5-72B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8195, "supports_streaming": true, "cost_per_1k_tokens": 0.0056}, "new_values": {"display_name": "Qwen2.5-72B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": null, "supports_streaming": true, "cost_per_1k_tokens": 0.0056}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:43:49.626781', '2025-07-27 16:43:49.627023', 133, 3, 'update_ai_model', 'ai_model', 3, '{"model_name": "Qwen/Qwen2.5-32B-Instruct", "old_values": {"display_name": "Qwen2.5-32B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8000, "supports_streaming": true, "cost_per_1k_tokens": 0.0024}, "new_values": {"display_name": "Qwen2.5-32B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": null, "supports_streaming": true, "cost_per_1k_tokens": 0.0024}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:44:07.190561', '2025-07-27 16:44:07.190665', 134, 3, 'update_ai_model', 'ai_model', 5, '{"model_name": "deepseek-ai/DeepSeek-V2.5", "old_values": {"display_name": "DeepSeek-V2.5", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8192, "supports_streaming": true, "cost_per_1k_tokens": 0.0014}, "new_values": {"display_name": "DeepSeek-V2.5", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8202, "supports_streaming": true, "cost_per_1k_tokens": 0.0014}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:44:12.375948', '2025-07-27 16:44:12.376219', 135, 3, 'update_ai_model', 'ai_model', 10, '{"model_name": "deepseek-chat", "old_values": {"display_name": "DeepSeek Chat", "is_active": true, "system_api_key": null, "allow_system_key_use": true, "max_tokens": 4096, "supports_streaming": true, "cost_per_1k_tokens": 0.0034}, "new_values": {"display_name": "DeepSeek Chat", "is_active": true, "system_api_key": null, "allow_system_key_use": true, "max_tokens": null, "supports_streaming": true, "cost_per_1k_tokens": 0.0034}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:49:43.7596', '2025-07-27 16:49:43.759685', 136, 3, 'update_ai_model', 'ai_model', 3, '{"model_name": "Qwen/Qwen2.5-32B-Instruct", "old_values": {"display_name": "Qwen2.5-32B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": null, "supports_streaming": true, "cost_per_1k_tokens": 0.0024}, "new_values": {"display_name": "Qwen2.5-32B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": null, "supports_streaming": true, "cost_per_1k_tokens": 0.0024}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:49:47.83231', '2025-07-27 16:49:47.832394', 137, 3, 'update_ai_model', 'ai_model', 5, '{"model_name": "deepseek-ai/DeepSeek-V2.5", "old_values": {"display_name": "DeepSeek-V2.5", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 8202, "supports_streaming": true, "cost_per_1k_tokens": 0.0014}, "new_values": {"display_name": "DeepSeek-V2.5", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": 4000, "supports_streaming": true, "cost_per_1k_tokens": 0.0014}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:49:51.916804', '2025-07-27 16:49:51.916897', 138, 3, 'update_ai_model', 'ai_model', 10, '{"model_name": "deepseek-chat", "old_values": {"display_name": "DeepSeek Chat", "is_active": true, "system_api_key": null, "allow_system_key_use": true, "max_tokens": null, "supports_streaming": true, "cost_per_1k_tokens": 0.0034}, "new_values": {"display_name": "DeepSeek Chat", "is_active": false, "system_api_key": null, "allow_system_key_use": true, "max_tokens": null, "supports_streaming": true, "cost_per_1k_tokens": 0.0034}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:51:47.671556', '2025-07-27 16:51:47.671705', 139, 3, 'update_ai_model', 'ai_model', 3, '{"model_name": "Qwen/Qwen2.5-32B-Instruct", "old_values": {"display_name": "Qwen2.5-32B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": null, "supports_streaming": true, "cost_per_1k_tokens": 0.0024}, "new_values": {"display_name": "Qwen2.5-32B-Instruct", "is_active": true, "system_api_key": "***", "allow_system_key_use": true, "max_tokens": null, "supports_streaming": true, "cost_per_1k_tokens": 0.0024}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:53:05.97146', '2025-07-27 16:53:05.971545', 140, 3, 'create_user', 'user', 8, '{"username": "awang", "email": "<EMAIL>", "is_admin": false}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 16:56:24.355632', '2025-07-27 16:56:24.355771', 141, 3, 'update_ai_model', 'ai_model', 10, '{"model_name": "deepseek-chat", "old_values": {"display_name": "DeepSeek Chat", "is_active": false, "system_api_key": null, "allow_system_key_use": true, "max_tokens": null, "supports_streaming": true, "cost_per_1k_tokens": 0.0034}, "new_values": {"display_name": "DeepSeek Chat", "is_active": true, "system_api_key": null, "allow_system_key_use": true, "max_tokens": null, "supports_streaming": true, "cost_per_1k_tokens": 0.0034}}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 17:18:54.332401', '2025-07-27 17:18:54.332514', 142, 3, 'create_user', 'user', 11, '{"username": "hhhh", "email": "<EMAIL>", "is_admin": false}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-27 17:53:24.890146', '2025-07-27 17:53:24.890216', 143, 3, 'create_user', 'user', 12, '{"username": "ahhh", "email": "<EMAIL>", "is_admin": false}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-28 10:49:46.632635', '2025-07-28 10:49:46.632739', 144, 3, 'create_ai_model', 'ai_model', 13, '{"model_name": "gemini-2.5-flash", "display_name": "gemini-2.5-flash", "provider_id": 5}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-28 10:59:14.940658', '2025-07-28 10:59:14.940762', 145, 3, 'update_ai_provider', 'ai_provider', 5, '{"name": "google", "display_name": "Google"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-28 11:10:15.776694', '2025-07-28 11:10:15.776789', 146, 3, 'update_ai_provider', 'ai_provider', 5, '{"name": "google", "display_name": "Google"}', NULL);
INSERT INTO public.operation_logs VALUES ('2025-07-28 11:34:46.682402', '2025-07-28 11:34:46.682627', 147, 3, 'create_ai_model', 'ai_model', 14, '{"model_name": "gemini-2.5-pro", "display_name": "gemini-2.5-pro", "provider_id": 5}', NULL);


--
-- Data for Name: sessions; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: system_settings; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.system_settings VALUES ('allow_registration', 'true', '是否允许用户注册', '2025-07-25 08:09:20.305509');
INSERT INTO public.system_settings VALUES ('max_file_size', '50', '最大文件上传大小(MB)', '2025-07-25 08:09:20.305673');
INSERT INTO public.system_settings VALUES ('default_storage_quota', '1024', '默认存储配额(MB)', '2025-07-25 08:09:20.30852');
INSERT INTO public.system_settings VALUES ('test_setting', 'test_value_123', '测试设置项', '2025-07-27 10:36:20.500537');
INSERT INTO public.system_settings VALUES ('max_file_size_mb', '100', '最大上传文件大小(MB)', '2025-07-27 16:04:06.004349');
INSERT INTO public.system_settings VALUES ('document_process_timeout', '300', '文档处理超时时间(秒)', '2025-07-27 16:06:11.599819');
INSERT INTO public.system_settings VALUES ('allow_user_registration', 'true', '是否允许用户注册', '2025-07-27 16:04:08.037186');
INSERT INTO public.system_settings VALUES ('default_model_id', '11', '新用户注册时的默认AI模型', '2025-07-27 16:06:11.522601');
INSERT INTO public.system_settings VALUES ('default_storage_quota_gb', '2', '用户默认存储配额(GB)', '2025-07-27 16:04:10.086069');
INSERT INTO public.system_settings VALUES ('chat_retention_days', '0', '对话历史保留天数', '2025-07-27 16:06:11.619649');


--
-- Data for Name: user_api_keys; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.user_api_keys VALUES ('2025-07-28 11:13:36.364153', '2025-07-28 11:13:36.364245', 3, 4, 5, 'AIzaSyD4mv0k7MOh4kGpdm9lsz4uQlFuE3StATI', 'Google API密钥');


--
-- Data for Name: user_quotas; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.user_quotas VALUES (3, 50, 1000, 10240, '2025-07-25 08:09:20.761979');
INSERT INTO public.user_quotas VALUES (4, 20, 101, 1024, '2025-07-25 08:09:20.766694');
INSERT INTO public.user_quotas VALUES (8, 5, 100, 1024, '2025-07-27 16:53:05.966677');
INSERT INTO public.user_quotas VALUES (9, 5, 100, 1024, '2025-07-27 17:14:59.983131');
INSERT INTO public.user_quotas VALUES (10, 5, 100, 1024, '2025-07-27 17:15:55.127039');
INSERT INTO public.user_quotas VALUES (11, 5, 100, 1024, '2025-07-27 17:18:54.325517');
INSERT INTO public.user_quotas VALUES (12, 5, 100, 1024, '2025-07-27 17:53:24.880012');


--
-- Data for Name: user_settings; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.user_settings VALUES ('2025-07-27 08:14:53.504573', '2025-07-27 08:14:53.504779', 4, 4, 1, '[28]', 30, 'auto', 'medium', false, false, '{"1": {"enabled": true, "useSystemKey": true, "selectedModel": null}, "2": {"enabled": true, "useSystemKey": true, "selectedModel": null}, "3": {"enabled": true, "useSystemKey": true, "selectedModel": null}, "4": {"enabled": true, "useSystemKey": true, "selectedModel": null}, "5": {"enabled": true, "useSystemKey": false, "selectedModel": null}}');
INSERT INTO public.user_settings VALUES ('2025-07-27 16:53:31.656558', '2025-07-27 16:53:31.656679', 6, 8, NULL, '[]', 30, 'auto', 'medium', false, false, '{"1": {"enabled": true, "useSystemKey": true, "selectedModel": null}, "2": {"enabled": false, "useSystemKey": true, "selectedModel": null}, "3": {"enabled": false, "useSystemKey": true, "selectedModel": null}, "4": {"enabled": false, "useSystemKey": true, "selectedModel": null}, "5": {"enabled": false, "useSystemKey": true, "selectedModel": null}}');
INSERT INTO public.user_settings VALUES ('2025-07-27 17:14:59.983347', '2025-07-27 17:14:59.983439', 7, 9, 11, NULL, 30, 'auto', 'medium', false, false, NULL);
INSERT INTO public.user_settings VALUES ('2025-07-27 17:15:55.127204', '2025-07-27 17:15:55.127278', 8, 10, 11, NULL, 30, 'auto', 'medium', false, false, NULL);
INSERT INTO public.user_settings VALUES ('2025-07-27 09:56:44.335079', '2025-07-27 09:56:44.335249', 5, 3, NULL, NULL, 30, 'auto', 'medium', false, false, NULL);
INSERT INTO public.user_settings VALUES ('2025-07-27 17:18:54.325718', '2025-07-27 17:18:54.325799', 9, 11, 11, NULL, 30, 'auto', 'medium', false, false, '{"1": {"enabled": true, "useSystemKey": true, "selectedModel": null}, "2": {"enabled": true, "useSystemKey": true, "selectedModel": null}, "3": {"enabled": true, "useSystemKey": true, "selectedModel": null}, "4": {"enabled": true, "useSystemKey": true, "selectedModel": null}, "5": {"enabled": true, "useSystemKey": true, "selectedModel": null}}');
INSERT INTO public.user_settings VALUES ('2025-07-27 17:53:24.880257', '2025-07-27 17:53:24.880351', 10, 12, 11, NULL, 30, 'auto', 'medium', false, false, NULL);


--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.users VALUES ('2025-07-27 17:14:59.972477', '2025-07-27 17:14:59.972572', 9, 'testuser_1753636497', '<EMAIL>', '$2b$12$I6AvpzgPSnxoHoGDhk9ZxunA/G.n1gFtJJqCChaqCFRicZwZGlERK', '测试用户', NULL, NULL, false, 'active', '2025-07-27 17:15:02.247685');
INSERT INTO public.users VALUES ('2025-07-27 17:15:55.120866', '2025-07-27 17:15:55.120963', 10, 'testuser_1753636552', '<EMAIL>', '$2b$12$ou8W/jTZE1jKt46/yFocuey57eA6gK0bXP5kbl2333JlrjTjWNJh2', '测试用户', NULL, NULL, false, 'active', '2025-07-27 17:15:57.358613');
INSERT INTO public.users VALUES ('2025-07-27 17:18:54.316801', '2025-07-27 17:18:54.316903', 11, 'hhhh', '<EMAIL>', '$2b$12$BWSirAgmEeVhuOycEqu/1.EmVzOFAAOxvB8EC6AJoEuaHOiBNoRQu', '测试hhh', NULL, NULL, false, 'active', '2025-07-27 17:29:54.210188');
INSERT INTO public.users VALUES ('2025-07-25 08:09:20.545141', '2025-07-25 08:09:20.545197', 3, 'admin', '<EMAIL>', '$2b$12$ebZ.5EHndlT2jBoBJ/8o6.Swq.fFLNPki0I.wYdiFdftxCzwRwdI2', '系统管理员', NULL, NULL, true, 'active', '2025-07-28 11:34:13.963737');
INSERT INTO public.users VALUES ('2025-07-27 16:53:05.960836', '2025-07-27 16:53:05.960923', 8, 'awang', '<EMAIL>', '$2b$12$BrzSGsoZK3ni0L5Ah88k7uCeDSevR6rIpxclE.W/dGv0vrXwHZshK', '阿汪', NULL, NULL, false, 'active', '2025-07-27 17:48:15.004891');
INSERT INTO public.users VALUES ('2025-07-27 17:53:24.857342', '2025-07-27 17:53:24.857566', 12, 'ahhh', '<EMAIL>', '$2b$12$zQTvUA0Q1Nao.ThkLrJarOe9mXMPqmTNkq/2YtDkT4KT0Zjaq3i2K', 'ahhh', NULL, NULL, false, 'active', '2025-07-27 17:53:47.119969');
INSERT INTO public.users VALUES ('2025-07-25 08:09:20.753686', '2025-07-25 08:09:20.753768', 4, 'testuser', '<EMAIL>', '$2b$12$2ioNP/eYt/l6xVSn0MEesOPCpcCrs7CD236NDADA9NrkUUsRBQsL2', 'testuser', NULL, NULL, false, 'active', '2025-07-28 13:28:29.264327');


--
-- Name: ai_models_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.ai_models_id_seq', 14, true);


--
-- Name: ai_providers_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.ai_providers_id_seq', 10, true);


--
-- Name: chat_messages_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.chat_messages_id_seq', 76, true);


--
-- Name: chat_sessions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.chat_sessions_id_seq', 29, true);


--
-- Name: document_chunks_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.document_chunks_id_seq', 1, false);


--
-- Name: documents_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.documents_id_seq', 53, true);


--
-- Name: knowledge_bases_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.knowledge_bases_id_seq', 35, true);


--
-- Name: operation_logs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.operation_logs_id_seq', 147, true);


--
-- Name: sessions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.sessions_id_seq', 1, false);


--
-- Name: user_api_keys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.user_api_keys_id_seq', 3, true);


--
-- Name: user_settings_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.user_settings_id_seq', 10, true);


--
-- Name: users_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.users_id_seq', 12, true);


--
-- Name: ai_models ai_models_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ai_models
    ADD CONSTRAINT ai_models_pkey PRIMARY KEY (id);


--
-- Name: ai_providers ai_providers_name_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ai_providers
    ADD CONSTRAINT ai_providers_name_key UNIQUE (name);


--
-- Name: ai_providers ai_providers_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ai_providers
    ADD CONSTRAINT ai_providers_pkey PRIMARY KEY (id);


--
-- Name: chat_messages chat_messages_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.chat_messages
    ADD CONSTRAINT chat_messages_pkey PRIMARY KEY (id);


--
-- Name: chat_sessions chat_sessions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.chat_sessions
    ADD CONSTRAINT chat_sessions_pkey PRIMARY KEY (id);


--
-- Name: document_chunks document_chunks_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.document_chunks
    ADD CONSTRAINT document_chunks_pkey PRIMARY KEY (id);


--
-- Name: documents documents_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT documents_pkey PRIMARY KEY (id);


--
-- Name: knowledge_bases knowledge_bases_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.knowledge_bases
    ADD CONSTRAINT knowledge_bases_pkey PRIMARY KEY (id);


--
-- Name: operation_logs operation_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.operation_logs
    ADD CONSTRAINT operation_logs_pkey PRIMARY KEY (id);


--
-- Name: sessions sessions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.sessions
    ADD CONSTRAINT sessions_pkey PRIMARY KEY (id);


--
-- Name: system_settings system_settings_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.system_settings
    ADD CONSTRAINT system_settings_pkey PRIMARY KEY (key);


--
-- Name: user_api_keys user_api_keys_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_api_keys
    ADD CONSTRAINT user_api_keys_pkey PRIMARY KEY (id);


--
-- Name: user_quotas user_quotas_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_quotas
    ADD CONSTRAINT user_quotas_pkey PRIMARY KEY (user_id);


--
-- Name: user_settings user_settings_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_settings
    ADD CONSTRAINT user_settings_pkey PRIMARY KEY (id);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: ix_sessions_token; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_sessions_token ON public.sessions USING btree (token);


--
-- Name: ix_user_settings_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_user_settings_user_id ON public.user_settings USING btree (user_id);


--
-- Name: ix_users_email; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_users_email ON public.users USING btree (email);


--
-- Name: ix_users_username; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_users_username ON public.users USING btree (username);


--
-- Name: ai_models ai_models_provider_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ai_models
    ADD CONSTRAINT ai_models_provider_id_fkey FOREIGN KEY (provider_id) REFERENCES public.ai_providers(id);


--
-- Name: chat_messages chat_messages_model_id_used_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.chat_messages
    ADD CONSTRAINT chat_messages_model_id_used_fkey FOREIGN KEY (model_id_used) REFERENCES public.ai_models(id);


--
-- Name: chat_messages chat_messages_session_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.chat_messages
    ADD CONSTRAINT chat_messages_session_id_fkey FOREIGN KEY (session_id) REFERENCES public.chat_sessions(id);


--
-- Name: chat_sessions chat_sessions_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.chat_sessions
    ADD CONSTRAINT chat_sessions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: document_chunks document_chunks_doc_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.document_chunks
    ADD CONSTRAINT document_chunks_doc_id_fkey FOREIGN KEY (doc_id) REFERENCES public.documents(id);


--
-- Name: documents documents_kb_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT documents_kb_id_fkey FOREIGN KEY (kb_id) REFERENCES public.knowledge_bases(id);


--
-- Name: documents documents_uploader_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT documents_uploader_id_fkey FOREIGN KEY (uploader_id) REFERENCES public.users(id);


--
-- Name: knowledge_bases knowledge_bases_owner_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.knowledge_bases
    ADD CONSTRAINT knowledge_bases_owner_id_fkey FOREIGN KEY (owner_id) REFERENCES public.users(id);


--
-- Name: operation_logs operation_logs_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.operation_logs
    ADD CONSTRAINT operation_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: sessions sessions_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.sessions
    ADD CONSTRAINT sessions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: user_api_keys user_api_keys_provider_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_api_keys
    ADD CONSTRAINT user_api_keys_provider_id_fkey FOREIGN KEY (provider_id) REFERENCES public.ai_providers(id);


--
-- Name: user_api_keys user_api_keys_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_api_keys
    ADD CONSTRAINT user_api_keys_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: user_quotas user_quotas_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_quotas
    ADD CONSTRAINT user_quotas_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: user_settings user_settings_default_model_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_settings
    ADD CONSTRAINT user_settings_default_model_id_fkey FOREIGN KEY (default_model_id) REFERENCES public.ai_models(id);


--
-- Name: user_settings user_settings_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_settings
    ADD CONSTRAINT user_settings_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- PostgreSQL database dump complete
--

