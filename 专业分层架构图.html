<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基于RAG技术的个性化教学智能体系统架构</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 20px;
            font-size: 24px;
            font-weight: 600;
            letter-spacing: 1px;
        }
        
        .architecture {
            position: relative;
        }
        
        /* 应用层 - 橙色 */
        .layer-app {
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);
            padding: 20px;
            position: relative;
        }
        
        .layer-title {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: white;
            font-weight: 600;
            font-size: 16px;
            writing-mode: vertical-rl;
            text-orientation: mixed;
        }
        
        .components {
            display: flex;
            gap: 15px;
            margin-right: 60px;
        }
        
        .component {
            background: rgba(255, 255, 255, 0.9);
            padding: 15px 20px;
            border-radius: 8px;
            font-size: 13px;
            font-weight: 500;
            color: #333;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            line-height: 1.4;
            text-align: center;
            flex: 1;
            min-height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .component:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }
        
        /* 服务层 - 紫色 */
        .layer-service {
            background: linear-gradient(135deg, #a855f7 0%, #8b5cf6 100%);
            padding: 20px;
            position: relative;
        }
        
        .service-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            margin-right: 60px;
        }
        
        .service-row:last-child {
            margin-bottom: 0;
        }
        
        .service-component {
            background: rgba(255, 255, 255, 0.9);
            padding: 12px 16px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            color: #333;
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            flex: 1;
            text-align: center;
            line-height: 1.3;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .service-component:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 10px rgba(0, 0, 0, 0.15);
        }
        
        /* 数据中台 */
        .data-platform {
            background: linear-gradient(135deg, #e879f9 0%, #c084fc 100%);
            padding: 15px 20px;
            margin: 0 20px;
            margin-right: 80px;
            border-radius: 8px;
            text-align: center;
            color: white;
            font-weight: 600;
            font-size: 16px;
        }
        
        /* 数据资产区域 */
        .data-assets {
            background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
            padding: 20px;
            margin: 0 20px;
            margin-right: 80px;
            border-radius: 8px;
            border: 2px solid #a855f7;
        }
        
        .asset-section {
            margin-bottom: 15px;
        }
        
        .asset-title {
            font-weight: 600;
            color: #7c3aed;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .asset-items {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .asset-section .asset-items {
            display: block;
        }
        
        .asset-item {
            background: #a855f7;
            color: white;
            padding: 10px 15px;
            border-radius: 6px;
            font-size: 11px;
            font-weight: 500;
            line-height: 1.3;
            text-align: center;
            margin-bottom: 8px;
            display: block;
            width: 100%;
            box-sizing: border-box;
        }
        
        /* 计算层 - 绿色 */
        .layer-compute {
            background: linear-gradient(135deg, #34d399 0%, #10b981 100%);
            padding: 20px;
            position: relative;
        }
        
        .compute-section {
            margin-bottom: 15px;
            margin-right: 60px;
        }
        
        .compute-title {
            color: white;
            font-weight: 600;
            margin-bottom: 10px;
            font-size: 16px;
        }
        
        .compute-items {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .compute-item {
            background: rgba(255, 255, 255, 0.9);
            padding: 15px 20px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 500;
            color: #333;
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
            line-height: 1.3;
            text-align: center;
            flex: 1;
            min-height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* 基础设施层 - 灰色 */
        .layer-infra {
            background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
            padding: 20px;
            position: relative;
        }
        
        .infra-items {
            display: flex;
            gap: 20px;
            margin-right: 60px;
        }
        
        .infra-item {
            background: rgba(255, 255, 255, 0.9);
            padding: 15px 25px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            color: #333;
            flex: 1;
            text-align: center;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        /* 网络互通层 - 橙色 */
        .layer-network {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            padding: 15px 20px;
            text-align: center;
            color: white;
            font-weight: 600;
            font-size: 16px;
        }
        
        /* 连接箭头 */
        .arrow {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 15px solid transparent;
            border-right: 15px solid transparent;
            z-index: 10;
        }
        
        .arrow-down {
            border-top: 20px solid #666;
            bottom: -20px;
        }
        
        .arrow-up {
            border-bottom: 20px solid #666;
            top: -20px;
        }
        
        /* 响应式设计 */
        @media (max-width: 1200px) {
            .components, .service-row, .compute-items, .infra-items {
                flex-wrap: wrap;
            }
            
            .layer-title {
                position: static;
                writing-mode: horizontal-tb;
                text-orientation: initial;
                margin-bottom: 15px;
                transform: none;
            }
            
            .components, .service-row, .compute-section, .infra-items {
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">基于RAG技术的个性化教学智能体系统架构</div>
        
        <div class="architecture">
            <!-- 应用层 -->
            <div class="layer-app">
                <div class="layer-title">用户界面层</div>
                <div class="components">
                    <div class="component">用户端<br/>Vue.js 3 + TypeScript + Element Plus UI<br/>知识库管理 | 智能问答 | 文档上传</div>
                    <div class="component">管理员端<br/>用户管理 + AI模型配置 + 系统监控<br/>Chart.js/ECharts数据可视化</div>
                </div>
            </div>

            <!-- 服务层 -->
            <div class="layer-service">
                <div class="layer-title">网关服务层</div>

                <div class="service-row">
                    <div class="service-component">Nginx反向代理<br/>静态文件服务 + 负载均衡</div>
                    <div class="service-component">CORS跨域处理<br/>请求路由转发 + 安全策略</div>
                    <div class="service-component">JWT身份认证<br/>权限验证 + 会话管理</div>
                </div>

                <div class="data-platform">应用服务层</div>

                <div class="data-assets">
                    <div class="asset-section">
                        <div class="asset-title">应用服务组件</div>
                        <div class="asset-items">
                            <div class="asset-item">用户认证服务<br/>FastAPI + SQLModel + 权限控制</div>
                            <div class="asset-item">知识库管理<br/>文档上传解析 + 批量处理 + 版本控制</div>
                            <div class="asset-item">对话管理<br/>多轮会话维护 + 上下文管理 + 历史记录</div>
                            <div class="asset-item">AI模型管理<br/>多模型手动切换 + 参数配置 + 性能监控</div>
                        </div>
                    </div>
                </div>

                <div class="data-platform" style="margin-top: 20px;">AI核心层</div>

                <div class="data-assets" style="margin-top: 15px;">
                    <div class="asset-section">
                        <div class="asset-title">AI核心组件</div>
                        <div class="asset-items">
                            <div class="asset-item">多模型接入<br/>OpenAI GPT | Claude | DeepSeek | Qwen<br/>SiliconFlow | 通义千问</div>
                            <div class="asset-item">RAG检索引擎<br/>FAISS向量检索 + 余弦相似度匹配<br/>语义相似度阈值动态调节</div>
                            <div class="asset-item">文本向量化<br/>Sentence-BERT编码 + BGE-M3<br/>1024维语义向量空间</div>
                            <div class="asset-item">文档处理<br/>智能分块算法 + 语义边界识别<br/>多格式解析引擎</div>
                            <div class="asset-item">流式输出<br/>SSE实时响应 + 增量推送</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据持久层 -->
            <div class="layer-compute">
                <div class="layer-title">数据持久层</div>

                <div class="compute-items" style="margin-bottom: 15px;">
                    <div class="compute-item">PostgreSQL<br/>用户数据 + 知识库元数据 + 对话记录<br/>索引优化 + 事务管理</div>
                    <div class="compute-item">FAISS向量库<br/>文档向量索引 + 高效检索<br/>IVF索引 + 量化压缩</div>
                    <div class="compute-item">Redis缓存<br/>会话状态缓存 + 检索结果缓存<br/>过期策略 + 内存优化</div>
                    <div class="compute-item">本地文件系统<br/>文档存储 + 媒体文件<br/>目录结构 + 权限管理</div>
                </div>
            </div>

            <!-- 部署运行层 -->
            <div class="layer-infra">
                <div class="layer-title">部署运行层</div>
                <div class="infra-items">
                    <div class="infra-item">Docker容器化<br/>前后端分离部署 + 微服务架构<br/>镜像管理 + 服务编排</div>
                    <div class="infra-item">宝塔面板<br/>服务管理 + 监控告警<br/>日志管理 + 性能优化</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
