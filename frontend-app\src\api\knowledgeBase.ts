import request from './request'
import type { KnowledgeBase } from '@/types'

export interface CreateKnowledgeBaseRequest {
  name: string
  description?: string
}

export interface UpdateKnowledgeBaseRequest {
  name?: string
  description?: string
}

export interface KnowledgeBaseListParams {
  offset?: number
  limit?: number
}

// 知识库API
export const knowledgeBaseAPI = {
  // 获取知识库列表
  getList: (params?: KnowledgeBaseListParams): Promise<KnowledgeBase[]> => {
    return request.get('/knowledge-bases/', { params })
  },

  // 创建知识库
  create: (data: CreateKnowledgeBaseRequest): Promise<KnowledgeBase> => {
    return request.post('/knowledge-bases/', data)
  },

  // 获取知识库详情
  getDetail: (id: number): Promise<KnowledgeBase> => {
    return request.get(`/knowledge-bases/${id}`)
  },

  // 更新知识库
  update: (id: number, data: UpdateKnowledgeBaseRequest): Promise<KnowledgeBase> => {
    return request.put(`/knowledge-bases/${id}`, data)
  },

  // 删除知识库
  delete: (id: number): Promise<{ message: string }> => {
    return request.delete(`/knowledge-bases/${id}`)
  }
}
