<template>
  <div class="p-6">
    <h1 class="text-2xl font-bold mb-4">Element Plus 测试页面</h1>

    <div class="space-y-4">
      <p>如果您能看到这个页面，说明Vue应用正在正常工作！</p>
      <p>当前时间: {{ currentTime }}</p>

      <!-- 测试 Element Plus 按钮 -->
      <div class="space-x-2">
        <el-button @click="updateTime">更新时间</el-button>
        <el-button type="primary">主要按钮</el-button>
        <el-button type="success">成功按钮</el-button>
        <el-button type="warning">警告按钮</el-button>
        <el-button type="danger">危险按钮</el-button>
      </div>

      <!-- 测试 Element Plus 输入框 -->
      <div class="max-w-md">
        <el-input v-model="testInput" placeholder="请输入测试内容" />
        <p class="mt-2">输入内容: {{ testInput }}</p>
      </div>

      <!-- 测试 Element Plus 下拉菜单 -->
      <div>
        <el-dropdown>
          <el-button type="primary">
            下拉菜单
            <el-icon class="el-icon--right">
              <arrow-down />
            </el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>选项1</el-dropdown-item>
              <el-dropdown-item>选项2</el-dropdown-item>
              <el-dropdown-item>选项3</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>

      <!-- 测试消息提示 -->
      <div class="space-x-2">
        <el-button @click="showMessage('success')">成功消息</el-button>
        <el-button @click="showMessage('warning')">警告消息</el-button>
        <el-button @click="showMessage('error')">错误消息</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const currentTime = ref(new Date().toLocaleString())
const testInput = ref('')

const updateTime = () => {
  currentTime.value = new Date().toLocaleString()
}

const showMessage = (type: 'success' | 'warning' | 'error') => {
  ElMessage({
    message: `这是一个${type === 'success' ? '成功' : type === 'warning' ? '警告' : '错误'}消息`,
    type,
  })
}
</script>
