# 演示素材准备清单

## 📚 知识库素材准备

### 1. 机械制造专业知识库
**文件名**：`mechanical-manufacturing-kb`

**需要准备的文档**：
- 📄 **数控编程技术教材** (PDF格式)
  - 包含G代码、M代码详细说明
  - 数控车床、铣床操作规程
  - 编程实例和案例分析
  
- 📄 **机械设计基础** (PDF格式)
  - 机械零件设计原理
  - 材料选择和热处理
  - 公差配合标准
  
- 📄 **实训操作手册** (Word转PDF)
  - 安全操作规程
  - 设备维护保养
  - 常见故障排除

**演示问题**：
```
1. "数控车床G01指令的具体用法是什么？"
2. "机械加工中如何选择合适的切削参数？"
3. "数控编程时坐标系如何建立？"
4. "车床加工螺纹的编程方法"
```

### 2. 护理专业知识库
**文件名**：`nursing-professional-kb`

**需要准备的文档**：
- 📄 **基础护理学教材** (PDF格式)
  - 护理基本技能操作
  - 无菌技术原理和应用
  - 患者安全管理
  
- 📄 **临床护理操作规范** (PDF格式)
  - 静脉输液操作流程
  - 导尿术操作标准
  - 伤口护理技术
  
- 📄 **护理安全管理制度** (Word转PDF)
  - 用药安全管理
  - 感染控制措施
  - 应急处理预案

**演示问题**：
```
1. "静脉输液的操作步骤和注意事项？"
2. "如何预防和处理输液反应？"
3. "无菌技术的基本原则是什么？"
4. "导尿术的适应症和禁忌症"
```

### 3. 计算机专业知识库
**文件名**：`computer-science-kb`

**需要准备的文档**：
- 📄 **Java程序设计教材** (PDF格式)
  - 面向对象编程概念
  - 集合框架详解
  - 多线程编程
  
- 📄 **数据结构与算法** (PDF格式)
  - 线性表、栈、队列
  - 树和图的算法
  - 排序和查找算法
  
- 📄 **计算机网络基础** (PDF格式)
  - OSI七层模型
  - TCP/IP协议族
  - 网络安全基础

**演示问题**：
```
1. "Java中ArrayList和LinkedList的区别？"
2. "什么是TCP三次握手？"
3. "如何实现单例设计模式？"
4. "二叉树的遍历算法有哪些？"
```

### 4. 成绩分析知识库
**文件名**：`grade-analysis-kb`

**需要准备的文档**：
- 📊 **学生成绩数据样本** (Excel转PDF)
  - 多个班级的成绩数据
  - 不同科目的分数分布
  - 学期对比数据
  
- 📄 **教育统计学基础** (PDF格式)
  - 描述性统计方法
  - 正态分布检验
  - 相关性分析
  
- 📄 **成绩分析报告模板** (Word转PDF)
  - 分析指标说明
  - 图表制作规范
  - 结论撰写要求

**演示问题**：
```
1. "如何计算班级平均分和标准差？"
2. "生成本学期各科目成绩分布的柱状图"
3. "分析学生成绩的正态分布特征"
4. "制作各专业技能考核通过率对比图"
```

---

## 🎯 测试数据准备

### 可视化演示数据

#### 1. 学生成绩数据 (Excel格式)
```
学号    姓名    专业        数学    英语    专业课1  专业课2  总分
2021001 张三    机械制造    85      78      92      88      343
2021002 李四    护理        78      85      90      87      340
2021003 王五    计算机      92      88      85      90      355
...
```

#### 2. 专业技能考核数据
```
专业        考核项目        通过人数    总人数    通过率
机械制造    数控编程        45         50       90%
护理        静脉输液        48         50       96%
计算机      Java编程        42         50       84%
...
```

#### 3. 学习进度数据
```
学生    周次    完成度    知识点掌握数    总知识点数
张三    第1周   80%       16            20
张三    第2周   85%       17            20
张三    第3周   90%       18            20
...
```

---

## 📸 界面截图素材

### 1. 系统主界面截图
- **文件名**：`main-interface.png`
- **内容**：显示系统Logo、导航菜单、功能模块
- **要求**：高清截图，界面整洁美观

### 2. 知识库管理界面
- **文件名**：`knowledge-base-management.png`
- **内容**：知识库列表、上传按钮、处理状态
- **要求**：显示多个已创建的知识库

### 3. 文档上传界面
- **文件名**：`document-upload.png`
- **内容**：拖拽上传区域、文件列表、进度条
- **要求**：显示上传过程和成功状态

### 4. AI对话界面
- **文件名**：`ai-chat-interface.png`
- **内容**：对话框、消息历史、知识来源显示
- **要求**：显示专业问答的完整对话

### 5. 数据可视化界面
- **文件名**：`data-visualization.png`
- **内容**：图表展示、交互控件、导出按钮
- **要求**：显示生成的图表效果

### 6. 系统设置界面
- **文件名**：`system-settings.png`
- **内容**：模型配置、参数设置、性能监控
- **要求**：显示多模型支持功能

---

## 🎨 技术架构图素材

### 1. 系统整体架构图
- **文件名**：`system-architecture.png`
- **内容**：前端、后端、数据库、AI模型的关系
- **制作工具**：Draw.io、Visio或PPT

### 2. RAG技术流程图
- **文件名**：`rag-workflow.png`
- **内容**：文档处理→向量化→检索→生成的完整流程
- **要求**：清晰展示每个步骤

### 3. 多模型融合架构图
- **文件名**：`multi-model-architecture.png`
- **内容**：模型调度器、各种AI模型、智能选择机制
- **要求**：突出创新性

### 4. 数据流向图
- **文件名**：`data-flow-diagram.png`
- **内容**：用户输入→检索→AI处理→结果输出
- **要求**：简洁明了

---

## 📊 对比演示素材

### 1. 传统AI vs RAG系统对比
**准备内容**：
- 同一个专业问题的两种回答
- 传统AI：可能包含错误信息
- RAG系统：基于文档的准确回答
- 知识来源的可追溯性展示

### 2. 成本效益对比图表
**数据内容**：
```
对比项目        传统方式    RAG方式    节省比例
Token消耗       5000       1000       80%
月度成本        ¥2000      ¥400       80%
回答准确率      70%        95%        +25%
响应时间        5秒        2秒        60%
```

### 3. 平台功能对比表
```
功能特性        Coze    Dify    本平台
存储空间        100MB   有限    GB级
支持模型        国内    国外    全覆盖
部署方式        云端    混合    私有
成本控制        付费    高昂    零成本
专业适配        通用    通用    深度定制
```

---

## 🎬 录制场景准备

### 场景1：知识库创建演示
**准备工作**：
- 清空现有知识库（或准备演示账号）
- 准备好要上传的文档文件
- 确保上传速度和处理速度正常

### 场景2：专业问答演示
**准备工作**：
- 预先测试所有演示问题
- 确保回答质量和响应速度
- 准备多个不同专业的问题

### 场景3：可视化功能演示
**准备工作**：
- 准备好测试数据
- 测试图表生成功能
- 确保图表美观和交互正常

### 场景4：系统管理演示
**准备工作**：
- 展示多模型配置
- 显示系统监控数据
- 演示性能优化效果

---

## ✅ 演示前检查清单

### 技术环境检查
- [ ] 系统服务全部正常启动
- [ ] 网络连接稳定
- [ ] 所有演示功能测试通过
- [ ] 备用方案准备就绪

### 素材文件检查
- [ ] 所有知识库文档已准备
- [ ] 测试问题已验证
- [ ] 界面截图已保存
- [ ] 架构图已制作完成

### 录制设备检查
- [ ] 录屏软件设置正确
- [ ] 音频设备工作正常
- [ ] 屏幕分辨率适合录制
- [ ] 存储空间充足

### 演示脚本检查
- [ ] 解说词已熟练掌握
- [ ] 时间分配合理
- [ ] 操作流程已练习
- [ ] 重点内容已标记

---

## 📝 注意事项

### 录制技巧
1. **语速控制**：保持适中语速，确保观众能跟上
2. **操作流畅**：避免频繁的鼠标移动和点击
3. **重点突出**：用箭头或高亮标注重要内容
4. **画面稳定**：避免快速切换和跳转

### 内容重点
1. **突出创新性**：重点展示RAG技术和多模型融合
2. **强调实用性**：展示真实的教学应用场景
3. **证明效果**：用数据和对比证明系统优势
4. **体现专业性**：展示对职业教育的深度理解

### 质量保证
1. **多次试录**：确保每个环节都流畅
2. **时间控制**：严格控制在10分钟内
3. **画质音质**：确保高清画面和清晰音频
4. **备用方案**：准备录制好的备用片段

---

这个清单涵盖了视频录制所需的所有素材。你现在可以按照这个清单开始准备，有什么具体问题需要我帮助解决吗？
