var e;(()=>{function t(e,t,a,s,l,n,r){try{var i=e[n](r),o=i.value}catch(e){return void a(e)}i.done?t(o):Promise.resolve(o).then(s,l)}e=function(e){return function(){var a=this,s=arguments;return new Promise(function(l,n){var r=e.apply(a,s);function i(e){t(r,l,n,i,o,"next",e)}function o(e){t(r,l,n,i,o,"throw",e)}i(void 0)})}}})();import{E as t,c as a}from"./elementPlus-Di4PDIm8.js";import{dB as s,dN as l,dU as n,dd as r,df as i,dg as o,dj as d,dk as c,dl as u,ed as g}from"./vendor-BJ-uKP15.js";import{b as v}from"./admin-BSai4urc.js";const p={class:"p-6 space-y-6"},y={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},f={class:"space-y-4"},x={class:"flex items-center space-x-4"},m={key:0,class:"text-green-600"},h={class:"flex items-center space-x-4"},_={key:0,class:"text-green-600"},k={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},b={class:"space-y-4"},A={class:"flex items-center space-x-4"},I={key:0,class:"text-green-600"},w={class:"flex items-center space-x-4"},P={key:0,class:"text-green-600"},S={class:"flex items-center space-x-4"},C={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},$={class:"space-y-4"},j={class:"flex items-center space-x-4"},B={key:0,class:"text-green-600"},D={class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-6"},N={class:"text-sm text-gray-600 dark:text-gray-300 overflow-auto"},U={class:"text-center"};var z=u({__name:"TestPage",setup(u){const z=n({providers:!1,models:!1,settings:!1,updateSetting:!1,logs:!1,all:!1}),E=n({}),J=(L=e(function*(){try{z.value.providers=!0;const e=yield v.getAIProviders();E.value.providers=e,a.success(`成功获取 ${e.length} 个AI提供商`)}catch(e){a.error("测试AI提供商失败")}finally{z.value.providers=!1}}),function(){return L.apply(this,arguments)});var L;const M=(O=e(function*(){try{z.value.models=!0;const e=yield v.getAIModels();E.value.models=e,a.success(`成功获取 ${e.length} 个AI模型`)}catch(e){a.error("测试AI模型失败")}finally{z.value.models=!1}}),function(){return O.apply(this,arguments)});var O;const R=(T=e(function*(){try{z.value.settings=!0;const e=yield v.getSettings();E.value.settings=e,a.success(`成功获取 ${e.length} 个系统设置`)}catch(e){a.error("测试系统设置失败")}finally{z.value.settings=!1}}),function(){return T.apply(this,arguments)});var T;const q=(F=e(function*(){try{z.value.updateSetting=!0;const e=yield v.updateSetting("test_frontend_setting",{value:"test_value_from_frontend",description:"前端测试设置"});E.value.updateSetting=e,a.success("设置更新成功")}catch(e){a.error("测试更新设置失败")}finally{z.value.updateSetting=!1}}),function(){return F.apply(this,arguments)});var F;const G=(H=e(function*(){try{const e=Date.now(),t={name:`test_provider_${e}`,display_name:`测试供应商_${e}`,base_url:"https://api.test.com",description:"前端测试用供应商",is_active:!0},s=yield v.createAIProvider(t);a.success(`供应商创建成功: ${s.display_name}`),yield v.deleteAIProvider(s.id),a.success("供应商删除成功")}catch(t){var e;a.error((null===(e=t.response)||void 0===e||null===(e=e.data)||void 0===e?void 0:e.detail)||"测试供应商CRUD失败")}}),function(){return H.apply(this,arguments)});var H;const K=(Q=e(function*(){try{z.value.logs=!0;const e=yield v.getLogs({limit:10});E.value.logs=e,a.success(`成功获取 ${e.length} 条操作日志`)}catch(e){a.error("测试操作日志失败")}finally{z.value.logs=!1}}),function(){return Q.apply(this,arguments)});var Q;const V=(W=e(function*(){try{z.value.all=!0,yield Promise.all([J(),M(),R(),K()]),yield q(),a.success("所有功能测试完成！")}catch(e){a.error("测试失败")}finally{z.value.all=!1}}),function(){return W.apply(this,arguments)});var W;return(e,a)=>{const n=t;return s(),o("div",p,[a[11]||(a[11]=r("div",{class:"text-center mb-8"},[r("h1",{class:"text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2"},"功能测试页面"),r("p",{class:"text-gray-600 dark:text-gray-400"},"测试各个API功能是否正常工作")],-1)),r("div",y,[a[2]||(a[2]=r("h2",{class:"text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4"},"AI模型管理测试",-1)),r("div",f,[r("div",x,[c(n,{onClick:J,loading:z.value.providers},{default:l(()=>a[0]||(a[0]=[d(" 测试获取AI提供商 ",-1)])),_:1,__:[0]},8,["loading"]),E.value.providers?(s(),o("span",m," ✅ 成功获取 "+g(E.value.providers.length)+" 个提供商 ",1)):i("",!0)]),r("div",h,[c(n,{onClick:M,loading:z.value.models},{default:l(()=>a[1]||(a[1]=[d(" 测试获取AI模型 ",-1)])),_:1,__:[1]},8,["loading"]),E.value.models?(s(),o("span",_," ✅ 成功获取 "+g(E.value.models.length)+" 个模型 ",1)):i("",!0)])])]),r("div",k,[a[6]||(a[6]=r("h2",{class:"text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4"},"系统设置测试",-1)),r("div",b,[r("div",A,[c(n,{onClick:R,loading:z.value.settings},{default:l(()=>a[3]||(a[3]=[d(" 测试获取系统设置 ",-1)])),_:1,__:[3]},8,["loading"]),E.value.settings?(s(),o("span",I," ✅ 成功获取 "+g(E.value.settings.length)+" 个设置 ",1)):i("",!0)]),r("div",w,[c(n,{onClick:q,loading:z.value.updateSetting},{default:l(()=>a[4]||(a[4]=[d(" 测试更新设置 ",-1)])),_:1,__:[4]},8,["loading"]),E.value.updateSetting?(s(),o("span",P," ✅ 设置更新成功 ")):i("",!0)]),r("div",S,[c(n,{onClick:G,type:"warning"},{default:l(()=>a[5]||(a[5]=[d(" 测试供应商创建/删除 ",-1)])),_:1,__:[5]})])])]),r("div",C,[a[8]||(a[8]=r("h2",{class:"text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4"},"系统日志测试",-1)),r("div",$,[r("div",j,[c(n,{onClick:K,loading:z.value.logs},{default:l(()=>a[7]||(a[7]=[d(" 测试获取操作日志 ",-1)])),_:1,__:[7]},8,["loading"]),E.value.logs?(s(),o("span",B," ✅ 成功获取 "+g(E.value.logs.length)+" 条日志 ",1)):i("",!0)])])]),r("div",D,[a[9]||(a[9]=r("h3",{class:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4"},"测试结果",-1)),r("pre",N,g(JSON.stringify(E.value,null,2)),1)]),r("div",U,[c(n,{type:"primary",size:"large",onClick:V,loading:z.value.all},{default:l(()=>a[10]||(a[10]=[d(" 一键测试所有功能 ",-1)])),_:1,__:[10]},8,["loading"])])])}}});export{z as default};