<template>
  <div id="app" class="min-h-screen w-full bg-gray-50 dark:bg-dark-900">
    <!-- 全局加载指示器 -->
    <div v-if="isLoading" class="fixed inset-0 bg-white dark:bg-dark-900 flex items-center justify-center z-50">
      <div class="text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p class="text-gray-600 dark:text-gray-400">加载中...</p>
      </div>
    </div>

    <!-- 路由视图 -->
    <router-view />

    <!-- 全局通知容器 -->
    <div id="notifications" class="fixed top-4 right-4 z-50 space-y-2">
      <!-- Element Plus 通知会自动插入到这里 -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onErrorCaptured, nextTick } from 'vue'

const isLoading = ref(false)

// 应用初始化
onMounted(async () => {
  try {
    // 等待Vue实例完全挂载
    await nextTick()

    // 检查本地存储的token
    const token = localStorage.getItem('token')
    if (token) {
      // 这里可以验证token有效性
      // 暂时跳过API调用，直接设置为已登录状态
      console.log('发现本地token，跳过验证')
    }
  } catch (error) {
    console.error('应用初始化失败:', error)
  }
})

// 全局错误处理
onErrorCaptured((error, _instance, info) => {
  console.error('Vue组件错误:', error, info)

  // 特殊处理Pinia相关错误
  if (error.message && error.message.includes('getActivePinia')) {
    console.warn('Pinia初始化错误，尝试重新加载页面')
    // 可以选择显示用户友好的错误消息
    // ElMessage.error('应用初始化失败，请刷新页面')
  }

  return false // 阻止错误继续传播
})
</script>

<style>
/* 全局样式 */
#app {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 移除页面过渡动画以获得更快的切换速度 */

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 深色模式滚动条 */
.dark ::-webkit-scrollbar-track {
  background: #2d3748;
}

.dark ::-webkit-scrollbar-thumb {
  background: #4a5568;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #718096;
}
</style>
