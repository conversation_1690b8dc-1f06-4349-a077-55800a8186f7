var e=Object.create,t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,o=Object.getPrototypeOf,a=Object.prototype.hasOwnProperty,l=(e,t)=>function(){return t||(0,e[n(e)[0]])((t={exports:{}}).exports,t),t.exports},p=(e,r)=>{for(var n in r)t(e,n,{get:r[n],enumerable:!0})},u=(l,p,u)=>(u=null!=l?e(o(l)):{},((e,o,l,p)=>{if(o&&"object"==typeof o||"function"==typeof o)for(var u,b=n(o),c=0,f=b.length;c<f;c++)a.call(e,u=b[c])||undefined===u||t(e,u,{get:(e=>o[e]).bind(null,u),enumerable:!(p=r(o,u))||p.enumerable});return e})(!p&&l&&l.__esModule?u:t(u,"default",{value:l,enumerable:!0}),l));export{l as b,p as c,u as d};