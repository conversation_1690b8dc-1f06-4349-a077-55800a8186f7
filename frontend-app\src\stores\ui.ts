import { ref } from 'vue'
import { defineStore } from 'pinia'

export const useUIStore = defineStore('ui', () => {
  // 专注模式状态
  const focusMode = ref(false)
  // 专注模式前的知识库面板状态
  const knowledgeBasePanelBeforeFocus = ref(false)

  // 切换专注模式
  const toggleFocusMode = () => {
    focusMode.value = !focusMode.value
  }

  // 设置专注模式
  const setFocusMode = (value: boolean) => {
    focusMode.value = value
  }

  // 保存专注模式前的知识库面板状态
  const saveKnowledgeBasePanelState = (state: boolean) => {
    knowledgeBasePanelBeforeFocus.value = state
  }

  // 获取专注模式前的知识库面板状态
  const getKnowledgeBasePanelState = () => {
    return knowledgeBasePanelBeforeFocus.value
  }

  return {
    focusMode,
    toggleFocusMode,
    setFocusMode,
    saveKnowledgeBasePanelState,
    getKnowledgeBasePanelState
  }
})
