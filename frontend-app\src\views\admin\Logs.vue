<template>
  <div class="space-y-6">
    <!-- 页面头部 -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">
          系统日志
        </h1>
        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
          查看系统操作日志和错误记录，监控系统运行状态
        </p>
      </div>
      <div class="mt-4 sm:mt-0 flex items-center space-x-3">
        <el-button @click="refreshLogs" :loading="loading">
          <el-icon class="mr-2"><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button @click="exportLogs">
          <el-icon class="mr-2"><Download /></el-icon>
          导出日志
        </el-button>
        <el-button @click="clearLogs" type="danger">
          <el-icon class="mr-2"><Delete /></el-icon>
          清空日志
        </el-button>
      </div>
    </div>

    <!-- 筛选和搜索区域 -->
    <div class="card-tech p-6">
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-4">
        <!-- 搜索框 -->
        <div class="lg:col-span-2">
          <el-input
            v-model="searchQuery"
            placeholder="搜索操作者、IP地址、操作内容..."
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <!-- 日志类型筛选 -->
        <div>
          <el-select v-model="typeFilter" placeholder="日志类型" @change="handleFilter">
            <el-option label="全部类型" value="" />
            <el-option label="用户活动" value="user_activity" />
            <el-option label="管理操作" value="admin_action" />
            <el-option label="系统错误" value="system_error" />
          </el-select>
        </div>

        <!-- 时间范围筛选 -->
        <div>
          <el-select v-model="timeFilter" placeholder="时间范围" @change="handleFilter">
            <el-option label="全部时间" value="" />
            <el-option label="最近1小时" value="1h" />
            <el-option label="最近24小时" value="24h" />
            <el-option label="最近7天" value="7d" />
            <el-option label="最近30天" value="30d" />
          </el-select>
        </div>
      </div>

      <!-- 高级筛选 -->
      <div class="mt-4 pt-4 border-t border-gray-200 dark:border-dark-700">
        <el-collapse>
          <el-collapse-item title="高级筛选" name="advanced">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  操作者
                </label>
                <el-input
                  v-model="advancedFilters.operator"
                  placeholder="输入操作者用户名"
                  clearable
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  IP地址
                </label>
                <el-input
                  v-model="advancedFilters.ipAddress"
                  placeholder="输入IP地址"
                  clearable
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  自定义时间范围
                </label>
                <el-date-picker
                  v-model="advancedFilters.dateRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                />
              </div>
            </div>

            <div class="mt-4 flex items-center space-x-3">
              <el-button type="primary" @click="applyAdvancedFilters">
                应用筛选
              </el-button>
              <el-button @click="resetAdvancedFilters">
                重置
              </el-button>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>

    <!-- 日志统计 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <div class="card-tech p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400">总日志数</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {{ logStats.total }}
            </p>
          </div>
          <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
            <el-icon :size="20" class="text-blue-600 dark:text-blue-400">
              <Document />
            </el-icon>
          </div>
        </div>
      </div>

      <div class="card-tech p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400">用户活动</p>
            <p class="text-2xl font-bold text-green-600 dark:text-green-400">
              {{ logStats.userActivity }}
            </p>
          </div>
          <div class="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
            <el-icon :size="20" class="text-green-600 dark:text-green-400">
              <User />
            </el-icon>
          </div>
        </div>
      </div>

      <div class="card-tech p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400">管理操作</p>
            <p class="text-2xl font-bold text-orange-600 dark:text-orange-400">
              {{ logStats.adminAction }}
            </p>
          </div>
          <div class="w-10 h-10 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
            <el-icon :size="20" class="text-orange-600 dark:text-orange-400">
              <Tools />
            </el-icon>
          </div>
        </div>
      </div>

      <div class="card-tech p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400">系统错误</p>
            <p class="text-2xl font-bold text-red-600 dark:text-red-400">
              {{ logStats.systemError }}
            </p>
          </div>
          <div class="w-10 h-10 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center">
            <el-icon :size="20" class="text-red-600 dark:text-red-400">
              <Warning />
            </el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 日志列表 -->
    <div class="card-tech" v-loading="loading">
      <div class="p-6 border-b border-gray-200 dark:border-dark-700">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
            日志记录
          </h3>
          <div class="flex items-center space-x-3">
            <span class="text-sm text-gray-500">
              显示 {{ paginatedLogs.length }} / {{ filteredLogs.length }} 条记录
            </span>
            <el-select v-model="pageSize" size="small" style="width: 100px" @change="handlePageSizeChange">
              <el-option label="20" :value="20" />
              <el-option label="50" :value="50" />
              <el-option label="100" :value="100" />
            </el-select>
          </div>
        </div>
      </div>

      <!-- 日志表格 -->
      <el-table :data="paginatedLogs" style="width: 100%" row-class-name="log-row">
        <el-table-column prop="timestamp" label="时间" width="180">
          <template #default="{ row }">
            <span class="text-sm text-gray-600 dark:text-gray-400">
              {{ formatDateTime(row.timestamp) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="type" label="类型" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="getLogTypeColor(row.type)" size="small">
              {{ getLogTypeText(row.type) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="operator" label="操作者" width="150">
          <template #default="{ row }">
            <div class="flex items-center space-x-2">
              <div class="w-6 h-6 rounded-full flex items-center justify-center"
                   :class="getOperatorIconClass(row.type)">
                <el-icon :size="12">
                  <component :is="getOperatorIcon(row.type)" />
                </el-icon>
              </div>
              <span class="font-medium text-gray-900 dark:text-gray-100">
                {{ row.operator }}
              </span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="action" label="操作内容" min-width="300">
          <template #default="{ row }">
            <div>
              <p class="text-gray-900 dark:text-gray-100">{{ row.action }}</p>
              <p v-if="row.details" class="text-sm text-gray-500 mt-1">
                {{ row.details }}
              </p>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="ipAddress" label="IP地址" width="150">
          <template #default="{ row }">
            <span class="text-sm text-gray-600 dark:text-gray-400 font-mono">
              {{ row.ipAddress }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="100" align="center">
          <template #default="{ row }">
            <el-button size="small" @click="viewLogDetail(row)">
              <el-icon><View /></el-icon>
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div v-if="filteredLogs.length > pageSize" class="flex justify-center">
      <el-pagination
        v-model:current-page="currentPage"
        :page-size="pageSize"
        :total="filteredLogs.length"
        layout="prev, pager, next, jumper, total"
        @current-change="handlePageChange"
      />
    </div>

    <!-- 日志详情对话框 -->
    <el-dialog
      v-model="showLogDetail"
      title="日志详情"
      width="600px"
    >
      <div v-if="selectedLog" class="space-y-4">
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              时间
            </label>
            <p class="text-gray-900 dark:text-gray-100">
              {{ formatDateTime(selectedLog.timestamp) }}
            </p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              类型
            </label>
            <el-tag :type="getLogTypeColor(selectedLog.type)" size="small">
              {{ getLogTypeText(selectedLog.type) }}
            </el-tag>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              操作者
            </label>
            <p class="text-gray-900 dark:text-gray-100">
              {{ selectedLog.operator }}
            </p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              IP地址
            </label>
            <p class="text-gray-900 dark:text-gray-100 font-mono">
              {{ selectedLog.ipAddress }}
            </p>
          </div>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            操作内容
          </label>
          <p class="text-gray-900 dark:text-gray-100">
            {{ selectedLog.action }}
          </p>
        </div>

        <div v-if="selectedLog.details">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            详细信息
          </label>
          <div class="bg-gray-50 dark:bg-dark-700 rounded-lg p-3">
            <pre class="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">{{ selectedLog.details }}</pre>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { SystemLog } from '@/types'
import {
  Refresh,
  Download,
  Delete,
  Search,
  Document,
  User,
  Tools,
  Warning,
  View,
  Setting
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const searchQuery = ref('')
const typeFilter = ref('')
const timeFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const showLogDetail = ref(false)
const selectedLog = ref<SystemLog | null>(null)

// 高级筛选
const advancedFilters = ref({
  operator: '',
  ipAddress: '',
  dateRange: null as [string, string] | null
})

// 日志统计
const logStats = ref({
  total: 1247,
  userActivity: 856,
  adminAction: 234,
  systemError: 157
})

// 模拟日志数据
const logs = ref<SystemLog[]>([
  {
    id: 1,
    type: 'user_activity',
    operator: 'user123',
    ipAddress: '***********00',
    action: '创建了新的知识库"技术文档"',
    details: '知识库ID: 123\n描述: 包含各种技术文档和API参考',
    timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString()
  },
  {
    id: 2,
    type: 'admin_action',
    operator: 'admin',
    ipAddress: '***********',
    action: '更新了系统配置',
    details: '修改了AI模型设置\n启用了GPT-4模型\n禁用了Claude-3模型',
    timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString()
  },
  {
    id: 3,
    type: 'system_error',
    operator: 'system',
    ipAddress: '127.0.0.1',
    action: '文档处理失败',
    details: 'Error: Unsupported file format\nFile: document.xyz\nSize: 2.5MB\nUser: user456',
    timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString()
  },
  {
    id: 4,
    type: 'user_activity',
    operator: 'user456',
    ipAddress: '*************',
    action: '上传了5个文档',
    details: '知识库: 产品需求\n文档列表:\n- 需求文档1.pdf\n- 需求文档2.docx\n- 流程图.png\n- 原型设计.fig\n- 测试用例.xlsx',
    timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString()
  },
  {
    id: 5,
    type: 'admin_action',
    operator: 'admin',
    ipAddress: '***********',
    action: '创建了新用户账户',
    details: '用户名: newuser\n邮箱: <EMAIL>\n角色: user\n存储配额: 10GB',
    timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString()
  },
  {
    id: 6,
    type: 'user_activity',
    operator: 'user789',
    ipAddress: '*************',
    action: '开始了AI对话会话',
    details: '模型: GPT-4\n知识库: 技术文档, 产品需求\n会话ID: 789',
    timestamp: new Date(Date.now() - 75 * 60 * 1000).toISOString()
  },
  {
    id: 7,
    type: 'system_error',
    operator: 'system',
    ipAddress: '127.0.0.1',
    action: 'API调用超时',
    details: 'Endpoint: /api/ai/chat\nTimeout: 30s\nUser: user123\nModel: GPT-4',
    timestamp: new Date(Date.now() - 90 * 60 * 1000).toISOString()
  },
  {
    id: 8,
    type: 'admin_action',
    operator: 'admin',
    ipAddress: '***********',
    action: '执行了数据库优化',
    details: '优化类型: 索引重建\n耗时: 45秒\n优化表: users, knowledge_bases, documents',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
  }
])

// 计算属性
const filteredLogs = computed(() => {
  let filtered = logs.value

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(log =>
      log.operator.toLowerCase().includes(query) ||
      log.action.toLowerCase().includes(query) ||
      log.ipAddress.includes(query) ||
      (log.details && log.details.toLowerCase().includes(query))
    )
  }

  // 类型过滤
  if (typeFilter.value) {
    filtered = filtered.filter(log => log.type === typeFilter.value)
  }

  // 时间过滤
  if (timeFilter.value) {
    const now = new Date()
    let cutoffTime: Date

    switch (timeFilter.value) {
      case '1h':
        cutoffTime = new Date(now.getTime() - 60 * 60 * 1000)
        break
      case '24h':
        cutoffTime = new Date(now.getTime() - 24 * 60 * 60 * 1000)
        break
      case '7d':
        cutoffTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30d':
        cutoffTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      default:
        cutoffTime = new Date(0)
    }

    filtered = filtered.filter(log => new Date(log.timestamp) >= cutoffTime)
  }

  // 高级筛选
  if (advancedFilters.value.operator) {
    filtered = filtered.filter(log =>
      log.operator.toLowerCase().includes(advancedFilters.value.operator.toLowerCase())
    )
  }

  if (advancedFilters.value.ipAddress) {
    filtered = filtered.filter(log =>
      log.ipAddress.includes(advancedFilters.value.ipAddress)
    )
  }

  if (advancedFilters.value.dateRange) {
    const [start, end] = advancedFilters.value.dateRange
    filtered = filtered.filter(log => {
      const logTime = new Date(log.timestamp)
      return logTime >= new Date(start) && logTime <= new Date(end)
    })
  }

  // 按时间倒序排列
  return filtered.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
})

const paginatedLogs = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredLogs.value.slice(start, end)
})

// 工具方法
const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const getLogTypeColor = (type: string) => {
  switch (type) {
    case 'user_activity':
      return 'primary'
    case 'admin_action':
      return 'warning'
    case 'system_error':
      return 'danger'
    default:
      return 'info'
  }
}

const getLogTypeText = (type: string) => {
  switch (type) {
    case 'user_activity':
      return '用户活动'
    case 'admin_action':
      return '管理操作'
    case 'system_error':
      return '系统错误'
    default:
      return '未知'
  }
}

// 图标映射
const iconMap: Record<string, any> = {
  User,
  Setting,
  Warning,
  Document
}

const getOperatorIcon = (type: string) => {
  switch (type) {
    case 'user_activity':
      return iconMap.User
    case 'admin_action':
      return iconMap.Setting
    case 'system_error':
      return iconMap.Warning
    default:
      return iconMap.Document
  }
}

const getOperatorIconClass = (type: string) => {
  switch (type) {
    case 'user_activity':
      return 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
    case 'admin_action':
      return 'bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-400'
    case 'system_error':
      return 'bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400'
    default:
      return 'bg-gray-100 dark:bg-gray-900 text-gray-600 dark:text-gray-400'
  }
}

// 事件处理方法
const handleSearch = () => {
  currentPage.value = 1
}

const handleFilter = () => {
  currentPage.value = 1
}

const handlePageChange = (page: number) => {
  currentPage.value = page
}

const handlePageSizeChange = () => {
  currentPage.value = 1
}

const applyAdvancedFilters = () => {
  currentPage.value = 1
}

const resetAdvancedFilters = () => {
  advancedFilters.value = {
    operator: '',
    ipAddress: '',
    dateRange: null
  }
  currentPage.value = 1
}

// 操作方法
const refreshLogs = async () => {
  loading.value = true
  try {
    // 这里应该调用API刷新日志数据
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('日志已刷新')
  } catch (error) {
    ElMessage.error('刷新失败')
  } finally {
    loading.value = false
  }
}

const exportLogs = async () => {
  try {
    const exportData = {
      timestamp: new Date().toISOString(),
      filters: {
        search: searchQuery.value,
        type: typeFilter.value,
        time: timeFilter.value,
        advanced: advancedFilters.value
      },
      logs: filteredLogs.value
    }

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `system-logs-${new Date().toISOString().split('T')[0]}.json`
    a.click()
    URL.revokeObjectURL(url)

    ElMessage.success('日志导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const clearLogs = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有日志吗？此操作不可恢复。',
      '确认清空',
      {
        confirmButtonText: '清空',
        cancelButtonText: '取消',
        type: 'error',
        confirmButtonClass: 'el-button--danger'
      }
    )

    logs.value = []
    logStats.value = {
      total: 0,
      userActivity: 0,
      adminAction: 0,
      systemError: 0
    }

    ElMessage.success('日志已清空')
  } catch (error) {
    // 用户取消操作
  }
}

const viewLogDetail = (log: SystemLog) => {
  selectedLog.value = log
  showLogDetail.value = true
}

onMounted(() => {
  // 这里可以加载实际的日志数据
  // loadLogs()
})
</script>
