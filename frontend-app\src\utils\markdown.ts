import { marked } from 'marked'
import hljs from 'highlight.js'
// 使用适合暗色模式的主题
import 'highlight.js/styles/atom-one-dark.css'

// 配置marked
marked.setOptions({
  highlight: function(code, lang) {
    const codeStr = String(code || '')
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(codeStr, { language: lang }).value
      } catch (err) {
        console.error('代码高亮失败:', err)
      }
    }
    return hljs.highlightAuto(codeStr).value
  },
  breaks: true,
  gfm: true,
  tables: true  // 确保启用表格支持
})

// 自定义渲染器
const renderer = new marked.Renderer()

// 自定义代码块渲染
renderer.code = function(token) {
  // 从token对象中提取属性
  const codeStr = String(token.text || '')
  const language = token.lang || ''

  const validLang = language && hljs.getLanguage(language) ? language : 'plaintext'
  const highlightedCode = hljs.highlight(codeStr, { language: validLang }).value

  // 检查是否为可执行的代码类型
  const executableLanguages = ['html', 'javascript', 'js', 'python', 'echarts', 'chart', 'json']
  const isExecutable = executableLanguages.includes(validLang.toLowerCase()) ||
                      codeStr.includes('echarts') ||
                      codeStr.includes('Chart.js') ||
                      codeStr.includes('option') ||
                      codeStr.includes('plt.') ||
                      codeStr.includes('matplotlib')

  // 生成唯一ID
  const codeId = 'code_' + Math.random().toString(36).substr(2, 9)

  return `
    <div class="code-block-container relative group rounded-xl overflow-hidden border border-gray-200 dark:border-gray-700 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 shadow-lg" data-code-id="${codeId}">
      <div class="code-block-header flex items-center justify-between bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 px-4 py-3 border-b border-gray-200 dark:border-gray-600">
        <div class="flex items-center space-x-2">
          <div class="flex space-x-1">
            <div class="w-3 h-3 rounded-full bg-red-400"></div>
            <div class="w-3 h-3 rounded-full bg-yellow-400"></div>
            <div class="w-3 h-3 rounded-full bg-green-400"></div>
          </div>
          <span class="text-sm font-medium text-gray-600 dark:text-gray-300">${validLang}</span>
        </div>
        <div class="flex items-center space-x-2">
          ${isExecutable ? `
          <button
            class="run-code-btn transition-all duration-200 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white px-3 py-1.5 rounded-lg text-xs font-medium shadow-md hover:shadow-lg transform hover:scale-105"
            onclick="runCode('${codeId}', '${validLang}')"
            data-code="${encodeURIComponent(codeStr)}"
            title="运行代码"
          >
            <svg class="w-3 h-3 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m6-10V7a3 3 0 11-6 0V4a3 3 0 11-6 0v3a3 3 0 11-6 0v3"></path>
            </svg>
            运行
          </button>
          ` : ''}
          <button
            class="copy-code-btn transition-all duration-200 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-3 py-1.5 rounded-lg text-xs font-medium shadow-md hover:shadow-lg transform hover:scale-105"
            onclick="copyCode(this)"
            data-code="${encodeURIComponent(codeStr)}"
            title="复制代码"
          >
            <svg class="w-3 h-3 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
            </svg>
            复制
          </button>
        </div>
      </div>
      <div class="relative">
        <pre class="hljs p-6 overflow-x-auto text-sm leading-relaxed bg-transparent"><code class="language-${validLang}">${highlightedCode}</code></pre>
        <div class="absolute bottom-3 right-3 bg-black/20 dark:bg-white/10 backdrop-blur-sm px-2 py-1 rounded-md">
          <span class="text-xs font-mono text-gray-600 dark:text-gray-300">${validLang}</span>
        </div>
      </div>
      <!-- 运行结果容器 -->
      <div id="result_${codeId}" class="code-result hidden border-t border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-800/50"></div>
    </div>
  `
}

// 自定义行内代码渲染
renderer.codespan = function(token) {
  const codeStr = String(token.text || '')
  return `<code class="inline-code bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/30 dark:to-purple-900/30 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-md text-sm font-mono border border-blue-200 dark:border-blue-700/50">${codeStr}</code>`
}

// 自定义链接渲染
renderer.link = function(token) {
  const hrefStr = String(token.href || '')
  const titleStr = String(token.title || '')
  const textStr = this.parser.parseInline(token.tokens || [])
  return `<a href="${hrefStr}" title="${titleStr}" target="_blank" rel="noopener noreferrer" class="text-blue-600 dark:text-blue-400 hover:text-purple-600 dark:hover:text-purple-400 transition-colors duration-200 underline decoration-2 underline-offset-2 hover:decoration-purple-500">${textStr}</a>`
}

// 禁用自定义表格渲染，使用默认渲染器
// 这样可以避免API兼容性问题，让marked使用默认的表格渲染
// 我们通过CSS来美化表格样式

marked.use({ renderer })

// 添加简洁的表格样式
const tableStyle = document.createElement('style')
tableStyle.textContent = `
  /* 简洁的Markdown表格样式 */
  .markdown-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 16px 0;
    border: 1px solid #d1d5db;
    background: white;
  }

  .markdown-content table th {
    background: #f9fafb;
    color: #374151;
    padding: 12px 16px;
    text-align: left;
    font-weight: 600;
    font-size: 14px;
    border: 1px solid #d1d5db;
    border-bottom: 2px solid #9ca3af;
  }

  .markdown-content table td {
    padding: 12px 16px;
    border: 1px solid #d1d5db;
    vertical-align: top;
    font-size: 14px;
    line-height: 1.5;
    max-width: 300px;
    word-wrap: break-word;
  }

  .markdown-content table tr:nth-child(even) {
    background-color: #f9fafb;
  }

  /* 表格内的代码块样式 */
  .markdown-content table code {
    background: #f3f4f6;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #374151;
    border: 1px solid #e5e7eb;
  }

  .markdown-content table pre {
    background: #f9fafb;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    padding: 8px;
    margin: 4px 0;
    overflow-x: auto;
    font-size: 12px;
    line-height: 1.4;
    max-width: 280px;
  }

  .markdown-content table pre code {
    background: transparent;
    padding: 0;
    border: none;
  }

  /* 响应式表格 */
  @media (max-width: 768px) {
    .markdown-content table {
      font-size: 12px;
    }

    .markdown-content table th,
    .markdown-content table td {
      padding: 8px 12px;
    }
  }
`
if (!document.head.querySelector('#markdown-table-style')) {
  tableStyle.id = 'markdown-table-style'
  document.head.appendChild(tableStyle)
}

/**
 * 预处理文本，提取被错误包裹在代码块中的表格
 */
function preprocessMarkdown(text: string): string {
  // 更精确的正则表达式，匹配包含表格的代码块
  const codeBlockRegex = /```(?:markdown|table|)\s*\n([\s\S]*?)\n```/g

  return text.replace(codeBlockRegex, (match, content) => {
    const trimmedContent = content.trim()

    // 检查是否确实是表格格式
    const lines = trimmedContent.split('\n').filter((line: string) => line.trim())

    // 更严格的表格完整性检查
    const hasTableHeaders = lines.some((line: string) => line.includes('|'))
    const hasSeparatorLine = lines.some((line: string) => /^\s*\|?[-\s\|:]+\|?\s*$/.test(line))
    const hasTableRows = lines.filter((line: string) => line.includes('|')).length >= 2

    // 检查表格是否完整（至少有表头、分隔符、一行数据）
    const isCompleteTable = hasTableHeaders && hasSeparatorLine && hasTableRows

    // 额外检查：确保不是流式渲染中的不完整表格
    const endsWithIncompleteRow = trimmedContent.endsWith('|') && !trimmedContent.endsWith('|\n')

    if (isCompleteTable && !endsWithIncompleteRow) {
      console.log('预处理：提取被代码块包裹的完整表格')
      return '\n\n' + trimmedContent + '\n\n'
    }

    // 如果不是完整表格，保持原样（避免流式渲染中的闪烁）
    return match
  })
}

/**
 * 渲染markdown文本为HTML (同步版本)
 */
export function renderMarkdown(text: string): string {
  try {
    // 预处理：提取被错误包裹的表格
    const preprocessedText = preprocessMarkdown(text)

    // 使用marked的同步API
    const result = marked.parse(preprocessedText, { async: false })
    return typeof result === 'string' ? result : String(result)
  } catch (error) {
    console.error('Markdown渲染失败:', error)
    console.error('输入文本:', text)
    return `<pre>${text}</pre>`
  }
}

/**
 * 渲染markdown文本为HTML (异步版本)
 */
export async function renderMarkdownAsync(text: string): Promise<string> {
  try {
    const result = await marked.parse(text)
    return typeof result === 'string' ? result : String(result)
  } catch (error) {
    console.error('Markdown渲染失败:', error)
    console.error('输入文本:', text)
    return `<pre>${text}</pre>`
  }
}

// 全局函数声明
declare global {
  interface Window {
    copyCode: (button: HTMLButtonElement) => void
    runCode: (codeId: string, language: string) => void
    echarts?: any
  }
}

window.copyCode = function(button: HTMLButtonElement) {
  const code = decodeURIComponent(button.getAttribute('data-code') || '')
  navigator.clipboard.writeText(code).then(() => {
    const originalText = button.innerHTML
    button.innerHTML = `
      <svg class="w-3 h-3 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
      </svg>
      已复制
    `
    button.style.background = 'linear-gradient(135deg, #10b981, #059669)'

    setTimeout(() => {
      button.innerHTML = originalText
      button.style.background = 'linear-gradient(135deg, #3b82f6, #8b5cf6)'
    }, 2000)
  }).catch(err => {
    console.error('复制失败:', err)
    button.innerHTML = '复制失败'
    setTimeout(() => {
      button.innerHTML = `
        <svg class="w-3 h-3 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
        </svg>
        复制
      `
    }, 2000)
  })
}

// 运行代码函数
window.runCode = function(codeId: string, language: string) {
  const codeContainer = document.querySelector(`[data-code-id="${codeId}"]`)
  const runButton = codeContainer?.querySelector('.run-code-btn') as HTMLButtonElement
  const resultContainer = document.getElementById(`result_${codeId}`)

  if (!codeContainer || !runButton || !resultContainer) {
    console.error('找不到代码容器或结果容器')
    return
  }

  const code = decodeURIComponent(runButton.getAttribute('data-code') || '')

  // 显示加载状态
  const originalText = runButton.innerHTML
  runButton.innerHTML = `
    <svg class="w-3 h-3 mr-1 inline animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
    </svg>
    运行中...
  `
  runButton.disabled = true

  try {
    // 根据语言类型执行不同的处理
    if (language.toLowerCase() === 'html' || code.includes('<html') || code.includes('<!DOCTYPE')) {
      executeHtmlCode(code, resultContainer)
    } else if (language.toLowerCase() === 'javascript' || language.toLowerCase() === 'js') {
      executeJavaScriptCode(code, resultContainer)
    } else if (language.toLowerCase() === 'python' || code.includes('plt.') || code.includes('matplotlib')) {
      executePythonCode(code, resultContainer)
    } else if (language.toLowerCase() === 'json' || language.toLowerCase() === 'echarts' || code.includes('option')) {
      executeChartCode(code, resultContainer)
    } else {
      showResult(resultContainer, '不支持的代码类型', 'error')
    }

    // 显示结果容器
    resultContainer.classList.remove('hidden')

  } catch (error) {
    console.error('代码执行失败:', error)
    showResult(resultContainer, `执行失败: ${error}`, 'error')
    resultContainer.classList.remove('hidden')
  } finally {
    // 恢复按钮状态
    setTimeout(() => {
      runButton.innerHTML = originalText
      runButton.disabled = false
    }, 1000)
  }
}

/**
 * 复制代码到剪贴板
 */
export function copyCode(button: HTMLButtonElement) {
  const code = decodeURIComponent(button.dataset.code || '')
  
  if (navigator.clipboard) {
    navigator.clipboard.writeText(code).then(() => {
      // 显示复制成功提示
      const originalText = button.textContent
      button.textContent = '已复制!'
      button.classList.add('bg-green-500')
      button.classList.remove('bg-blue-500')
      
      setTimeout(() => {
        button.textContent = originalText
        button.classList.remove('bg-green-500')
        button.classList.add('bg-blue-500')
      }, 2000)
    }).catch(err => {
      console.error('复制失败:', err)
      fallbackCopyTextToClipboard(code)
    })
  } else {
    fallbackCopyTextToClipboard(code)
  }
}

/**
 * 降级复制方法
 */
function fallbackCopyTextToClipboard(text: string) {
  const textArea = document.createElement('textarea')
  textArea.value = text
  textArea.style.position = 'fixed'
  textArea.style.left = '-999999px'
  textArea.style.top = '-999999px'
  document.body.appendChild(textArea)
  textArea.focus()
  textArea.select()
  
  try {
    document.execCommand('copy')
    // 移除console.log
  } catch (err) {
    // 移除console.error
  }
  
  document.body.removeChild(textArea)
}

// 代码执行辅助函数
function executeHtmlCode(code: string, resultContainer: HTMLElement) {
  const iframe = document.createElement('iframe')
  iframe.style.width = '100%'
  iframe.style.height = '400px'
  iframe.style.border = '1px solid #e5e7eb'
  iframe.style.borderRadius = '8px'
  iframe.style.background = 'white'

  // 使用更安全的沙箱设置，只允许脚本执行，不允许同源访问
  iframe.sandbox.add('allow-scripts')

  showResult(resultContainer, iframe, 'success')

  // 修复CDN链接问题
  let fixedCode = code
    .replace(/src="https:\/\/cdn\.jsdelivr\.net\/npm\/chart\.js"/g, 'src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"')
    .replace(/src="https:\/\/cdn\.jsdelivr\.net\/npm\/echarts"/g, 'src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"')

  // 使用srcdoc属性而不是document.write，更安全
  iframe.srcdoc = fixedCode
}

function executeJavaScriptCode(code: string, resultContainer: HTMLElement) {
  try {
    // 创建一个安全的执行环境
    const result = new Function(code)()
    showResult(resultContainer, `执行结果: ${JSON.stringify(result, null, 2)}`, 'success')
  } catch (error) {
    showResult(resultContainer, `JavaScript执行错误: ${error}`, 'error')
  }
}

function executePythonCode(code: string, resultContainer: HTMLElement) {
  // Python代码暂时显示提示信息
  showResult(resultContainer, 'Python代码执行需要后端支持，当前仅显示代码内容。', 'info')
}

async function executeChartCode(code: string, resultContainer: HTMLElement) {
  try {
    // 预处理代码，替换常见的占位符
    let processedCode = code
      .replace(/XX/g, '0')  // 替换 XX 为 0
      .replace(/\[XX,\s*XX,\s*XX,\s*XX,\s*XX\]/g, '[85, 90, 78, 92, 88]')  // 替换数组中的XX
      .replace(/\[XX,\s*XX,\s*XX\]/g, '[75, 80, 85]')  // 替换3个XX的数组
      .replace(/\[XX,\s*XX\]/g, '[70, 80]')  // 替换2个XX的数组
      .replace(/\{\s*XX\s*\}/g, '{}')  // 替换对象中的XX
      .replace(/"\s*XX\s*"/g, '"示例数据"')  // 替换字符串中的XX

    // 尝试解析为图表配置
    let config
    try {
      config = JSON.parse(processedCode)
    } catch (jsonError) {
      try {
        config = new Function('return ' + processedCode)()
      } catch (funcError) {
        console.error('JSON解析错误:', jsonError)
        console.error('Function解析错误:', funcError)
        console.error('原始代码:', code)
        console.error('处理后代码:', processedCode)
        throw new Error(`无法解析图表配置: JSON错误(${(jsonError as Error).message}), Function错误(${(funcError as Error).message})`)
      }
    }

    // 验证配置对象
    if (!config || typeof config !== 'object') {
      throw new Error('图表配置必须是一个对象')
    }

    // 检测图表类型并验证
    const isEChartsConfig = config.series || config.dataset
    const isChartJSConfig = config.type && config.data

    if (!isEChartsConfig && !isChartJSConfig) {
      throw new Error('图表配置必须包含ECharts格式(series/dataset)或Chart.js格式(type/data)的必要字段')
    }

    // 创建图表容器
    const chartContainer = document.createElement('div')
    chartContainer.style.width = '100%'
    chartContainer.style.minHeight = '300px'
    chartContainer.style.height = 'auto'
    chartContainer.style.aspectRatio = '16/9' // 设置宽高比，保持图表比例
    chartContainer.style.maxHeight = '600px' // 设置最大高度，避免过高
    chartContainer.style.backgroundColor = 'white' // 设置白色背景
    chartContainer.style.borderRadius = '8px' // 添加圆角
    chartContainer.style.padding = '10px' // 添加内边距
    chartContainer.id = 'chart_' + Math.random().toString(36).substring(2, 11)

    showResult(resultContainer, chartContainer, 'success')

    // 根据配置类型选择合适的图表库
    try {
      if (isEChartsConfig) {
        // 使用ECharts渲染
        const echarts = await import('echarts')
        const chart = echarts.init(chartContainer)

        // 确保配置中有白色背景
        const chartConfig = {
          backgroundColor: 'white',
          ...config
        }

        // 安全地设置配置，添加错误处理
        chart.setOption(chartConfig, true) // 第二个参数true表示不合并配置

        // 添加窗口大小变化监听
        const resizeHandler = () => chart.resize()
        window.addEventListener('resize', resizeHandler)

        // 清理函数（可选）
        setTimeout(() => {
          window.removeEventListener('resize', resizeHandler)
        }, 30000) // 30秒后移除监听器

      } else if (isChartJSConfig) {
        // 使用Chart.js渲染
        const canvas = document.createElement('canvas')
        chartContainer.appendChild(canvas)

        // 为Chart.js设置特定的容器样式
        chartContainer.style.position = 'relative'
        chartContainer.style.height = '400px' // Chart.js需要明确的高度

        const { Chart, registerables } = await import('chart.js')
        Chart.register(...registerables)

        // 确保Chart.js配置包含响应式设置和白色背景
        const chartConfig = {
          ...config,
          options: {
            responsive: true,
            maintainAspectRatio: false, // 允许图表填满容器
            plugins: {
              ...config.options?.plugins,
              legend: {
                ...config.options?.plugins?.legend
              }
            },
            backgroundColor: 'white', // 设置白色背景
            ...config.options
          }
        }

        new Chart(canvas, chartConfig)
      }

    } catch (importError: any) {
      console.error('图表渲染失败:', importError)
      const errorMessage = importError?.message || '未知错误'
      chartContainer.innerHTML = `<div class="p-4 text-center text-gray-500">图表渲染失败: ${errorMessage}</div>`
    }
  } catch (error: any) {
    console.error('图表配置错误:', error)
    const errorMessage = error?.message || '未知错误'

    // 当图表解析失败时，显示原始代码
    const codeElement = document.createElement('pre')
    codeElement.className = 'bg-gray-100 p-4 rounded text-sm overflow-auto'
    codeElement.textContent = code

    const errorDiv = document.createElement('div')
    errorDiv.className = 'space-y-2'
    errorDiv.innerHTML = `
      <div class="text-red-600 font-medium">图表配置解析错误: ${errorMessage}</div>
      <div class="text-gray-600 text-sm">原始代码:</div>
    `
    errorDiv.appendChild(codeElement)

    showResult(resultContainer, errorDiv, 'error')
  }
}

function showResult(container: HTMLElement, content: any, type: 'success' | 'error' | 'info') {
  const colors = {
    success: 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-200',
    error: 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200',
    info: 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200'
  }

  container.innerHTML = ''
  container.className = `code-result border-t border-gray-200 dark:border-gray-600 p-4 ${colors[type]}`

  if (typeof content === 'string') {
    container.innerHTML = `<pre class="whitespace-pre-wrap font-mono text-sm">${content}</pre>`
  } else {
    container.appendChild(content)
  }
}

/**
 * 复制整个消息内容
 */
export function copyMessage(content: string) {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(content).then(() => {
      // 移除console.log
    }).catch(() => {
      // 移除console.error
      fallbackCopyTextToClipboard(content)
    })
  } else {
    fallbackCopyTextToClipboard(content)
  }
}

// 将copyCode函数挂载到全局，供HTML中的onclick使用
if (typeof window !== 'undefined') {
  (window as any).copyCode = copyCode
}
