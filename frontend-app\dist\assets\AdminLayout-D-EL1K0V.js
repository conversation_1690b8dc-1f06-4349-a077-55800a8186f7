var e;(()=>{function t(e,t,a,r,s,l,o){try{var n=e[l](o),d=n.value}catch(e){return void a(e)}n.done?t(d):Promise.resolve(d).then(r,s)}e=function(e){return function(){var a=this,r=arguments;return new Promise(function(s,l){var o=e.apply(a,r);function n(e){t(o,s,l,n,d,"next",e)}function d(e){t(o,s,l,n,d,"throw",e)}n(void 0)})}}})();import{E as t,J as a,K as r,N as s,q as l,r as o,s as n}from"./elementPlus-Di4PDIm8.js";import{bB as d,bE as i,bG as u,bT as c,bU as m,b_ as f,bx as v,c1 as g,c4 as x,c5 as y,cT as p,cU as h,ca as b,cd as _,d8 as k,dB as w,dD as j,dF as z,dH as C,dN as D,dU as U,d_ as A,dc as B,dd as I,de as S,df as E,dg as P,dj as T,dk as L,dl as N,ea as $,ed as q}from"./vendor-BJ-uKP15.js";import{b as F}from"./index-Byt5TjPh.js";import"./api-D-gMiCJf.js";import{b as G}from"./settings-46b1LTsi.js";import{b as H}from"./_plugin-vue_export-helper-CjD0mXop.js";const J={class:"h-screen flex bg-gray-50 dark:bg-gray-900 overflow-hidden"},K={class:"flex items-center justify-between h-16 px-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0"},M={key:0,class:"flex items-center"},O={class:"flex-1 px-2 py-4 space-y-1 overflow-y-auto"},Q={key:0,class:"truncate"},R={class:"border-t border-gray-200 dark:border-gray-700 p-4 flex-shrink-0"},V={class:"flex items-center"},W={key:0,class:"ml-3 flex-1 min-w-0"},X={class:"text-sm font-medium text-gray-900 dark:text-white truncate"},Y={class:"flex-1 flex flex-col min-w-0"},Z={class:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 flex-shrink-0"},ee={class:"px-6 py-4"},te={class:"flex justify-between items-center"},ae={class:"text-lg font-semibold text-gray-900 dark:text-gray-100"},re={class:"text-sm text-gray-600 dark:text-gray-400"},se={class:"flex items-center space-x-4"},le={class:"flex-1 overflow-auto"},oe={class:"p-6"};var ne=H(N({__name:"AdminLayout",setup(N){const H=h(),ne=p(),de=F(),ie=G(),ue=U(!1),ce=()=>{ue.value=!ue.value},me=B(()=>document.documentElement.classList.contains("dark")),fe=(ve=e(function*(){const e=ie.userSettings.theme;let t;t="light"===e?"dark":"dark"===e?"system":"light",yield ie.applyTheme(t)}),function(){return ve.apply(this,arguments)});var ve;const ge={DataBoard:v,User:b,Setting:g,Document:d},xe=[{name:"仪表盘",to:"/admin/dashboard",icon:"DataBoard",title:"系统概览",description:"查看系统整体运行状态和关键指标"},{name:"用户管理",to:"/admin/users",icon:"User",title:"用户管理",description:"管理平台用户，调整权限和配额"},{name:"AI模型管理",to:"/admin/ai-models",icon:"Setting",title:"AI模型管理",description:"配置AI模型参数和系统密钥"},{name:"操作日志",to:"/admin/operation-logs",icon:"Document",title:"操作日志",description:"查看管理员操作记录和审计日志"},{name:"系统设置",to:"/admin/settings",icon:"Setting",title:"系统设置",description:"配置系统参数和全局设置"},{name:"系统日志",to:"/admin/logs",icon:"Document",title:"系统日志",description:"查看系统运行日志和错误记录"}],ye=B(()=>xe.find(e=>e.to===ne.path)||{title:"管理后台",description:"系统管理控制台"}),pe=B(()=>ye.value.title),he=B(()=>ye.value.description),be=e=>{switch(e){case"user-view":H.push("/user/home");break;case"logout":_e()}},_e=(ke=e(function*(){yield de.logout(),H.push("/login")}),function(){return ke.apply(this,arguments)});var ke;const we=()=>{window.location.reload()};return(e,v)=>{var g,p,h;const b=s,U=t,B=r,N=z("router-link"),F=a,G=o,H=n,ne=l,ie=z("router-view");return w(),P("div",J,[I("div",{class:$(["bg-white dark:bg-gray-800 shadow-lg transition-all duration-300 ease-in-out flex flex-col border-r border-gray-200 dark:border-gray-700",ue.value?"w-16":"w-64"])},[I("div",K,[ue.value?E("",!0):(w(),P("div",M,v[0]||(v[0]=[I("h1",{class:"text-xl font-bold text-gray-900 dark:text-white"}," 管理后台 ",-1)]))),L(U,{onClick:ce,size:"small",text:"",class:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"},{default:D(()=>[L(b,null,{default:D(()=>[ue.value?(w(),S(A(i),{key:0})):(w(),S(A(u),{key:1}))]),_:1})]),_:1})]),I("nav",O,[(w(),P(k,null,j(xe,t=>L(N,{key:t.to,to:t.to,class:$(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 relative",[e.$route.path===t.to?"bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200":"text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white"]])},{default:D(()=>{return[(w(),S(C((a=t.icon,ge[a]||d)),{class:$(["flex-shrink-0 h-5 w-5",[ue.value?"mx-auto":"mr-3",e.$route.path===t.to?"text-blue-500 dark:text-blue-300":"text-gray-400 group-hover:text-gray-500 dark:text-gray-400 dark:group-hover:text-gray-300"]])},null,8,["class"])),ue.value?E("",!0):(w(),P("span",Q,q(t.name),1)),ue.value?(w(),S(B,{key:1,content:t.name,placement:"right","show-after":500},{default:D(()=>v[1]||(v[1]=[I("div",{class:"absolute inset-0"},null,-1)])),_:2,__:[1]},1032,["content"])):E("",!0)];var a}),_:2},1032,["to","class"])),64))]),I("div",R,[I("div",V,[L(F,{size:32,src:null===(g=A(de).user)||void 0===g?void 0:g.avatar_url},{default:D(()=>{var e;return[T(q(null===(e=A(de).user)||void 0===e||null===(e=e.username)||void 0===e?void 0:e.charAt(0).toUpperCase()),1)]}),_:1},8,["src"]),ue.value?E("",!0):(w(),P("div",W,[I("div",X,q((null===(p=A(de).user)||void 0===p?void 0:p.display_name)||(null===(h=A(de).user)||void 0===h?void 0:h.username)),1),v[2]||(v[2]=I("div",{class:"text-xs text-gray-500 dark:text-gray-400"}," 管理员 ",-1))])),ue.value?(w(),S(B,{key:2,content:"更多选项",placement:"right"},{default:D(()=>[L(ne,{onCommand:be},{dropdown:D(()=>[L(H,null,{default:D(()=>[L(G,{command:"user-view"},{default:D(()=>[L(b,null,{default:D(()=>[L(A(_))]),_:1}),v[5]||(v[5]=T(" 用户视图 ",-1))]),_:1,__:[5]}),L(G,{command:"logout"},{default:D(()=>[L(b,null,{default:D(()=>[L(A(y))]),_:1}),v[6]||(v[6]=T(" 退出登录 ",-1))]),_:1,__:[6]})]),_:1})]),default:D(()=>[L(U,{size:"small",text:""},{default:D(()=>[L(b,null,{default:D(()=>[L(A(m))]),_:1})]),_:1})]),_:1})]),_:1})):(w(),S(ne,{key:1,onCommand:be},{dropdown:D(()=>[L(H,null,{default:D(()=>[L(G,{command:"user-view"},{default:D(()=>[L(b,null,{default:D(()=>[L(A(_))]),_:1}),v[3]||(v[3]=T(" 用户视图 ",-1))]),_:1,__:[3]}),L(G,{command:"logout"},{default:D(()=>[L(b,null,{default:D(()=>[L(A(y))]),_:1}),v[4]||(v[4]=T(" 退出登录 ",-1))]),_:1,__:[4]})]),_:1})]),default:D(()=>[L(U,{size:"small",text:""},{default:D(()=>[L(b,null,{default:D(()=>[L(A(m))]),_:1})]),_:1})]),_:1}))])])],2),I("div",Y,[I("header",Z,[I("div",ee,[I("div",te,[I("div",null,[I("h2",ae,q(pe.value),1),I("p",re,q(he.value),1)]),I("div",se,[L(U,{onClick:we,size:"small"},{default:D(()=>[L(b,null,{default:D(()=>[L(A(f))]),_:1}),v[7]||(v[7]=T(" 刷新 ",-1))]),_:1,__:[7]}),L(U,{onClick:fe,size:"small",circle:""},{default:D(()=>[L(b,null,{default:D(()=>[me.value?(w(),S(A(x),{key:0})):(w(),S(A(c),{key:1}))]),_:1})]),_:1})])])])]),I("main",le,[I("div",oe,[L(ie)])])])])}}}),[["__scopeId","data-v-9e69f227"]]);export{ne as default};