#!/usr/bin/env python3
"""
测试PDF分批处理功能
"""
import sys
import os
import asyncio

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.document_service import DocumentService

async def test_pdf_processing():
    """测试PDF处理功能"""
    service = DocumentService()
    
    # 查找一个PDF文件进行测试
    test_files = [
        "uploads/3904963_1753971094_fb2341c6.pdf",  # 从日志中看到的文件
        "uploads/681709_1753971428_7f61dfe0.pdf",   # 另一个测试文件
    ]
    
    for file_path in test_files:
        if os.path.exists(file_path):
            print(f"测试文件: {file_path}")
            try:
                # 测试分批处理
                print("开始分批处理...")
                chunks = await service.process_document_in_batches(
                    file_path, 
                    "test.pdf",
                    batch_size=10  # 小批次测试
                )
                print(f"✅ 分批处理成功，生成 {len(chunks)} 个分块")
                
                # 显示第一个分块的内容预览
                if chunks:
                    first_chunk = chunks[0]
                    content_preview = first_chunk['content'][:200] + "..." if len(first_chunk['content']) > 200 else first_chunk['content']
                    print(f"第一个分块预览: {content_preview}")
                
                return True
                
            except Exception as e:
                print(f"❌ 分批处理失败: {e}")
                continue
    
    print("❌ 没有找到可测试的PDF文件")
    return False

if __name__ == "__main__":
    result = asyncio.run(test_pdf_processing())
    if result:
        print("🎉 PDF分批处理功能正常")
    else:
        print("💥 PDF分批处理功能异常")
