"""
AI模型管理API
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlmodel import Session, select
from app.core.database import SessionDep
from app.core.auth import get_current_active_user, get_current_admin_user
from app.models.user import User
from app.models.ai import AIProvider, AIModel, UserAPIKey
from app.schemas.ai import (
    AIProviderCreate,
    AIProviderUpdate,
    AIProviderResponse,
    AIModelCreate,
    AIModelUpdate,
    AIModelResponse,
    AIModelSimpleResponse,
    UserAPIKeyCreate,
    UserAPIKeyUpdate,
    UserAPIKeyResponse,
    TestConnectionRequest,
    TestConnectionResponse
)
from app.services.ai_service import test_ai_connection
from app.api.chat import parse_ai_error

router = APIRouter()


# ==================== 供应商管理 ====================

@router.get("/providers", response_model=List[AIProviderResponse])
async def get_ai_providers(
    session: SessionDep,
    current_user: User = Depends(get_current_active_user)
):
    """获取AI供应商列表"""
    statement = select(AIProvider).where(AIProvider.is_active == True)
    providers = session.exec(statement).all()
    return providers


@router.post("/providers", response_model=AIProviderResponse)
async def create_ai_provider(
    provider_data: AIProviderCreate,
    session: SessionDep,
    current_user: User = Depends(get_current_admin_user)
):
    """创建AI供应商（仅管理员）"""
    # 检查供应商名称是否已存在
    existing_statement = select(AIProvider).where(AIProvider.name == provider_data.name)
    existing_provider = session.exec(existing_statement).first()

    if existing_provider:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="供应商名称已存在"
        )

    provider = AIProvider(**provider_data.model_dump())
    session.add(provider)
    session.commit()
    session.refresh(provider)

    return provider


@router.put("/providers/{provider_id}", response_model=AIProviderResponse)
async def update_ai_provider(
    provider_id: int,
    provider_data: AIProviderUpdate,
    session: SessionDep,
    current_user: User = Depends(get_current_admin_user)
):
    """更新AI供应商（仅管理员）"""
    statement = select(AIProvider).where(AIProvider.id == provider_id)
    provider = session.exec(statement).first()

    if not provider:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="供应商不存在"
        )

    update_data = provider_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(provider, field, value)

    session.add(provider)
    session.commit()
    session.refresh(provider)

    return provider


@router.delete("/providers/{provider_id}")
async def delete_ai_provider(
    provider_id: int,
    session: SessionDep,
    current_user: User = Depends(get_current_admin_user)
):
    """删除AI供应商（仅管理员）"""
    statement = select(AIProvider).where(AIProvider.id == provider_id)
    provider = session.exec(statement).first()

    if not provider:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="供应商不存在"
        )

    # 检查是否有关联的模型
    models_statement = select(AIModel).where(AIModel.provider_id == provider_id)
    models = session.exec(models_statement).all()

    if models:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无法删除有关联模型的供应商，请先删除相关模型"
        )

    session.delete(provider)
    session.commit()

    return {"message": "供应商删除成功"}


# ==================== 模型管理 ====================

@router.get("/models", response_model=List[AIModelResponse])
async def get_ai_models(
    session: SessionDep,
    current_user: User = Depends(get_current_active_user)
):
    """获取AI模型列表"""
    from sqlalchemy.orm import selectinload

    statement = select(AIModel).where(AIModel.is_active == True).options(selectinload(AIModel.provider))
    models = session.exec(statement).all()
    return models


@router.get("/models/{model_id}/status")
async def check_model_status(
    model_id: int,
    session: SessionDep,
    current_user: User = Depends(get_current_active_user)
):
    """检查AI模型状态"""
    # 获取模型信息
    model_statement = select(AIModel).where(AIModel.id == model_id)
    ai_model = session.exec(model_statement).first()

    if not ai_model:
        raise HTTPException(status_code=404, detail="模型不存在")

    try:
        # 尝试获取AI客户端来检查模型状态
        from app.services.ai_service import get_ai_client_for_model
        ai_client = await get_ai_client_for_model(model_id, current_user.id, session)

        # 如果能成功获取客户端，说明模型可用
        return {
            "model_id": model_id,
            "model_name": ai_model.model_name,
            "status": "available",
            "message": "模型可用"
        }
    except Exception as e:
        error_message = parse_ai_error(str(e))
        return {
            "model_id": model_id,
            "model_name": ai_model.model_name,
            "status": "unavailable",
            "message": error_message
        }


@router.post("/models", response_model=AIModelResponse)
async def create_ai_model(
    model_data: AIModelCreate,
    session: SessionDep,
    current_user: User = Depends(get_current_admin_user)
):
    """创建AI模型（仅管理员）"""
    # 验证供应商是否存在
    provider_statement = select(AIProvider).where(AIProvider.id == model_data.provider_id)
    provider = session.exec(provider_statement).first()

    if not provider:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="供应商不存在"
        )

    # 检查同一供应商下是否已有相同模型名称
    existing_statement = select(AIModel).where(
        AIModel.provider_id == model_data.provider_id,
        AIModel.model_name == model_data.model_name
    )
    existing_model = session.exec(existing_statement).first()

    if existing_model:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该供应商下已存在相同名称的模型"
        )

    model = AIModel(**model_data.model_dump())
    session.add(model)
    session.commit()
    session.refresh(model)

    return model


@router.put("/models/{model_id}", response_model=AIModelResponse)
async def update_ai_model(
    model_id: int,
    model_data: AIModelUpdate,
    session: SessionDep,
    current_user: User = Depends(get_current_admin_user)
):
    """更新AI模型（仅管理员）"""
    statement = select(AIModel).where(AIModel.id == model_id)
    model = session.exec(statement).first()

    if not model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="模型不存在"
        )

    update_data = model_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(model, field, value)

    session.add(model)
    session.commit()
    session.refresh(model)

    return model


@router.delete("/models/{model_id}")
async def delete_ai_model(
    model_id: int,
    session: SessionDep,
    current_user: User = Depends(get_current_admin_user)
):
    """删除AI模型（仅管理员）"""
    statement = select(AIModel).where(AIModel.id == model_id)
    model = session.exec(statement).first()

    if not model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="模型不存在"
        )

    session.delete(model)
    session.commit()

    return {"message": "模型删除成功"}


# ==================== 用户API密钥管理 ====================

@router.get("/api-keys", response_model=List[UserAPIKeyResponse])
async def get_user_api_keys(
    session: SessionDep,
    current_user: User = Depends(get_current_active_user)
):
    """获取用户的API密钥列表"""
    from sqlmodel import select

    # 使用join查询来获取provider信息
    statement = (
        select(UserAPIKey, AIProvider)
        .join(AIProvider, UserAPIKey.provider_id == AIProvider.id)
        .where(UserAPIKey.user_id == current_user.id)
    )

    results = session.exec(statement).all()

    # 构建响应数据
    api_keys = []
    for api_key, provider in results:
        # 创建一个新的UserAPIKey对象并设置provider
        key_dict = api_key.model_dump()
        key_dict['provider'] = provider
        api_keys.append(key_dict)

    return api_keys


@router.post("/api-keys", response_model=UserAPIKeyResponse)
async def create_user_api_key(
    api_key_data: UserAPIKeyCreate,
    session: SessionDep,
    current_user: User = Depends(get_current_active_user)
):
    """创建用户API密钥"""
    # 验证供应商是否存在
    provider_statement = select(AIProvider).where(AIProvider.id == api_key_data.provider_id)
    provider = session.exec(provider_statement).first()

    if not provider:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="供应商不存在"
        )

    # 检查是否已存在该供应商的密钥
    existing_statement = select(UserAPIKey).where(
        UserAPIKey.user_id == current_user.id,
        UserAPIKey.provider_id == api_key_data.provider_id
    )
    existing_key = session.exec(existing_statement).first()

    if existing_key:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该供应商的API密钥已存在，请使用更新接口"
        )

    # 创建新密钥
    new_key = UserAPIKey(
        user_id=current_user.id,
        provider_id=api_key_data.provider_id,
        api_key=api_key_data.api_key,  # 实际应用中需要加密
        description=api_key_data.description
    )

    session.add(new_key)
    session.commit()
    session.refresh(new_key)

    return new_key


@router.put("/api-keys/{key_id}", response_model=UserAPIKeyResponse)
async def update_user_api_key(
    key_id: int,
    api_key_data: UserAPIKeyUpdate,
    session: SessionDep,
    current_user: User = Depends(get_current_active_user)
):
    """更新用户API密钥"""
    statement = select(UserAPIKey).where(
        UserAPIKey.id == key_id,
        UserAPIKey.user_id == current_user.id
    )
    api_key = session.exec(statement).first()

    if not api_key:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="API密钥不存在"
        )

    update_data = api_key_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(api_key, field, value)

    session.add(api_key)
    session.commit()
    session.refresh(api_key)

    return api_key


@router.delete("/api-keys/{key_id}")
async def delete_user_api_key(
    key_id: int,
    session: SessionDep,
    current_user: User = Depends(get_current_active_user)
):
    """删除用户API密钥"""
    statement = select(UserAPIKey).where(
        UserAPIKey.id == key_id,
        UserAPIKey.user_id == current_user.id
    )
    api_key = session.exec(statement).first()

    if not api_key:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="API密钥不存在"
        )

    session.delete(api_key)
    session.commit()

    return {"message": "API密钥删除成功"}


# ==================== 连接测试 ====================

@router.post("/test-connection", response_model=TestConnectionResponse)
async def test_ai_connection_endpoint(
    test_data: TestConnectionRequest,
    session: SessionDep,
    current_user: User = Depends(get_current_active_user)
):
    """测试AI连接"""
    import time

    # 获取供应商信息
    provider_statement = select(AIProvider).where(AIProvider.id == test_data.provider_id)
    provider = session.exec(provider_statement).first()

    if not provider:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="供应商不存在"
        )

    # 确定使用的API密钥
    api_key = test_data.api_key
    if not api_key:
        # 查找用户的API密钥
        user_key_statement = select(UserAPIKey).where(
            UserAPIKey.user_id == current_user.id,
            UserAPIKey.provider_id == test_data.provider_id
        )
        user_key = session.exec(user_key_statement).first()

        if user_key:
            api_key = user_key.api_key
        else:
            # 检查是否有可用的系统密钥
            # 查找该供应商下的任何模型是否有系统密钥
            model_with_key_statement = select(AIModel).where(
                AIModel.provider_id == provider.id,
                AIModel.allow_system_key_use == True,
                AIModel.system_api_key.isnot(None)
            )
            model_with_key = session.exec(model_with_key_statement).first()

            if model_with_key:
                api_key = model_with_key.system_api_key
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="未找到可用的API密钥"
                )

    if not api_key:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="API密钥不能为空"
        )

    try:
        start_time = time.time()

        # 这里应该调用实际的AI服务测试连接
        # 暂时模拟测试
        success = await test_ai_connection(
            provider_name=provider.name,
            model_name=test_data.model_name,
            api_key=api_key,
            base_url=provider.base_url
        )

        response_time = time.time() - start_time

        return TestConnectionResponse(
            success=success,
            message="连接测试成功" if success else "连接测试失败",
            response_time=response_time
        )

    except Exception as e:
        return TestConnectionResponse(
            success=False,
            message=f"连接测试失败: {str(e)}"
        )
