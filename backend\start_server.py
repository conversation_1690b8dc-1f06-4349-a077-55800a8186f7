#!/usr/bin/env python3
"""
宝塔环境启动脚本
用于在宝塔面板中启动FastAPI后端服务
"""
import os
import sys
import uvicorn
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """启动FastAPI应用"""
    print("正在启动AI知识库后端服务...")
    print(f"项目根目录: {project_root}")
    print(f"Python路径: {sys.path}")
    
    # 设置环境变量
    os.environ.setdefault("PYTHONPATH", str(project_root))
    
    try:
        # 启动uvicorn服务器
        uvicorn.run(
            "app.main:app",
            host="127.0.0.1",  # 只监听本地，通过nginx代理
            port=8000,
            reload=False,  # 生产环境不启用热重载
            workers=1,  # 单进程，避免资源冲突
            log_level="info",
            access_log=True
        )
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
