import request, { uploadRequest } from './request'

// 文档相关接口类型
export interface Document {
  id: number
  kb_id: number
  uploader_id: number
  filename: string
  storage_path: string
  file_type: string
  file_size: number
  status: 'pending' | 'processing' | 'completed' | 'failed'
  error_message?: string
  created_at: string
  updated_at: string
}

export interface DocumentListParams {
  offset?: number
  limit?: number
}

export interface BatchDeleteRequest {
  document_ids: number[]
}

export interface BatchUploadResponse {
  message: string
  uploaded_documents: Document[]
  failed_uploads: Array<{
    filename: string
    error: string
  }>
}

export interface BatchDeleteResponse {
  message: string
  deleted_count: number
  failed_deletes: Array<{
    document_id: number
    error: string
  }>
}

// 文档API
export const documentAPI = {
  // 获取文档列表
  getList: (kbId: number, params?: DocumentListParams): Promise<Document[]> => {
    return request.get(`/documents/kb/${kbId}`, { params })
  },

  // 上传文档
  upload: (kbId: number, file: File, onProgress?: (progress: number) => void): Promise<Document> => {
    const formData = new FormData()
    formData.append('file', file)

    return uploadRequest.post(`/documents/upload/${kbId}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      }
    })
  },

  // 批量上传文档
  batchUpload: (kbId: number, files: File[], onProgress?: (progress: number) => void): Promise<BatchUploadResponse> => {
    const formData = new FormData()
    files.forEach(file => {
      formData.append('files', file)
    })

    return uploadRequest.post(`/documents/batch-upload/${kbId}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      }
    })
  },

  // 获取文档详情
  getDetail: (id: number): Promise<Document> => {
    return request.get(`/documents/${id}`)
  },

  // 删除文档
  delete: (id: number): Promise<{ message: string }> => {
    return request.delete(`/documents/${id}`)
  },

  // 批量删除文档
  batchDelete: (data: BatchDeleteRequest): Promise<BatchDeleteResponse> => {
    return request.delete('/documents/batch-delete', { data })
  },

  // 文档向量化
  vectorize: (id: number): Promise<{ message: string }> => {
    return request.post(`/documents/${id}/vectorize`)
  },

  // 重试文档处理
  retry: (id: number): Promise<{ message: string; document_id: number; status: string; chunks_count?: number }> => {
    return request.post(`/documents/${id}/retry`)
  },

  // 下载文档
  download: (id: number): Promise<Blob> => {
    return request.get(`/documents/${id}/download`, {
      responseType: 'blob'
    })
  }
}
