# 多阶段构建：构建阶段
FROM node:20-alpine AS builder

# 设置工作目录
WORKDIR /app

# 配置npm国内镜像源
RUN npm config set registry https://registry.npmmirror.com

# 复制package文件
COPY package*.json ./

# 清理npm缓存并安装依赖（包括开发依赖）
RUN npm cache clean --force && \
    npm install --prefer-offline --no-audit --no-fund

# 复制源代码
COPY . .

# 设置生产环境变量和Node.js内存限制
ENV NODE_ENV=production
ENV VITE_APP_ENV=production
ENV NODE_OPTIONS="--max-old-space-size=4096"

# 构建应用（使用构建脚本）
RUN chmod +x build.sh && ./build.sh

# 生产阶段：使用Nginx提供静态文件服务
FROM nginx:alpine


# 暴露端口
EXPOSE 80

# 启动Nginx
CMD ["nginx", "-g", "daemon off;"]
