"""
用户相关的Pydantic模式
"""
from datetime import datetime
from typing import Optional
from pydantic import BaseModel, EmailStr


class UserBase(BaseModel):
    """用户基础模式"""
    username: str
    email: EmailStr
    display_name: Optional[str] = None
    bio: Optional[str] = None


class UserCreate(UserBase):
    """用户创建模式"""
    password: str


class UserUpdate(BaseModel):
    """用户更新模式"""
    display_name: Optional[str] = None
    bio: Optional[str] = None
    avatar_url: Optional[str] = None


class UserPasswordUpdate(BaseModel):
    """用户密码更新模式"""
    current_password: str
    new_password: str


class UserResponse(UserBase):
    """用户响应模式"""
    id: int
    avatar_url: Optional[str] = None
    is_admin: bool
    status: str
    last_login_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class UserLogin(BaseModel):
    """用户登录模式"""
    username: str
    password: str


class Token(BaseModel):
    """令牌模式"""
    access_token: str
    token_type: str = "bearer"


class TokenData(BaseModel):
    """令牌数据模式"""
    user_id: Optional[int] = None


class UserSettingsBase(BaseModel):
    """用户设置基础模式"""
    default_model_id: Optional[int] = None
    default_knowledge_bases: Optional[list[int]] = None
    chat_retention_days: int = 30
    theme: str = "auto"
    font_size: str = "medium"
    enable_high_contrast: bool = False
    enable_reduced_motion: bool = False
    provider_preferences: Optional[dict] = None


class UserSettingsUpdate(UserSettingsBase):
    """用户设置更新模式"""
    pass


class UserSettingsResponse(UserSettingsBase):
    """用户设置响应模式"""
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
