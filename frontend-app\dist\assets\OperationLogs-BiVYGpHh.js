var e;(()=>{function a(e,a,t,l,s,r,d){try{var u=e[r](d),i=u.value}catch(e){return void t(e)}u.done?a(i):Promise.resolve(i).then(l,s)}e=function(e){return function(){var t=this,l=arguments;return new Promise(function(s,r){var d=e.apply(t,l);function u(e){a(d,s,r,u,i,"next",e)}function i(e){a(d,s,r,u,i,"throw",e)}u(void 0)})}}})();import{E as a,N as t,c as l,d as s,f as r,g as d,k as u,l as i,m as n,u as o,y as c}from"./elementPlus-Di4PDIm8.js";import{bO as g,b_ as v,cd as y,dB as m,dN as x,dO as p,dU as f,d_ as _,dd as b,df as k,dg as w,dj as h,dk as C,dl as V,dy as j,ed as I}from"./vendor-BJ-uKP15.js";import{b as z}from"./admin-BSai4urc.js";const P={key:0,class:"h-full flex items-center justify-center"},N={class:"text-center"},O={key:1,class:"space-y-6"},U={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},D={class:"mt-4 sm:mt-0"},q={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},A={class:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4"},L={class:"flex items-center space-x-4"},S={class:"flex items-center space-x-4"},B={class:"bg-white dark:bg-gray-800 rounded-lg shadow"},J={class:"text-gray-600 dark:text-gray-400 text-sm"},E={class:"text-gray-600 dark:text-gray-400"},F={class:"text-gray-600 dark:text-gray-400"},G={class:"text-sm"},H={key:0,class:"text-gray-600 dark:text-gray-400"},K={class:"whitespace-pre-wrap text-xs"},M={key:1,class:"text-gray-400"},Q={class:"text-gray-600 dark:text-gray-400 text-sm"},R={class:"p-4 border-t border-gray-200 dark:border-gray-700"},T={key:0,class:"space-y-4"},W={class:"grid grid-cols-2 gap-4"},X={class:"text-sm text-gray-600 dark:text-gray-400"},Y={class:"text-sm text-gray-600 dark:text-gray-400"},Z={class:"text-sm text-gray-600 dark:text-gray-400"},$={class:"text-sm text-gray-600 dark:text-gray-400"},ee={class:"text-sm text-gray-600 dark:text-gray-400"},ae={key:0},te={class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},le={class:"text-sm text-gray-600 dark:text-gray-400 whitespace-pre-wrap"},se={class:"dialog-footer"};var re=V({__name:"OperationLogs",setup(V){const re=f(!0),de=f(!1),ue=f([]),ie=f(0),ne=f(1),oe=f(20),ce=f(""),ge=f(!1),ve=f(null),ye=e=>({create_user:"创建用户",update_user:"更新用户",delete_user:"删除用户",update_user_quota:"更新配额",update_ai_model:"更新AI模型",update_system_setting:"更新系统设置"}[e]||e),me=e=>e.includes("create")?"success":e.includes("delete")?"danger":e.includes("update")?"warning":"info",xe=e=>({user:"用户",user_quota:"用户配额",ai_model:"AI模型",system_setting:"系统设置"}[e]||e),pe=e=>new Date(e).toLocaleString("zh-CN"),fe=e=>{try{const a=JSON.parse(e);return JSON.stringify(a,null,2)}catch(a){return e}},_e=(be=e(function*(){try{de.value=!0;const e={offset:(ne.value-1)*oe.value,limit:oe.value,action_filter:ce.value||void 0};ue.value=yield z.getLogs(e),ie.value=ue.value.length>=oe.value?1e3:ue.value.length}catch(e){l.error("加载操作日志失败")}finally{de.value=!1}}),function(){return be.apply(this,arguments)});var be;const ke=e=>{ne.value=e,_e()};return j(e(function*(){try{yield _e()}finally{re.value=!1}})),(e,l)=>{const f=t,V=a,j=i,z=n,be=d,we=c,he=r,Ce=u,Ve=o,je=s;return re.value?(m(),w("div",P,[b("div",N,[C(f,{size:48,class:"text-blue-500 animate-spin mb-4"},{default:x(()=>[C(_(g))]),_:1}),l[5]||(l[5]=b("p",{class:"text-gray-600 dark:text-gray-400"},"正在加载操作日志...",-1))])])):(m(),w("div",O,[b("div",U,[l[7]||(l[7]=b("div",null,[b("h1",{class:"text-2xl font-bold text-gray-900 dark:text-gray-100"}," 操作日志 "),b("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," 查看系统管理员的所有操作记录 ")],-1)),b("div",D,[C(V,{onClick:_e,loading:de.value},{default:x(()=>[C(f,{class:"mr-2"},{default:x(()=>[C(_(v))]),_:1}),l[6]||(l[6]=h(" 刷新 ",-1))]),_:1,__:[6]},8,["loading"])])]),b("div",q,[b("div",A,[b("div",L,[l[8]||(l[8]=b("span",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"操作类型：",-1)),C(z,{modelValue:ce.value,"onUpdate:modelValue":l[0]||(l[0]=e=>ce.value=e),placeholder:"选择操作类型",style:{width:"200px"},onChange:_e},{default:x(()=>[C(j,{label:"全部操作",value:""}),C(j,{label:"创建用户",value:"create_user"}),C(j,{label:"更新用户",value:"update_user"}),C(j,{label:"删除用户",value:"delete_user"}),C(j,{label:"更新配额",value:"update_user_quota"}),C(j,{label:"更新AI模型",value:"update_ai_model"}),C(j,{label:"更新系统设置",value:"update_system_setting"})]),_:1},8,["modelValue"])]),b("div",S,[l[9]||(l[9]=b("span",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"每页显示：",-1)),C(z,{modelValue:oe.value,"onUpdate:modelValue":l[1]||(l[1]=e=>oe.value=e),style:{width:"100px"},onChange:_e},{default:x(()=>[C(j,{label:"10",value:10}),C(j,{label:"20",value:20}),C(j,{label:"50",value:50}),C(j,{label:"100",value:100})]),_:1},8,["modelValue"])])])]),p((m(),w("div",B,[C(he,{data:ue.value,style:{width:"100%"}},{default:x(()=>[C(be,{prop:"created_at",label:"时间",width:"180"},{default:x(({row:e})=>[b("span",J,I(pe(e.created_at)),1)]),_:1}),C(be,{prop:"action",label:"操作",width:"150"},{default:x(({row:e})=>[C(we,{type:me(e.action),size:"small"},{default:x(()=>[h(I(ye(e.action)),1)]),_:2},1032,["type"])]),_:1}),C(be,{prop:"target_type",label:"目标类型",width:"120"},{default:x(({row:e})=>[b("span",E,I(xe(e.target_type)),1)]),_:1}),C(be,{prop:"target_id",label:"目标ID",width:"100",align:"center"},{default:x(({row:e})=>[b("span",F,I(e.target_id||"-"),1)]),_:1}),C(be,{prop:"details",label:"详细信息","min-width":"300"},{default:x(({row:e})=>[b("div",G,[e.details?(m(),w("div",H,[b("pre",K,I(fe(e.details)),1)])):(m(),w("div",M," 无详细信息 "))])]),_:1}),C(be,{prop:"ip_address",label:"IP地址",width:"140"},{default:x(({row:e})=>[b("span",Q,I(e.ip_address||"-"),1)]),_:1}),C(be,{label:"操作",width:"100",align:"center"},{default:x(({row:e})=>[C(V,{size:"small",onClick:a=>(ve.value=e,void(ge.value=!0))},{default:x(()=>[C(f,null,{default:x(()=>[C(_(y))]),_:1}),l[10]||(l[10]=h(" 详情 ",-1))]),_:2,__:[10]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),b("div",R,[C(Ce,{"current-page":ne.value,"onUpdate:currentPage":l[2]||(l[2]=e=>ne.value=e),"page-size":oe.value,total:ie.value,layout:"total, prev, pager, next, jumper",onCurrentChange:ke},null,8,["current-page","page-size","total"])])])),[[je,de.value]]),C(Ve,{modelValue:ge.value,"onUpdate:modelValue":l[4]||(l[4]=e=>ge.value=e),title:"操作详情",width:"700px"},{footer:x(()=>[b("div",se,[C(V,{onClick:l[3]||(l[3]=e=>ge.value=!1)},{default:x(()=>l[18]||(l[18]=[h("关闭",-1)])),_:1,__:[18]})])]),default:x(()=>[ve.value?(m(),w("div",T,[b("div",W,[b("div",null,[l[11]||(l[11]=b("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," 操作时间 ",-1)),b("div",X,I(pe(ve.value.created_at)),1)]),b("div",null,[l[12]||(l[12]=b("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," 操作类型 ",-1)),C(we,{type:me(ve.value.action),size:"small"},{default:x(()=>[h(I(ye(ve.value.action)),1)]),_:1},8,["type"])]),b("div",null,[l[13]||(l[13]=b("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," 目标类型 ",-1)),b("div",Y,I(xe(ve.value.target_type)),1)]),b("div",null,[l[14]||(l[14]=b("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," 目标ID ",-1)),b("div",Z,I(ve.value.target_id||"-"),1)]),b("div",null,[l[15]||(l[15]=b("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," 操作用户ID ",-1)),b("div",$,I(ve.value.user_id),1)]),b("div",null,[l[16]||(l[16]=b("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," IP地址 ",-1)),b("div",ee,I(ve.value.ip_address||"-"),1)])]),ve.value.details?(m(),w("div",ae,[l[17]||(l[17]=b("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," 详细信息 ",-1)),b("div",te,[b("pre",le,I(fe(ve.value.details)),1)])])):k("",!0)])):k("",!0)]),_:1},8,["modelValue"])]))}}});export{re as default};