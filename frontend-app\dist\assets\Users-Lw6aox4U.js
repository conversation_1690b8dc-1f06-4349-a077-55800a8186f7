var e;(()=>{function a(e,a,l,s,t,d,r){try{var u=e[d](r),i=u.value}catch(e){return void l(e)}u.done?a(i):Promise.resolve(i).then(s,t)}e=function(e){return function(){var l=this,s=arguments;return new Promise(function(t,d){var r=e.apply(l,s);function u(e){a(r,t,d,u,i,"next",e)}function i(e){a(r,t,d,u,i,"throw",e)}u(void 0)})}}})();import{B as a,E as l,J as s,L as t,N as d,b as r,c as u,d as i,f as n,g as o,l as m,m as _,n as c,o as p,p as v,u as f,y,z as g}from"./elementPlus-Di4PDIm8.js";import{bD as b,bO as x,bY as w,b_ as k,by as h,c0 as V,c1 as C,dB as U,dN as z,dO as j,dS as B,dU as q,d_ as L,dc as F,dd as T,de as $,df as N,dg as P,dj as S,dk as D,dl as O,dy as Q,ed as R}from"./vendor-BJ-uKP15.js";import{b as A}from"./index-Byt5TjPh.js";import{b as E}from"./admin-BSai4urc.js";const I={key:0,class:"h-full flex items-center justify-center"},J={class:"text-center"},M={key:1,class:"space-y-6"},Y={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},G={class:"mt-4 sm:mt-0"},H={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},K={class:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4"},W={class:"flex-1 max-w-md"},X={class:"flex items-center space-x-4"},Z={class:"bg-white dark:bg-gray-800 rounded-lg shadow"},ee={class:"flex items-center space-x-3"},ae={class:"font-medium text-gray-900 dark:text-gray-100"},le={class:"text-sm text-gray-500"},se={class:"text-gray-600 dark:text-gray-400"},te={class:"text-gray-500 dark:text-gray-400 text-sm"},de={class:"text-gray-500 dark:text-gray-400 text-sm"},re={class:"flex items-center justify-center space-x-1"},ue={class:"dialog-footer"},ie={class:"dialog-footer"};var ne=O({__name:"Users",setup(O){const ne=A(),oe=q(!0),me=q(!1),_e=q(!1),ce=q([]),pe=q(""),ve=q(""),fe=q(!1),ye=q(!1),ge=q(null),be=q(null),xe=B({username:"",email:"",password:"",display_name:"",is_admin:!1,status:"active"}),we=B({max_kbs:5,max_docs_per_kb:100,max_storage_mb:1024}),ke=q(),he=q(),Ve=F(()=>{var e;return null===(e=ne.user)||void 0===e?void 0:e.id}),Ce=F(()=>{let e=ce.value;if(pe.value){const a=pe.value.toLowerCase();e=e.filter(e=>e.username.toLowerCase().includes(a)||e.email.toLowerCase().includes(a)||e.display_name&&e.display_name.toLowerCase().includes(a))}return ve.value&&(e=e.filter(e=>e.status===ve.value)),e}),Ue={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"用户名长度在 3 到 20 个字符",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱",trigger:"blur"},{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度至少 6 个字符",trigger:"blur"}]},ze={max_kbs:[{required:!0,message:"请输入最大知识库数量",trigger:"blur"}],max_docs_per_kb:[{required:!0,message:"请输入每个知识库最大文档数",trigger:"blur"}],max_storage_mb:[{required:!0,message:"请输入最大存储空间",trigger:"blur"}]},je=e=>new Date(e).toLocaleString("zh-CN"),Be=()=>{},qe=(Le=e(function*(){try{me.value=!0,ce.value=yield E.getUsers()}catch(e){u.error("加载用户列表失败")}finally{me.value=!1}}),function(){return Le.apply(this,arguments)});var Le;const Fe=(Te=e(function*(e){try{be.value=e;const a=yield E.getUserQuota(e.id);we.max_kbs=a.max_kbs,we.max_docs_per_kb=a.max_docs_per_kb,we.max_storage_mb=a.max_storage_mb,ye.value=!0}catch(a){u.error("获取用户配额失败")}}),function(e){return Te.apply(this,arguments)});var Te;const $e=(Ne=e(function*(e){try{const a="active"===e.status?"disabled":"active",l="active"===a?"启用":"禁用";yield r.confirm(`确定要${l}用户 "${e.username}" 吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),yield E.updateUserStatus(e.id,{status:a}),e.status=a,u.success(`用户${l}成功`)}catch(a){"cancel"!==a&&u.error("更新用户状态失败")}}),function(e){return Ne.apply(this,arguments)});var Ne;const Pe=(Se=e(function*(e){try{yield r.confirm(`确定要删除用户 "${e.username}" 吗？此操作不可恢复！`,"确认删除",{confirmButtonText:"删除",cancelButtonText:"取消",type:"error"}),yield E.deleteUser(e.id),yield qe(),u.success("用户删除成功")}catch(a){"cancel"!==a&&u.error("删除用户失败")}}),function(e){return Se.apply(this,arguments)});var Se;const De=(Oe=e(function*(){try{var e;if(yield null===(e=ke.value)||void 0===e?void 0:e.validate(),_e.value=!0,ge.value){const e={username:xe.username,email:xe.email,display_name:xe.display_name,is_admin:xe.is_admin,status:xe.status};yield E.updateUser(ge.value.id,e),u.success("用户更新成功")}else{const e={username:xe.username,email:xe.email,password:xe.password,display_name:xe.display_name,is_admin:xe.is_admin};yield E.createUser(e),u.success("用户创建成功")}fe.value=!1,yield qe()}catch(a){u.error("保存用户失败")}finally{_e.value=!1}}),function(){return Oe.apply(this,arguments)});var Oe;const Qe=(Re=e(function*(){try{var e;if(yield null===(e=he.value)||void 0===e?void 0:e.validate(),!be.value)return;_e.value=!0;const a={max_kbs:we.max_kbs,max_docs_per_kb:we.max_docs_per_kb,max_storage_mb:we.max_storage_mb};yield E.updateUserQuota(be.value.id,a),u.success("配额更新成功"),ye.value=!1}catch(a){u.error("更新配额失败")}finally{_e.value=!1}}),function(){return Re.apply(this,arguments)});var Re;const Ae=()=>{var e;ge.value=null,xe.username="",xe.email="",xe.password="",xe.display_name="",xe.is_admin=!1,xe.status="active",null===(e=ke.value)||void 0===e||e.resetFields()},Ee=()=>{var e;be.value=null,we.max_kbs=5,we.max_docs_per_kb=100,we.max_storage_mb=1024,null===(e=he.value)||void 0===e||e.resetFields()};return Q(e(function*(){try{yield qe()}finally{oe.value=!1}})),(e,r)=>{const u=d,B=l,q=t,F=m,O=_,Q=s,A=o,E=y,ne=n,ce=v,be=g,Le=a,Te=p,Ne=f,Se=c,Oe=i;return oe.value?(U(),P("div",I,[T("div",J,[D(u,{size:48,class:"text-blue-500 animate-spin mb-4"},{default:z(()=>[D(L(x))]),_:1}),r[16]||(r[16]=T("p",{class:"text-gray-600 dark:text-gray-400"},"正在加载用户数据...",-1))])])):(U(),P("div",M,[T("div",Y,[r[18]||(r[18]=T("div",null,[T("h1",{class:"text-2xl font-bold text-gray-900 dark:text-gray-100"}," 用户管理 "),T("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," 管理系统中的所有用户账户，调整权限和配额 ")],-1)),T("div",G,[D(B,{type:"primary",onClick:r[0]||(r[0]=e=>fe.value=!0)},{default:z(()=>[D(u,{class:"mr-2"},{default:z(()=>[D(L(w))]),_:1}),r[17]||(r[17]=S(" 创建用户 ",-1))]),_:1,__:[17]})])]),T("div",H,[T("div",K,[T("div",W,[D(q,{modelValue:pe.value,"onUpdate:modelValue":r[1]||(r[1]=e=>pe.value=e),placeholder:"搜索用户名、邮箱...",clearable:"",onInput:Be},{prefix:z(()=>[D(u,null,{default:z(()=>[D(L(V))]),_:1})]),_:1},8,["modelValue"])]),T("div",X,[D(O,{modelValue:ve.value,"onUpdate:modelValue":r[2]||(r[2]=e=>ve.value=e),placeholder:"状态",style:{width:"120px"},onChange:qe},{default:z(()=>[D(F,{label:"全部",value:""}),D(F,{label:"活跃",value:"active"}),D(F,{label:"禁用",value:"disabled"}),D(F,{label:"待审核",value:"pending"})]),_:1},8,["modelValue"]),D(B,{onClick:qe,loading:me.value},{default:z(()=>[D(u,null,{default:z(()=>[D(L(k))]),_:1})]),_:1},8,["loading"])])])]),j((U(),P("div",Z,[D(ne,{data:Ce.value,style:{width:"100%"}},{default:z(()=>[D(A,{prop:"username",label:"用户","min-width":"200"},{default:z(({row:e})=>[T("div",ee,[D(Q,{size:40,src:e.avatar_url},{default:z(()=>[S(R(e.username.charAt(0).toUpperCase()),1)]),_:2},1032,["src"]),T("div",null,[T("div",ae,R(e.display_name||e.username),1),T("div",le," @"+R(e.username),1)])])]),_:1}),D(A,{prop:"email",label:"邮箱","min-width":"200"},{default:z(({row:e})=>[T("span",se,R(e.email),1)]),_:1}),D(A,{prop:"is_admin",label:"角色",width:"100",align:"center"},{default:z(({row:e})=>[D(E,{type:e.is_admin?"danger":"primary",size:"small"},{default:z(()=>[S(R(e.is_admin?"管理员":"用户"),1)]),_:2},1032,["type"])]),_:1}),D(A,{prop:"status",label:"状态",width:"100",align:"center"},{default:z(({row:e})=>[D(E,{type:"active"===e.status?"success":"disabled"===e.status?"danger":"warning",size:"small"},{default:z(()=>{return[S(R((a=e.status,{active:"活跃",disabled:"禁用",pending:"待审核"}[a]||a)),1)];var a}),_:2},1032,["type"])]),_:1}),D(A,{prop:"last_login_at",label:"最后登录",width:"150"},{default:z(({row:e})=>[T("span",te,R(e.last_login_at?je(e.last_login_at):"从未登录"),1)]),_:1}),D(A,{prop:"created_at",label:"创建时间",width:"150"},{default:z(({row:e})=>[T("span",de,R(je(e.created_at)),1)]),_:1}),D(A,{label:"操作",width:"250",align:"center",fixed:"right"},{default:z(({row:e})=>[T("div",re,[D(B,{size:"small",onClick:a=>{return ge.value=l=e,xe.username=l.username,xe.email=l.email,xe.display_name=l.display_name||"",xe.is_admin=l.is_admin,xe.status=l.status,xe.password="",void(fe.value=!0);var l}},{default:z(()=>[D(u,null,{default:z(()=>[D(L(b))]),_:1}),r[19]||(r[19]=S(" 编辑 ",-1))]),_:2,__:[19]},1032,["onClick"]),D(B,{size:"small",onClick:a=>Fe(e)},{default:z(()=>[D(u,null,{default:z(()=>[D(L(C))]),_:1}),r[20]||(r[20]=S(" 配额 ",-1))]),_:2,__:[20]},1032,["onClick"]),D(B,{size:"small",type:"active"===e.status?"warning":"success",onClick:a=>$e(e)},{default:z(()=>[S(R("active"===e.status?"禁用":"启用"),1)]),_:2},1032,["type","onClick"]),D(B,{size:"small",type:"danger",onClick:a=>Pe(e),disabled:e.is_admin&&e.id===Ve.value},{default:z(()=>[D(u,null,{default:z(()=>[D(L(h))]),_:1})]),_:2},1032,["onClick","disabled"])])]),_:1})]),_:1},8,["data"])])),[[Oe,me.value]]),D(Ne,{modelValue:fe.value,"onUpdate:modelValue":r[10]||(r[10]=e=>fe.value=e),title:ge.value?"编辑用户":"创建用户",width:"600px",onClose:Ae},{footer:z(()=>[T("div",ue,[D(B,{onClick:r[9]||(r[9]=e=>fe.value=!1)},{default:z(()=>r[26]||(r[26]=[S("取消",-1)])),_:1,__:[26]}),D(B,{type:"primary",onClick:De,loading:_e.value},{default:z(()=>[S(R(ge.value?"更新":"创建"),1)]),_:1},8,["loading"])])]),default:z(()=>[D(Te,{model:xe,rules:Ue,ref_key:"userFormRef",ref:ke,"label-width":"100px"},{default:z(()=>[D(ce,{label:"用户名",prop:"username"},{default:z(()=>[D(q,{modelValue:xe.username,"onUpdate:modelValue":r[3]||(r[3]=e=>xe.username=e),placeholder:"请输入用户名",disabled:!!ge.value},null,8,["modelValue","disabled"])]),_:1}),D(ce,{label:"显示名称",prop:"display_name"},{default:z(()=>[D(q,{modelValue:xe.display_name,"onUpdate:modelValue":r[4]||(r[4]=e=>xe.display_name=e),placeholder:"请输入显示名称"},null,8,["modelValue"])]),_:1}),D(ce,{label:"邮箱",prop:"email"},{default:z(()=>[D(q,{modelValue:xe.email,"onUpdate:modelValue":r[5]||(r[5]=e=>xe.email=e),placeholder:"请输入邮箱",type:"email"},null,8,["modelValue"])]),_:1}),ge.value?N("",!0):(U(),$(ce,{key:0,label:"密码",prop:"password"},{default:z(()=>[D(q,{modelValue:xe.password,"onUpdate:modelValue":r[6]||(r[6]=e=>xe.password=e),placeholder:"请输入密码",type:"password","show-password":""},null,8,["modelValue"])]),_:1})),D(ce,{label:"角色",prop:"is_admin"},{default:z(()=>[D(Le,{modelValue:xe.is_admin,"onUpdate:modelValue":r[7]||(r[7]=e=>xe.is_admin=e)},{default:z(()=>[D(be,{label:!1},{default:z(()=>r[21]||(r[21]=[S("普通用户",-1)])),_:1,__:[21]}),D(be,{label:!0},{default:z(()=>r[22]||(r[22]=[S("管理员",-1)])),_:1,__:[22]})]),_:1},8,["modelValue"])]),_:1}),ge.value?(U(),$(ce,{key:1,label:"状态",prop:"status"},{default:z(()=>[D(Le,{modelValue:xe.status,"onUpdate:modelValue":r[8]||(r[8]=e=>xe.status=e)},{default:z(()=>[D(be,{label:"active"},{default:z(()=>r[23]||(r[23]=[S("活跃",-1)])),_:1,__:[23]}),D(be,{label:"disabled"},{default:z(()=>r[24]||(r[24]=[S("禁用",-1)])),_:1,__:[24]}),D(be,{label:"pending"},{default:z(()=>r[25]||(r[25]=[S("待审核",-1)])),_:1,__:[25]})]),_:1},8,["modelValue"])]),_:1})):N("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),D(Ne,{modelValue:ye.value,"onUpdate:modelValue":r[15]||(r[15]=e=>ye.value=e),title:"配额管理",width:"500px",onClose:Ee},{footer:z(()=>[T("div",ie,[D(B,{onClick:r[14]||(r[14]=e=>ye.value=!1)},{default:z(()=>r[27]||(r[27]=[S("取消",-1)])),_:1,__:[27]}),D(B,{type:"primary",onClick:Qe,loading:_e.value},{default:z(()=>r[28]||(r[28]=[S(" 更新配额 ",-1)])),_:1,__:[28]},8,["loading"])])]),default:z(()=>[D(Te,{model:we,rules:ze,ref_key:"quotaFormRef",ref:he,"label-width":"150px"},{default:z(()=>[D(ce,{label:"最大知识库数量",prop:"max_kbs"},{default:z(()=>[D(Se,{modelValue:we.max_kbs,"onUpdate:modelValue":r[11]||(r[11]=e=>we.max_kbs=e),min:1,max:100,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),D(ce,{label:"每个知识库最大文档数",prop:"max_docs_per_kb"},{default:z(()=>[D(Se,{modelValue:we.max_docs_per_kb,"onUpdate:modelValue":r[12]||(r[12]=e=>we.max_docs_per_kb=e),min:1,max:1e3,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),D(ce,{label:"最大存储空间(MB)",prop:"max_storage_mb"},{default:z(()=>[D(Se,{modelValue:we.max_storage_mb,"onUpdate:modelValue":r[13]||(r[13]=e=>we.max_storage_mb=e),min:100,max:1e4,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])]))}}});export{ne as default};