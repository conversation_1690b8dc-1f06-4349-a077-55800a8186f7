import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
// import vueDevTools from 'vite-plugin-vue-devtools'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    // vueDevTools(), // 生产环境禁用开发工具
    AutoImport({
      resolvers: [ElementPlusResolver()],
      imports: ['vue', 'vue-router'],
      dts: true,
    }),
    Components({
      resolvers: [ElementPlusResolver()],
      dts: true,
    }),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  build: {
    // 生产环境配置
    minify: 'terser',
    target: 'es2015',
    // 减少内存使用
    chunkSizeWarningLimit: 1000,
    // Terser配置，用于移除console
    terserOptions: {
      compress: {
        // 移除所有console语句
        drop_console: true,
        // 移除debugger语句
        drop_debugger: true,
        // 移除无用代码
        dead_code: true,
        // 移除未使用的变量
        unused: true,
        // 移除console的所有方法
        pure_funcs: ['console.log', 'console.info', 'console.debug', 'console.warn', 'console.error', 'console.trace', 'console.time', 'console.timeEnd'],
        // 更激进的优化
        passes: 2,
        // 移除空的语句块
        collapse_vars: true,
        // 移除未使用的函数
        pure_getters: true
      },
      mangle: {
        // 混淆变量名
        toplevel: true,
        // 混淆属性名（谨慎使用）
        properties: false
      },
      format: {
        // 移除注释
        comments: false,
        // 移除空格
        beautify: false
      }
    },
    rollupOptions: {
      output: {
        // 添加时间戳到文件名，确保缓存失效
        entryFileNames: 'assets/[name]-[hash].js',
        chunkFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]',
        manualChunks(id) {
          if (id.includes('node_modules')) {
            if (id.includes('vue') || id.includes('pinia')) {
              return 'vendor'
            }
            if (id.includes('element-plus')) {
              return 'elementPlus'
            }
            return 'vendor'
          }
        }
      }
    }
  },
  esbuild: {
    // 临时保留console.log用于调试
    // drop: ['console', 'debugger'],
  },
  server: {
    port: 3000,
    host: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path
      },
    },
  },
})
