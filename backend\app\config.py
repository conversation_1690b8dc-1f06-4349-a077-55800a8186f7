"""
应用配置模块
"""
from pydantic_settings import BaseSettings
from typing import Optional


class Settings(BaseSettings):
    """应用设置"""
    
    # 应用基础配置
    app_name: str = "AI Knowledge Base"
    app_version: str = "1.0.0"
    debug: bool = True
    
    # 数据库配置
    database_url: str = "postgresql+asyncpg://postgres:111222@localhost:5432/aiknowledgebase"
    database_host: str = "localhost"
    database_port: int = 5432
    database_user: str = "postgres"
    database_password: str = "111222"
    database_name: str = "aiknowledgebase"
    
    # JWT配置
    secret_key: str = "abcXyz123_4x9KpQvE8jHmN2qRtSvWnZr5t7w-"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 10080 # 7天 (7 * 24 * 60)
    
    # Redis配置
    redis_host: str = "localhost"  # 修改为localhost，适配宝塔部署
    redis_port: int = 6379
    redis_db: int = 0
    redis_url: Optional[str] = None  # 添加redis_url字段以兼容环境变量

    # 文件上传配置
    upload_dir: str = "./uploads"
    max_file_size: int = 300  # MB

    # AI模型配置
    openai_api_key: Optional[str] = None
    anthropic_api_key: Optional[str] = None
    siliconflow_api_key: str = "sk-ltvyukqqyhwhedkxyqhsqnrtqehlfdzpflskkfkqwetoiixm"
    deepseek_api_key: Optional[str] = None
    google_api_key: Optional[str] = None
    
    class Config:
        env_file = ".env"
        case_sensitive = False


class ProcessingConfig:
    """文档处理配置"""

    # 文件大小分类阈值 (MB)
    LARGE_FILE_THRESHOLD = 10   # 大文件阈值：10MB
    MEDIUM_FILE_THRESHOLD = 1   # 中等文件阈值：1MB

    # 并发控制配置 - 根据您的要求调整大文件并发数为10
    LARGE_FILE_CONCURRENCY = 10   # 大文件并发数：10个
    MEDIUM_FILE_CONCURRENCY = 5   # 中等文件并发数：5个
    SMALL_FILE_CONCURRENCY = 10   # 小文件并发数：10个

    # 批次处理配置
    PDF_BATCH_SIZE = 30           # PDF分批处理页数
    VECTOR_BATCH_SIZE = 50        # 向量化批次大小

    # 超时配置
    LARGE_FILE_TIMEOUT = 600      # 大文件处理超时：10分钟
    MEDIUM_FILE_TIMEOUT = 300     # 中等文件处理超时：5分钟
    SMALL_FILE_TIMEOUT = 120      # 小文件处理超时：2分钟

    @classmethod
    def get_file_category(cls, file_size_mb: float) -> str:
        """根据文件大小获取文件类别"""
        if file_size_mb > cls.LARGE_FILE_THRESHOLD:
            return "large"
        elif file_size_mb > cls.MEDIUM_FILE_THRESHOLD:
            return "medium"
        else:
            return "small"

    @classmethod
    def get_concurrency_limit(cls, file_size_mb: float) -> int:
        """根据文件大小获取并发限制"""
        category = cls.get_file_category(file_size_mb)
        if category == "large":
            return cls.LARGE_FILE_CONCURRENCY
        elif category == "medium":
            return cls.MEDIUM_FILE_CONCURRENCY
        else:
            return cls.SMALL_FILE_CONCURRENCY

    @classmethod
    def get_timeout(cls, file_size_mb: float) -> int:
        """根据文件大小获取超时时间"""
        category = cls.get_file_category(file_size_mb)
        if category == "large":
            return cls.LARGE_FILE_TIMEOUT
        elif category == "medium":
            return cls.MEDIUM_FILE_TIMEOUT
        else:
            return cls.SMALL_FILE_TIMEOUT

    @classmethod
    def get_batch_size(cls, file_size_mb: float) -> int:
        """根据文件大小获取批次大小"""
        if file_size_mb > cls.LARGE_FILE_THRESHOLD:
            return cls.PDF_BATCH_SIZE
        else:
            return cls.PDF_BATCH_SIZE * 2  # 小文件可以用更大的批次


# 全局设置实例
settings = Settings()
