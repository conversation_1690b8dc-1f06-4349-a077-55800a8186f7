"""
知识库管理API
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlmodel import Session, select
from app.core.database import SessionDep, get_session
from app.core.auth import get_current_active_user
from app.models.user import User, UserQuota
from app.models.knowledge_base import KnowledgeBase, Document, DocumentChunk
from app.schemas.knowledge_base import (
    KnowledgeBaseCreate, 
    KnowledgeBaseUpdate, 
    KnowledgeBaseResponse,
    KnowledgeBaseWithDocuments
)

router = APIRouter()


@router.post("/", response_model=KnowledgeBaseResponse)
async def create_knowledge_base(
    kb_data: KnowledgeBaseCreate,
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep
):
    """创建知识库"""
    # 获取用户配额
    quota_statement = select(UserQuota).where(UserQuota.user_id == current_user.id)
    user_quota = session.exec(quota_statement).first()

    # 如果没有配额记录，创建默认配额
    if not user_quota:
        user_quota = UserQuota(
            user_id=current_user.id,
            max_kbs=5,  # 默认最多5个知识库
            max_docs_per_kb=100,  # 默认每个知识库最多100个文档
            max_storage_mb=1024  # 默认最多1GB存储
        )
        session.add(user_quota)
        session.commit()
        session.refresh(user_quota)

    # 检查用户当前知识库数量
    statement = select(KnowledgeBase).where(KnowledgeBase.owner_id == current_user.id)
    user_kbs = session.exec(statement).all()

    # 检查是否达到知识库数量限制
    if len(user_kbs) >= user_quota.max_kbs:
        raise HTTPException(
            status_code=400,
            detail=f"已达到知识库数量限制，最多可创建 {user_quota.max_kbs} 个知识库"
        )

    new_kb = KnowledgeBase(
        name=kb_data.name,
        description=kb_data.description,
        owner_id=current_user.id
    )

    session.add(new_kb)
    session.commit()
    session.refresh(new_kb)

    return new_kb


@router.get("/", response_model=List[KnowledgeBaseResponse])
async def get_knowledge_bases(
    current_user: User = Depends(get_current_active_user),
    session: Session = Depends(get_session),
    offset: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100)
):
    """获取用户的知识库列表"""
    from sqlmodel import func
    from app.models.knowledge_base import Document

    statement = (
        select(KnowledgeBase)
        .where(KnowledgeBase.owner_id == current_user.id)
        .offset(offset)
        .limit(limit)
    )
    knowledge_bases = session.exec(statement).all()

    # 为每个知识库添加文档数量
    result = []
    for kb in knowledge_bases:
        # 统计该知识库的文档数量
        doc_count_statement = select(func.count(Document.id)).where(Document.kb_id == kb.id)
        doc_count = session.exec(doc_count_statement).first() or 0

        # 转换为字典并添加文档数量
        kb_dict = kb.model_dump()
        kb_dict["document_count"] = doc_count
        result.append(kb_dict)

    return result


@router.get("/{kb_id}", response_model=KnowledgeBaseWithDocuments)
async def get_knowledge_base(
    kb_id: int,
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep
):
    """获取特定知识库详情"""
    statement = select(KnowledgeBase).where(
        KnowledgeBase.id == kb_id,
        KnowledgeBase.owner_id == current_user.id
    )
    kb = session.exec(statement).first()
    
    if not kb:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识库不存在"
        )
    
    # 获取知识库的文档
    doc_statement = select(Document).where(Document.kb_id == kb_id)
    documents = session.exec(doc_statement).all()
    
    # 构造响应
    kb_dict = kb.model_dump()
    kb_dict["documents"] = documents
    
    return kb_dict


@router.put("/{kb_id}", response_model=KnowledgeBaseResponse)
async def update_knowledge_base(
    kb_id: int,
    kb_update: KnowledgeBaseUpdate,
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep
):
    """更新知识库"""
    statement = select(KnowledgeBase).where(
        KnowledgeBase.id == kb_id,
        KnowledgeBase.owner_id == current_user.id
    )
    kb = session.exec(statement).first()
    
    if not kb:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识库不存在"
        )
    
    # 更新字段
    if kb_update.name is not None:
        kb.name = kb_update.name
    if kb_update.description is not None:
        kb.description = kb_update.description
    
    session.add(kb)
    session.commit()
    session.refresh(kb)
    
    return kb


@router.delete("/{kb_id}")
async def delete_knowledge_base(
    kb_id: int,
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep
):
    """删除知识库"""
    statement = select(KnowledgeBase).where(
        KnowledgeBase.id == kb_id,
        KnowledgeBase.owner_id == current_user.id
    )
    kb = session.exec(statement).first()
    
    if not kb:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识库不存在"
        )
    
    # 级联删除相关数据
    try:
        # 1. 删除知识库下的所有文档分块
        chunks_statement = select(DocumentChunk).join(Document).where(Document.kb_id == kb_id)
        chunks = session.exec(chunks_statement).all()
        for chunk in chunks:
            session.delete(chunk)

        # 2. 删除知识库下的所有文档
        docs_statement = select(Document).where(Document.kb_id == kb_id)
        documents = session.exec(docs_statement).all()
        for doc in documents:
            session.delete(doc)

        # 3. 删除知识库本身
        session.delete(kb)
        session.commit()

        return {"message": "知识库删除成功"}
    except Exception as e:
        session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除知识库失败: {str(e)}"
        )
