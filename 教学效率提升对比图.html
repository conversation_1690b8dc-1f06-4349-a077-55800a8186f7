<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教学效率提升对比图</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 30px;
        }
        
        .title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            letter-spacing: -0.5px;
        }
        
        .subtitle {
            font-size: 16px;
            opacity: 0.9;
            font-weight: 400;
        }
        
        .content {
            padding: 40px;
            background: white;
        }
        
        .metrics-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .metric-card {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            border-left: 4px solid #10b981;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }
        
        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(16, 185, 129, 0.15);
        }
        
        .metric-title {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: 700;
            color: #059669;
            margin-bottom: 5px;
        }
        
        .metric-desc {
            font-size: 12px;
            color: #6b7280;
        }
        
        .chart-section {
            margin-bottom: 40px;
        }
        
        .section-title {
            font-size: 22px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 20px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
            margin-bottom: 30px;
        }
        
        .efficiency-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-top: 40px;
        }
        
        .detail-card {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            padding: 25px;
            border-radius: 12px;
            border-left: 4px solid #667eea;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        .detail-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .detail-content {
            color: #64748b;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .improvement-list {
            list-style: none;
            margin-top: 15px;
        }
        
        .improvement-list li {
            padding: 8px 0;
            color: #475569;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .improvement-list li::before {
            content: '✓';
            color: #10b981;
            font-weight: bold;
            font-size: 14px;
        }
        
        .time-comparison {
            background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
            padding: 30px;
            border-radius: 15px;
            margin-top: 40px;
        }
        
        .comparison-title {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .comparison-item {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .comparison-task {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 15px;
        }
        
        .time-before {
            font-size: 14px;
            color: #dc2626;
            margin-bottom: 5px;
        }
        
        .time-after {
            font-size: 14px;
            color: #059669;
            margin-bottom: 10px;
        }
        
        .time-saved {
            font-size: 18px;
            font-weight: 700;
            color: #7c3aed;
            background: #f3e8ff;
            padding: 8px 12px;
            border-radius: 20px;
            display: inline-block;
        }
        
        @media (max-width: 768px) {
            .metrics-overview {
                grid-template-columns: 1fr;
            }
            
            .chart-container {
                height: 300px;
                padding: 20px;
            }
            
            .efficiency-details {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">📊 教学效率提升对比图</div>
            <div class="subtitle">Teaching Efficiency Improvement Comparison</div>
        </div>
        
        <div class="content">
            <!-- 核心指标概览 -->
            <div class="metrics-overview">
                <div class="metric-card">
                    <div class="metric-title">教案准备时间</div>
                    <div class="metric-value">↓70%</div>
                    <div class="metric-desc">8小时 → 2.4小时</div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-title">答疑效率</div>
                    <div class="metric-value">↑300%</div>
                    <div class="metric-desc">响应速度大幅提升</div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-title">课程设计</div>
                    <div class="metric-value">↓60%</div>
                    <div class="metric-desc">设计时间显著减少</div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-title">题库整理</div>
                    <div class="metric-value">↑200%</div>
                    <div class="metric-desc">整理速度大幅提升</div>
                </div>
            </div>
            
            <!-- 效率提升对比图表 -->
            <div class="chart-section">
                <div class="section-title">
                    📈 教师工作效率对比
                </div>
                <div class="chart-container">
                    <canvas id="efficiencyChart"></canvas>
                </div>
            </div>

            <!-- 详细时间对比 -->
            <div class="time-comparison">
                <div class="comparison-title">⏱️ 具体工作时间对比</div>
                <div class="comparison-grid">
                    <div class="comparison-item">
                        <div class="comparison-task">📝 教案准备</div>
                        <div class="time-before">使用前：8小时</div>
                        <div class="time-after">使用后：2.4小时</div>
                        <div class="time-saved">节省70%</div>
                    </div>

                    <div class="comparison-item">
                        <div class="comparison-task">❓ 学生答疑</div>
                        <div class="time-before">使用前：响应慢</div>
                        <div class="time-after">使用后：即时响应</div>
                        <div class="time-saved">效率↑300%</div>
                    </div>

                    <div class="comparison-item">
                        <div class="comparison-task">🎯 课程设计</div>
                        <div class="time-before">使用前：设计复杂</div>
                        <div class="time-after">使用后：智能辅助</div>
                        <div class="time-saved">省时60%</div>
                    </div>

                    <div class="comparison-item">
                        <div class="comparison-task">📚 题库整理</div>
                        <div class="time-before">使用前：手工整理</div>
                        <div class="time-after">使用后：智能分类</div>
                        <div class="time-saved">提速200%</div>
                    </div>
                </div>
            </div>

            <!-- 效率提升详情 -->
            <div class="efficiency-details">
                <div class="detail-card">
                    <div class="detail-title">
                        🚀 教案准备效率提升
                    </div>
                    <div class="detail-content">
                        通过AI智能辅助，教师教案准备时间从8小时缩短至2.4小时，节省70%的时间。
                        系统能够快速生成教学大纲、知识点梳理和教学活动设计。
                    </div>
                    <ul class="improvement-list">
                        <li>智能生成教学大纲</li>
                        <li>自动梳理知识点</li>
                        <li>推荐教学活动</li>
                        <li>快速内容检索</li>
                    </ul>
                </div>

                <div class="detail-card">
                    <div class="detail-title">
                        💬 答疑效率大幅提升
                    </div>
                    <div class="detail-content">
                        基于RAG技术的智能问答系统，答疑效率提升300%，实现24小时即时响应，
                        大大减轻教师答疑负担。
                    </div>
                    <ul class="improvement-list">
                        <li>24小时即时响应</li>
                        <li>准确率超过95%</li>
                        <li>多轮对话支持</li>
                        <li>个性化解答</li>
                    </ul>
                </div>

                <div class="detail-card">
                    <div class="detail-title">
                        🎨 课程设计智能化
                    </div>
                    <div class="detail-content">
                        AI辅助课程设计，省时60%。系统能够根据教学目标自动推荐课程结构、
                        教学方法和评估方式。
                    </div>
                    <ul class="improvement-list">
                        <li>智能课程结构设计</li>
                        <li>教学方法推荐</li>
                        <li>评估方式建议</li>
                        <li>内容适配优化</li>
                    </ul>
                </div>

                <div class="detail-card">
                    <div class="detail-title">
                        📊 题库管理自动化
                    </div>
                    <div class="detail-content">
                        智能题库整理系统，提速200%。自动分类、标签化管理，
                        快速检索和组卷功能大幅提升工作效率。
                    </div>
                    <ul class="improvement-list">
                        <li>自动题目分类</li>
                        <li>智能标签管理</li>
                        <li>快速检索功能</li>
                        <li>一键组卷生成</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 教学效率对比图表
        const ctx = document.getElementById('efficiencyChart').getContext('2d');

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['教案准备时间', '答疑响应速度', '课程设计时间', '题库整理速度'],
                datasets: [
                    {
                        label: '使用前',
                        data: [8, 1, 100, 1], // 教案8h, 答疑基准1, 课程设计基准100%, 题库基准1
                        backgroundColor: 'rgba(239, 68, 68, 0.8)',
                        borderColor: 'rgba(239, 68, 68, 1)',
                        borderWidth: 2,
                        borderRadius: 8,
                        borderSkipped: false
                    },
                    {
                        label: '使用后',
                        data: [2.4, 4, 40, 3], // 教案2.4h, 答疑4倍效率, 课程设计40%, 题库3倍速度
                        backgroundColor: 'rgba(16, 185, 129, 0.8)',
                        borderColor: 'rgba(16, 185, 129, 1)',
                        borderWidth: 2,
                        borderRadius: 8,
                        borderSkipped: false
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '教师工作效率对比（使用前 vs 使用后）',
                        font: {
                            size: 18,
                            weight: 'bold'
                        },
                        color: '#1e293b',
                        padding: 20
                    },
                    legend: {
                        position: 'top',
                        labels: {
                            font: {
                                size: 14,
                                weight: '500'
                            },
                            color: '#1e293b',
                            usePointStyle: true,
                            pointStyle: 'rect'
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: '#667eea',
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                const datasetLabel = context.dataset.label;
                                const value = context.parsed.y;
                                const label = context.label;

                                if (label === '教案准备时间') {
                                    return datasetLabel + ': ' + value + '小时';
                                } else if (label === '答疑响应速度') {
                                    return datasetLabel + ': ' + (value === 1 ? '基准速度' : value + '倍效率');
                                } else if (label === '课程设计时间') {
                                    return datasetLabel + ': ' + value + '%时间';
                                } else if (label === '题库整理速度') {
                                    return datasetLabel + ': ' + (value === 1 ? '基准速度' : value + '倍速度');
                                }
                            },
                            afterLabel: function(context) {
                                const label = context.label;
                                if (context.dataset.label === '使用后') {
                                    if (label === '教案准备时间') {
                                        return '节省: 70%';
                                    } else if (label === '答疑响应速度') {
                                        return '提升: 300%';
                                    } else if (label === '课程设计时间') {
                                        return '节省: 60%';
                                    } else if (label === '题库整理速度') {
                                        return '提升: 200%';
                                    }
                                }
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            font: {
                                size: 12
                            },
                            color: '#64748b'
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)',
                            drawBorder: false
                        },
                        title: {
                            display: true,
                            text: '效率指标（相对值）',
                            font: {
                                size: 14,
                                weight: 'bold'
                            },
                            color: '#1e293b'
                        }
                    },
                    x: {
                        ticks: {
                            font: {
                                size: 12,
                                weight: '500'
                            },
                            color: '#1e293b'
                        },
                        grid: {
                            display: false
                        }
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeInOutQuart'
                }
            }
        });
    </script>
</body>
</html>
