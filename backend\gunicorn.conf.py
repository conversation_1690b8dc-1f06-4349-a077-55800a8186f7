"""
Gunicorn配置文件
用于宝塔环境部署
"""
import multiprocessing
import os

# 服务器配置
bind = "127.0.0.1:8000"
workers = 1  # 单进程，避免资源冲突

# Worker配置
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 1000
timeout = 300  # 增加超时时间到5分钟，适应大文件处理
keepalive = 2
max_requests = 1000
max_requests_jitter = 100

# 日志配置
accesslog = "-"
errorlog = "-"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# 进程配置
preload_app = True
daemon = False

# 临时文件目录
tmp_upload_dir = None

# 安全配置
limit_request_line = 8192
limit_request_fields = 100
limit_request_field_size = 8192

# 性能配置
worker_tmp_dir = "/dev/shm" if os.path.exists("/dev/shm") else None

def when_ready(server):
    """服务器启动完成时的回调"""
    print("AI知识库后端服务已启动")
    print(f"Workers: {workers}")
    print(f"Timeout: {timeout}s")
    print(f"Bind: {bind}")

def worker_int(worker):
    """Worker收到SIGINT信号时的回调"""
    print(f"Worker {worker.pid} 收到中断信号")

def worker_abort(worker):
    """Worker被强制终止时的回调"""
    print(f"Worker {worker.pid} 被强制终止")
    
def on_exit(server):
    """服务器退出时的回调"""
    print("AI知识库后端服务已停止")
