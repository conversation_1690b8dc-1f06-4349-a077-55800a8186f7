<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教学领域覆盖图表</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 40px;
        }
        
        .title {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 15px;
            letter-spacing: -0.5px;
        }
        
        .subtitle {
            font-size: 18px;
            opacity: 0.9;
            font-weight: 400;
            margin-bottom: 20px;
        }
        
        .coverage-badge {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
        }
        
        .content {
            padding: 50px;
            background: white;
        }
        
        .coverage-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 25px;
            margin-bottom: 50px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            border-left: 4px solid #667eea;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 30px rgba(102, 126, 234, 0.15);
        }
        
        .stat-icon {
            font-size: 32px;
            margin-bottom: 15px;
        }
        
        .stat-title {
            font-size: 16px;
            color: #64748b;
            margin-bottom: 10px;
            font-weight: 500;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
        }
        
        .stat-desc {
            font-size: 13px;
            color: #6b7280;
        }
        
        .main-chart-section {
            margin-bottom: 50px;
        }
        
        .chart-title {
            font-size: 24px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .chart-container {
            position: relative;
            height: 500px;
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 2px solid #f1f5f9;
        }
        
        .coverage-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-top: 50px;
        }
        
        .detail-section {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            padding: 35px;
            border-radius: 15px;
            border-left: 4px solid #667eea;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
        }
        
        .detail-title {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .detail-content {
            color: #64748b;
            font-size: 15px;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .category-list {
            display: grid;
            gap: 15px;
        }
        
        .category-item {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            border-left: 3px solid #667eea;
        }
        
        .category-name {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
        }
        
        .category-desc {
            font-size: 13px;
            color: #64748b;
            margin-bottom: 10px;
        }
        
        .subject-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
        }
        
        .subject-tag {
            background: #667eea;
            color: white;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }
        
        .summary-section {
            background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
            padding: 40px;
            border-radius: 20px;
            margin-top: 50px;
            text-align: center;
        }
        
        .summary-title {
            font-size: 24px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 20px;
        }
        
        .summary-content {
            color: #64748b;
            font-size: 16px;
            line-height: 1.8;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .highlight {
            color: #667eea;
            font-weight: 600;
        }
        
        .highlight-green {
            color: #10b981;
            font-weight: 600;
        }
        
        .highlight-orange {
            color: #f59e0b;
            font-weight: 600;
        }

        .chart-explanation {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 25px;
            border-radius: 15px;
            border-left: 4px solid #667eea;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
            height: fit-content;
        }

        .explanation-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 20px;
            text-align: center;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }

        .explanation-section {
            margin-bottom: 20px;
        }

        .explanation-subtitle {
            font-size: 14px;
            font-weight: 600;
            color: #475569;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .explanation-text {
            font-size: 12px;
            color: #64748b;
            line-height: 1.5;
            margin-left: 15px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 6px 0;
            margin-left: 15px;
            font-size: 12px;
            color: #475569;
        }

        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 3px;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        @media (max-width: 768px) {
            .main-chart-section > div {
                grid-template-columns: 1fr !important;
            }

            .chart-explanation {
                order: -1;
                margin-bottom: 20px;
            }
            .content {
                padding: 30px;
            }
            
            .coverage-stats {
                grid-template-columns: 1fr;
            }
            
            .coverage-details {
                grid-template-columns: 1fr;
            }
            
            .chart-container {
                height: 400px;
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">📚 教学领域覆盖图表</div>
            <div class="subtitle">Educational Field Coverage Analysis</div>
            <div class="coverage-badge">
                🌍 全领域覆盖
            </div>
        </div>
        
        <div class="content">
            <!-- 覆盖统计概览 -->
            <div class="coverage-stats">
                <div class="stat-card">
                    <div class="stat-icon">🎓</div>
                    <div class="stat-title">教育层次</div>
                    <div class="stat-value">4大层次</div>
                    <div class="stat-desc">基础/高等/继续/企业</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">📚</div>
                    <div class="stat-title">学科类别</div>
                    <div class="stat-value">4大类别</div>
                    <div class="stat-desc">理工/人文/艺术/语言</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">🌟</div>
                    <div class="stat-title">具体学科</div>
                    <div class="stat-value">15+学科</div>
                    <div class="stat-desc">全面专业覆盖</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">👥</div>
                    <div class="stat-title">适用人群</div>
                    <div class="stat-value">全年龄段</div>
                    <div class="stat-desc">个性化适配</div>
                </div>
            </div>
            
            <!-- 主要图表展示 -->
            <div class="main-chart-section">
                <div class="chart-title">📊 教学领域覆盖分布</div>
                <div style="display: grid; grid-template-columns: 1fr 350px; gap: 30px; align-items: start;">
                    <div class="chart-container">
                        <canvas id="coverageChart"></canvas>
                    </div>

                    <!-- 图表说明 -->
                    <div class="chart-explanation">
                        <div class="explanation-title">📋 图表说明</div>

                        <div class="explanation-section">
                            <div class="explanation-subtitle"> 教育层次</div>
                            <div class="legend-item">
                                <div class="legend-color" style="background: rgba(59, 130, 246, 0.7);"></div>
                                <span>基础教育 95%</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color" style="background: rgba(16, 185, 129, 0.7);"></div>
                                <span>高等教育 90%</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color" style="background: rgba(245, 158, 11, 0.7);"></div>
                                <span>继续教育 85%</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color" style="background: rgba(239, 68, 68, 0.7);"></div>
                                <span>企业培训 80%</span>
                            </div>
                        </div>

                        <div class="explanation-section">
                            <div class="explanation-subtitle">📚 学科类别</div>
                            <div class="legend-item">
                                <div class="legend-color" style="background: rgba(139, 92, 246, 0.7);"></div>
                                <span>理工类 92%</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color" style="background: rgba(6, 182, 212, 0.7);"></div>
                                <span>人文社科 88%</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color" style="background: rgba(249, 115, 22, 0.7);"></div>
                                <span>艺术类 75%</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color" style="background: rgba(132, 204, 22, 0.7);"></div>
                                <span>语言类 82%</span>
                            </div>
                        </div>

                        <div class="explanation-section">
                            <div class="explanation-subtitle">📊 说明</div>
                            <div class="explanation-text">
                                扇形面积表示覆盖程度<br>
                                数值为该领域覆盖完整度<br>
                                实现全教育生态链覆盖
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 详细覆盖说明 -->
            <div class="coverage-details">
                <div class="detail-section">
                    <div class="detail-title">
                        🎓 教育层次覆盖
                    </div>
                    <div class="detail-content">
                        平台全面覆盖各个教育阶段，从基础教育到企业培训，
                        为不同年龄段和学习需求的用户提供专业的AI教学服务。
                    </div>
                    <div class="category-list">
                        <div class="category-item">
                            <div class="category-name">基础教育</div>
                            <div class="category-desc">覆盖K12教育全阶段</div>
                            <div class="subject-tags">
                                <div class="subject-tag">小学</div>
                                <div class="subject-tag">初中</div>
                                <div class="subject-tag">高中</div>
                            </div>
                        </div>

                        <div class="category-item">
                            <div class="category-name">高等教育</div>
                            <div class="category-desc">大学本科及研究生教育</div>
                            <div class="subject-tags">
                                <div class="subject-tag">本科</div>
                                <div class="subject-tag">硕士</div>
                                <div class="subject-tag">博士</div>
                            </div>
                        </div>

                        <div class="category-item">
                            <div class="category-name">继续教育</div>
                            <div class="category-desc">成人教育和技能培训</div>
                            <div class="subject-tags">
                                <div class="subject-tag">在职教育</div>
                                <div class="subject-tag">技能培训</div>
                                <div class="subject-tag">自学考试</div>
                            </div>
                        </div>

                        <div class="category-item">
                            <div class="category-name">企业培训</div>
                            <div class="category-desc">职业认证和专业培训</div>
                            <div class="subject-tags">
                                <div class="subject-tag">职业认证</div>
                                <div class="subject-tag">专业培训</div>
                                <div class="subject-tag">技能提升</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="detail-section">
                    <div class="detail-title">
                        📚 学科领域覆盖
                    </div>
                    <div class="detail-content">
                        涵盖理工、人文社科、艺术、语言四大学科类别，
                        为各专业领域提供精准的知识问答和学习指导。
                    </div>
                    <div class="category-list">
                        <div class="category-item">
                            <div class="category-name">理工类</div>
                            <div class="category-desc">STEM学科全覆盖</div>
                            <div class="subject-tags">
                                <div class="subject-tag">数学</div>
                                <div class="subject-tag">物理</div>
                                <div class="subject-tag">化学</div>
                                <div class="subject-tag">计算机</div>
                                <div class="subject-tag">工程</div>
                            </div>
                        </div>

                        <div class="category-item">
                            <div class="category-name">人文社科</div>
                            <div class="category-desc">人文社会科学领域</div>
                            <div class="subject-tags">
                                <div class="subject-tag">语文</div>
                                <div class="subject-tag">历史</div>
                                <div class="subject-tag">地理</div>
                                <div class="subject-tag">政治</div>
                            </div>
                        </div>

                        <div class="category-item">
                            <div class="category-name">艺术类</div>
                            <div class="category-desc">创意艺术设计领域</div>
                            <div class="subject-tags">
                                <div class="subject-tag">美术</div>
                                <div class="subject-tag">音乐</div>
                                <div class="subject-tag">设计</div>
                            </div>
                        </div>

                        <div class="category-item">
                            <div class="category-name">语言类</div>
                            <div class="category-desc">多语种外语教学</div>
                            <div class="subject-tags">
                                <div class="subject-tag">英语</div>
                                <div class="subject-tag">日语</div>
                                <div class="subject-tag">法语</div>
                                <div class="subject-tag">德语</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 总结 -->
            <div class="summary-section">
                <div class="summary-title">🎯 教学领域全覆盖优势</div>
                <div class="summary-content">
                    平台实现了<span class="highlight">4大教育层次</span>和<span class="highlight">4大学科类别</span>的全面覆盖，
                    从<span class="highlight-green">基础教育到企业培训</span>，从<span class="highlight-orange">理工科到人文艺术</span>，
                    涵盖<span class="highlight">15+具体学科</span>，为各个教育领域提供专业、精准的AI教学服务。
                    真正实现了教育数字化的<span class="highlight">全场景应用</span>，
                    满足<span class="highlight-green">全年龄段学习者</span>的多样化学习需求。
                </div>
            </div>
        </div>
    </div>

    <script>
        // 教学领域覆盖分布图表（使用极坐标面积图）
        const ctx = document.getElementById('coverageChart').getContext('2d');

        new Chart(ctx, {
            type: 'polarArea',
            data: {
                labels: [
                    '基础教育\n(小/初/高)',
                    '高等教育\n(本/硕/博)',
                    '继续教育\n(在职/技能)',
                    '企业培训\n(认证/专业)',
                    '理工类\n(数理化/计算机)',
                    '人文社科\n(语史地政)',
                    '艺术类\n(美术/音乐)',
                    '语言类\n(英日法德)'
                ],
                datasets: [{
                    label: '覆盖程度',
                    data: [95, 90, 85, 80, 92, 88, 75, 82], // 各领域覆盖程度
                    backgroundColor: [
                        'rgba(59, 130, 246, 0.7)',   // 蓝色 - 基础教育
                        'rgba(16, 185, 129, 0.7)',   // 绿色 - 高等教育
                        'rgba(245, 158, 11, 0.7)',   // 橙色 - 继续教育
                        'rgba(239, 68, 68, 0.7)',    // 红色 - 企业培训
                        'rgba(139, 92, 246, 0.7)',   // 紫色 - 理工类
                        'rgba(6, 182, 212, 0.7)',    // 青色 - 人文社科
                        'rgba(249, 115, 22, 0.7)',   // 橙红 - 艺术类
                        'rgba(132, 204, 22, 0.7)'    // 绿黄 - 语言类
                    ],
                    borderColor: [
                        'rgba(59, 130, 246, 1)',
                        'rgba(16, 185, 129, 1)',
                        'rgba(245, 158, 11, 1)',
                        'rgba(239, 68, 68, 1)',
                        'rgba(139, 92, 246, 1)',
                        'rgba(6, 182, 212, 1)',
                        'rgba(249, 115, 22, 1)',
                        'rgba(132, 204, 22, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '教学领域覆盖分布（教育层次 + 学科类别）',
                        font: {
                            size: 18,
                            weight: 'bold'
                        },
                        color: '#1e293b',
                        padding: 20
                    },
                    legend: {
                        display: false // 隐藏图例，因为标签已经很详细
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: '#667eea',
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            title: function(context) {
                                return context[0].label.replace('\n', ' ');
                            },
                            label: function(context) {
                                const value = context.parsed.r;
                                const label = context.label;

                                let description = '';
                                if (label.includes('基础教育')) {
                                    description = 'K12教育全阶段覆盖';
                                } else if (label.includes('高等教育')) {
                                    description = '大学本科及研究生教育';
                                } else if (label.includes('继续教育')) {
                                    description = '成人教育和技能培训';
                                } else if (label.includes('企业培训')) {
                                    description = '职业认证和专业培训';
                                } else if (label.includes('理工类')) {
                                    description = 'STEM学科全覆盖';
                                } else if (label.includes('人文社科')) {
                                    description = '人文社会科学领域';
                                } else if (label.includes('艺术类')) {
                                    description = '创意艺术设计领域';
                                } else if (label.includes('语言类')) {
                                    description = '多语种外语教学';
                                }

                                return [
                                    '覆盖程度: ' + value + '%',
                                    description
                                ];
                            }
                        }
                    }
                },
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 100,
                        min: 0,
                        ticks: {
                            stepSize: 20,
                            font: {
                                size: 11
                            },
                            color: '#64748b',
                            callback: function(value) {
                                return value + '%';
                            }
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        angleLines: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        pointLabels: {
                            font: {
                                size: 11,
                                weight: '500'
                            },
                            color: '#1e293b'
                        }
                    }
                },
                animation: {
                    duration: 2500,
                    easing: 'easeInOutQuart'
                }
            }
        });
    </script>
</body>
</html>
