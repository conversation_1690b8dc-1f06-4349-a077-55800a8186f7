"""
FastAPI 主应用入口
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.config import settings
from app.core.database import create_db_and_tables
from app.services.ai_service import initialize_ai_clients, get_ai_service
from app.services.vector_service import get_vector_service
from app.services.cache_service import get_cache_service
import asyncio
import logging

# 创建FastAPI应用实例
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="智能知识库平台后端API",
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 临时允许所有来源进行调试
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头部
    expose_headers=["*"],
)


@app.on_event("startup")
async def on_startup():
    """应用启动时初始化服务"""
    # 记录配置信息
    logging.info(f"Redis配置: {settings.redis_host}:{settings.redis_port}/{settings.redis_db}")
    logging.info(f"数据库URL配置: {settings.database_url}")
    logging.info(f"文件上传配置: 最大文件大小 {settings.max_file_size}MB, 上传目录 {settings.upload_dir}")

    # 创建数据库表
    create_db_and_tables()

    # 初始化缓存服务
    try:
        cache_service = get_cache_service()
        # 使用配置中的Redis参数重新初始化
        cache_service.__init__(settings.redis_host, settings.redis_port, settings.redis_db)
        await cache_service.initialize()
        logging.info("缓存服务初始化成功")
    except Exception as e:
        logging.error(f"缓存服务初始化失败: {e}")

    # 初始化向量服务
    try:
        vector_service = get_vector_service()
        # 使用配置中的Redis参数重新初始化
        vector_service.__init__(settings.redis_host, settings.redis_port, settings.redis_db)
        await vector_service.initialize()
        logging.info("向量服务初始化成功")
    except Exception as e:
        logging.error(f"向量服务初始化失败: {e}")

    # 初始化AI客户端
    try:
        providers_config = {
            "siliconflow": {
                "api_key": "sk-ltvyukqqyhwhedkxyqhsqnrtqehlfdzpflskkfkqwetoiixm",
                "base_url": "https://api.siliconflow.cn/v1"
            }
            # 可以添加更多AI提供商配置
        }
        await initialize_ai_clients(providers_config)
        logging.info("AI客户端初始化成功")
    except Exception as e:
        logging.error(f"AI客户端初始化失败: {e}")
        # 不抛出异常，允许系统在没有AI客户端的情况下运行


@app.on_event("shutdown")
async def on_shutdown():
    """应用关闭时清理资源"""
    try:
        cache_service = get_cache_service()
        await cache_service.close()
        logging.info("缓存服务已关闭")
    except Exception as e:
        logging.error(f"缓存服务关闭失败: {e}")


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": f"欢迎使用 {settings.app_name}",
        "version": settings.app_version,
        "docs": "/docs"
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy"}


# 导入和注册路由
from app.api import auth, users, knowledge_bases, documents, chat, ai_models, admin, stats
app.include_router(auth.router, prefix="/api/auth", tags=["认证"])
app.include_router(users.router, prefix="/api/users", tags=["用户"])
app.include_router(knowledge_bases.router, prefix="/api/knowledge-bases", tags=["知识库"])
app.include_router(documents.router, prefix="/api/documents", tags=["文档"])
app.include_router(chat.router, prefix="/api/chat", tags=["聊天"])
app.include_router(ai_models.router, prefix="/api/ai", tags=["AI模型"])
app.include_router(admin.router, prefix="/api/admin", tags=["管理"])
app.include_router(stats.router, prefix="/api/stats", tags=["统计"])


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug
    )
