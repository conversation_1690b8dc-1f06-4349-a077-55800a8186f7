<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统稳定性监控图表</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
            padding: 20px;
            color: #1e293b;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(15, 23, 42, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 40px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .title {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 15px;
            letter-spacing: -0.5px;
        }
        
        .subtitle {
            font-size: 18px;
            opacity: 0.8;
            font-weight: 400;
            margin-bottom: 20px;
        }
        
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            padding: 12px 24px;
            background: rgba(16, 185, 129, 0.2);
            border: 2px solid #10b981;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
        }
        
        .status-dot {
            width: 12px;
            height: 12px;
            background: #10b981;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
        }
        
        .content {
            padding: 50px;
            background: white;
        }
        
        .main-chart-section {
            margin-bottom: 50px;
        }
        
        .chart-title {
            font-size: 24px;
            font-weight: 600;
            color: white;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .chart-container {
            position: relative;
            height: 500px;
            background: white;
            border-radius: 20px;
            padding: 40px;
            border: 2px solid #f1f5f9;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        
        .metric-card {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 30px;
            border-radius: 15px;
            border: 2px solid #f1f5f9;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #10b981, #3b82f6, #8b5cf6);
        }
        
        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.15);
        }
        
        .metric-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        
        .metric-title {
            font-size: 16px;
            color: #64748b;
            font-weight: 500;
        }
        
        .metric-icon {
            font-size: 28px;
        }
        
        .metric-value {
            font-size: 36px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 10px;
        }
        
        .metric-desc {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 15px;
        }
        
        .metric-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .status-excellent {
            color: #10b981;
        }
        
        .status-good {
            color: #3b82f6;
        }
        
        .status-warning {
            color: #f59e0b;
        }
        
        .performance-summary {
            background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
            padding: 40px;
            border-radius: 20px;
            margin-top: 40px;
            border: 2px solid #f1f5f9;
            text-align: center;
        }
        
        .summary-title {
            font-size: 24px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 20px;
        }
        
        .summary-content {
            color: #64748b;
            font-size: 16px;
            line-height: 1.8;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .highlight {
            color: #10b981;
            font-weight: 600;
        }
        
        .highlight-blue {
            color: #3b82f6;
            font-weight: 600;
        }
        
        .highlight-purple {
            color: #8b5cf6;
            font-weight: 600;
        }
        
        @media (max-width: 768px) {
            .content {
                padding: 30px;
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
            }
            
            .chart-container {
                height: 400px;
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">🔧 系统稳定性监控</div>
            <div class="subtitle">System Stability Monitoring Dashboard</div>
            <div class="status-indicator">
                <div class="status-dot"></div>
                系统运行状态：优秀
            </div>
        </div>
        
        <div class="content">
            <!-- 核心稳定性指标 -->
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-header">
                        <div class="metric-title">系统可用性</div>
                        <div class="metric-icon">🟢</div>
                    </div>
                    <div class="metric-value">99.9%</div>
                    <div class="metric-desc">7×24小时持续稳定运行</div>
                    <div class="metric-status status-excellent">
                        ✓ 运行状态优秀
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-header">
                        <div class="metric-title">平均响应时间</div>
                        <div class="metric-icon">⚡</div>
                    </div>
                    <div class="metric-value">&lt;2秒</div>
                    <div class="metric-desc">用户请求平均响应时间</div>
                    <div class="metric-status status-excellent">
                        ✓ 响应速度优秀
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-header">
                        <div class="metric-title">并发处理能力</div>
                        <div class="metric-icon">👥</div>
                    </div>
                    <div class="metric-value">1000+</div>
                    <div class="metric-desc">同时支持用户数量</div>
                    <div class="metric-status status-excellent">
                        ✓ 高并发处理
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-header">
                        <div class="metric-title">服务运行时间</div>
                        <div class="metric-icon">⏰</div>
                    </div>
                    <div class="metric-value">24/7</div>
                    <div class="metric-desc">全天候不间断服务</div>
                    <div class="metric-status status-excellent">
                        ✓ 持续在线服务
                    </div>
                </div>
            </div>
            
            <!-- 主要图表展示 -->
            <div class="main-chart-section">
                <div class="chart-title">📊 系统稳定性综合指标</div>
                <div class="chart-container">
                    <canvas id="stabilityChart"></canvas>
                </div>
            </div>

            <!-- 性能总结 -->
            <div class="performance-summary">
                <div class="summary-title">🎯 系统稳定性总结</div>
                <div class="summary-content">
                    系统当前运行状态<span class="highlight">优秀</span>，各项稳定性指标均达到行业领先水平。
                    <span class="highlight">99.9%</span>的系统可用性确保了服务的持续稳定运行，
                    <span class="highlight-blue">&lt;2秒</span>的平均响应时间为用户提供了流畅的使用体验。
                    系统具备强大的<span class="highlight-purple">1000+并发</span>处理能力，
                    能够轻松应对大规模教育应用场景的高并发访问需求。
                    <span class="highlight">7×24小时</span>全天候不间断服务，
                    为教育机构提供了可靠的技术保障。
                </div>
            </div>
        </div>
    </div>

    <script>
        // 系统稳定性综合指标雷达图
        const ctx = document.getElementById('stabilityChart').getContext('2d');

        new Chart(ctx, {
            type: 'radar',
            data: {
                labels: [
                    '系统可用性',
                    '响应速度',
                    '并发处理',
                    '服务稳定性',
                    '故障恢复',
                    '负载均衡'
                ],
                datasets: [
                    {
                        label: '当前性能',
                        data: [99.9, 98.5, 95.0, 99.5, 97.0, 96.5],
                        backgroundColor: 'rgba(16, 185, 129, 0.2)',
                        borderColor: 'rgba(16, 185, 129, 0.8)',
                        borderWidth: 3,
                        pointBackgroundColor: 'rgba(16, 185, 129, 1)',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 6
                    },
                    {
                        label: '行业标准',
                        data: [95.0, 85.0, 80.0, 90.0, 85.0, 88.0],
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        borderColor: 'rgba(59, 130, 246, 0.6)',
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(59, 130, 246, 0.8)',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '系统稳定性指标对比（当前性能 vs 行业标准）',
                        font: {
                            size: 18,
                            weight: 'bold'
                        },
                        color: '#1e293b',
                        padding: 20
                    },
                    legend: {
                        position: 'bottom',
                        labels: {
                            font: {
                                size: 14,
                                weight: '500'
                            },
                            color: '#1e293b',
                            usePointStyle: true,
                            pointStyle: 'circle',
                            padding: 20
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: '#10b981',
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                const label = context.dataset.label;
                                const value = context.parsed.r;
                                const metric = context.label;

                                let unit = '%';
                                let description = '';

                                if (metric === '响应速度') {
                                    if (label === '当前性能') {
                                        description = ' (<2秒)';
                                    } else {
                                        description = ' (3-5秒)';
                                    }
                                } else if (metric === '并发处理') {
                                    if (label === '当前性能') {
                                        description = ' (1000+用户)';
                                    } else {
                                        description = ' (500-800用户)';
                                    }
                                }

                                return label + ': ' + value + unit + description;
                            },
                            afterLabel: function(context) {
                                if (context.dataset.label === '当前性能') {
                                    const current = context.parsed.r;
                                    const standard = context.chart.data.datasets[1].data[context.dataIndex];
                                    const improvement = ((current - standard) / standard * 100).toFixed(1);
                                    return '超越行业标准: +' + improvement + '%';
                                }
                            }
                        }
                    }
                },
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 100,
                        min: 0,
                        ticks: {
                            stepSize: 20,
                            font: {
                                size: 12
                            },
                            color: '#64748b',
                            callback: function(value) {
                                return value + '%';
                            }
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        angleLines: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        pointLabels: {
                            font: {
                                size: 13,
                                weight: '500'
                            },
                            color: '#1e293b'
                        }
                    }
                },
                animation: {
                    duration: 2500,
                    easing: 'easeInOutQuart'
                }
            }
        });

        // 实时数据更新模拟
        let updateCounter = 0;
        setInterval(() => {
            updateCounter++;

            // 模拟轻微的性能波动
            const chart = Chart.getChart('stabilityChart');
            if (chart && updateCounter % 10 === 0) {
                const currentData = chart.data.datasets[0].data;

                // 添加轻微的随机波动（±1%）
                for (let i = 0; i < currentData.length; i++) {
                    const baseValue = [99.9, 98.5, 95.0, 99.5, 97.0, 96.5][i];
                    const fluctuation = (Math.random() - 0.5) * 2; // -1 到 +1 的随机数
                    currentData[i] = Math.max(90, Math.min(100, baseValue + fluctuation));
                }

                chart.update('none'); // 无动画更新
            }
        }, 3000); // 每3秒更新一次
    </script>
</body>
</html>
