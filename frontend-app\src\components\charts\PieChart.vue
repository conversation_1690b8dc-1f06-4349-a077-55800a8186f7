<template>
  <BaseChart :option="chartOption" :width="width" :height="height" :theme="theme" />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import BaseChart from './BaseChart.vue'

interface PieData {
  name: string
  value: number
  color?: string
}

interface Props {
  data: PieData[]
  title?: string
  width?: string
  height?: string
  theme?: string
  showLegend?: boolean
  radius?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '300px',
  theme: 'light',
  showLegend: true,
  radius: () => ['40%', '70%']
})

const chartOption = computed(() => {
  const isDark = props.theme === 'dark'
  
  return {
    title: props.title ? {
      text: props.title,
      left: 'center',
      textStyle: {
        color: isDark ? '#e5e7eb' : '#374151',
        fontSize: 16,
        fontWeight: 'bold'
      }
    } : undefined,
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        const data = params.data
        const actualValue = data.actualValue !== undefined ? data.actualValue : data.value
        return `${params.seriesName}<br/>${params.name}<br/>数值: ${actualValue}<br/>占比: ${params.percent}%`
      },
      backgroundColor: isDark ? '#374151' : '#ffffff',
      borderColor: isDark ? '#4b5563' : '#e5e7eb',
      textStyle: {
        color: isDark ? '#e5e7eb' : '#374151'
      }
    },
    legend: props.showLegend ? {
      orient: 'vertical',
      right: 10,
      top: 'center',
      textStyle: {
        color: isDark ? '#e5e7eb' : '#374151'
      }
    } : undefined,
    series: [
      {
        name: props.title || '数据分布',
        type: 'pie',
        radius: props.radius,
        center: ['40%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 8,
          borderColor: isDark ? '#1f2937' : '#ffffff',
          borderWidth: 2
        },
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}\n{d}%',
          fontSize: 12,
          color: isDark ? '#e5e7eb' : '#374151'
        },
        labelLine: {
          show: true,
          length: 15,
          length2: 10
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold'
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },

        data: props.data.map((item, index) => ({
          ...item,
          itemStyle: {
            color: item.color || [
              '#3b82f6', '#10b981', '#f59e0b', '#ef4444', 
              '#8b5cf6', '#06b6d4', '#84cc16', '#f97316'
            ][index % 8]
          }
        }))
      }
    ]
  }
})
</script>
