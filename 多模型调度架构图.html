<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多模型调度架构图</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 30px;
        }
        
        .title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            letter-spacing: -0.5px;
        }
        
        .subtitle {
            font-size: 16px;
            opacity: 0.9;
            font-weight: 400;
        }
        
        .architecture {
            padding: 40px;
            background: white;
        }
        
        .layer {
            margin-bottom: 30px;
            position: relative;
        }
        
        .layer-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 15px;
            padding: 10px 20px;
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .components {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .component {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 20px;
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            text-align: center;
            min-width: 200px;
            flex: 1;
            max-width: 300px;
        }
        
        .component:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
            border-color: #667eea;
        }
        
        .component-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
        }
        
        .component-desc {
            font-size: 13px;
            color: #64748b;
            line-height: 1.4;
        }
        
        /* 特殊样式 */
        .api-layer {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            border-color: #3b82f6;
        }
        
        .adapter-layer {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border-color: #f59e0b;
        }
        
        .model-layer {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            border-color: #10b981;
        }
        
        .management-layer {
            background: linear-gradient(135deg, #fce7f3 0%, #fbcfe8 100%);
            border-color: #ec4899;
        }
        
        /* 模型卡片 */
        .models-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }
        
        .model-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .model-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }
        
        .model-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: 700;
            color: white;
        }
        
        .openai { background: linear-gradient(135deg, #10b981 0%, #059669 100%); }
        .claude { background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); }
        .deepseek { background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%); }
        .qwen { background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); }
        .siliconflow { background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%); }
        .tongyi { background: linear-gradient(135deg, #ec4899 0%, #db2777 100%); }
        
        .model-name {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
        }
        
        .model-desc {
            font-size: 12px;
            color: #64748b;
            line-height: 1.4;
        }
        
        /* 连接线 */
        .connection {
            position: relative;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .connection::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 2px;
            height: 20px;
            background: linear-gradient(to bottom, #667eea, #764ba2);
        }
        
        .connection::after {
            content: '↓';
            font-size: 20px;
            color: #667eea;
            background: white;
            padding: 0 10px;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .components {
                flex-direction: column;
            }
            
            .models-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">📊 多模型调度架构图</div>
            <div class="subtitle">Multi-Model Scheduling Architecture</div>
        </div>
        
        <div class="architecture">
            <!-- 统一API接入层 -->
            <div class="layer">
                <div class="layer-title">🌐 统一API接入层</div>
                <div class="components">
                    <div class="component api-layer">
                        <div class="component-title">请求标准化</div>
                        <div class="component-desc">统一请求格式<br/>参数规范化<br/>消息格式统一</div>
                    </div>
                    <div class="component api-layer">
                        <div class="component-title">基础错误处理</div>
                        <div class="component-desc">HTTP状态码处理<br/>异常信息解析<br/>错误消息统一</div>
                    </div>
                </div>
            </div>
            
            <div class="connection"></div>
            
            <!-- 模型适配层 -->
            <div class="layer">
                <div class="layer-title">🔄 模型适配层</div>
                <div class="components">
                    <div class="component adapter-layer">
                        <div class="component-title">协议转换</div>
                        <div class="component-desc">API协议适配<br/>请求格式转换<br/>认证方式适配</div>
                    </div>
                    <div class="component adapter-layer">
                        <div class="component-title">参数映射</div>
                        <div class="component-desc">参数名称映射<br/>数据类型转换<br/>默认值处理</div>
                    </div>
                    <div class="component adapter-layer">
                        <div class="component-title">响应标准化</div>
                        <div class="component-desc">输出格式统一<br/>错误码标准化<br/>元数据处理</div>
                    </div>
                </div>
            </div>
            
            <div class="connection"></div>

            <!-- 模型管理层 -->
            <div class="layer">
                <div class="layer-title">⚙️ 模型管理层</div>
                <div class="components">
                    <div class="component management-layer">
                        <div class="component-title">模型配置</div>
                        <div class="component-desc">模型参数设置<br/>启用/禁用状态<br/>系统密钥配置</div>
                    </div>
                    <div class="component management-layer">
                        <div class="component-title">状态检查</div>
                        <div class="component-desc">模型可用性检测<br/>API连接测试<br/>状态显示</div>
                    </div>
                    <div class="component management-layer">
                        <div class="component-title">手动切换</div>
                        <div class="component-desc">用户模型选择<br/>管理员模型管理<br/>API密钥管理</div>
                    </div>
                </div>
            </div>

            <div class="connection"></div>

            <!-- 多模型接入 -->
            <div class="layer">
                <div class="layer-title">🤖 多模型接入</div>
                <div class="models-grid">
                    <div class="model-card">
                        <div class="model-icon openai">GPT</div>
                        <div class="model-name">OpenAI GPT</div>
                        <div class="model-desc">GPT-3.5 / GPT-4<br/>强大的通用语言模型<br/>支持多种任务场景</div>
                    </div>

                    <div class="model-card">
                        <div class="model-icon claude">C</div>
                        <div class="model-name">Claude</div>
                        <div class="model-desc">Anthropic Claude系列<br/>安全可靠的AI助手<br/>擅长对话和分析</div>
                    </div>

                    <div class="model-card">
                        <div class="model-icon deepseek">DS</div>
                        <div class="model-name">DeepSeek</div>
                        <div class="model-desc">DeepSeek系列模型<br/>高性能开源模型<br/>支持代码和推理</div>
                    </div>

                    <div class="model-card">
                        <div class="model-icon qwen">Q</div>
                        <div class="model-name">Qwen</div>
                        <div class="model-desc">通义千问系列<br/>中文优化模型<br/>多模态能力强</div>
                    </div>

                    <div class="model-card">
                        <div class="model-icon siliconflow">SF</div>
                        <div class="model-name">SiliconFlow</div>
                        <div class="model-desc">模型服务聚合平台<br/>统一接口访问<br/>多模型支持</div>
                    </div>

                    <div class="model-card">
                        <div class="model-icon tongyi">TY</div>
                        <div class="model-name">通义千问</div>
                        <div class="model-desc">阿里云大模型<br/>企业级服务<br/>稳定可靠</div>
                    </div>
                </div>
            </div>

            <!-- 架构特点总结 -->
            <div style="margin-top: 40px; padding: 30px; background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%); border-radius: 12px; border: 2px dashed #667eea;">
                <h3 style="color: #1e293b; margin-bottom: 20px; font-size: 20px; text-align: center;">🎯 多模型调度架构特点</h3>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                    <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                        <h4 style="color: #667eea; margin-bottom: 10px;">🔄 统一接口</h4>
                        <p style="color: #64748b; font-size: 14px; line-height: 1.5;">
                            通过AIService提供统一的调用接口，屏蔽不同模型API的差异，简化上层应用调用。
                        </p>
                    </div>

                    <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                        <h4 style="color: #667eea; margin-bottom: 10px;">🎛️ 手动管理</h4>
                        <p style="color: #64748b; font-size: 14px; line-height: 1.5;">
                            支持用户手动选择模型，管理员可配置模型参数，灵活满足不同使用需求。
                        </p>
                    </div>

                    <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                        <h4 style="color: #667eea; margin-bottom: 10px;">� 密钥管理</h4>
                        <p style="color: #64748b; font-size: 14px; line-height: 1.5;">
                            支持用户个人API密钥和系统密钥，灵活的密钥管理策略确保服务可用性。
                        </p>
                    </div>

                    <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                        <h4 style="color: #667eea; margin-bottom: 10px;">📊 状态监控</h4>
                        <p style="color: #64748b; font-size: 14px; line-height: 1.5;">
                            提供模型可用性检测和状态显示，帮助用户了解模型当前状态。
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
