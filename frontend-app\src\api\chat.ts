import request from './request'

// 获取API基础URL
const getApiBaseUrl = () => {
  // 优先使用环境变量
  if (import.meta.env.VITE_API_BASE_URL) {
    return import.meta.env.VITE_API_BASE_URL
  }

  // 生产环境使用相对路径
  if (import.meta.env.PROD) {
    return '/api'
  }

  // 开发环境使用localhost
  return 'http://localhost:8000/api'
}

// 解析并友好化错误信息
function parseErrorMessage(rawError: string): string {
  // 如果包含模型被禁用的错误
  if (rawError.includes('Model disabled') || rawError.includes('30003')) {
    return '当前选择的AI模型已被禁用，请在设置中选择其他可用的模型'
  }

  // 如果包含API密钥相关错误
  if (rawError.includes('401') || rawError.includes('Unauthorized') || rawError.includes('Invalid API key')) {
    return 'API密钥无效或已过期，请在设置中检查并更新API密钥'
  }

  // 如果包含配额不足错误
  if (rawError.includes('quota') || rawError.includes('insufficient') || rawError.includes('limit')) {
    return 'API配额不足，请检查您的账户余额或联系服务提供商'
  }

  // 如果包含网络连接错误
  if (rawError.includes('network') || rawError.includes('timeout') || rawError.includes('connection')) {
    return '网络连接失败，请检查网络连接后重试'
  }

  // 如果包含服务器错误
  if (rawError.includes('500') || rawError.includes('Internal Server Error')) {
    return '服务器内部错误，请稍后重试'
  }

  // 默认返回简化的错误信息
  return 'AI服务调用失败，请检查模型设置和API密钥配置'
}

// 判断错误是否可以重试
function isRetryableError(error: any): boolean {
  if (!error) return false
  const errorStr = error instanceof Error ? error.message : String(error)
  return errorStr.includes('timeout') || 
         errorStr.includes('超时') || 
         errorStr.includes('network') || 
         errorStr.includes('网络') ||
         errorStr.includes('connection') ||
         errorStr.includes('连接') ||
         errorStr.includes('500') ||
         errorStr.includes('服务器') ||
         errorStr.includes('server')
}

// 延迟函数
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// 聊天相关接口类型
export interface ChatSession {
  id: number
  user_id: number
  title: string
  created_at: string
  updated_at: string
}

export interface ChatMessage {
  id: number
  session_id: number
  role: 'user' | 'assistant' | 'system'
  content: string
  model_id_used?: number
  referenced_kbs?: number[]
  created_at: string
  sources?: DocumentSource[]
  isError?: boolean // 标记是否为错误消息
}

export interface DocumentSource {
  documentId: number
  documentName: string
  relevance: number
  excerpt: string
}

export interface CreateSessionRequest {
  title?: string
}

export interface ChatRequest {
  message: string
  model_id: number  // 修改为number类型，与后端保持一致
  knowledge_base_ids?: number[]
  history_limit?: number
  relevance_threshold?: number
  max_sources?: number  // 添加参考来源数量配置
}

export interface RegenerateMessageRequest {
  message_id: number
  model_id: number  // 修改为number类型，与后端保持一致
  knowledge_base_ids?: number[]
  history_limit?: number
  relevance_threshold?: number
  max_sources?: number  // 添加参考来源数量配置
}

export interface BatchDeleteMessagesRequest {
  message_ids: number[]
}

export interface ChatHistoryParams {
  limit?: number
  include_system?: boolean
}

export interface ChatHistoryResponse {
  messages: ChatMessage[]
  total_count: number
  session_id: number
  limited: boolean
}

export interface SessionListParams {
  offset?: number
  limit?: number
}

// 聊天API
export const chatAPI = {
  // 获取聊天会话列表
  getSessions: (params?: SessionListParams): Promise<ChatSession[]> => {
    return request.get('/chat/sessions', { params })
  },

  // 创建聊天会话
  createSession: (data?: CreateSessionRequest): Promise<ChatSession> => {
    return request.post('/chat/sessions', data || {})
  },

  // 删除聊天会话
  deleteSession: (sessionId: number): Promise<{ message: string }> => {
    return request.delete(`/chat/sessions/${sessionId}`)
  },

  // 更新聊天会话
  updateSession: (sessionId: number, data: { title?: string }): Promise<ChatSession> => {
    return request.put(`/chat/sessions/${sessionId}`, data)
  },

  // 获取聊天消息列表
  getMessages: (sessionId: number, params?: SessionListParams): Promise<ChatMessage[]> => {
    return request.get(`/chat/sessions/${sessionId}/messages`, { params })
  },

  // 获取聊天历史记录
  getChatHistory: (sessionId: number, params?: ChatHistoryParams): Promise<ChatHistoryResponse> => {
    return request.get(`/chat/sessions/${sessionId}/messages/history`, { params })
  },

  // 流式聊天 - 使用fetch实现流式响应
  streamChat: async (sessionId: number, data: ChatRequest, onMessage: (content: string) => void, onComplete: (message: ChatMessage) => void, onError: (error: string) => void, abortController?: AbortController) => {
    let retryCount = 0
    const maxRetries = 3
    const initialDelay = 1000 // 初始延迟

    while (retryCount < maxRetries) {
    try {
      const token = localStorage.getItem('token')
      // 使用动态API地址
      const apiBaseUrl = getApiBaseUrl()
      const response = await fetch(`${apiBaseUrl}/chat/sessions/${sessionId}/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(data),
        signal: abortController?.signal
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('无法获取响应流')
      }

      const decoder = new TextDecoder()
      let buffer = ''

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        buffer += decoder.decode(value, { stream: true })
        const lines = buffer.split('\n')
        buffer = lines.pop() || ''

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))
              if (data.type === 'content') {
                onMessage(data.content)
              } else if (data.type === 'done') {
                // 处理新的响应格式，包含用户消息和AI消息
                const responseData = {
                  ai_message: data.ai_message || data.message, // 兼容旧格式
                  user_message: data.user_message
                }
                onComplete(responseData)
                return
              } else if (data.type === 'error') {
                // 解析并友好化错误信息
                const errorMessage = parseErrorMessage(data.content)
                onError(errorMessage)
                return
              }
            } catch (e) {
              console.warn('解析SSE数据失败:', line)
            }
          }
        }
      }
    } catch (error) {
      // 检查是否是用户主动取消的请求
      if (error instanceof Error && error.name === 'AbortError') {
        return // 用户主动取消，不显示错误
      }

      // 检查错误消息中是否包含abort相关信息
      const errorMessage = error instanceof Error ? error.message : '发送消息失败'
      if (errorMessage.includes('aborted') || errorMessage.includes('AbortError') || errorMessage.includes('BodyStreamBuffer was aborted')) {
        return // 忽略abort错误
      }

        // 如果是可重试错误，则延迟并重试
        if (isRetryableError(error)) {
          const delayTime = initialDelay * Math.pow(2, retryCount) // 指数退避
          console.warn(`请求失败，正在重试... (第 ${retryCount + 1} 次，延迟 ${delayTime}ms)`)
          await delay(delayTime)
          retryCount++
        } else {
      onError(errorMessage)
          return // 非可重试错误，直接返回
        }
      }
    }
    onError('发送消息失败，已达到最大重试次数')
  },

  // 发送聊天消息（非流式）
  sendMessage: (sessionId: number, data: ChatRequest): Promise<ChatMessage> => {
    return request.post(`/chat/sessions/${sessionId}/stream`, data)
  },

  // 删除聊天消息
  deleteMessage: (messageId: number): Promise<{ message: string }> => {
    return request.delete(`/chat/messages/${messageId}`)
  },

  // 批量删除聊天消息
  batchDeleteMessages: (data: BatchDeleteMessagesRequest): Promise<{ message: string; deleted_count: number; failed_deletes: any[] }> => {
    return request.delete('/chat/messages/batch', { data })
  },

  // 重新生成消息
  regenerateMessage: (data: RegenerateMessageRequest): Promise<ChatMessage> => {
    return request.post('/chat/messages/regenerate', data)
  },

  // 流式重新生成消息
  streamRegenerateMessage: async (data: RegenerateMessageRequest, onMessage: (content: string) => void, onComplete: (message: ChatMessage) => void, onError: (error: string) => void, abortController?: AbortController) => {
    let retryCount = 0
    const maxRetries = 3
    const initialDelay = 1000 // 初始延迟

    while (retryCount < maxRetries) {
    try {
      const token = localStorage.getItem('token')
      // 使用动态API地址
      const apiBaseUrl = getApiBaseUrl()
      const response = await fetch(`${apiBaseUrl}/chat/messages/regenerate/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(data),
        signal: abortController?.signal
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('无法获取响应流')
      }

      const decoder = new TextDecoder()
      let buffer = ''

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        buffer += decoder.decode(value, { stream: true })
        const lines = buffer.split('\n')
        buffer = lines.pop() || ''

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))
              console.log('收到SSE数据:', data)

              if (data.type === 'content') {
                onMessage(data.content)
              } else if (data.type === 'done') {
                console.log('收到完成信号:', data.message)
                onComplete(data.message)
                return
              } else if (data.type === 'error') {
                console.error('收到错误信号:', data.error)
                onError(data.error)
                return
              }
            } catch (e) {
              console.warn('解析SSE数据失败:', line, e)
            }
          }
        }
      }
    } catch (error) {
      // 检查是否是用户主动取消的请求
      if (error instanceof Error && error.name === 'AbortError') {
        return // 用户主动取消，不显示错误
      }

      // 检查错误消息中是否包含abort相关信息
      const errorMessage = error instanceof Error ? error.message : '重新生成消息失败'
      if (errorMessage.includes('aborted') || errorMessage.includes('AbortError') || errorMessage.includes('BodyStreamBuffer was aborted')) {
        return // 忽略abort错误
      }

        // 如果是可重试错误，则延迟并重试
        if (isRetryableError(error)) {
          const delayTime = initialDelay * Math.pow(2, retryCount) // 指数退避
          console.warn(`请求失败，正在重试... (第 ${retryCount + 1} 次，延迟 ${delayTime}ms)`)
          await delay(delayTime)
          retryCount++
        } else {
      onError(errorMessage)
          return // 非可重试错误，直接返回
        }
      }
    }
    onError('重新生成消息失败，已达到最大重试次数')
  },

  // 清空聊天会话
  clearSession: (sessionId: number): Promise<{ message: string }> => {
    return request.delete(`/chat/sessions/${sessionId}/messages`)
  },

  // 检查模型状态
  checkModelStatus: (modelId: number): Promise<{
    model_id: number
    model_name: string
    status: 'available' | 'unavailable'
    message: string
  }> => {
    return request.get(`/ai-models/models/${modelId}/status`)
  }
}
