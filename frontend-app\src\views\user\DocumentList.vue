<template>
  <div class="space-y-6">
    <!-- 面包屑导航 -->
    <div class="flex items-center space-x-2 text-sm">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>
          <router-link to="/user/knowledge-base" class="text-blue-600 hover:text-blue-800">
            我的知识库
          </router-link>
        </el-breadcrumb-item>
        <el-breadcrumb-item>
          <span class="text-gray-900 dark:text-gray-100 font-medium">
            {{ currentKnowledgeBase?.name || '知识库' }}
          </span>
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 页面头部 -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100 flex items-center">
          <span>文档管理</span>
          <el-tag v-if="currentKnowledgeBase" class="ml-3" type="info" size="small">
            {{ currentKnowledgeBase.name }}
          </el-tag>
          </h1>
        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
          管理知识库中的文档
          </p>
          </div>
      <div class="mt-4 sm:mt-0 space-x-2">
        <el-button
          @click="router.back()"
          class="bg-gray-200 text-gray-800 dark:bg-zinc-700 dark:text-gray-300 dark:border-zinc-600 hover:bg-gray-300 dark:hover:bg-zinc-600"
        >
          <el-icon class="mr-1"><ArrowLeft /></el-icon>
          返回
          </el-button>
        <el-button
          type="primary"
          @click="showUploadDialog = true"
          class="bg-emerald-600 text-white border-0 hover:bg-emerald-700"
        >
          <el-icon class="mr-1"><Upload /></el-icon>
            上传文档
          </el-button>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="p-4 bg-white dark:bg-[#1e1e1e] rounded-lg border border-gray-200 dark:border-gray-800">
      <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-2 md:space-y-0">
        <!-- 搜索框 -->
        <div class="flex-1 md:max-w-md">
          <el-input
            v-model="searchQuery"
            placeholder="搜索文档..."
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <!-- 批量操作工具栏 -->
        <div v-if="selectedDocuments.length > 0" class="flex items-center space-x-3">
          <span class="text-sm text-gray-600 dark:text-gray-400">
            已选择 {{ selectedDocuments.length }} 个文档
          </span>
          <el-button size="small" @click="downloadSelected">
            <el-icon class="mr-1"><Download /></el-icon>
            打包下载
          </el-button>
          <el-button size="small" type="danger" @click="deleteSelected">
            <el-icon class="mr-1"><Delete /></el-icon>
            批量删除
          </el-button>
          <el-button size="small" @click="clearSelection">
            取消选择
          </el-button>
        </div>
      </div>
    </div>

    <!-- 文档列表 -->
    <div class="bg-white dark:bg-[#1e1e1e] rounded-lg border border-gray-200/80 dark:border-zinc-800/80 overflow-hidden" v-loading="loading">
      <!-- 空状态 -->
      <div v-if="filteredDocuments.length === 0 && !loading" class="text-center py-12">
        <el-icon :size="64" class="text-gray-400 mb-4">
          <Box />
        </el-icon>
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
          {{ searchQuery ? '未找到匹配的文档' : '还没有文档' }}
        </h3>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          {{ searchQuery ? '尝试调整搜索条件' : '上传您的第一个文档来开始构建知识库' }}
        </p>
        <el-button v-if="!searchQuery" type="primary" @click="showUploadDialog = true" class="bg-emerald-600 text-white border-0 hover:bg-emerald-700 shadow-lg">
          <el-icon class="mr-2"><Upload /></el-icon>
          上传文档
        </el-button>
      </div>

      <!-- 文档表格 -->
      <el-table
        v-else
        :data="paginatedDocuments"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column prop="filename" label="文件名" min-width="250">
          <template #default="{ row }">
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 rounded-lg flex items-center justify-center"
                   :class="getFileIconClass(row.filename)">
                <el-icon :size="16">
                  <component :is="getFileIcon(row.filename)" />
                </el-icon>
              </div>
              <div>
                <div class="font-medium text-gray-900 dark:text-gray-100">
                  {{ row.originalName || row.filename }}
                </div>
                <div class="text-xs text-gray-500">
                  {{ row.filename }}
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="160" align="center">
          <template #default="{ row }">
            <div class="flex flex-col items-center space-y-1">
              <el-tag
                :type="getStatusType(row.status)"
                size="small"
              >
                {{ getStatusText(row.status) }}
              </el-tag>
              <!-- 失败状态显示错误信息和重试按钮 -->
              <div v-if="row.status === 'failed'" class="flex flex-col items-center space-y-1">
                <el-tooltip
                  v-if="row.error_message"
                  :content="row.error_message"
                  placement="top"
                  :show-after="500"
                >
                  <el-icon class="text-red-500 cursor-help" :size="14">
                    <Warning />
                  </el-icon>
                </el-tooltip>
                <el-button
                  size="small"
                  type="primary"
                  plain
                  @click="retryDocument(row)"
                  :loading="retryingDocuments.has(row.id)"
                  class="text-xs px-2 py-1"
                >
                  重试
                </el-button>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="file_size" label="大小" width="100" align="right">
          <template #default="{ row }">
            <span class="text-gray-600 dark:text-gray-400 text-sm">
              {{ formatFileSize(row.file_size) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="created_at" label="上传日期" width="150">
          <template #default="{ row }">
            <span class="text-gray-500 dark:text-gray-400 text-sm">
              {{ formatDate(row.created_at) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" align="center">
          <template #default="{ row }">
            <div class="flex items-center justify-center space-x-2">
              <el-button size="small" @click="previewDocument(row)">
                <el-icon><View /></el-icon>
              </el-button>
              <el-button size="small" @click="downloadDocument(row)">
                <el-icon><Download /></el-icon>
              </el-button>
              <el-button size="small" @click="renameDocument(row)">
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button size="small" type="danger" @click="deleteDocument(row)">
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div v-if="filteredDocuments.length > pageSize" class="flex justify-center">
      <el-pagination
        v-model:current-page="currentPage"
        :page-size="pageSize"
        :total="filteredDocuments.length"
        layout="prev, pager, next, jumper, total"
        @current-change="handlePageChange"
      />
    </div>

    <!-- 上传文档对话框 -->
    <el-dialog
      v-model="showUploadDialog"
      title="上传文档"
      width="600px"
      @close="resetUpload"
      class="custom-dialog"
    >
      <div class="space-y-4">
        <!-- 拖拽上传区域 -->
        <el-upload
          ref="uploadRef"
          class="upload-demo"
          drag
          multiple
          :auto-upload="false"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          :file-list="uploadFileList"
          accept=".pdf,.doc,.docx,.txt,.md,.ppt,.pptx,.xls,.xlsx"
        >
          <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持 PDF、Word、PowerPoint、Excel、Markdown、文本文件<br>
              批量上传：最多5个文件，总大小不超过300MB，单个文件不超过300MB
            </div>
          </template>
        </el-upload>

        <!-- 文件选择状态提示 -->
        <div v-if="uploadFileList.length > 0 && uploadProgress.length === 0" class="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div class="flex items-center justify-between text-sm">
            <span class="text-blue-700 dark:text-blue-300">
              已选择 {{ uploadFileList.length }}/5 个文件
            </span>
            <span class="text-blue-600 dark:text-blue-400">
              总大小: {{ formatTotalSize() }}
            </span>
          </div>
        </div>

        <!-- 上传进度 -->
        <div v-if="uploadProgress.length > 0" class="space-y-2">
          <h4 class="font-medium text-gray-900 dark:text-gray-100">上传进度</h4>
          <div
            v-for="progress in uploadProgress"
            :key="progress.id"
            class="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-dark-700 rounded-lg"
          >
            <div class="flex-1">
              <div class="flex items-center justify-between mb-1">
                <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {{ progress.name }}
                </span>
                <span class="text-sm" :class="{
                  'text-gray-500': progress.status === 'uploading',
                  'text-blue-500': progress.status === 'processing',
                  'text-green-500': progress.status === 'success',
                  'text-red-500': progress.status === 'error'
                }">
                  <template v-if="progress.status === 'uploading'">{{ progress.progress }}%</template>
                  <template v-else-if="progress.status === 'processing'">处理中...</template>
                  <template v-else-if="progress.status === 'success'">✓ 完成</template>
                  <template v-else-if="progress.status === 'error'">✗ 失败</template>
                </span>
              </div>
              <el-progress
                :percentage="progress.progress"
                :status="progress.status === 'error' ? 'exception' : progress.status === 'success' ? 'success' : undefined"
                :show-text="false"
                :stroke-width="6"
              />
              <div v-if="progress.status === 'error' && progress.error" class="text-red-500 text-xs mt-1">
                {{ progress.error }}
              </div>
            </div>
            <el-button
              v-if="progress.status === 'error'"
              size="small"
              type="danger"
              @click="removeUploadProgress(progress.id)"
            >
              移除
            </el-button>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showUploadDialog = false">取消</el-button>
          <el-button
            type="primary"
            @click="startUpload"
            :loading="uploading"
            :disabled="uploadFileList.length === 0"
          >
            开始上传
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="showPreviewDialog"
      :title="previewingDocument?.filename || '文档预览'"
      width="80%"
      top="5vh"
      class="custom-dialog"
    >
      <div class="h-96 bg-gray-50 dark:bg-dark-700 rounded-lg flex items-center justify-center">
        <div class="text-center">
          <el-icon :size="48" class="text-gray-400 mb-4">
            <Document />
          </el-icon>
          <p class="text-gray-600 dark:text-gray-400">
            文档预览功能开发中...
          </p>
        </div>
      </div>
    </el-dialog>

    <!-- 重命名对话框 -->
    <el-dialog
      v-model="showRenameDialog"
      title="重命名文档"
      width="400px"
      class="custom-dialog"
    >
      <el-form :model="renameForm" :rules="renameRules" ref="renameFormRef">
        <el-form-item label="新名称" prop="name">
          <el-input
            v-model="renameForm.name"
            placeholder="请输入新的文档名称"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showRenameDialog = false">取消</el-button>
          <el-button type="primary" @click="confirmRename" :loading="renaming">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useDocumentStore } from '@/stores/document'
import { useKnowledgeBaseStore } from '@/stores/knowledgeBase'
import { documentAPI } from '@/api/document'
import type { Document, KnowledgeBase } from '@/types'
import {
  Document as DocumentIcon,
  Clock,
  ChatDotRound,
  Upload,
  Search,
  Download,
  Delete,
  Box,
  View,
  Edit,
  UploadFilled,
  Files,
  Picture,
  VideoPlay,
  Headset,
  ArrowLeft,
  Warning
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const documentStore = useDocumentStore()
const knowledgeBaseStore = useKnowledgeBaseStore()

// 响应式数据
const loading = ref(false)
const uploading = ref(false)
const renaming = ref(false)
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const selectedDocuments = ref<Document[]>([])
const retryingDocuments = ref(new Set<number>())

// 对话框状态
const showUploadDialog = ref(false)
const showPreviewDialog = ref(false)
const showRenameDialog = ref(false)

// 表单和数据
const uploadRef = ref()
const uploadFileList = ref<any[]>([])
const uploadProgress = ref<any[]>([])
const renameFormRef = ref()
const renamingDocument = ref<Document | null>(null)
const previewingDocument = ref<Document | null>(null)

const renameForm = ref({
  name: ''
})

const renameRules = {
  name: [
    { required: true, message: '请输入文档名称', trigger: 'blur' },
    { min: 1, max: 100, message: '名称长度在 1 到 100 个字符', trigger: 'blur' }
  ]
}

// 当前知识库信息
const currentKnowledgeBase = ref<KnowledgeBase | null>(null)

// 使用store中的文档数据
const documents = computed(() => documentStore.documents)

// 计算属性
const filteredDocuments = computed(() => {
  if (!searchQuery.value) {
    return documents.value
  }

  const query = searchQuery.value.toLowerCase()
  return documents.value.filter(doc =>
    doc.filename.toLowerCase().includes(query)
  )
})

const paginatedDocuments = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredDocuments.value.slice(start, end)
})

// 工具方法
const formatRelativeTime = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 60) {
    return `${minutes} 分钟前`
  } else if (hours < 24) {
    return `${hours} 小时前`
  } else {
    return `${days} 天前`
  }
}

const formatDate = (dateString: string) => {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  if (isNaN(date.getTime())) return '无效日期'
  return date.toLocaleDateString('zh-CN')
}

const formatFileSize = (bytes: number) => {
  if (!bytes || isNaN(bytes)) return '未知'
  const sizes = ['B', 'KB', 'MB', 'GB']
  if (bytes === 0) return '0 B'
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

const formatTotalSize = () => {
  const totalSize = uploadFileList.value.reduce((sum, file) => {
    return sum + (file.raw ? file.raw.size : 0)
  }, 0)
  return formatFileSize(totalSize)
}

const getFileIcon = (filename: string) => {
  if (!filename) return Files
  const ext = filename.split('.').pop()?.toLowerCase()
  switch (ext) {
    case 'pdf':
      return DocumentIcon
    case 'doc':
    case 'docx':
      return Files
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
      return Picture
    case 'mp4':
    case 'avi':
    case 'mov':
      return VideoPlay
    case 'mp3':
    case 'wav':
      return Headset
    default:
      return DocumentIcon
  }
}

const getFileIconClass = (filename: string) => {
  if (!filename) return 'bg-gray-100 dark:bg-zinc-700 text-gray-600 dark:text-zinc-400'
  const ext = filename.split('.').pop()?.toLowerCase()
  switch (ext) {
    case 'pdf':
      return 'bg-red-500/10 text-red-500'
    case 'doc':
    case 'docx':
      return 'bg-blue-500/10 text-blue-500'
    case 'xls':
    case 'xlsx':
      return 'bg-emerald-500/10 text-emerald-500'
    case 'ppt':
    case 'pptx':
      return 'bg-orange-500/10 text-orange-500'
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
      return 'bg-purple-500/10 text-purple-500'
    default:
      return 'bg-zinc-500/10 text-zinc-500'
  }
}

const getStatusType = (status: string) => {
  if (!status) return 'info'
  switch (status) {
    case 'completed':
      return 'success'
    case 'processing':
      return 'warning'
    case 'failed':
      return 'danger'
    case 'pending':
      return 'info'
    default:
      return 'info'
  }
}

const getStatusText = (status: string) => {
  if (!status) return '未知'
  switch (status) {
    case 'completed':
      return '已完成'
    case 'processing':
      return '处理中'
    case 'failed':
      return '失败'
    case 'pending':
      return '等待处理'
    default:
      return '未知'
  }
}

// 事件处理方法
const handleSearch = () => {
  currentPage.value = 1
}

const handlePageChange = (page: number) => {
  currentPage.value = page
}

const handleSelectionChange = (selection: Document[]) => {
  selectedDocuments.value = selection
}

const clearSelection = () => {
  selectedDocuments.value = []
}

const startChat = () => {
  router.push(`/user/chat?kb=${route.params.id}`)
}

// 文档操作方法
const previewDocument = (doc: Document) => {
  previewingDocument.value = doc
  showPreviewDialog.value = true
}

const downloadDocument = async (doc: Document) => {
  // 显示功能开发中提示，不调用API
  ElMessage({
    message: '文档下载功能开发中...',
    type: 'info'
  })
}

const retryDocument = async (doc: Document) => {
  try {
    retryingDocuments.value.add(doc.id)

    const result = await documentAPI.retry(doc.id)

    ElMessage.success(result.message || '文档重新处理成功')

    // 刷新文档列表
    const kbId = Number(route.params.id)
    if (kbId) {
      await loadDocuments(kbId, true)
    }
  } catch (error: any) {
    console.error('重试文档失败:', error)

    let errorMessage = '重试失败'

    if (error.response) {
      // 服务器返回了错误响应
      if (error.response.status === 500) {
        errorMessage = '服务器内部错误，请稍后重试或联系管理员'
      } else if (error.response.status === 404) {
        errorMessage = '文档不存在或已被删除'
      } else if (error.response.status === 403) {
        errorMessage = '没有权限操作此文档'
      } else if (error.response.data?.detail) {
        errorMessage = error.response.data.detail
      }
    } else if (error.request) {
      // 网络错误
      errorMessage = '网络连接失败，请检查网络连接'
    } else {
      // 其他错误
      errorMessage = error.message || '未知错误'
    }

    ElMessage.error(errorMessage)
  } finally {
    retryingDocuments.value.delete(doc.id)
  }
}

const renameDocument = (doc: Document) => {
  renamingDocument.value = doc
  renameForm.value.name = doc.filename
  showRenameDialog.value = true
}

const deleteDocument = async (doc: Document) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除文档"${doc.filename}"吗？`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    const result = await documentStore.deleteDocument(doc.id)
    if (result.success) {
      ElMessage.success('文档删除成功')
      
      // 删除成功后，强制刷新文档列表以确保数据同步
      const knowledgeBaseId = Number(route.params.id)
      if (knowledgeBaseId) {
        await loadDocuments(knowledgeBaseId, true)
      }
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    // 用户取消删除
  }
}

const downloadSelected = async () => {
  if (selectedDocuments.value.length === 0) return

  try {
    ElMessage.success(`开始打包下载 ${selectedDocuments.value.length} 个文档`)
    // 这里应该调用批量下载API
  } catch (error) {
    ElMessage.error('打包下载失败')
  }
}

const deleteSelected = async () => {
  if (selectedDocuments.value.length === 0) return

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedDocuments.value.length} 个文档吗？`,
      '确认批量删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    const selectedIds = selectedDocuments.value.map(doc => doc.id)
    const result = await documentStore.batchDeleteDocuments(selectedIds)

    if (result.success) {
      selectedDocuments.value = []
      ElMessage.success('批量删除成功')
      
      // 批量删除成功后，重新获取文档列表以确保数据同步
      const knowledgeBaseId = Number(route.params.id)
      if (knowledgeBaseId) {
        await loadDocuments(knowledgeBaseId)
      }
    } else {
      ElMessage.error(result.message || '批量删除失败')
    }
  } catch (error) {
    // 用户取消删除
  }
}

const confirmRename = async () => {
  if (!renameFormRef.value || !renamingDocument.value) return

  try {
    await renameFormRef.value.validate()
    renaming.value = true

    // 注意：当前API可能不支持重命名，这里只是模拟
    // 实际应该调用 documentStore.updateDocument 或类似的API
    ElMessage.success('重命名成功')
    showRenameDialog.value = false
  } catch (error) {
    ElMessage.error('重命名失败')
  } finally {
    renaming.value = false
  }
}

// 上传相关方法
const handleFileChange = (_file: any, fileList: any[]) => {
  // 1. 检查文件数量限制（最多5个文件）
  if (fileList.length > 5) {
    ElMessage.error('批量上传最多支持5个文件，请重新选择')

    // 只保留前5个文件
    const limitedFiles = fileList.slice(0, 5)
    uploadFileList.value = limitedFiles

    // 更新上传组件的文件列表
    if (uploadRef.value) {
      uploadRef.value.clearFiles()
      limitedFiles.forEach(file => {
        if (file.raw) {
          uploadRef.value.handleStart(file.raw)
        }
      })
    }
    return
  }

  // 2. 检查单个文件大小（300MB = 300 * 1024 * 1024 bytes）
  const maxFileSize = 300 * 1024 * 1024
  const oversizedFiles = fileList.filter(file =>
    file.raw && file.raw.size > maxFileSize
  )

  if (oversizedFiles.length > 0) {
    const fileNames = oversizedFiles.map(f => f.name).join(', ')
    ElMessage.error(`文件太大，请选择较小的文件: ${fileNames}。单个文件不超过 300MB`)

    // 移除过大的文件
    const validFiles = fileList.filter(file =>
      !file.raw || file.raw.size <= maxFileSize
    )
    uploadFileList.value = validFiles

    // 更新上传组件的文件列表
    if (uploadRef.value) {
      uploadRef.value.clearFiles()
      validFiles.forEach(file => {
        if (file.raw) {
          uploadRef.value.handleStart(file.raw)
        }
      })
    }
    return
  }

  // 3. 检查总文件大小（不超过300MB）
  const totalSize = fileList.reduce((sum, file) => {
    return sum + (file.raw ? file.raw.size : 0)
  }, 0)

  if (totalSize > maxFileSize) {
    const totalSizeMB = Math.round(totalSize / (1024 * 1024))
    ElMessage.error(`所选文件总大小为 ${totalSizeMB}MB，超过300MB限制，请重新选择`)

    // 清空文件列表
    uploadFileList.value = []
    if (uploadRef.value) {
      uploadRef.value.clearFiles()
    }
    return
  }

  // 检查同名文件
  const existingFiles = documents.value.map(doc => doc.filename)
  const duplicateFiles = fileList.filter(file =>
    existingFiles.includes(file.name)
  )

  if (duplicateFiles.length > 0) {
    const fileNames = duplicateFiles.map(f => f.name).join(', ')
    ElMessageBox.confirm(
      `检测到同名文件: ${fileNames}。是否继续上传？上传后将覆盖原文件。`,
      '同名文件警告',
      {
        confirmButtonText: '继续上传',
        cancelButtonText: '取消',
        type: 'warning',
      }
    ).then(() => {
      uploadFileList.value = fileList
    }).catch(() => {
      // 移除同名文件
      const filteredList = fileList.filter(file =>
        !existingFiles.includes(file.name)
      )
      uploadFileList.value = filteredList
      if (uploadRef.value) {
        uploadRef.value.clearFiles()
        filteredList.forEach(file => {
          uploadRef.value.handleStart(file.raw)
        })
      }
    })
  } else {
    uploadFileList.value = fileList
  }
}

const handleFileRemove = (_file: any, fileList: any[]) => {
  uploadFileList.value = fileList
}

const startUpload = async () => {
  if (uploadFileList.value.length === 0) return

  const kbId = parseInt(route.params.id as string)
  if (!kbId) {
    ElMessage.error('知识库ID无效')
    return
  }

  uploading.value = true
  uploadProgress.value = []

  try {
    // 准备文件列表
    const files = uploadFileList.value.map(item => item.raw).filter(Boolean)

    if (files.length === 0) {
      ElMessage.error('没有有效的文件')
      return
    }

    // 初始化进度项
    files.forEach(file => {
      const progressItem = {
        id: Date.now() + Math.random(),
        name: file.name,
        progress: 0,
        status: 'uploading'
      }
      uploadProgress.value.push(progressItem)
    })

    // 创建进度回调函数
    const onProgress = (progress: number) => {
      // 更新所有文件的进度（批量上传时共享进度）
      uploadProgress.value.forEach(item => {
        if (item.status === 'uploading') {
          // 网络上传完成就显示100%，不等待后端处理
          item.progress = progress
        }
      })
    }

    // 调用批量上传API
    const result = await documentStore.uploadDocuments(kbId, files, onProgress)

    if (result.success && result.data) {
      // 上传完成，立即更新进度为100%
      uploadProgress.value.forEach(item => {
        if (item.status === 'uploading') {
          item.progress = 100
          item.status = 'success'
        }
      })

      const uploadedCount = result.data.uploaded_documents?.length || 0
      const failedUploads = result.data.failed_uploads || []

      // 处理失败的文件
      if (failedUploads.length > 0) {
        failedUploads.forEach(failed => {
          const progressItem = uploadProgress.value.find(item => item.name === failed.filename)
          if (progressItem) {
            progressItem.status = 'error'
            progressItem.error = failed.error
            progressItem.progress = 0
          }
        })
        ElMessage.warning(`${failedUploads.length} 个文件上传失败`)
      }

      if (uploadedCount > 0) {
        ElMessage.success(`成功上传 ${uploadedCount} 个文档，正在后台处理中...`)
      }

      // 立即关闭对话框，不等待后端处理
      setTimeout(() => {
        showUploadDialog.value = false
        resetUpload()
        // 异步刷新文档列表，不阻塞关闭
        loadDocuments(kbId, true)
      }, 1000) // 缩短延迟时间
    } else {
      // 上传失败，更新所有进度为失败
      uploadProgress.value.forEach(item => {
        item.status = 'error'
        item.error = result.message
      })
      ElMessage.error(result.message || '上传失败')
    }
  } catch (error: any) {
    // 处理异常，更新所有进度为失败
    uploadProgress.value.forEach(item => {
      item.status = 'error'
      item.error = '上传异常'
    })
    ElMessage.error('上传失败: ' + (error.message || '未知错误'))
  } finally {
    uploading.value = false
  }
}

const removeUploadProgress = (id: string) => {
  const index = uploadProgress.value.findIndex(item => item.id === id)
  if (index !== -1) {
    uploadProgress.value.splice(index, 1)
  }
}

const resetUpload = () => {
  uploadFileList.value = []
  uploadProgress.value = []
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

// 加载知识库信息
const loadKnowledgeBase = async (id: number) => {
  try {
    const result = await knowledgeBaseStore.fetchKnowledgeBase(id)
    if (result.success && result.data) {
      currentKnowledgeBase.value = result.data
    } else {
      ElMessage.error(result.message || '获取知识库信息失败')
    }
  } catch (error) {
    ElMessage.error('获取知识库信息失败')
  }
}

// 加载文档列表
const loadDocuments = async (kbId: number, forceRefresh = false) => {
  try {
    // 如果是强制刷新，先清空当前列表
    if (forceRefresh) {
      documentStore.documents = []
    }

    const result = await documentStore.fetchDocuments(kbId)
    if (!result.success) {
      ElMessage.error(result.message || '获取文档列表失败')
    }
  } catch (error) {
    ElMessage.error('获取文档列表失败')
  }
}

onMounted(() => {
  // 根据路由参数加载知识库信息
  const knowledgeBaseId = Number(route.params.id)
  if (knowledgeBaseId) {
    loadKnowledgeBase(knowledgeBaseId)
    loadDocuments(knowledgeBaseId)
  }
})
</script>

<style scoped>
/* Dialog Styles */
:deep(.custom-dialog .el-dialog) {
  border-radius: 16px;
  background: #2a2a2a;
  border: 1px solid #3f3f46;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
}

:deep(.custom-dialog .el-dialog__header) {
  background: #1e1e1e;
  border-radius: 16px 16px 0 0;
  padding: 20px 24px;
  border-bottom: 1px solid #3f3f46;
}

:deep(.custom-dialog .el-dialog__title) {
  font-weight: 600;
  font-size: 18px;
  color: #e5e7eb;
}

:deep(.custom-dialog .el-dialog__body) {
  padding: 24px;
  color: #d1d5db;
}

:deep(.custom-dialog .el-form-item__label) {
  font-weight: 500;
  color: #a1a1aa;
}

:deep(.custom-dialog .el-input__wrapper),
:deep(.custom-dialog .el-textarea__inner) {
  border-radius: 8px;
  border: 1px solid #3f3f46;
  background: #1e1e1e;
  box-shadow: none;
  color: #e5e7eb;
  transition: all 0.2s ease;
}

:deep(.custom-dialog .el-input__wrapper:hover),
:deep(.custom-dialog .el-textarea__inner:hover) {
  border-color: #52525b;
}

:deep(.custom-dialog .el-input__wrapper.is-focus),
:deep(.custom-dialog .el-textarea__inner:focus) {
  border-color: #10b981;
  box-shadow: 0 0 0 2px #18181b, 0 0 0 4px #10b981;
}

:deep(.custom-dialog .el-button--primary) {
  background-color: #059669;
  border-color: #059669;
}
:deep(.custom-dialog .el-button--primary:hover) {
  background-color: #047857;
  border-color: #047857;
}

/* Table Dark Theme */
:deep(.el-table) {
  background-color: transparent;
}
:deep(.el-table th),
:deep(.el-table tr),
:deep(.el-table td) {
  background-color: transparent;
  border-color: #3f3f46;
  color: #a1a1aa;
}
:deep(.el-table thead) {
  color: #e5e7eb;
}
:deep(.el-table--enable-row-hover .el-table__body tr:hover > td) {
  background-color: #f9fafb !important;
}

/* Upload Component Dark Theme -> Converted to Light Theme */
.dark :deep(.el-upload-dragger) {
  background-color: #f9fafb;
  border: 2px dashed #d1d5db;
  transition: all 0.2s ease;
}
.dark :deep(.el-upload-dragger:hover) {
  border-color: #10b981;
  box-shadow: none;
  background-color: #f0fdf4;
}
.dark :deep(.el-upload-dragger .el-icon--upload) {
  color: #10b981;
  text-shadow: none;
}
.dark :deep(.el-upload__text) {
  color: #6b7280;
}
.dark :deep(.el-upload__text em) {
  color: #059669;
}
.dark :deep(.el-upload-list__item) {
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  color: #374151;
}
.dark :deep(.el-upload-list__item-name) {
  color: #374151;
}
.dark :deep(.el-upload-list__item:hover) {
  background-color: #f9fafb;
}

/* Custom Dialog Light Theme */
:deep(.custom-dialog .el-dialog) {
  border-radius: 12px;
  background: #ffffff;
}
:deep(.custom-dialog .el-dialog__header) {
  background-color: #ffffff !important;
  border-bottom: 1px solid #e5e7eb;
  padding: 16px 24px;
}
:deep(.custom-dialog .el-dialog__title) {
  color: #111827 !important;
  font-weight: 600;
}
:deep(.custom-dialog .el-dialog__headerbtn .el-icon) {
  color: #6b7280 !important;
}
:deep(.custom-dialog .el-dialog__headerbtn:hover .el-icon) {
  color: #111827 !important;
}
:deep(.custom-dialog .el-dialog__body) {
  padding: 24px;
  color: #374151;
}
:deep(.custom-dialog .el-dialog__footer) {
  padding: 16px 24px;
  border-top: 1px solid #e5e7eb;
}

/* 确保对话框中的输入框为白色背景 */
:deep(.custom-dialog .el-input__wrapper) {
  background-color: #ffffff !important;
  box-shadow: 0 0 0 1px #e5e7eb inset !important;
  border: none;
}

:deep(.custom-dialog .el-input__inner) {
  color: #111827 !important;
  background-color: #ffffff !important;
}

:deep(.custom-dialog .el-form-item__label) {
  color: #374151 !important;
}

:deep(.custom-dialog .el-button--primary) {
  background-color: #059669;
  border-color: #059669;
}
:deep(.custom-dialog .el-button--primary:hover) {
  background-color: #047857;
  border-color: #047857;
}


.tech-button {
  background-color: transparent;
  color: #a1a1aa; /* zinc-400 */
  border: 1px solid #3f3f46; /* zinc-700 */
  border-radius: 8px;
  transition: all 0.2s ease;
}
.tech-button:hover {
  background-color: #27272a; /* zinc-800 */
  color: #e4e7e7; /* zinc-200 */
  border-color: #52525b; /* zinc-600 */
  transform: translateY(-1px);
}
</style>
