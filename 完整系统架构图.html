<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>双端系统架构图</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Microsoft YaHei', Arial, sans-serif;
            background: #ffffff;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 720px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 28px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
            text-align: center;
             margin-left: 30px;
        }
        
        .header p {
            font-size: 16px;
            color: #6b7280;
             margin-left: 300px;
        }
        
        .architecture-layers {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .layer-row {
            display: grid;
            grid-template-columns: 100px 1fr 1fr;
            gap: 8px;
            align-items: stretch;
        }

        .layer-label {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 700;
            color: #374151;
            text-align: center;
            writing-mode: vertical-rl;
            text-orientation: mixed;
            padding: 8px;
        }

        .single-layer-row {
            display: grid;
            grid-template-columns: 100px 1fr;
            gap: 8px;
            align-items: stretch;
        }

        .single-layer-content {
            background: #666666;
            border-radius: 8px;
            padding: 12px;
            color: white;
            text-align: center;
            font-size: 12px;
            font-weight: 500;
            line-height: 1.4;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .single-layer-content:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .user-column, .admin-column {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .column-header {
            border-radius: 12px;
            padding: 15px 10px;
            text-align: center;
            color: white;
            font-size: 16px;
            font-weight: 700;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 6px;
        }

        .user-column .column-header {
            background: linear-gradient(135deg, #E0CB15 0%, #DE8431 100%);
        }

        .admin-column .column-header {
            background: linear-gradient(135deg, #DE8431 0%, #E0CB15 100%);
        }

        .header-icon {
            font-size: 24px;
            color: white;
        }

        .function-card {
            background: #666666;
            border-radius: 6px;
            padding: 8px;
            color: white;
            text-align: center;
            font-size: 11px;
            font-weight: 500;
            line-height: 1.3;
            transition: all 0.3s ease;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }

        .function-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
        }

        .service-row {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
        }

        .service-card {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            transition: all 0.3s ease;
        }

        .service-card:hover {
            border-color: #10b981;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .ai-row {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
        }

        .ai-card {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            transition: all 0.3s ease;
        }

        .ai-card:hover {
            border-color: #8b5cf6;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .storage-row {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
        }

        .storage-card {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            transition: all 0.3s ease;
        }

        .storage-card:hover {
            border-color: #f59e0b;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
        }

        .card-desc {
            font-size: 12px;
            color: #6b7280;
            line-height: 1.4;
        }
        
        .flow-section {
            background: #feffff;
            border-radius: 12px;
            padding: 20px;
            margin-top: 15px;
        }

        .flow-title {
            font-size: 18px;
            font-weight: 700;
            color: #374151;
            text-align: center;
            margin-bottom: 15px;
        }

        .flow-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
            gap: 10px;
        }

        .flow-step {
            background: white;
            border-radius: 6px;
            padding: 12px;
            text-align: center;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }

        .step-number {
            background: #3b82f6;
            color: white;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            font-weight: 600;
            margin: 0 auto 8px;
        }

        .step-text {
            font-size: 11px;
            color: #374151;
            line-height: 1.3;
        }
        
        @media (max-width: 768px) {
            .container {
                max-width: 95%;
            }

            .layer-row, .single-layer-row {
                grid-template-columns: 1fr;
                gap: 8px;
            }

            .layer-label {
                writing-mode: horizontal-tb;
                text-orientation: mixed;
                padding: 10px;
                border-radius: 6px;
                background: #f1f5f9;
                font-size: 12px;
            }

            .flow-steps {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>双端系统架构图</h1>
        </div>
        
        <div class="architecture-layers">
            <!-- 用户界面层 -->
            <div class="layer-row">
                <div class="layer-label">
                     用户界面层
                </div>
                <div class="user-column">
                    <div class="column-header">
                        <div class="header-icon">
                            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 12C14.7614 12 17 9.76142 17 7C17 4.23858 14.7614 2 12 2C9.23858 2 7 4.23858 7 7C7 9.76142 9.23858 12 12 12Z" fill="white"/>
                                <path d="M12 14C7.58172 14 4 17.5817 4 22H20C20 17.5817 16.4183 14 12 14Z" fill="white"/>
                            </svg>
                        </div>
                        <div>用户</div>
                    </div>
                    <div class="function-card">
                        基于RAG的，多轮，<br>实时响应
                    </div>
                    <div class="function-card">
                        Markdown，图表，<br>代码高亮
                    </div>
                    <div class="function-card">
                        可自定义的上下文记<br>忆
                    </div>
                    <div class="function-card">
                        AI仅从知识库回答
                    </div>
                    <div class="function-card">
                        支持多种模型，智能<br>推荐
                    </div>
                </div>
                <div class="admin-column">
                    <div class="column-header">
                        <div class="header-icon">
                            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" fill="white"/>
                                <path d="M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.258 9.77251 19.9887C9.5799 19.7194 9.31074 19.5143 9 19.4C8.69838 19.2669 8.36381 19.2272 8.03941 19.286C7.71502 19.3448 7.41568 19.4995 7.18 19.73L7.12 19.79C6.93425 19.976 6.71368 20.1235 6.47088 20.2241C6.22808 20.3248 5.96783 20.3766 5.705 20.3766C5.44217 20.3766 5.18192 20.3248 4.93912 20.2241C4.69632 20.1235 4.47575 19.976 4.29 19.79C4.10405 19.6043 3.95653 19.3837 3.85588 19.1409C3.75523 18.8981 3.70343 18.6378 3.70343 18.375C3.70343 18.1122 3.75523 17.8519 3.85588 17.6091C3.95653 17.3663 4.10405 17.1457 4.29 16.96L4.35 16.9C4.58054 16.6643 4.73519 16.365 4.794 16.0406C4.85282 15.7162 4.81312 15.3816 4.68 15.08C4.55324 14.7842 4.34276 14.532 4.07447 14.3543C3.80618 14.1766 3.49179 14.0813 3.17 14.08H3C2.46957 14.08 1.96086 13.8693 1.58579 13.4942C1.21071 13.1191 1 12.6104 1 12.08C1 11.5496 1.21071 11.0409 1.58579 10.6658C1.96086 10.2907 2.46957 10.08 3 10.08H3.09C3.42099 10.0723 3.742 9.96512 4.0113 9.77251C4.28059 9.5799 4.48572 9.31074 4.6 9C4.73312 8.69838 4.77282 8.36381 4.714 8.03941C4.65519 7.71502 4.50054 7.41568 4.27 7.18L4.21 7.12C4.02405 6.93425 3.87653 6.71368 3.77588 6.47088C3.67523 6.22808 3.62343 5.96783 3.62343 5.705C3.62343 5.44217 3.67523 5.18192 3.77588 4.93912C3.87653 4.69632 4.02405 4.47575 4.21 4.29C4.39575 4.10405 4.61632 3.95653 4.85912 3.85588C5.10192 3.75523 5.36217 3.70343 5.625 3.70343C5.88783 3.70343 6.14808 3.75523 6.39088 3.85588C6.63368 3.95653 6.85425 4.10405 7.04 4.29L7.1 4.35C7.33568 4.58054 7.63502 4.73519 7.95941 4.794C8.28381 4.85282 8.61838 4.81312 8.92 4.68H9C9.29577 4.55324 9.54802 4.34276 9.72569 4.07447C9.90337 3.80618 9.99872 3.49179 10 3.17V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z" fill="white"/>
                            </svg>
                        </div>
                        <div>管理员</div>
                    </div>
                    <div class="function-card">
                        知识库配额管理<br>化，版本控制
                    </div>
                    <div class="function-card">
                        多级权限管理<br>(RBAC)
                    </div>
                    <div class="function-card">
                        模型参数调优，检索<br>阈值
                    </div>
                    <div class="function-card">
                        用户行为，知识库分<br>析
                    </div>
                    <div class="function-card">
                        API密钥安全管理
                    </div>
                </div>
            </div>

            <!-- 应用服务层 -->
            <div class="single-layer-row">
                <div class="layer-label">
                  
                    应用服务层
                </div>
                <div class="single-layer-content">
                    <strong>用户认证服务</strong>：FastAPI + SQLModel + JWT身份认证 + 权限控制管理<br><br>
                    <strong>知识库管理</strong>：文档上传解析 + 批量处理引擎 + 版本控制系统<br><br>
                    <strong>对话管理</strong>：多轮会话维护 + 上下文管理 + 流式响应处理<br><br>
                    <strong>AI模型管理</strong>：多模型统一接入 + 参数配置优化 + 智能路由调度
                </div>
            </div>

            <!-- AI算法层 -->
            <div class="single-layer-row">
                <div class="layer-label">
                    AI算法层
                </div>
                <div class="single-layer-content">
                    <strong>RAG检索引擎</strong>：FAISS向量检索 + SentenceBERT编码 + 余弦相似度计算<br><br>
                    <strong>多模型融合</strong>：GPT/gemini + 通义千问/DeepSeek + 统一API接入层<br><br>
                    <strong>文档处理引擎</strong>：多格式解析支持 + 智能语义分块 + 向量化处理
                </div>
            </div>

            <!-- 数据存储层 -->
            <div class="single-layer-row">
                <div class="layer-label">
                    数据存储层
                </div>
                <div class="single-layer-content">
                    <strong>PostgreSQL</strong>：关系数据存储 + 用户信息管理 + 系统配置数据<br><br>
                    <strong>FAISS向量库</strong>：1024维语义向量 + 高速相似度搜索 + 分布式存储<br><br>
                    <strong>Redis缓存</strong>：会话状态缓存 + 检索结果缓存 + 用户权限缓存<br><br>
                    <strong>MinIO对象存储</strong>：文档文件存储 + 媒体资源管理 + 备份恢复
                </div>
            </div>
        </div>

    </div>
</body>
</html>
