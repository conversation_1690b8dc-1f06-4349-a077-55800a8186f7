<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>检索精度对比柱状图</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 30px;
        }
        
        .title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            letter-spacing: -0.5px;
        }
        
        .subtitle {
            font-size: 16px;
            opacity: 0.9;
            font-weight: 400;
        }
        
        .chart-container {
            padding: 40px;
            background: white;
        }
        
        .chart-wrapper {
            position: relative;
            height: 500px;
            margin-bottom: 30px;
        }
        
        .insights {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .insight-card {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 25px;
            border-radius: 12px;
            border-left: 4px solid #667eea;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        .insight-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .insight-content {
            color: #64748b;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .legend {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
        }
        
        .traditional {
            background: linear-gradient(135deg, #f87171 0%, #ef4444 100%);
        }
        
        .rag {
            background: linear-gradient(135deg, #34d399 0%, #10b981 100%);
        }
        
        .stats-summary {
            background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
            padding: 25px;
            border-radius: 12px;
            margin-top: 30px;
            text-align: center;
        }
        
        .stats-title {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 15px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #64748b;
            font-weight: 500;
        }
        
        @media (max-width: 768px) {
            .chart-wrapper {
                height: 400px;
            }
            
            .legend {
                flex-direction: column;
                align-items: center;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">📊 检索精度对比柱状图</div>
            <div class="subtitle">Retrieval Accuracy Comparison Chart</div>
        </div>
        
        <div class="chart-container">
            <!-- 图例 -->
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color traditional"></div>
                    <span>传统关键词搜索</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color rag"></div>
                    <span>RAG智能检索</span>
                </div>
            </div>
            
            <!-- 图表 -->
            <div class="chart-wrapper">
                <canvas id="comparisonChart"></canvas>
            </div>
            
            <!-- 统计总结 -->
            <div class="stats-summary">
                <div class="stats-title">🎯 检索性能提升统计</div>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">+41.7%</div>
                        <div class="stat-label">平均准确率提升</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">+65%</div>
                        <div class="stat-label">多轮对话提升</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">+50%</div>
                        <div class="stat-label">长尾查询提升</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">92%</div>
                        <div class="stat-label">最高准确率</div>
                    </div>
                </div>
            </div>

            <!-- 详细分析 -->
            <div class="insights">
                <div class="insight-card">
                    <div class="insight-title">
                        🎯 语义匹配优势
                    </div>
                    <div class="insight-content">
                        RAG检索通过Sentence-BERT和BGE-M3模型进行语义向量化，能够理解查询的深层含义，相比传统关键词匹配提升了27个百分点，达到92%的语义匹配准确率。
                    </div>
                </div>

                <div class="insight-card">
                    <div class="insight-title">
                        🧠 上下文理解能力
                    </div>
                    <div class="insight-content">
                        传统搜索无法理解查询的上下文关系，而RAG检索通过768维语义向量空间捕获上下文信息，在上下文理解方面提升了43个百分点。
                    </div>
                </div>

                <div class="insight-card">
                    <div class="insight-title">
                        🔍 长尾查询处理
                    </div>
                    <div class="insight-content">
                        对于复杂、专业的长尾查询，RAG检索通过语义相似度阈值动态调节，能够找到相关但不完全匹配关键词的内容，准确率提升50个百分点。
                    </div>
                </div>

                <div class="insight-card">
                    <div class="insight-title">
                        💬 多轮对话支持
                    </div>
                    <div class="insight-content">
                        RAG检索结合对话管理系统，能够维护多轮对话的上下文连贯性，在多轮对话场景下的准确率达到90%，相比传统搜索提升65个百分点。
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 图表配置
        const ctx = document.getElementById('comparisonChart').getContext('2d');

        const data = {
            labels: ['语义匹配', '上下文理解', '长尾查询', '多轮对话', '专业领域'],
            datasets: [
                {
                    label: '传统关键词搜索',
                    data: [65, 45, 35, 25, 55],
                    backgroundColor: 'rgba(248, 113, 113, 0.8)',
                    borderColor: 'rgba(239, 68, 68, 1)',
                    borderWidth: 2,
                    borderRadius: 6,
                    borderSkipped: false,
                },
                {
                    label: 'RAG智能检索',
                    data: [92, 88, 85, 90, 87],
                    backgroundColor: 'rgba(52, 211, 153, 0.8)',
                    borderColor: 'rgba(16, 185, 129, 1)',
                    borderWidth: 2,
                    borderRadius: 6,
                    borderSkipped: false,
                }
            ]
        };

        const config = {
            type: 'bar',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '传统搜索 vs RAG检索 - 准确率对比',
                        font: {
                            size: 18,
                            weight: 'bold',
                            family: 'Inter'
                        },
                        color: '#1e293b',
                        padding: 20
                    },
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: '#667eea',
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ' + context.parsed.y + '%';
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            },
                            font: {
                                family: 'Inter',
                                size: 12
                            },
                            color: '#64748b'
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)',
                            drawBorder: false
                        }
                    },
                    x: {
                        ticks: {
                            font: {
                                family: 'Inter',
                                size: 12,
                                weight: '500'
                            },
                            color: '#1e293b'
                        },
                        grid: {
                            display: false
                        }
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeInOutQuart'
                }
            }
        };

        new Chart(ctx, config);
    </script>
</body>
</html>
