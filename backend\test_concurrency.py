#!/usr/bin/env python3
"""
测试并发配置脚本
"""
import sys
import os
sys.path.append('.')

from app.config import ProcessingConfig

def test_concurrency_config():
    """测试并发配置"""
    print("🔧 文档处理并发配置测试")
    print("=" * 50)
    
    # 测试文件大小分类
    test_files = [
        (0.5, "小文件"),
        (5.0, "中等文件"), 
        (15.0, "大文件"),
        (50.0, "超大文件")
    ]
    
    print("📊 文件分类和并发限制:")
    for size_mb, desc in test_files:
        category = ProcessingConfig.get_file_category(size_mb)
        concurrency = ProcessingConfig.get_concurrency_limit(size_mb)
        timeout = ProcessingConfig.get_timeout(size_mb)
        batch_size = ProcessingConfig.get_batch_size(size_mb)
        
        print(f"  {desc} ({size_mb}MB):")
        print(f"    类别: {category}")
        print(f"    并发数: {concurrency}")
        print(f"    超时时间: {timeout}秒")
        print(f"    批次大小: {batch_size}")
        print()
    
    print("🚀 总并发能力:")
    print(f"  大文件并发: {ProcessingConfig.LARGE_FILE_CONCURRENCY}")
    print(f"  中等文件并发: {ProcessingConfig.MEDIUM_FILE_CONCURRENCY}")
    print(f"  小文件并发: {ProcessingConfig.SMALL_FILE_CONCURRENCY}")
    print(f"  理论最大并发: {ProcessingConfig.LARGE_FILE_CONCURRENCY + ProcessingConfig.MEDIUM_FILE_CONCURRENCY + ProcessingConfig.SMALL_FILE_CONCURRENCY}")
    
    print("\n✅ 配置测试完成！")

if __name__ == "__main__":
    test_concurrency_config()
