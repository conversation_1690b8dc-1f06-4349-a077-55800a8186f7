import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { UserInfo, LoginRequest, RegisterRequest } from '@/types'
import { authAPI } from '@/api/auth'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<UserInfo | null>(null)
  const token = ref<string | null>(null)
  const loading = ref(false)

  // 初始化：从localStorage恢复状态
  const initFromStorage = () => {
    const savedToken = localStorage.getItem('token')
    const savedUser = localStorage.getItem('user')

    if (savedToken && savedUser) {
      try {
        token.value = savedToken
        user.value = JSON.parse(savedUser)
        console.log('🔄 从localStorage恢复认证状态:', user.value?.username)
      } catch (error) {
        console.warn('恢复用户信息失败:', error)
        localStorage.removeItem('token')
        localStorage.removeItem('user')
      }
    }
  }

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.is_admin === true)
  const isUser = computed(() => user.value?.is_admin === false)

  // 设置用户信息
  const setUser = (userData: UserInfo) => {
    user.value = userData
    localStorage.setItem('user', JSON.stringify(userData))
    console.log('💾 设置用户信息:', {
      username: userData.username,
      is_admin: userData.is_admin
    })
  }

  // 设置token
  const setToken = (newToken: string) => {
    token.value = newToken
    localStorage.setItem('token', newToken)
  }

  // 清除认证信息
  const clearAuth = () => {
    console.log('🧹 清除认证信息')
    user.value = null
    token.value = null
    localStorage.removeItem('token')
    localStorage.removeItem('user')
  }

  // 登录
  const login = async (loginForm: LoginRequest) => {
    loading.value = true
    try {
      console.log('� 开始登录:', loginForm.username)

      // 清除之前的认证信息
      clearAuth()

      // 调用登录API
      const response = await authAPI.login(loginForm)
      const { access_token } = response

      console.log('✅ 登录成功，获取到token')

      // 设置token
      setToken(access_token)

      // 获取用户信息
      const userData = await authAPI.getCurrentUser()

      console.log('� 获取到用户信息:', {
        username: userData.username,
        is_admin: userData.is_admin
      })

      // 设置用户信息
      setUser(userData)

      return { success: true, user: userData }
    } catch (error: any) {
      console.error('❌ 登录失败:', error)
      clearAuth()
      return {
        success: false,
        message: error.response?.data?.detail || error.message || '登录失败'
      }
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (registerForm: RegisterRequest) => {
    loading.value = true
    try {
      const userData = await authAPI.register(registerForm)

      setUser(userData)

      return { success: true, user: userData }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.detail || '注册失败'
      }
    } finally {
      loading.value = false
    }
  }

  // 退出登录
  const logout = async () => {
    try {
      console.log('🚪 开始退出登录')

      // 调用后端登出API
      try {
        await authAPI.logout()
        console.log('✅ 后端登出成功')
      } catch (error) {
        console.warn('⚠️ 后端登出失败:', error)
      }

      // 清除认证信息
      clearAuth()

      // 跳转到登录页
      window.location.href = '/login'

    } catch (error) {
      console.error('❌ 退出登录失败:', error)
      // 即使出错也要清除状态
      clearAuth()
      window.location.href = '/login'
    }
  }

  // 获取用户信息
  const fetchUserInfo = async () => {
    if (!token.value) return false

    // 如果已经有用户信息，直接返回
    if (user.value) {
      console.log('⏭️ 已有用户信息，跳过获取')
      return true
    }

    try {
      console.log('🔍 获取用户信息...')
      const userData = await authAPI.getCurrentUser()

      console.log('� 获取到用户信息:', {
        username: userData.username,
        is_admin: userData.is_admin
      })

      setUser(userData)
      return true
    } catch (error) {
      console.error('❌ 获取用户信息失败:', error)
      clearAuth()
      return false
    }
  }

  // 更新用户信息
  const updateProfile = async (profileData: Partial<UserInfo>) => {
    loading.value = true
    try {
      const userData = await authAPI.updateProfile(profileData)
      setUser(userData)
      return { success: true, user: userData }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.detail || '更新失败'
      }
    } finally {
      loading.value = false
    }
  }

  // 修改密码
  const changePassword = async (currentPassword: string, newPassword: string) => {
    loading.value = true
    try {
      await authAPI.changePassword({
        current_password: currentPassword,
        new_password: newPassword
      })
      return { success: true, message: '密码修改成功' }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.detail || '密码修改失败'
      }
    } finally {
      loading.value = false
    }
  }

  // 初始化：从localStorage恢复状态
  initFromStorage()

  return {
    // 状态
    user,
    token,
    loading,

    // 计算属性
    isAuthenticated,
    isAdmin,
    isUser,

    // 方法
    setUser,
    setToken,
    clearAuth,
    login,
    register,
    logout,
    fetchUserInfo,
    updateProfile,
    changePassword
  }
})
