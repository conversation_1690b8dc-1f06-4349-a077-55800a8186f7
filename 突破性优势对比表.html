<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>对比现有方案的突破性优势</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
            padding: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 1000px;
            width: 100%;
        }
        
        .table-title {
            background: #2563eb;
            color: white;
            text-align: center;
            padding: 16px;
            font-size: 18px;
            font-weight: 600;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
        }
        
        .table-header {
            background: #2563eb;
            color: white;
        }
        
        .table-header th {
            padding: 12px 8px;
            text-align: center;
            font-weight: 600;
            border-right: 2px solid rgba(255, 255, 255, 0.4);
            font-size: 14px;
        }
        
        .table-header th:first-child {
            width: 25%;
            text-align: center;
        }
        
        .table-header th:nth-child(2),
        .table-header th:nth-child(3),
        .table-header th:nth-child(4) {
            width: 25%;
        }
        
        .table-header th:last-child {
            border-right: none;
        }
        
        .table-row {
            border-bottom: 2px solid #e5e7eb;
        }
        
        .table-row:hover {
            background: #f0f9ff;
        }
        
        .table-row:last-child {
            border-bottom: none;
        }
        
        .table-cell {
            padding: 10px 8px;
            border-right: 2px solid #e5e7eb;
            vertical-align: middle;
            line-height: 1.4;
            text-align: center;
        }
        
        .table-cell:first-child {
            background: #f0f9ff;
            font-weight: 600;
            color: #1e40af;
            text-align: center;
            border-right: 3px solid #2563eb;
        }
        
        .table-cell:last-child {
            border-right: none;
        }
        
        .coze-platform {
            color: #f59e0b;
            font-weight: 500;
        }
        
        .dify-platform {
            color: #8b5cf6;
            font-weight: 500;
        }
        
        .our-platform {
            color: #059669;
            font-weight: 600;
            background: #f0fdf4;
        }
        
        .advantage-highlight {
            background: #dcfce7;
            border-left: 3px solid #16a34a;
            font-weight: 600;
        }
        
        .status-icon {
            display: inline-block;
            margin-right: 4px;
            font-weight: bold;
        }
        
        .check {
            color: #10b981;
        }
        
        .cross {
            color: #ef4444;
        }
        
        .partial {
            color: #f59e0b;
        }
        
        .star {
            color: #fbbf24;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 15px;
            }
            
            .comparison-table {
                font-size: 11px;
            }
            
            .table-header th,
            .table-cell {
                padding: 8px 4px;
            }
        }
    </style>
</head>
<body>
    <div class="table-container">
        <div class="table-title">对比现有方案的突破性优势</div>
        <table class="comparison-table">
            <thead class="table-header">
                <tr>
                    <th>对比维度</th>
                    <th>Coze平台</th>
                    <th>Dify平台</th>
                    <th>本平台</th>
                </tr>
            </thead>
            <tbody>
                <tr class="table-row">
                    <td class="table-cell">支持模型</td>
                    <td class="table-cell coze-platform">
                        <span class="status-icon partial">△</span>仅国内模型
                    </td>
                    <td class="table-cell dify-platform">
                        <span class="status-icon partial">△</span>主要国外模型
                    </td>
                    <td class="table-cell our-platform advantage-highlight">
                        <span class="status-icon check">✓</span>国内外全覆盖
                    </td>
                </tr>
                
                <tr class="table-row">
                    <td class="table-cell">存储空间</td>
                    <td class="table-cell coze-platform">
                        <span class="status-icon cross">✗</span>&lt;100MB
                    </td>
                    <td class="table-cell dify-platform">
                        <span class="status-icon cross">✗</span>&lt;100MB
                    </td>
                    <td class="table-cell our-platform advantage-highlight">
                        <span class="status-icon star">★</span>可自定义(GB级)
                    </td>
                </tr>
                
                <tr class="table-row">
                    <td class="table-cell">部署方式</td>
                    <td class="table-cell coze-platform">
                        <span class="status-icon cross">✗</span>云端托管
                    </td>
                    <td class="table-cell dify-platform">
                        <span class="status-icon partial">△</span>云端/私有
                    </td>
                    <td class="table-cell our-platform advantage-highlight">
                        <span class="status-icon check">✓</span>完全私有化
                    </td>
                </tr>
                
                <tr class="table-row">
                    <td class="table-cell">成本控制</td>
                    <td class="table-cell coze-platform">
                        <span class="status-icon cross">✗</span>按使用付费
                    </td>
                    <td class="table-cell dify-platform">
                        <span class="status-icon cross">✗</span>高昂费用
                    </td>
                    <td class="table-cell our-platform advantage-highlight">
                        <span class="status-icon star">★</span>RAG优化80%节约
                    </td>
                </tr>
                
                <tr class="table-row">
                    <td class="table-cell">数据安全</td>
                    <td class="table-cell coze-platform">
                        <span class="status-icon cross">✗</span>云端存储
                    </td>
                    <td class="table-cell dify-platform">
                        <span class="status-icon cross">✗</span>有风险
                    </td>
                    <td class="table-cell our-platform advantage-highlight">
                        <span class="status-icon check">✓</span>完全自主可控
                    </td>
                </tr>
                
                <tr class="table-row">
                    <td class="table-cell">内容渲染</td>
                    <td class="table-cell coze-platform">
                        <span class="status-icon cross">✗</span>基础文本
                    </td>
                    <td class="table-cell dify-platform">
                        <span class="status-icon cross">✗</span>基础文本
                    </td>
                    <td class="table-cell our-platform advantage-highlight">
                        <span class="status-icon star">★</span>Markdown+HTML+图表
                    </td>
                </tr>
                
                <tr class="table-row">
                    <td class="table-cell">相似度调节</td>
                    <td class="table-cell coze-platform">
                        <span class="status-icon cross">✗</span>不支持
                    </td>
                    <td class="table-cell dify-platform">
                        <span class="status-icon cross">✗</span>不支持
                    </td>
                    <td class="table-cell our-platform advantage-highlight">
                        <span class="status-icon star">★</span>0.1-1.0精确控制
                    </td>
                </tr>
                
                <tr class="table-row">
                    <td class="table-cell">可视化能力</td>
                    <td class="table-cell coze-platform">
                        <span class="status-icon cross">✗</span>无
                    </td>
                    <td class="table-cell dify-platform">
                        <span class="status-icon partial">△</span>基础
                    </td>
                    <td class="table-cell our-platform advantage-highlight">
                        <span class="status-icon star">★</span>双引擎+自然语言
                    </td>
                </tr>
                
                <tr class="table-row">
                    <td class="table-cell">教育适配</td>
                    <td class="table-cell coze-platform">
                        <span class="status-icon cross">✗</span>通用
                    </td>
                    <td class="table-cell dify-platform">
                        <span class="status-icon cross">✗</span>通用
                    </td>
                    <td class="table-cell our-platform advantage-highlight">
                        <span class="status-icon star">★</span>学科深度定制
                    </td>
                </tr>
                
                <tr class="table-row">
                    <td class="table-cell">AI幻觉控制</td>
                    <td class="table-cell coze-platform">
                        <span class="status-icon cross">✗</span>存在
                    </td>
                    <td class="table-cell dify-platform">
                        <span class="status-icon cross">✗</span>存在
                    </td>
                    <td class="table-cell our-platform advantage-highlight">
                        <span class="status-icon star">★</span>RAG零幻觉保证
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</body>
</html>
