<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统性能监控仪表盘</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            min-height: 100vh;
            padding: 20px;
            color: white;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: white;
            text-align: center;
            padding: 30px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            letter-spacing: -0.5px;
        }
        
        .subtitle {
            font-size: 16px;
            opacity: 0.8;
            font-weight: 400;
        }
        
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin-top: 15px;
            padding: 8px 16px;
            background: rgba(16, 185, 129, 0.2);
            border: 1px solid #10b981;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .content {
            padding: 40px;
            background: #0f172a;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .metric-card {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            padding: 25px;
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #10b981, #3b82f6);
        }
        
        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
        }
        
        .metric-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }
        
        .metric-title {
            font-size: 14px;
            color: #94a3b8;
            font-weight: 500;
        }
        
        .metric-icon {
            font-size: 20px;
        }
        
        .metric-value {
            font-size: 32px;
            font-weight: 700;
            color: white;
            margin-bottom: 8px;
        }
        
        .metric-desc {
            font-size: 12px;
            color: #64748b;
        }
        
        .metric-trend {
            display: flex;
            align-items: center;
            gap: 5px;
            margin-top: 10px;
            font-size: 12px;
        }
        
        .trend-up {
            color: #10b981;
        }
        
        .trend-stable {
            color: #3b82f6;
        }
        
        .gauges-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .gauge-card {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            padding: 30px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        
        .gauge-title {
            font-size: 18px;
            font-weight: 600;
            color: white;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .gauge-container {
            position: relative;
            height: 250px;
        }
        
        .performance-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-top: 40px;
        }
        
        .detail-card {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            padding: 25px;
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
        
        .detail-title {
            font-size: 18px;
            font-weight: 600;
            color: white;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .detail-content {
            color: #94a3b8;
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .feature-list {
            list-style: none;
        }
        
        .feature-list li {
            padding: 6px 0;
            color: #cbd5e1;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .feature-list li::before {
            content: '✓';
            color: #10b981;
            font-weight: bold;
            font-size: 14px;
        }
        
        .alert-section {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            padding: 25px;
            border-radius: 15px;
            margin-top: 40px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .alert-title {
            font-size: 18px;
            font-weight: 600;
            color: white;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .alert-content {
            color: #94a3b8;
            font-size: 14px;
            line-height: 1.6;
            text-align: center;
        }
        
        .highlight {
            color: #10b981;
            font-weight: 600;
        }
        
        @media (max-width: 768px) {
            .metrics-grid {
                grid-template-columns: 1fr;
            }
            
            .gauges-section {
                grid-template-columns: 1fr;
            }
            
            .performance-details {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">📊 系统性能监控仪表盘</div>
            <div class="subtitle">System Performance Monitoring Dashboard</div>
            <div class="status-indicator">
                <div class="status-dot"></div>
                系统运行正常
            </div>
        </div>
        
        <div class="content">
            <!-- 核心性能指标 -->
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-header">
                        <div class="metric-title">系统可用性</div>
                        <div class="metric-icon">🟢</div>
                    </div>
                    <div class="metric-value">99.9%</div>
                    <div class="metric-desc">7×24小时持续运行</div>
                    <div class="metric-trend trend-stable">
                        ➡️ 稳定运行
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-header">
                        <div class="metric-title">响应时间</div>
                        <div class="metric-icon">⚡</div>
                    </div>
                    <div class="metric-value">&lt;2秒</div>
                    <div class="metric-desc">平均响应时间</div>
                    <div class="metric-trend trend-up">
                        ⬆️ 性能优秀
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-header">
                        <div class="metric-title">并发支持</div>
                        <div class="metric-icon">👥</div>
                    </div>
                    <div class="metric-value">1000+</div>
                    <div class="metric-desc">同时在线用户</div>
                    <div class="metric-trend trend-up">
                        ⬆️ 高并发处理
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-header">
                        <div class="metric-title">准确率</div>
                        <div class="metric-icon">🎯</div>
                    </div>
                    <div class="metric-value">&gt;95%</div>
                    <div class="metric-desc">权威内容回答</div>
                    <div class="metric-trend trend-up">
                        ⬆️ 专业可靠
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-header">
                        <div class="metric-title">可追溯性</div>
                        <div class="metric-icon">🔍</div>
                    </div>
                    <div class="metric-value">100%</div>
                    <div class="metric-desc">来源完全可追溯</div>
                    <div class="metric-trend trend-stable">
                        ➡️ 透明可信
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-header">
                        <div class="metric-title">AI幻觉率</div>
                        <div class="metric-icon">🛡️</div>
                    </div>
                    <div class="metric-value">&lt;1%</div>
                    <div class="metric-desc">极低错误率</div>
                    <div class="metric-trend trend-up">
                        ⬆️ 高度可靠
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-header">
                        <div class="metric-title">术语识别</div>
                        <div class="metric-icon">📚</div>
                    </div>
                    <div class="metric-value">&gt;98%</div>
                    <div class="metric-desc">专业术语识别率</div>
                    <div class="metric-trend trend-up">
                        ⬆️ 专业精准
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-header">
                        <div class="metric-title">运行时长</div>
                        <div class="metric-icon">⏰</div>
                    </div>
                    <div class="metric-value">24/7</div>
                    <div class="metric-desc">全天候服务</div>
                    <div class="metric-trend trend-stable">
                        ➡️ 持续在线
                    </div>
                </div>
            </div>

            <!-- 仪表盘图表 -->
            <div class="gauges-section">
                <div class="gauge-card">
                    <div class="gauge-title">🔧 系统稳定性监控</div>
                    <div class="gauge-container">
                        <canvas id="stabilityGauge"></canvas>
                    </div>
                </div>

                <div class="gauge-card">
                    <div class="gauge-title">🎯 准确性指标监控</div>
                    <div class="gauge-container">
                        <canvas id="accuracyGauge"></canvas>
                    </div>
                </div>

                <div class="gauge-card">
                    <div class="gauge-title">⚡ 性能指标监控</div>
                    <div class="gauge-container">
                        <canvas id="performanceGauge"></canvas>
                    </div>
                </div>
            </div>

            <!-- 详细性能分析 -->
            <div class="performance-details">
                <div class="detail-card">
                    <div class="detail-title">
                        🔧 系统稳定性保障
                    </div>
                    <div class="detail-content">
                        系统采用高可用架构设计，确保7×24小时持续稳定运行，
                        支持1000+并发用户同时访问，响应时间始终保持在2秒以内。
                    </div>
                    <ul class="feature-list">
                        <li>7×24小时持续运行</li>
                        <li>支持1000+并发访问</li>
                        <li>平均响应时间&lt;2秒</li>
                        <li>系统可用性&gt;99.9%</li>
                    </ul>
                </div>

                <div class="detail-card">
                    <div class="detail-title">
                        🎯 专业性能保证
                    </div>
                    <div class="detail-content">
                        基于RAG技术的智能问答系统，权威内容回答准确率超过95%，
                        所有回答来源100%可追溯，AI幻觉率控制在1%以下。
                    </div>
                    <ul class="feature-list">
                        <li>回答准确率&gt;95%</li>
                        <li>来源100%可追溯</li>
                        <li>AI幻觉率&lt;1%</li>
                        <li>专业术语识别&gt;98%</li>
                    </ul>
                </div>

                <div class="detail-card">
                    <div class="detail-title">
                        📊 实时监控体系
                    </div>
                    <div class="detail-content">
                        建立完善的实时监控体系，对系统各项关键指标进行24小时监控，
                        确保任何异常情况都能及时发现和处理。
                    </div>
                    <ul class="feature-list">
                        <li>实时性能监控</li>
                        <li>智能异常告警</li>
                        <li>自动故障恢复</li>
                        <li>详细日志记录</li>
                    </ul>
                </div>

                <div class="detail-card">
                    <div class="detail-title">
                        🛡️ 质量控制机制
                    </div>
                    <div class="detail-content">
                        多层次质量控制机制确保系统输出的高质量和可靠性，
                        通过严格的内容审核和智能检测，最大化降低错误率。
                    </div>
                    <ul class="feature-list">
                        <li>多层内容审核</li>
                        <li>智能质量检测</li>
                        <li>持续优化改进</li>
                        <li>用户反馈机制</li>
                    </ul>
                </div>
            </div>

            <!-- 系统状态总结 -->
            <div class="alert-section">
                <div class="alert-title">🎯 系统性能总结</div>
                <div class="alert-content">
                    系统当前运行状态<span class="highlight">优秀</span>，各项核心指标均达到或超过设计标准。
                    <span class="highlight">99.9%</span>的系统可用性确保了服务的持续稳定，
                    <span class="highlight">&lt;2秒</span>的响应时间提供了优秀的用户体验，
                    <span class="highlight">&gt;95%</span>的准确率保证了专业内容的可靠性。
                    系统具备强大的<span class="highlight">1000+并发</span>处理能力，
                    能够满足大规模教育应用的需求。
                </div>
            </div>
        </div>
    </div>

    <script>
        // 系统稳定性仪表盘
        const stabilityCtx = document.getElementById('stabilityGauge').getContext('2d');
        new Chart(stabilityCtx, {
            type: 'doughnut',
            data: {
                labels: ['可用性', '响应时间', '并发处理'],
                datasets: [{
                    data: [99.9, 98.5, 95.0], // 稳定性指标
                    backgroundColor: [
                        '#10b981', // 绿色 - 可用性
                        '#3b82f6', // 蓝色 - 响应时间
                        '#f59e0b'  // 橙色 - 并发处理
                    ],
                    borderColor: '#0f172a',
                    borderWidth: 3,
                    cutout: '60%'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            font: { size: 11, weight: '500' },
                            color: '#cbd5e1',
                            usePointStyle: true,
                            pointStyle: 'circle',
                            padding: 10
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: '#10b981',
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                const label = context.label;
                                const value = context.parsed;

                                let unit = '%';
                                if (label === '响应时间') {
                                    return label + ': <2秒 (优秀)';
                                } else if (label === '并发处理') {
                                    return label + ': 1000+ (高性能)';
                                }
                                return label + ': ' + value + unit;
                            }
                        }
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeInOutQuart'
                }
            }
        });

        // 准确性指标仪表盘
        const accuracyCtx = document.getElementById('accuracyGauge').getContext('2d');
        new Chart(accuracyCtx, {
            type: 'doughnut',
            data: {
                labels: ['回答准确率', '可追溯性', '术语识别'],
                datasets: [{
                    data: [95.5, 100, 98.2], // 准确性指标
                    backgroundColor: [
                        '#8b5cf6', // 紫色 - 回答准确率
                        '#06b6d4', // 青色 - 可追溯性
                        '#84cc16'  // 绿色 - 术语识别
                    ],
                    borderColor: '#0f172a',
                    borderWidth: 3,
                    cutout: '60%'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            font: { size: 11, weight: '500' },
                            color: '#cbd5e1',
                            usePointStyle: true,
                            pointStyle: 'circle',
                            padding: 10
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: '#8b5cf6',
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                const label = context.label;
                                const value = context.parsed;
                                return label + ': ' + value + '%';
                            }
                        }
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeInOutQuart'
                }
            }
        });

        // 性能指标仪表盘
        const performanceCtx = document.getElementById('performanceGauge').getContext('2d');
        new Chart(performanceCtx, {
            type: 'doughnut',
            data: {
                labels: ['整体性能', '错误率控制', '服务质量'],
                datasets: [{
                    data: [96.8, 99.0, 97.5], // 性能指标
                    backgroundColor: [
                        '#ef4444', // 红色 - 整体性能
                        '#f97316', // 橙色 - 错误率控制
                        '#10b981'  // 绿色 - 服务质量
                    ],
                    borderColor: '#0f172a',
                    borderWidth: 3,
                    cutout: '60%'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            font: { size: 11, weight: '500' },
                            color: '#cbd5e1',
                            usePointStyle: true,
                            pointStyle: 'circle',
                            padding: 10
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: '#ef4444',
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                const label = context.label;
                                const value = context.parsed;

                                if (label === '错误率控制') {
                                    return 'AI幻觉率: <1% (优秀)';
                                }
                                return label + ': ' + value + '%';
                            }
                        }
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeInOutQuart'
                }
            }
        });

        // 实时更新模拟（可选）
        setInterval(() => {
            // 这里可以添加实时数据更新逻辑
            // 例如通过API获取最新的系统指标数据
        }, 30000); // 每30秒更新一次
    </script>
</body>
</html>
