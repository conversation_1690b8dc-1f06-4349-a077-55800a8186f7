<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>技术创新对比表</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
            padding: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 1000px;
            width: 100%;
        }
        
        .table-title {
            background: #2563eb;
            color: white;
            text-align: center;
            padding: 16px;
            font-size: 18px;
            font-weight: 600;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
        }
        
        .table-header {
            background: #2563eb;
            color: white;
        }

        .table-header th {
            padding: 12px 8px;
            text-align: center;
            font-weight: 600;
            border-right: 2px solid rgba(255, 255, 255, 0.4);
            font-size: 14px;
        }

        .table-header th:first-child {
            width: 20%;
            text-align: center;
            padding-left: 8px;
        }
        
        .table-header th:nth-child(2),
        .table-header th:nth-child(3),
        .table-header th:nth-child(4) {
            width: 26.67%;
        }
        
        .table-header th:last-child {
            border-right: none;
        }
        
        .table-row {
            border-bottom: 2px solid #e5e7eb;
        }

        .table-row:hover {
            background: #f0f9ff;
        }

        .table-row:last-child {
            border-bottom: none;
        }

        .table-cell {
            padding: 10px 8px;
            border-right: 2px solid #e5e7eb;
            vertical-align: middle;
            line-height: 1.4;
            text-align: center;
        }

        .table-cell:first-child {
            background: #f0f9ff;
            font-weight: 600;
            color: #1e40af;
            text-align: center;
            border-right: 3px solid #2563eb;
        }
        
        .table-cell:last-child {
            border-right: none;
        }
        
        .our-platform {
            color: #059669;
            font-weight: 500;
        }

        .existing-solution {
            color: #dc2626;
            font-weight: 500;
        }

        .advantage {
            color: #7c3aed;
            font-weight: 600;
        }
        
        .status-icon {
            display: inline-block;
            margin-right: 4px;
            font-weight: bold;
        }
        
        .check {
            color: #10b981;
        }
        
        .cross {
            color: #ef4444;
        }
        
        .partial {
            color: #f59e0b;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 15px;
            }
            
            .comparison-table {
                font-size: 11px;
            }
            
            .table-header th,
            .table-cell {
                padding: 8px 4px;
            }
            
            .table-header th:first-child,
            .table-cell:first-child {
                padding-left: 4px;
                padding-right: 4px;
            }
        }
    </style>
</head>
<body>
    <div class="table-container">
        <div class="table-title">技术架构创新对比</div>
        <table class="comparison-table">
            <thead class="table-header">
                <tr>
                    <th>技术特性</th>
                    <th>本平台</th>
                    <th>现有方案</th>
                    <th>创新优势</th>
                </tr>
            </thead>
            <tbody>
                <tr class="table-row">
                    <td class="table-cell">AI模型接入架构</td>
                    <td class="table-cell our-platform">
                        <span class="status-icon check">✓</span>多模型统一接入<br>
                        标准化API适配层
                    </td>
                    <td class="table-cell existing-solution">
                        <span class="status-icon cross">✗</span>单一模型绑定<br>
                        厂商生态局限
                    </td>
                    <td class="table-cell advantage">
                        <span class="status-icon check">✓</span>首创应用<br>
                        生态开放性
                    </td>
                </tr>
                
                <tr class="table-row">
                    <td class="table-cell">语义检索技术</td>
                    <td class="table-cell our-platform">
                        <span class="status-icon check">✓</span>SentenceBERT架构<br>
                        FAISS分布式引擎
                    </td>
                    <td class="table-cell existing-solution">
                        <span class="status-icon partial">△</span>传统关键词匹配<br>
                        简单向量检索
                    </td>
                    <td class="table-cell advantage">
                        <span class="status-icon check">✓</span>毫秒级匹配<br>
                        深度语义理解
                    </td>
                </tr>
                
                <tr class="table-row">
                    <td class="table-cell">教育领域优化</td>
                    <td class="table-cell our-platform">
                        <span class="status-icon check">✓</span>专业术语优化<br>
                        知识体系算法
                    </td>
                    <td class="table-cell existing-solution">
                        <span class="status-icon cross">✗</span>通用化处理<br>
                        缺乏专业优化
                    </td>
                    <td class="table-cell advantage">
                        <span class="status-icon check">✓</span>教育专用<br>
                        精准匹配
                    </td>
                </tr>
                
                <tr class="table-row">
                    <td class="table-cell">相似度阈值控制</td>
                    <td class="table-cell our-platform">
                        <span class="status-icon check">✓</span>动态调节机制<br>
                        0.1-1.0精确控制
                    </td>
                    <td class="table-cell existing-solution">
                        <span class="status-icon cross">✗</span>固定阈值<br>
                        无法个性化调节
                    </td>
                    <td class="table-cell advantage">
                        <span class="status-icon check">✓</span>创新设计<br>
                        智能化转换
                    </td>
                </tr>
                
                <tr class="table-row">
                    <td class="table-cell">跨学科知识融合</td>
                    <td class="table-cell our-platform">
                        <span class="status-icon check">✓</span>多维度语义空间<br>
                        知识图谱关联
                    </td>
                    <td class="table-cell existing-solution">
                        <span class="status-icon partial">△</span>单一学科处理<br>
                        知识孤岛问题
                    </td>
                    <td class="table-cell advantage">
                        <span class="status-icon check">✓</span>复合型培养<br>
                        知识融合创新
                    </td>
                </tr>
                
                <tr class="table-row">
                    <td class="table-cell">检索响应速度</td>
                    <td class="table-cell our-platform">
                        <span class="status-icon check">✓</span>毫秒级响应<br>
                        分布式并行处理
                    </td>
                    <td class="table-cell existing-solution">
                        <span class="status-icon partial">△</span>秒级响应<br>
                        单机串行处理
                    </td>
                    <td class="table-cell advantage">
                        <span class="status-icon check">✓</span>性能突破<br>
                        用户体验优化
                    </td>
                </tr>
                
                <tr class="table-row">
                    <td class="table-cell">技术架构扩展性</td>
                    <td class="table-cell our-platform">
                        <span class="status-icon check">✓</span>模块化设计<br>
                        微服务架构
                    </td>
                    <td class="table-cell existing-solution">
                        <span class="status-icon cross">✗</span>单体架构<br>
                        扩展性受限
                    </td>
                    <td class="table-cell advantage">
                        <span class="status-icon check">✓</span>高可扩展<br>
                        未来适应性强
                    </td>
                </tr>
                
                <tr class="table-row">
                    <td class="table-cell">个性化适配能力</td>
                    <td class="table-cell our-platform">
                        <span class="status-icon check">✓</span>多参数调节<br>
                        学科特性适配
                    </td>
                    <td class="table-cell existing-solution">
                        <span class="status-icon cross">✗</span>标准化输出<br>
                        缺乏个性化
                    </td>
                    <td class="table-cell advantage">
                        <span class="status-icon check">✓</span>精准定制<br>
                        专业助教转换
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</body>
</html>
