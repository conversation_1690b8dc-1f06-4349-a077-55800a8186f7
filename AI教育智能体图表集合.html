<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>慧由数生 - AI教育智能体设计与应用报告图表集合</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background: #ffffff;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .chart-section {
            margin-bottom: 50px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            overflow: hidden;
        }
        
        .chart-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px 30px;
            font-size: 20px;
            font-weight: 600;
        }
        
        .chart-content {
            padding: 30px;
        }
        
        .chart-container {
            width: 100%;
            height: 500px;
            margin: 20px 0;
        }
        
        .chart-container.large {
            height: 600px;
        }
        
        .chart-container.medium {
            height: 400px;
        }
        
        .chart-description {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            border-left: 4px solid #4facfe;
        }
        
        .mermaid {
            text-align: center;
            background: white;
        }
        
        .grid-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }
        
        .grid-3 {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin: 30px 0;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .comparison-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: 600;
        }
        
        .comparison-table td {
            padding: 12px 15px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }
        
        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .highlight-green {
            background: #d4edda !important;
            color: #155724;
            font-weight: 600;
        }
        
        .highlight-red {
            background: #f8d7da !important;
            color: #721c24;
        }
        
        .highlight-yellow {
            background: #fff3cd !important;
            color: #856404;
        }
        
        @media (max-width: 768px) {
            .grid-2, .grid-3 {
                grid-template-columns: 1fr;
            }
            
            .chart-container {
                height: 350px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>慧由数生 - AI驱动的智能教育内容创生引擎</h1>
            <p>设计与应用报告 - 图表集合</p>
            <p>作者：汪耀辉、方瑞、杨代坤、廖斌</p>
        </div>

        <!-- 图表1：学科覆盖范围雷达图 -->
        <div class="chart-section">
            <div class="chart-header">📊 图表1：学科覆盖范围雷达图</div>
            <div class="chart-content">
                <div id="chart1" class="chart-container"></div>
                <div class="chart-description">
                    <strong>图表说明：</strong>展示平台支持的各学科领域覆盖程度，包括理工类、人文社科类、艺术类、语言类等专业AI助教的生成能力。
                </div>
            </div>
        </div>

        <!-- 图表2：系统架构图 -->
        <div class="chart-section">
            <div class="chart-header">🏗️ 图表2：系统架构图</div>
            <div class="chart-content">
                <div class="mermaid chart-container large">
                    graph TB
                        subgraph "用户端 User Interface"
                            A1[智能问答对话<br/>Markdown/HTML/图表展示]
                            A2[多格式内容渲染<br/>代码高亮/LaTeX公式]
                            A3[自定义会话管理<br/>记忆长度/专注模式]
                            A4[多模型切换<br/>性能/成本平衡]
                        end

                        subgraph "管理员端 Admin Interface"
                            B1[知识库管理<br/>批量上传/自动向量化]
                            B2[用户权限控制<br/>RBAC多级管理]
                            B3[系统配置管理<br/>参数调优/阈值设置]
                            B4[数据分析面板<br/>使用统计/质量分析]
                            B5[模型管理<br/>API配置/性能监控]
                        end

                        subgraph "核心服务层 Core Services"
                            C1[RAG检索引擎<br/>FAISS + 语义匹配]
                            C2[多模型调度<br/>统一API接入]
                            C3[向量化处理<br/>Sentence-BERT/BGE-M3]
                            C4[文档解析<br/>多格式支持]
                        end

                        subgraph "数据存储层 Data Layer"
                            D1[PostgreSQL<br/>关系数据存储]
                            D2[FAISS向量库<br/>768维语义向量]
                            D3[Redis缓存<br/>会话/检索缓存]
                        end

                        A1 --> C1
                        A2 --> C2
                        A3 --> C1
                        A4 --> C2
                        B1 --> C3
                        B1 --> C4
                        B2 --> D1
                        B3 --> C1
                        B4 --> D1
                        B5 --> C2
                        C1 --> D2
                        C2 --> D3
                        C3 --> D2
                        C4 --> D1
                </div>
                <div class="chart-description">
                    <strong>图表说明：</strong>展示用户端和管理员端的功能模块关系，以及核心服务层和数据存储层的架构设计。
                </div>
            </div>
        </div>

        <!-- 图表3：RAG技术架构流程图 -->
        <div class="chart-section">
            <div class="chart-header">🔄 图表3：RAG技术架构流程图</div>
            <div class="chart-content">
                <div class="mermaid chart-container large">
                    flowchart LR
                        A[文档上传<br/>PDF/Word/TXT/MD] --> B[文档解析<br/>文本提取/预处理]
                        B --> C[智能分块<br/>语义边界分割]
                        C --> D[向量化编码<br/>Sentence-BERT<br/>768维向量]
                        D --> E[FAISS索引<br/>向量存储/索引构建]
                        
                        F[用户提问<br/>自然语言输入] --> G[问题向量化<br/>同样编码器处理]
                        G --> H[语义检索<br/>余弦相似度匹配<br/>阈值0.1-1.0可调]
                        H --> I[上下文构建<br/>检索结果排序]
                        I --> J[LLM生成<br/>多模型调度]
                        J --> K[流式输出<br/>实时响应]
                        
                        E -.-> H
                        
                        style A fill:#e1f5fe
                        style F fill:#e8f5e8
                        style K fill:#fff3e0
                        style D fill:#f3e5f5
                        style H fill:#ffebee
                </div>
                <div class="chart-description">
                    <strong>图表说明：</strong>展示从文档上传到智能回答的完整RAG技术流程，包括文档处理、向量化、检索和生成各个环节。
                </div>
            </div>
        </div>

        <!-- 图表4：检索精度对比柱状图 -->
        <div class="chart-section">
            <div class="chart-header">📈 图表4：检索精度对比柱状图</div>
            <div class="chart-content">
                <div id="chart4" class="chart-container"></div>
                <div class="chart-description">
                    <strong>图表说明：</strong>对比传统关键词搜索与RAG语义检索的准确率差异，展示RAG技术在专业知识检索方面的显著优势。
                </div>
            </div>
        </div>

        <!-- 图表5：多模型调度架构图 -->
        <div class="chart-section">
            <div class="chart-header">🤖 图表5：多模型调度架构图</div>
            <div class="chart-content">
                <div class="mermaid chart-container large">
                    graph TB
                        subgraph "统一API接入层 Unified API Layer"
                            A1[标准化接口<br/>Request/Response统一]
                            A2[模型适配器<br/>API差异屏蔽]
                            A3[负载均衡<br/>智能路由分发]
                        end

                        subgraph "国内模型 Domestic Models"
                            B1[通义千问<br/>Qwen-Max]
                            B2[DeepSeek<br/>DeepSeek-Chat]
                            B3[智谱GLM<br/>GLM-4]
                            B4[文心一言<br/>ERNIE-Bot]
                        end

                        subgraph "国外模型 International Models"
                            C1[GPT-4<br/>OpenAI]
                            C2[Claude-3<br/>Anthropic]
                            C3[Gemini<br/>Google]
                        end

                        subgraph "开源模型 Open Source Models"
                            D1[LLaMA<br/>Meta]
                            D2[ChatGLM<br/>清华]
                            D3[Baichuan<br/>百川]
                        end

                        subgraph "模型管理层 Model Management"
                            E1[性能监控<br/>响应时间/准确率]
                            E2[成本控制<br/>Token消耗统计]
                            E3[故障切换<br/>自动降级]
                            E4[配置管理<br/>参数调优]
                        end

                        A1 --> A2
                        A2 --> A3
                        A3 --> B1
                        A3 --> B2
                        A3 --> B3
                        A3 --> B4
                        A3 --> C1
                        A3 --> C2
                        A3 --> C3
                        A3 --> D1
                        A3 --> D2
                        A3 --> D3

                        E1 -.-> A3
                        E2 -.-> A3
                        E3 -.-> A3
                        E4 -.-> A2
                </div>
                <div class="chart-description">
                    <strong>图表说明：</strong>展示统一API接入层和模型适配机制，实现对国内外主流AI模型的无缝集成和智能调度。
                </div>
            </div>
        </div>

        <!-- 图表6：模型性能对比雷达图 -->
        <div class="chart-section">
            <div class="chart-header">🎯 图表6：模型性能对比雷达图</div>
            <div class="chart-content">
                <div id="chart6" class="chart-container"></div>
                <div class="chart-description">
                    <strong>图表说明：</strong>对比不同AI模型在各学科领域的表现，包括理工科、人文社科、艺术类、语言类等维度的能力评估。
                </div>
            </div>
        </div>

        <!-- 图表7：向量化处理流程图 -->
        <div class="chart-section">
            <div class="chart-header">🔧 图表7：向量化处理流程图</div>
            <div class="chart-content">
                <div class="mermaid chart-container large">
                    flowchart TD
                        A[多格式文档输入<br/>PDF/Word/TXT/MD/Excel/PPT] --> B{文档类型识别}

                        B -->|PDF| C1[PDF解析器<br/>PyPDF2/pdfplumber]
                        B -->|Word| C2[Word解析器<br/>python-docx]
                        B -->|其他| C3[通用解析器<br/>文本提取]

                        C1 --> D[文本预处理<br/>清洗/去噪/格式化]
                        C2 --> D
                        C3 --> D

                        D --> E[智能分块算法<br/>语义边界识别]
                        E --> F[重叠窗口处理<br/>避免信息丢失]

                        F --> G{向量化模型选择}
                        G -->|中文优化| H1[Sentence-BERT<br/>768维向量]
                        G -->|多语言| H2[BGE-M3<br/>长文本处理]
                        G -->|中文专用| H3[Text2Vec<br/>中文优化]

                        H1 --> I[向量标准化<br/>L2归一化]
                        H2 --> I
                        H3 --> I

                        I --> J[FAISS索引构建<br/>IVF/HNSW算法]
                        J --> K[向量数据库存储<br/>持久化保存]

                        K --> L[增量更新机制<br/>版本控制]

                        style A fill:#e3f2fd
                        style D fill:#f3e5f5
                        style I fill:#e8f5e8
                        style K fill:#fff3e0
                </div>
                <div class="chart-description">
                    <strong>图表说明：</strong>展示从文档解析到向量存储的完整处理流程，包括多格式支持、智能分块、向量化编码等关键环节。
                </div>
            </div>
        </div>

        <!-- 图表8：数据可视化功能展示 -->
        <div class="chart-section">
            <div class="chart-header">📊 图表8：数据可视化功能展示</div>
            <div class="chart-content">
                <div class="grid-2">
                    <div id="chart8-1" class="chart-container medium"></div>
                    <div id="chart8-2" class="chart-container medium"></div>
                </div>
                <div class="grid-2">
                    <div id="chart8-3" class="chart-container medium"></div>
                    <div id="chart8-4" class="chart-container medium"></div>
                </div>
                <div class="chart-description">
                    <strong>图表说明：</strong>展示平台支持的各种图表类型和应用场景，包括柱状图、折线图、饼图、雷达图等多种可视化形式。
                </div>
            </div>
        </div>

        <!-- 图表9：成本对比柱状图 -->
        <div class="chart-section">
            <div class="chart-header">💰 图表9：成本对比柱状图</div>
            <div class="chart-content">
                <div id="chart9" class="chart-container"></div>
                <div class="chart-description">
                    <strong>图表说明：</strong>对比传统AI对话与RAG方式的Token消耗和成本差异，展示RAG技术在成本控制方面的显著优势。
                </div>
            </div>
        </div>

        <!-- 图表10：教学效率提升对比图 -->
        <div class="chart-section">
            <div class="chart-header">📈 图表10：教学效率提升对比图</div>
            <div class="chart-content">
                <div id="chart10" class="chart-container"></div>
                <div class="chart-description">
                    <strong>图表说明：</strong>展示使用平台前后教师工作效率的显著提升，包括教案准备、答疑效率、课程设计等多个维度。
                </div>
            </div>
        </div>

        <!-- 图表11：学习效果改善雷达图 -->
        <div class="chart-section">
            <div class="chart-header">🎯 图表11：学习效果改善雷达图</div>
            <div class="chart-content">
                <div id="chart11" class="chart-container"></div>
                <div class="chart-description">
                    <strong>图表说明：</strong>多维度展示学生学习效果的改善情况，包括问答准确率、响应速度、学习体验等关键指标。
                </div>
            </div>
        </div>

        <!-- 图表12：教育领域覆盖饼图 -->
        <div class="chart-section">
            <div class="chart-header">🎓 图表12：教育领域覆盖饼图</div>
            <div class="chart-content">
                <div id="chart12" class="chart-container"></div>
                <div class="chart-description">
                    <strong>图表说明：</strong>展示平台在各教育层次和学科领域的应用分布，体现广泛的适用性和覆盖面。
                </div>
            </div>
        </div>

        <!-- 图表13：系统性能监控仪表盘 -->
        <div class="chart-section">
            <div class="chart-header">⚡ 图表13：系统性能监控仪表盘</div>
            <div class="chart-content">
                <div class="grid-2">
                    <div id="chart13-1" class="chart-container medium"></div>
                    <div id="chart13-2" class="chart-container medium"></div>
                </div>
                <div class="grid-2">
                    <div id="chart13-3" class="chart-container medium"></div>
                    <div id="chart13-4" class="chart-container medium"></div>
                </div>
                <div class="chart-description">
                    <strong>图表说明：</strong>实时展示系统稳定性和准确性指标，包括响应时间、并发用户数、准确率、可用性等关键性能指标。
                </div>
            </div>
        </div>

        <!-- 图表14：技术创新对比表 -->
        <div class="chart-section">
            <div class="chart-header">🚀 图表14：技术创新对比表</div>
            <div class="chart-content">
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>对比维度</th>
                            <th>Coze平台</th>
                            <th>Dify平台</th>
                            <th>本平台</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>支持模型</strong></td>
                            <td class="highlight-red">仅国内模型</td>
                            <td class="highlight-yellow">主要国外模型</td>
                            <td class="highlight-green">国内外全覆盖</td>
                        </tr>
                        <tr>
                            <td><strong>存储空间</strong></td>
                            <td class="highlight-red">&lt;100MB</td>
                            <td class="highlight-red">&lt;100MB</td>
                            <td class="highlight-green">可自定义(GB级)</td>
                        </tr>
                        <tr>
                            <td><strong>部署方式</strong></td>
                            <td class="highlight-red">云端托管</td>
                            <td class="highlight-yellow">云端/私有</td>
                            <td class="highlight-green">完全私有化</td>
                        </tr>
                        <tr>
                            <td><strong>成本控制</strong></td>
                            <td class="highlight-red">按使用付费</td>
                            <td class="highlight-red">高昂费用</td>
                            <td class="highlight-green">RAG优化80%节约</td>
                        </tr>
                        <tr>
                            <td><strong>数据安全</strong></td>
                            <td class="highlight-red">云端存储</td>
                            <td class="highlight-yellow">有风险</td>
                            <td class="highlight-green">完全自主可控</td>
                        </tr>
                        <tr>
                            <td><strong>内容渲染</strong></td>
                            <td class="highlight-red">基础文本</td>
                            <td class="highlight-red">基础文本</td>
                            <td class="highlight-green">Markdown+HTML+图表</td>
                        </tr>
                        <tr>
                            <td><strong>相似度调节</strong></td>
                            <td class="highlight-red">不支持</td>
                            <td class="highlight-red">不支持</td>
                            <td class="highlight-green">0.1-1.0精确控制</td>
                        </tr>
                        <tr>
                            <td><strong>可视化能力</strong></td>
                            <td class="highlight-red">无</td>
                            <td class="highlight-yellow">基础</td>
                            <td class="highlight-green">双引擎+自然语言</td>
                        </tr>
                        <tr>
                            <td><strong>教育适配</strong></td>
                            <td class="highlight-red">通用</td>
                            <td class="highlight-red">通用</td>
                            <td class="highlight-green">学科深度定制</td>
                        </tr>
                        <tr>
                            <td><strong>AI幻觉控制</strong></td>
                            <td class="highlight-red">存在</td>
                            <td class="highlight-red">存在</td>
                            <td class="highlight-green">RAG零幻觉保证</td>
                        </tr>
                    </tbody>
                </table>
                <div class="chart-description">
                    <strong>图表说明：</strong>对比本平台与现有方案在技术架构上的创新突破，全面展示技术优势和竞争力。
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            }
        });

        // 图表1：学科覆盖范围雷达图
        const chart1 = echarts.init(document.getElementById('chart1'));
        const chart1Option = {
            title: {
                text: '学科覆盖范围评估',
                left: 'center',
                textStyle: {
                    fontSize: 18,
                    fontWeight: 'bold'
                }
            },
            tooltip: {
                trigger: 'item'
            },
            radar: {
                indicator: [
                    { name: '理工类', max: 100 },
                    { name: '人文社科类', max: 100 },
                    { name: '艺术类', max: 100 },
                    { name: '语言类', max: 100 },
                    { name: '医学类', max: 100 },
                    { name: '经管类', max: 100 }
                ],
                radius: '60%',
                axisName: {
                    color: '#333',
                    fontSize: 12
                }
            },
            series: [{
                name: '覆盖程度',
                type: 'radar',
                data: [{
                    value: [95, 90, 85, 92, 88, 90],
                    name: '本平台',
                    areaStyle: {
                        color: 'rgba(79, 172, 254, 0.3)'
                    },
                    lineStyle: {
                        color: '#4facfe',
                        width: 3
                    },
                    itemStyle: {
                        color: '#4facfe'
                    }
                }]
            }]
        };
        chart1.setOption(chart1Option);

        // 图表4：检索精度对比柱状图
        const chart4 = echarts.init(document.getElementById('chart4'));
        const chart4Option = {
            title: {
                text: '检索精度对比分析',
                left: 'center',
                textStyle: {
                    fontSize: 18,
                    fontWeight: 'bold'
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            legend: {
                data: ['传统关键词搜索', 'RAG语义检索'],
                top: '10%'
            },
            xAxis: {
                type: 'category',
                data: ['理工科', '人文社科', '艺术类', '语言类', '综合平均'],
                axisLabel: {
                    fontSize: 12
                }
            },
            yAxis: {
                type: 'value',
                name: '准确率 (%)',
                max: 100,
                axisLabel: {
                    formatter: '{value}%'
                }
            },
            series: [
                {
                    name: '传统关键词搜索',
                    type: 'bar',
                    data: [55, 62, 58, 65, 60],
                    itemStyle: {
                        color: '#ff6b6b'
                    }
                },
                {
                    name: 'RAG语义检索',
                    type: 'bar',
                    data: [96, 94, 92, 97, 95],
                    itemStyle: {
                        color: '#4facfe'
                    }
                }
            ]
        };
        chart4.setOption(chart4Option);

        // 图表6：模型性能对比雷达图
        const chart6 = echarts.init(document.getElementById('chart6'));
        const chart6Option = {
            title: {
                text: '不同AI模型性能对比',
                left: 'center',
                textStyle: {
                    fontSize: 18,
                    fontWeight: 'bold'
                }
            },
            tooltip: {
                trigger: 'item'
            },
            legend: {
                data: ['GPT-4', 'Claude-3', '通义千问', 'DeepSeek'],
                top: '10%'
            },
            radar: {
                indicator: [
                    { name: '理工科', max: 100 },
                    { name: '人文社科', max: 100 },
                    { name: '艺术创作', max: 100 },
                    { name: '语言翻译', max: 100 },
                    { name: '逻辑推理', max: 100 },
                    { name: '代码生成', max: 100 }
                ],
                radius: '60%'
            },
            series: [{
                type: 'radar',
                data: [
                    {
                        value: [95, 92, 85, 90, 96, 94],
                        name: 'GPT-4',
                        areaStyle: { color: 'rgba(255, 107, 107, 0.2)' },
                        lineStyle: { color: '#ff6b6b' }
                    },
                    {
                        value: [93, 96, 88, 94, 92, 89],
                        name: 'Claude-3',
                        areaStyle: { color: 'rgba(79, 172, 254, 0.2)' },
                        lineStyle: { color: '#4facfe' }
                    },
                    {
                        value: [88, 90, 82, 92, 85, 87],
                        name: '通义千问',
                        areaStyle: { color: 'rgba(87, 242, 135, 0.2)' },
                        lineStyle: { color: '#57f287' }
                    },
                    {
                        value: [90, 87, 80, 88, 93, 91],
                        name: 'DeepSeek',
                        areaStyle: { color: 'rgba(255, 159, 67, 0.2)' },
                        lineStyle: { color: '#ff9f43' }
                    }
                ]
            }]
        };
        chart6.setOption(chart6Option);

        // 图表8：数据可视化功能展示
        // 8-1: 柱状图示例
        const chart8_1 = echarts.init(document.getElementById('chart8-1'));
        const chart8_1Option = {
            title: { text: '学习进度统计', textStyle: { fontSize: 14 } },
            xAxis: { type: 'category', data: ['数学', '物理', '化学', '生物'] },
            yAxis: { type: 'value' },
            series: [{
                data: [85, 92, 78, 88],
                type: 'bar',
                itemStyle: { color: '#4facfe' }
            }]
        };
        chart8_1.setOption(chart8_1Option);

        // 8-2: 折线图示例
        const chart8_2 = echarts.init(document.getElementById('chart8-2'));
        const chart8_2Option = {
            title: { text: '成绩趋势分析', textStyle: { fontSize: 14 } },
            xAxis: { type: 'category', data: ['第1周', '第2周', '第3周', '第4周'] },
            yAxis: { type: 'value' },
            series: [{
                data: [75, 82, 88, 92],
                type: 'line',
                smooth: true,
                itemStyle: { color: '#57f287' }
            }]
        };
        chart8_2.setOption(chart8_2Option);

        // 8-3: 饼图示例
        const chart8_3 = echarts.init(document.getElementById('chart8-3'));
        const chart8_3Option = {
            title: { text: '知识点掌握分布', textStyle: { fontSize: 14 } },
            series: [{
                type: 'pie',
                radius: '60%',
                data: [
                    { value: 40, name: '已掌握', itemStyle: { color: '#57f287' } },
                    { value: 30, name: '部分掌握', itemStyle: { color: '#ff9f43' } },
                    { value: 20, name: '待学习', itemStyle: { color: '#ff6b6b' } },
                    { value: 10, name: '未涉及', itemStyle: { color: '#ddd' } }
                ]
            }]
        };
        chart8_3.setOption(chart8_3Option);

        // 8-4: 雷达图示例
        const chart8_4 = echarts.init(document.getElementById('chart8-4'));
        const chart8_4Option = {
            title: { text: '能力评估雷达图', textStyle: { fontSize: 14 } },
            radar: {
                indicator: [
                    { name: '理解能力', max: 100 },
                    { name: '应用能力', max: 100 },
                    { name: '分析能力', max: 100 },
                    { name: '创新能力', max: 100 }
                ],
                radius: '60%'
            },
            series: [{
                type: 'radar',
                data: [{
                    value: [85, 78, 82, 75],
                    areaStyle: { color: 'rgba(79, 172, 254, 0.3)' },
                    lineStyle: { color: '#4facfe' }
                }]
            }]
        };
        chart8_4.setOption(chart8_4Option);

        // 图表9：成本对比柱状图
        const chart9 = echarts.init(document.getElementById('chart9'));
        const chart9Option = {
            title: {
                text: 'Token消耗与成本对比',
                left: 'center',
                textStyle: {
                    fontSize: 18,
                    fontWeight: 'bold'
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: { type: 'shadow' }
            },
            legend: {
                data: ['传统AI对话', 'RAG优化方式'],
                top: '10%'
            },
            xAxis: {
                type: 'category',
                data: ['Token消耗量', '单次对话成本(元)', '年度运营成本(万元)']
            },
            yAxis: {
                type: 'value',
                name: '相对值'
            },
            series: [
                {
                    name: '传统AI对话',
                    type: 'bar',
                    data: [4000, 0.3, 5],
                    itemStyle: { color: '#ff6b6b' }
                },
                {
                    name: 'RAG优化方式',
                    type: 'bar',
                    data: [650, 0.06, 1],
                    itemStyle: { color: '#57f287' }
                }
            ]
        };
        chart9.setOption(chart9Option);

        // 图表10：教学效率提升对比图
        const chart10 = echarts.init(document.getElementById('chart10'));
        const chart10Option = {
            title: {
                text: '教学效率提升对比',
                left: 'center',
                textStyle: {
                    fontSize: 18,
                    fontWeight: 'bold'
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: { type: 'shadow' }
            },
            legend: {
                data: ['使用前', '使用后', '提升幅度'],
                top: '10%'
            },
            xAxis: {
                type: 'category',
                data: ['教案准备(小时)', '答疑效率(问题/小时)', '课程设计(小时)', '题库整理(小时)']
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    name: '使用前',
                    type: 'bar',
                    data: [8, 10, 6, 4],
                    itemStyle: { color: '#ff9f43' }
                },
                {
                    name: '使用后',
                    type: 'bar',
                    data: [2.4, 40, 2.4, 12],
                    itemStyle: { color: '#4facfe' }
                },
                {
                    name: '提升幅度',
                    type: 'line',
                    yAxisIndex: 1,
                    data: [70, 300, 60, 200],
                    itemStyle: { color: '#57f287' },
                    lineStyle: { width: 3 }
                }
            ],
            yAxis: [
                {
                    type: 'value',
                    name: '时间/效率',
                    position: 'left'
                },
                {
                    type: 'value',
                    name: '提升百分比(%)',
                    position: 'right'
                }
            ]
        };
        chart10.setOption(chart10Option);

        // 图表11：学习效果改善雷达图
        const chart11 = echarts.init(document.getElementById('chart11'));
        const chart11Option = {
            title: {
                text: '学习效果改善评估',
                left: 'center',
                textStyle: {
                    fontSize: 18,
                    fontWeight: 'bold'
                }
            },
            tooltip: {
                trigger: 'item'
            },
            legend: {
                data: ['使用前', '使用后'],
                top: '10%'
            },
            radar: {
                indicator: [
                    { name: '问答准确率', max: 100 },
                    { name: '响应速度', max: 100 },
                    { name: '学习体验', max: 100 },
                    { name: '知识理解', max: 100 },
                    { name: '学习兴趣', max: 100 },
                    { name: '自主学习', max: 100 }
                ],
                radius: '60%'
            },
            series: [{
                type: 'radar',
                data: [
                    {
                        value: [65, 60, 70, 68, 65, 62],
                        name: '使用前',
                        areaStyle: { color: 'rgba(255, 159, 67, 0.2)' },
                        lineStyle: { color: '#ff9f43' }
                    },
                    {
                        value: [95, 90, 92, 88, 85, 87],
                        name: '使用后',
                        areaStyle: { color: 'rgba(79, 172, 254, 0.2)' },
                        lineStyle: { color: '#4facfe' }
                    }
                ]
            }]
        };
        chart11.setOption(chart11Option);

        // 图表12：教育领域覆盖饼图
        const chart12 = echarts.init(document.getElementById('chart12'));
        const chart12Option = {
            title: {
                text: '教育领域应用分布',
                left: 'center',
                textStyle: {
                    fontSize: 18,
                    fontWeight: 'bold'
                }
            },
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c}% ({d}%)'
            },
            legend: {
                orient: 'vertical',
                left: 'left',
                top: 'middle'
            },
            series: [{
                name: '应用分布',
                type: 'pie',
                radius: ['40%', '70%'],
                center: ['60%', '50%'],
                data: [
                    { value: 35, name: '高等教育', itemStyle: { color: '#4facfe' } },
                    { value: 25, name: '基础教育', itemStyle: { color: '#57f287' } },
                    { value: 20, name: '继续教育', itemStyle: { color: '#ff9f43' } },
                    { value: 15, name: '企业培训', itemStyle: { color: '#ff6b6b' } },
                    { value: 5, name: '其他', itemStyle: { color: '#ddd' } }
                ],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }]
        };
        chart12.setOption(chart12Option);

        // 图表13：系统性能监控仪表盘
        // 13-1: 响应时间仪表盘
        const chart13_1 = echarts.init(document.getElementById('chart13-1'));
        const chart13_1Option = {
            title: { text: '平均响应时间', left: 'center', textStyle: { fontSize: 14 } },
            series: [{
                type: 'gauge',
                radius: '80%',
                data: [{ value: 1.8, name: '秒' }],
                detail: { fontSize: 20 },
                axisLine: {
                    lineStyle: {
                        color: [[0.3, '#57f287'], [0.7, '#ff9f43'], [1, '#ff6b6b']],
                        width: 8
                    }
                }
            }]
        };
        chart13_1.setOption(chart13_1Option);

        // 13-2: 并发用户数
        const chart13_2 = echarts.init(document.getElementById('chart13-2'));
        const chart13_2Option = {
            title: { text: '当前并发用户', left: 'center', textStyle: { fontSize: 14 } },
            series: [{
                type: 'gauge',
                radius: '80%',
                max: 1000,
                data: [{ value: 856, name: '用户' }],
                detail: { fontSize: 20 },
                axisLine: {
                    lineStyle: {
                        color: [[0.6, '#57f287'], [0.8, '#ff9f43'], [1, '#ff6b6b']],
                        width: 8
                    }
                }
            }]
        };
        chart13_2.setOption(chart13_2Option);

        // 13-3: 准确率
        const chart13_3 = echarts.init(document.getElementById('chart13-3'));
        const chart13_3Option = {
            title: { text: '问答准确率', left: 'center', textStyle: { fontSize: 14 } },
            series: [{
                type: 'gauge',
                radius: '80%',
                data: [{ value: 95.8, name: '%' }],
                detail: { fontSize: 20 },
                axisLine: {
                    lineStyle: {
                        color: [[0.8, '#ff6b6b'], [0.9, '#ff9f43'], [1, '#57f287']],
                        width: 8
                    }
                }
            }]
        };
        chart13_3.setOption(chart13_3Option);

        // 13-4: 系统可用性
        const chart13_4 = echarts.init(document.getElementById('chart13-4'));
        const chart13_4Option = {
            title: { text: '系统可用性', left: 'center', textStyle: { fontSize: 14 } },
            series: [{
                type: 'gauge',
                radius: '80%',
                data: [{ value: 99.95, name: '%' }],
                detail: { fontSize: 20 },
                axisLine: {
                    lineStyle: {
                        color: [[0.95, '#ff6b6b'], [0.99, '#ff9f43'], [1, '#57f287']],
                        width: 8
                    }
                }
            }]
        };
        chart13_4.setOption(chart13_4Option);

        // 响应式处理
        window.addEventListener('resize', function() {
            chart1.resize();
            chart4.resize();
            chart6.resize();
            chart8_1.resize();
            chart8_2.resize();
            chart8_3.resize();
            chart8_4.resize();
            chart9.resize();
            chart10.resize();
            chart11.resize();
            chart12.resize();
            chart13_1.resize();
            chart13_2.resize();
            chart13_3.resize();
            chart13_4.resize();
        });
    </script>
</body>
</html>
