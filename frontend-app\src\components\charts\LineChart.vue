<template>
  <BaseChart :option="chartOption" :width="width" :height="height" :theme="theme" />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import BaseChart from './BaseChart.vue'

interface LineData {
  name: string
  data: number[]
  color?: string
  smooth?: boolean
}

interface Props {
  data: LineData[]
  xAxisData: string[]
  title?: string
  width?: string
  height?: string
  theme?: string
  showGrid?: boolean
  yAxisName?: string
}

const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '300px',
  theme: 'light',
  showGrid: true
})

const chartOption = computed(() => {
  const isDark = props.theme === 'dark'
  
  return {
    title: props.title ? {
      text: props.title,
      left: 'center',
      textStyle: {
        color: isDark ? '#e5e7eb' : '#374151',
        fontSize: 16,
        fontWeight: 'bold'
      }
    } : undefined,
    tooltip: {
      trigger: 'axis',
      backgroundColor: isDark ? '#374151' : '#ffffff',
      borderColor: isDark ? '#4b5563' : '#e5e7eb',
      textStyle: {
        color: isDark ? '#e5e7eb' : '#374151'
      }
    },
    legend: {
      data: props.data.map(item => item.name),
      top: props.title ? 30 : 10,
      textStyle: {
        color: isDark ? '#e5e7eb' : '#374151'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
      show: props.showGrid,
      borderColor: isDark ? '#374151' : '#e5e7eb'
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: props.xAxisData,
      axisLine: {
        lineStyle: {
          color: isDark ? '#4b5563' : '#d1d5db'
        }
      },
      axisLabel: {
        color: isDark ? '#9ca3af' : '#6b7280'
      }
    },
    yAxis: {
      type: 'value',
      name: props.yAxisName,
      nameTextStyle: {
        color: isDark ? '#9ca3af' : '#6b7280'
      },
      axisLine: {
        lineStyle: {
          color: isDark ? '#4b5563' : '#d1d5db'
        }
      },
      axisLabel: {
        color: isDark ? '#9ca3af' : '#6b7280'
      },
      splitLine: {
        lineStyle: {
          color: isDark ? '#374151' : '#f3f4f6'
        }
      }
    },
    series: props.data.map((item, index) => ({
      name: item.name,
      type: 'line',
      smooth: item.smooth !== false,
      data: item.data,
      lineStyle: {
        color: item.color || [
          '#3b82f6', '#10b981', '#f59e0b', '#ef4444', 
          '#8b5cf6', '#06b6d4', '#84cc16', '#f97316'
        ][index % 8],
        width: 3
      },
      itemStyle: {
        color: item.color || [
          '#3b82f6', '#10b981', '#f59e0b', '#ef4444', 
          '#8b5cf6', '#06b6d4', '#84cc16', '#f97316'
        ][index % 8]
      },
      areaStyle: {
        opacity: 0.1,
        color: item.color || [
          '#3b82f6', '#10b981', '#f59e0b', '#ef4444', 
          '#8b5cf6', '#06b6d4', '#84cc16', '#f97316'
        ][index % 8]
      }
    }))
  }
})
</script>
