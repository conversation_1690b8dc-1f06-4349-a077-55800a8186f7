var e;(()=>{function s(e,s,t,r,a,i,n){try{var o=e[i](n),u=o.value}catch(e){return void t(e)}o.done?s(u):Promise.resolve(u).then(r,a)}e=function(e){return function(){var t=this,r=arguments;return new Promise(function(a,i){var n=e.apply(t,r);function o(e){s(n,a,i,o,u,"next",e)}function u(e){s(n,a,i,o,u,"throw",e)}o(void 0)})}}})();import{cW as s,dU as t,dc as r}from"./vendor-BJ-uKP15.js";import{b as a}from"./api-D-gMiCJf.js";const i=s("aiModels",()=>{const s=t([]),i=t([]),n=t([]),o=t(!1),u=r(()=>s.value.filter(e=>{if(!e.is_active)return!1;const s=i.value.find(s=>s.id===e.provider_id);return!!s&&(n.value.some(e=>e.provider_id===s.id)||e.allow_system_key_use&&e.system_api_key)})),l=(d=e(function*(){try{const e=yield a.get("/ai/providers");return i.value=e.data,{success:!0,data:i.value}}catch(s){var e;return{success:!1,message:(null===(e=s.response)||void 0===e||null===(e=e.data)||void 0===e?void 0:e.message)||"获取AI供应商失败"}}}),function(){return d.apply(this,arguments)});var d;const c=(v=e(function*(){try{const e=yield a.get("/ai/models");return s.value=e.data,{success:!0,data:s.value}}catch(t){var e;return{success:!1,message:(null===(e=t.response)||void 0===e||null===(e=e.data)||void 0===e?void 0:e.message)||"获取AI模型失败"}}}),function(){return v.apply(this,arguments)});var v;const f=(y=e(function*(){try{const e=yield a.get("/ai/api-keys");return n.value=e.data,{success:!0,data:n.value}}catch(s){var e;return{success:!1,message:(null===(e=s.response)||void 0===e||null===(e=e.data)||void 0===e?void 0:e.message)||"获取用户API密钥失败"}}}),function(){return y.apply(this,arguments)});var y;const p=(m=e(function*(){o.value=!0;try{return yield Promise.all([l(),c(),f()]),{success:!0}}catch(e){return{success:!1,message:"初始化AI模型数据失败"}}finally{o.value=!1}}),function(){return m.apply(this,arguments)});var m;return{aiModels:s,aiProviders:i,userApiKeys:n,loading:o,availableModels:u,getAvailableModels:e=>s.value.filter(s=>{if(!s.is_active)return!1;const t=i.value.find(e=>e.id===s.provider_id);if(!t)return!1;if(e){const s=e[t.id];if(!s||!s.enabled)return!1}return n.value.some(e=>e.provider_id===t.id)||s.allow_system_key_use&&s.system_api_key}),fetchAIProviders:l,fetchAIModels:c,fetchUserAPIKeys:f,initializeData:p,getModelById:e=>s.value.find(s=>s.id===e),getModelsByProvider:e=>s.value.filter(s=>s.provider_id===e&&s.is_active)}});export{i as b};