version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: ai-knowledge-postgres
    environment:
      POSTGRES_DB: aiknowledgebase
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 111222
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - ai-network
    restart: unless-stopped

  # Redis向量存储
  redis:
    image: redis:7-alpine
    container_name: ai-knowledge-redis
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - ai-network
    restart: unless-stopped

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ai-knowledge-backend
    environment:
      # 数据库配置
      DATABASE_URL: postgresql+asyncpg://postgres:111222@postgres:5432/aiknowledgebase
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      DATABASE_USER: postgres
      DATABASE_PASSWORD: 111222
      DATABASE_NAME: aiknowledgebase

      # Redis配置
      REDIS_URL: redis://redis:6379/0

      # JWT配置
      SECRET_KEY: abcXyz123_4x9KpQvE8jHmN2qRtSvWnZr5t7w-
      ALGORITHM: HS256
      ACCESS_TOKEN_EXPIRE_MINUTES: 10080  # 7天

      # CORS配置
      ALLOWED_ORIGINS: http://localhost,http://localhost:80,http://localhost:3000,https://aiknowledgebase.csicollege.cn,http://aiknowledgebase.csicollege.cn

      # 文件上传配置
      UPLOAD_DIR: /app/uploads
      MAX_FILE_SIZE: 52428800

      # AI服务配置
      SILICONFLOW_API_KEY: sk-ltvyukqqyhwhedkxyqhsqnrtqehlfdzpflskkfkqwetoiixm
      DEEPSEEK_API_KEY: ""

      # 应用配置
      APP_NAME: AI Knowledge Base
      APP_VERSION: 1.0.0
      DEBUG: false
      
    volumes:
      - ./backend/uploads:/app/uploads
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    networks:
      - ai-network
    restart: unless-stopped

  # 前端Web服务
  frontend:
    build:
      context: ./frontend-app
      dockerfile: Dockerfile
    container_name: ai-knowledge-frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - ai-network
    restart: unless-stopped

# 数据卷
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

# 网络
networks:
  ai-network:
    driver: bridge
