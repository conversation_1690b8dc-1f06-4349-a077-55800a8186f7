"""
知识库相关的Pydantic模式
"""
from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel


class KnowledgeBaseBase(BaseModel):
    """知识库基础模式"""
    name: str
    description: Optional[str] = None


class KnowledgeBaseCreate(KnowledgeBaseBase):
    """知识库创建模式"""
    pass


class KnowledgeBaseUpdate(BaseModel):
    """知识库更新模式"""
    name: Optional[str] = None
    description: Optional[str] = None


class KnowledgeBaseResponse(KnowledgeBaseBase):
    """知识库响应模式"""
    id: int
    owner_id: int
    created_at: datetime
    updated_at: datetime
    document_count: Optional[int] = 0  # 文档数量

    class Config:
        from_attributes = True


class DocumentBase(BaseModel):
    """文档基础模式"""
    filename: str
    file_type: Optional[str] = None


class DocumentResponse(DocumentBase):
    """文档响应模式"""
    id: int
    kb_id: int
    uploader_id: int
    storage_path: str
    file_size: Optional[int] = None
    status: str
    error_message: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class KnowledgeBaseWithDocuments(KnowledgeBaseResponse):
    """包含文档的知识库响应模式"""
    documents: List[DocumentResponse] = []


# 聊天相关模式
class ChatSessionCreate(BaseModel):
    """聊天会话创建模式"""
    title: str


class ChatSessionResponse(BaseModel):
    """聊天会话响应模式"""
    id: int
    user_id: int
    title: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class ChatRequest(BaseModel):
    """聊天请求模式"""
    message: str
    model_id: str
    knowledge_base_ids: Optional[List[int]] = []


class ChatMessageCreate(BaseModel):
    """聊天消息创建模式"""
    content: str
    role: str = "user"


class ChatMessageResponse(BaseModel):
    """聊天消息响应模式"""
    id: int
    session_id: int
    role: str
    content: str
    model_id_used: Optional[str] = None
    referenced_kbs: Optional[List[int]] = []
    created_at: datetime

    class Config:
        from_attributes = True
