var e;(()=>{function t(e,t,r,s,l,o,a){try{var n=e[o](a),i=n.value}catch(e){return void r(e)}n.done?t(i):Promise.resolve(i).then(s,l)}e=function(e){return function(){var r=this,s=arguments;return new Promise(function(l,o){var a=e.apply(r,s);function n(e){t(a,l,o,n,i,"next",e)}function i(e){t(a,l,o,n,i,"throw",e)}n(void 0)})}}})();import{c as t}from"./elementPlus-Di4PDIm8.js";import{cU as r,d0 as s,d1 as l,d3 as o,d6 as a,dB as n,dO as i,dU as u,dd as d,df as c,dg as m,dj as p,dl as b,dy as f,ed as v}from"./vendor-BJ-uKP15.js";import{b as g}from"./index-Byt5TjPh.js";import{b as h}from"./_plugin-vue_export-helper-CjD0mXop.js";import{b as x}from"./logo-D-wHJMD-.js";function y(){try{localStorage.getItem("token"),localStorage.getItem("user")}catch(e){}}"undefined"!=typeof window&&(window.debugAuth=y);const w={class:"min-h-screen flex items-center justify-center bg-gray-50"},k={class:"relative w-full max-w-md mx-4"},j={class:"bg-white rounded-2xl shadow-lg p-8"},C={class:"relative"},M={class:"relative"},z=["type"],A={key:0,class:"h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},B={key:1,class:"h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},_={class:"flex items-center justify-between"},S={class:"flex items-center"},U=["disabled"],V={key:0,class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",fill:"none",viewBox:"0 0 24 24"},I={class:"mt-6 pt-6 border-t border-gray-200"},L={class:"grid grid-cols-2 gap-3"};var P=h(b({__name:"Login",setup(b){const h=r(),P=g();f(()=>{P.clearAuth()});const q=u(!1),H=u(!1),O=u({username:"",password:"",rememberMe:!1}),D=(G=e(function*(){if(O.value.username&&O.value.password){q.value=!0;try{const e=yield P.login({username:O.value.username,password:O.value.password});e.success?(t.success("登录成功"),y(),h.push(P.isAdmin?"/admin/dashboard":"/user/home")):t.error(e.message||"登录失败")}catch(e){t.error("登录失败，请重试")}finally{q.value=!1}}else t.warning("请输入用户名和密码")}),function(){return G.apply(this,arguments)}),E=(F=e(function*(e){"admin"===e?(O.value.username="admin",O.value.password="admin123"):(O.value.username="testuser",O.value.password="testpass123"),yield D()}),function(e){return F.apply(this,arguments)});var F,G;return(e,t)=>(n(),m("div",w,[t[20]||(t[20]=d("div",{class:"absolute inset-0 overflow-hidden"},[d("div",{class:"absolute -top-40 -right-40 w-80 h-80 bg-blue-100 rounded-full filter blur-xl opacity-30"}),d("div",{class:"absolute -bottom-40 -left-40 w-80 h-80 bg-gray-100 rounded-full filter blur-xl opacity-30"}),d("div",{class:"absolute top-40 left-40 w-80 h-80 bg-slate-100 rounded-full filter blur-xl opacity-30"})],-1)),d("div",k,[d("div",j,[t[18]||(t[18]=d("div",{class:"text-center mb-8"},[d("img",{src:x,alt:"CogniSynth Logo",class:"w-24 h-24 mx-auto mb-4"}),d("h1",{class:"text-3xl font-bold text-gray-800 mb-2"},"CogniSynth"),d("p",{class:"text-gray-600"},"AI 驱动的智能学习与内容探索平台")],-1)),d("form",{onSubmit:a(D,["prevent"]),class:"space-y-6"},[d("div",null,[t[7]||(t[7]=d("label",{for:"username",class:"block text-sm font-medium text-gray-700 mb-2"}," 用户名 ",-1)),d("div",C,[t[6]||(t[6]=d("div",{class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},[d("svg",{class:"h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[d("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])],-1)),i(d("input",{id:"username","onUpdate:modelValue":t[0]||(t[0]=e=>O.value.username=e),type:"text",required:"",class:"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-gray-900 placeholder-gray-500",placeholder:"请输入用户名"},null,512),[[o,O.value.username]])])]),d("div",null,[t[11]||(t[11]=d("label",{for:"password",class:"block text-sm font-medium text-gray-700 mb-2"}," 密码 ",-1)),d("div",M,[t[10]||(t[10]=d("div",{class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},[d("svg",{class:"h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[d("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})])],-1)),i(d("input",{id:"password","onUpdate:modelValue":t[1]||(t[1]=e=>O.value.password=e),type:H.value?"text":"password",required:"",class:"block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-gray-900 placeholder-gray-500",placeholder:"请输入密码"},null,8,z),[[l,O.value.password]]),d("button",{type:"button",onClick:t[2]||(t[2]=e=>H.value=!H.value),class:"absolute inset-y-0 right-0 pr-3 flex items-center"},[H.value?(n(),m("svg",A,t[8]||(t[8]=[d("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},null,-1),d("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"},null,-1)]))):(n(),m("svg",B,t[9]||(t[9]=[d("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"},null,-1)])))])])]),d("div",_,[d("div",S,[i(d("input",{id:"remember-me","onUpdate:modelValue":t[3]||(t[3]=e=>O.value.rememberMe=e),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[s,O.value.rememberMe]]),t[12]||(t[12]=d("label",{for:"remember-me",class:"ml-2 block text-sm text-gray-700"}," 记住我 ",-1))]),t[13]||(t[13]=d("div",{class:"text-sm"},[d("a",{href:"#",class:"font-medium text-blue-600 hover:text-blue-500"}," 忘记密码？ ")],-1))]),d("button",{type:"submit",disabled:q.value,class:"w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"},[q.value?(n(),m("svg",V,t[14]||(t[14]=[d("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),d("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):c("",!0),p(" "+v(q.value?"登录中...":"登录"),1)],8,U)],32),d("div",I,[t[17]||(t[17]=d("p",{class:"text-center text-sm text-gray-600 mb-4"}," 快速测试登录 ",-1)),d("div",L,[d("button",{onClick:t[4]||(t[4]=e=>E("user")),class:"flex items-center justify-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"},t[15]||(t[15]=[d("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[d("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})],-1),p(" 用户登录 ",-1)])),d("button",{onClick:t[5]||(t[5]=e=>E("admin")),class:"flex items-center justify-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"},t[16]||(t[16]=[d("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[d("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})],-1),p(" 管理员 ",-1)]))])]),t[19]||(t[19]=d("div",{class:"mt-6 text-center"},[d("p",{class:"text-sm text-gray-600"},[p(" 还没有账户？ "),d("a",{href:"#",class:"font-medium text-blue-600 hover:text-blue-500"}," 立即注册 ")])],-1))])])]))}}),[["__scopeId","data-v-3c822780"]]);export{P as default};