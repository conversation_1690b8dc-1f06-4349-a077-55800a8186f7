import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { AIModel, AIProvider, UserAPIKey } from '@/types'
import { api } from '@/utils/api'

export const useAIModelsStore = defineStore('aiModels', () => {
  // 状态
  const aiModels = ref<AIModel[]>([])
  const aiProviders = ref<AIProvider[]>([])
  const userApiKeys = ref<UserAPIKey[]>([])
  const loading = ref(false)

  // 计算属性：可用的AI模型（根据用户启用的供应商和API密钥过滤）
  const availableModels = computed(() => {
    return aiModels.value.filter(model => {
      if (!model.is_active) return false

      // 查找该模型对应的供应商
      const provider = aiProviders.value.find(p => p.id === model.provider_id)
      if (!provider) return false

      // 检查供应商是否被用户启用（从用户设置中获取）
      // 这里需要从settings store获取供应商启用状态
      // 暂时返回true，后面会通过方法参数传入
      
      // 检查用户是否有该供应商的API密钥，或者该模型允许使用系统密钥
      const hasUserKey = userApiKeys.value.some(key => key.provider_id === provider.id)
      const hasSystemKey = model.allow_system_key_use && model.system_api_key

      return hasUserKey || hasSystemKey
    })
  })

  // 根据供应商偏好过滤可用模型
  const getAvailableModels = (providerPreferences?: Record<string, any>) => {
    return aiModels.value.filter(model => {
      if (!model.is_active) return false

      // 查找该模型对应的供应商
      const provider = aiProviders.value.find(p => p.id === model.provider_id)
      if (!provider) return false

      // 检查供应商是否被用户启用
      if (providerPreferences) {
        const prefs = providerPreferences[provider.id]
        if (!prefs || !prefs.enabled) return false
      }

      // 检查用户是否有该供应商的API密钥，或者该模型允许使用系统密钥
      const hasUserKey = userApiKeys.value.some(key => key.provider_id === provider.id)
      const hasSystemKey = model.allow_system_key_use && model.system_api_key

      return hasUserKey || hasSystemKey
    })
  }

  // 获取AI供应商列表
  const fetchAIProviders = async () => {
    try {
      const response = await api.get('/ai/providers')
      aiProviders.value = response.data
      return { success: true, data: aiProviders.value }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || '获取AI供应商失败'
      }
    }
  }

  // 获取AI模型列表
  const fetchAIModels = async () => {
    try {
      const response = await api.get('/ai/models')
      aiModels.value = response.data
      return { success: true, data: aiModels.value }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || '获取AI模型失败'
      }
    }
  }

  // 获取用户API密钥列表
  const fetchUserAPIKeys = async () => {
    try {
      const response = await api.get('/ai/api-keys')
      userApiKeys.value = response.data
      return { success: true, data: userApiKeys.value }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || '获取用户API密钥失败'
      }
    }
  }

  // 初始化所有数据
  const initializeData = async () => {
    loading.value = true
    try {
      await Promise.all([
        fetchAIProviders(),
        fetchAIModels(),
        fetchUserAPIKeys()
      ])
      return { success: true }
    } catch (error: any) {
      return {
        success: false,
        message: '初始化AI模型数据失败'
      }
    } finally {
      loading.value = false
    }
  }

  // 根据模型ID获取模型信息
  const getModelById = (modelId: number) => {
    return aiModels.value.find(model => model.id === modelId)
  }

  // 根据供应商ID获取模型列表
  const getModelsByProvider = (providerId: number) => {
    return aiModels.value.filter(model => 
      model.provider_id === providerId && model.is_active
    )
  }

  return {
    // 状态
    aiModels,
    aiProviders,
    userApiKeys,
    loading,
    
    // 计算属性
    availableModels,
    
    // 方法
    getAvailableModels,
    fetchAIProviders,
    fetchAIModels,
    fetchUserAPIKeys,
    initializeData,
    getModelById,
    getModelsByProvider
  }
})
