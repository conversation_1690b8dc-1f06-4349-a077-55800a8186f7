# 演示系统测试指南

## 🚀 快速启动系统

### 1. 后端启动
```bash
# 进入后端目录
cd backend

# 安装依赖（如果还没安装）
pip install -r requirements.txt

# 启动后端服务
python start_server.py
```

**预期结果**：
- 服务启动在 http://localhost:8000
- 看到 "Application startup complete" 消息
- API文档可访问：http://localhost:8000/docs

### 2. 前端启动
```bash
# 进入前端目录
cd frontend-app

# 安装依赖（如果还没安装）
npm install

# 启动前端服务
npm run dev
```

**预期结果**：
- 前端启动在 http://localhost:5173
- 浏览器自动打开系统界面
- 界面正常显示，无报错

---

## 🔧 系统功能测试

### 测试1：基础连接测试
**目标**：确保前后端通信正常

**步骤**：
1. 打开浏览器访问 http://localhost:5173
2. 检查浏览器控制台是否有错误
3. 检查网络请求是否正常

**预期结果**：
- 页面正常加载
- 无网络请求错误
- 系统界面完整显示

### 测试2：知识库创建测试
**目标**：验证知识库创建和文档上传功能

**步骤**：
1. 点击"创建知识库"按钮
2. 输入知识库名称："机械制造专业测试"
3. 上传测试PDF文档
4. 等待处理完成

**预期结果**：
- 知识库创建成功
- 文档上传进度正常显示
- 向量化处理完成
- 知识库列表中显示新创建的知识库

### 测试3：AI对话测试
**目标**：验证RAG问答功能

**测试问题**：
```
1. "什么是数控编程？"
2. "G01指令的作用是什么？"
3. "机械加工中如何保证精度？"
```

**步骤**：
1. 选择已创建的知识库
2. 在对话框中输入测试问题
3. 发送消息并等待回复
4. 检查回答质量和知识来源

**预期结果**：
- AI回答基于上传的文档内容
- 显示知识来源和相关度
- 回答准确且专业
- 响应时间<3秒

### 测试4：可视化功能测试
**目标**：验证图表生成功能

**测试指令**：
```
1. "生成一个柱状图显示各专业的学生人数"
2. "制作饼图展示不同课程的学时分布"
3. "创建折线图显示学习进度趋势"
```

**步骤**：
1. 在对话框中输入可视化指令
2. 等待图表生成
3. 检查图表显示效果
4. 测试图表交互功能

**预期结果**：
- 图表正确生成并显示
- 图表样式美观
- 支持交互操作
- 可以导出图表

### 测试5：多模型切换测试
**目标**：验证多模型支持功能

**步骤**：
1. 进入系统设置页面
2. 查看支持的AI模型列表
3. 切换不同的AI模型
4. 测试不同模型的回答效果

**预期结果**：
- 显示多个可用模型
- 模型切换功能正常
- 不同模型回答有差异
- 切换过程无错误

---

## 📊 性能测试

### 响应时间测试
**测试项目**：
- 页面加载时间：<3秒
- 文档上传时间：根据文件大小
- AI回答响应时间：<3秒
- 图表生成时间：<2秒

### 并发测试
**测试方法**：
- 同时打开多个浏览器标签
- 并发发送多个问题
- 观察系统稳定性

**预期结果**：
- 系统保持稳定
- 所有请求正常处理
- 无明显性能下降

---

## 🎯 演示场景预演

### 场景1：知识库创建演示
**演示流程**：
1. 展示空白的知识库列表
2. 点击"创建知识库"
3. 输入"机械制造专业"
4. 上传机械制造相关PDF文档
5. 展示处理进度和完成状态

**关键点**：
- 强调支持多种文档格式
- 突出智能分块处理
- 展示向量化过程

### 场景2：专业问答演示
**演示问题序列**：
```
问题1："数控车床G01指令的具体用法是什么？"
- 展示基于文档的准确回答
- 显示知识来源页码
- 强调零幻觉特性

问题2："机械加工中如何选择切削参数？"
- 展示专业性回答
- 显示多个相关文档片段
- 强调专业适配性

问题3："数控编程的安全注意事项有哪些？"
- 展示安全相关内容
- 强调实用性和准确性
```

### 场景3：可视化演示
**演示指令序列**：
```
指令1："生成本学期各专业技能考核通过率的柱状图"
- 展示自动数据处理
- 显示美观的图表效果
- 演示交互功能

指令2："制作学生学习进度趋势的折线图"
- 展示时间序列数据可视化
- 强调教学分析价值
```

### 场景4：系统管理演示
**演示内容**：
1. 多模型配置界面
2. 系统性能监控
3. 知识库管理功能
4. 用户权限设置

---

## 🔍 问题排查指南

### 常见问题及解决方案

#### 问题1：后端启动失败
**可能原因**：
- 端口被占用
- 依赖包未安装
- 数据库连接问题

**解决方案**：
```bash
# 检查端口占用
netstat -ano | findstr :8000

# 重新安装依赖
pip install -r requirements.txt --force-reinstall

# 检查数据库状态
python simple_check.py
```

#### 问题2：前端无法连接后端
**可能原因**：
- 后端服务未启动
- 跨域配置问题
- API地址配置错误

**解决方案**：
1. 确认后端服务正常运行
2. 检查前端API配置
3. 查看浏览器控制台错误信息

#### 问题3：文档上传失败
**可能原因**：
- 文件格式不支持
- 文件大小超限
- 存储空间不足

**解决方案**：
1. 检查文件格式（支持PDF、Word、TXT等）
2. 确认文件大小<100MB
3. 检查磁盘空间

#### 问题4：AI回答质量差
**可能原因**：
- 知识库内容不足
- 文档质量问题
- 模型配置不当

**解决方案**：
1. 增加相关专业文档
2. 检查文档内容质量
3. 尝试切换AI模型

---

## 📋 演示前最终检查清单

### 系统状态检查
- [ ] 后端服务正常启动
- [ ] 前端界面正常访问
- [ ] 数据库连接正常
- [ ] AI模型接口正常

### 功能测试检查
- [ ] 知识库创建功能正常
- [ ] 文档上传处理正常
- [ ] AI问答功能正常
- [ ] 可视化生成正常
- [ ] 多模型切换正常

### 演示数据检查
- [ ] 测试知识库已创建
- [ ] 演示文档已上传
- [ ] 测试问题已验证
- [ ] 可视化数据已准备

### 备用方案检查
- [ ] 录制好的演示视频
- [ ] 静态截图素材
- [ ] 备用测试数据
- [ ] 问题解决方案

---

## 🎬 录制建议

### 录制环境
- 关闭不必要的程序
- 清理桌面和浏览器标签
- 调整屏幕分辨率为1920x1080
- 确保网络连接稳定

### 录制技巧
- 提前练习操作流程
- 控制鼠标移动速度
- 避免频繁的点击和切换
- 重点内容用箭头标注

### 音频录制
- 使用专业麦克风
- 保持环境安静
- 语速适中，吐字清晰
- 提前准备解说词

---

按照这个测试指南，你可以系统性地验证所有功能，确保演示视频录制时不会出现技术问题。有什么具体的测试环节需要我帮助你详细说明吗？
