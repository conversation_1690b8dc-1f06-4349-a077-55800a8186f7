var e;(()=>{function t(e,t,a,l,r,s,d){try{var n=e[s](d),i=n.value}catch(e){return void a(e)}n.done?t(i):Promise.resolve(i).then(l,r)}e=function(e){return function(){var a=this,l=arguments;return new Promise(function(r,s){var d=e.apply(a,l);function n(e){t(d,r,s,n,i,"next",e)}function i(e){t(d,r,s,n,i,"throw",e)}n(void 0)})}}})();import{E as t,L as a,N as l,b as r,c as s,d,f as n,g as i,k as o,l as u,m as c,u as m,v as g,w as p,x as v,y}from"./elementPlus-Di4PDIm8.js";import{bB as x,bC as f,b_ as b,by as w,c0 as k,c1 as _,c6 as h,ca as D,cd as A,ce as S,dB as V,dH as C,dN as I,dO as j,dU as U,d_ as P,dc as z,dd as O,de as T,df as L,dg as R,dj as B,dk as Y,dl as E,dy as M,ea as H,ed as G}from"./vendor-BJ-uKP15.js";const N={class:"space-y-6"},W={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},F={class:"mt-4 sm:mt-0 flex items-center space-x-3"},J={class:"card-tech p-6"},$={class:"grid grid-cols-1 lg:grid-cols-4 gap-4"},q={class:"lg:col-span-2"},K={class:"mt-4 pt-4 border-t border-gray-200 dark:border-dark-700"},Q={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},X={class:"mt-4 flex items-center space-x-3"},Z={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},ee={class:"card-tech p-4"},te={class:"flex items-center justify-between"},ae={class:"text-2xl font-bold text-gray-900 dark:text-gray-100"},le={class:"w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center"},re={class:"card-tech p-4"},se={class:"flex items-center justify-between"},de={class:"text-2xl font-bold text-green-600 dark:text-green-400"},ne={class:"w-10 h-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center"},ie={class:"card-tech p-4"},oe={class:"flex items-center justify-between"},ue={class:"text-2xl font-bold text-orange-600 dark:text-orange-400"},ce={class:"w-10 h-10 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center"},me={class:"card-tech p-4"},ge={class:"flex items-center justify-between"},pe={class:"text-2xl font-bold text-red-600 dark:text-red-400"},ve={class:"w-10 h-10 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center"},ye={class:"card-tech"},xe={class:"p-6 border-b border-gray-200 dark:border-dark-700"},fe={class:"flex items-center justify-between"},be={class:"flex items-center space-x-3"},we={class:"text-sm text-gray-500"},ke={class:"text-sm text-gray-600 dark:text-gray-400"},_e={class:"flex items-center space-x-2"},he={class:"font-medium text-gray-900 dark:text-gray-100"},De={class:"text-gray-900 dark:text-gray-100"},Ae={key:0,class:"text-sm text-gray-500 mt-1"},Se={class:"text-sm text-gray-600 dark:text-gray-400 font-mono"},Ve={key:0,class:"flex justify-center"},Ce={key:0,class:"space-y-4"},Ie={class:"grid grid-cols-2 gap-4"},je={class:"text-gray-900 dark:text-gray-100"},Ue={class:"text-gray-900 dark:text-gray-100"},Pe={class:"text-gray-900 dark:text-gray-100 font-mono"},ze={class:"text-gray-900 dark:text-gray-100"},Oe={key:0},Te={class:"bg-gray-50 dark:bg-dark-700 rounded-lg p-3"},Le={class:"text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap"};var Re=E({__name:"Logs",setup(E){const Re=U(!1),Be=U(""),Ye=U(""),Ee=U(""),Me=U(1),He=U(20),Ge=U(!1),Ne=U(null),We=U({operator:"",ipAddress:"",dateRange:null}),Fe=U({total:1247,userActivity:856,adminAction:234,systemError:157}),Je=U([{id:1,type:"user_activity",operator:"user123",ipAddress:"***********00",action:'创建了新的知识库"技术文档"',details:"知识库ID: 123\n描述: 包含各种技术文档和API参考",timestamp:new Date(Date.now()-3e5).toISOString()},{id:2,type:"admin_action",operator:"admin",ipAddress:"***********",action:"更新了系统配置",details:"修改了AI模型设置\n启用了GPT-4模型\n禁用了Claude-3模型",timestamp:new Date(Date.now()-9e5).toISOString()},{id:3,type:"system_error",operator:"system",ipAddress:"127.0.0.1",action:"文档处理失败",details:"Error: Unsupported file format\nFile: document.xyz\nSize: 2.5MB\nUser: user456",timestamp:new Date(Date.now()-18e5).toISOString()},{id:4,type:"user_activity",operator:"user456",ipAddress:"*************",action:"上传了5个文档",details:"知识库: 产品需求\n文档列表:\n- 需求文档1.pdf\n- 需求文档2.docx\n- 流程图.png\n- 原型设计.fig\n- 测试用例.xlsx",timestamp:new Date(Date.now()-27e5).toISOString()},{id:5,type:"admin_action",operator:"admin",ipAddress:"***********",action:"创建了新用户账户",details:"用户名: newuser\n邮箱: <EMAIL>\n角色: user\n存储配额: 10GB",timestamp:new Date(Date.now()-36e5).toISOString()},{id:6,type:"user_activity",operator:"user789",ipAddress:"***********50",action:"开始了AI对话会话",details:"模型: GPT-4\n知识库: 技术文档, 产品需求\n会话ID: 789",timestamp:new Date(Date.now()-45e5).toISOString()},{id:7,type:"system_error",operator:"system",ipAddress:"127.0.0.1",action:"API调用超时",details:"Endpoint: /api/ai/chat\nTimeout: 30s\nUser: user123\nModel: GPT-4",timestamp:new Date(Date.now()-54e5).toISOString()},{id:8,type:"admin_action",operator:"admin",ipAddress:"***********",action:"执行了数据库优化",details:"优化类型: 索引重建\n耗时: 45秒\n优化表: users, knowledge_bases, documents",timestamp:new Date(Date.now()-72e5).toISOString()}]),$e=z(()=>{let e=Je.value;if(Be.value){const t=Be.value.toLowerCase();e=e.filter(e=>e.operator.toLowerCase().includes(t)||e.action.toLowerCase().includes(t)||e.ipAddress.includes(t)||e.details&&e.details.toLowerCase().includes(t))}if(Ye.value&&(e=e.filter(e=>e.type===Ye.value)),Ee.value){const t=new Date;let a;switch(Ee.value){case"1h":a=new Date(t.getTime()-36e5);break;case"24h":a=new Date(t.getTime()-864e5);break;case"7d":a=new Date(t.getTime()-6048e5);break;case"30d":a=new Date(t.getTime()-2592e6);break;default:a=new Date(0)}e=e.filter(e=>new Date(e.timestamp)>=a)}if(We.value.operator&&(e=e.filter(e=>e.operator.toLowerCase().includes(We.value.operator.toLowerCase()))),We.value.ipAddress&&(e=e.filter(e=>e.ipAddress.includes(We.value.ipAddress))),We.value.dateRange){const[t,a]=We.value.dateRange;e=e.filter(e=>{const l=new Date(e.timestamp);return l>=new Date(t)&&l<=new Date(a)})}return e.sort((e,t)=>new Date(t.timestamp).getTime()-new Date(e.timestamp).getTime())}),qe=z(()=>{const e=(Me.value-1)*He.value;return $e.value.slice(e,e+He.value)}),Ke=e=>new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}),Qe=e=>{switch(e){case"user_activity":return"primary";case"admin_action":return"warning";case"system_error":return"danger";default:return"info"}},Xe=e=>{switch(e){case"user_activity":return"用户活动";case"admin_action":return"管理操作";case"system_error":return"系统错误";default:return"未知"}},Ze={User:D,Setting:_,Warning:S,Document:x},et=e=>{switch(e){case"user_activity":return Ze.User;case"admin_action":return Ze.Setting;case"system_error":return Ze.Warning;default:return Ze.Document}},tt=e=>{switch(e){case"user_activity":return"bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400";case"admin_action":return"bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-400";case"system_error":return"bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400";default:return"bg-gray-100 dark:bg-gray-900 text-gray-600 dark:text-gray-400"}},at=()=>{Me.value=1},lt=()=>{Me.value=1},rt=e=>{Me.value=e},st=()=>{Me.value=1},dt=()=>{Me.value=1},nt=()=>{We.value={operator:"",ipAddress:"",dateRange:null},Me.value=1},it=(ot=e(function*(){Re.value=!0;try{yield new Promise(e=>setTimeout(e,1e3)),s.success("日志已刷新")}catch(e){s.error("刷新失败")}finally{Re.value=!1}}),function(){return ot.apply(this,arguments)});var ot;const ut=(ct=e(function*(){try{const e={timestamp:(new Date).toISOString(),filters:{search:Be.value,type:Ye.value,time:Ee.value,advanced:We.value},logs:$e.value},t=new Blob([JSON.stringify(e,null,2)],{type:"application/json"}),a=URL.createObjectURL(t),l=document.createElement("a");l.href=a,l.download=`system-logs-${(new Date).toISOString().split("T")[0]}.json`,l.click(),URL.revokeObjectURL(a),s.success("日志导出成功")}catch(e){s.error("导出失败")}}),function(){return ct.apply(this,arguments)});var ct;const mt=(gt=e(function*(){try{yield r.confirm("确定要清空所有日志吗？此操作不可恢复。","确认清空",{confirmButtonText:"清空",cancelButtonText:"取消",type:"error",confirmButtonClass:"el-button--danger"}),Je.value=[],Fe.value={total:0,userActivity:0,adminAction:0,systemError:0},s.success("日志已清空")}catch(e){}}),function(){return gt.apply(this,arguments)});var gt;return M(()=>{}),(e,r)=>{const s=l,_=t,U=a,z=u,E=c,M=g,Je=v,Ze=p,ot=i,ct=y,gt=n,pt=o,vt=m,yt=d;return V(),R("div",N,[O("div",W,[r[12]||(r[12]=O("div",null,[O("h1",{class:"text-2xl font-bold text-gray-900 dark:text-gray-100"}," 系统日志 "),O("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," 查看系统操作日志和错误记录，监控系统运行状态 ")],-1)),O("div",F,[Y(_,{onClick:it,loading:Re.value},{default:I(()=>[Y(s,{class:"mr-2"},{default:I(()=>[Y(P(b))]),_:1}),r[9]||(r[9]=B(" 刷新 ",-1))]),_:1,__:[9]},8,["loading"]),Y(_,{onClick:ut},{default:I(()=>[Y(s,{class:"mr-2"},{default:I(()=>[Y(P(f))]),_:1}),r[10]||(r[10]=B(" 导出日志 ",-1))]),_:1,__:[10]}),Y(_,{onClick:mt,type:"danger"},{default:I(()=>[Y(s,{class:"mr-2"},{default:I(()=>[Y(P(w))]),_:1}),r[11]||(r[11]=B(" 清空日志 ",-1))]),_:1,__:[11]})])]),O("div",J,[O("div",$,[O("div",q,[Y(U,{modelValue:Be.value,"onUpdate:modelValue":r[0]||(r[0]=e=>Be.value=e),placeholder:"搜索操作者、IP地址、操作内容...",clearable:"",onInput:at},{prefix:I(()=>[Y(s,null,{default:I(()=>[Y(P(k))]),_:1})]),_:1},8,["modelValue"])]),O("div",null,[Y(E,{modelValue:Ye.value,"onUpdate:modelValue":r[1]||(r[1]=e=>Ye.value=e),placeholder:"日志类型",onChange:lt},{default:I(()=>[Y(z,{label:"全部类型",value:""}),Y(z,{label:"用户活动",value:"user_activity"}),Y(z,{label:"管理操作",value:"admin_action"}),Y(z,{label:"系统错误",value:"system_error"})]),_:1},8,["modelValue"])]),O("div",null,[Y(E,{modelValue:Ee.value,"onUpdate:modelValue":r[2]||(r[2]=e=>Ee.value=e),placeholder:"时间范围",onChange:lt},{default:I(()=>[Y(z,{label:"全部时间",value:""}),Y(z,{label:"最近1小时",value:"1h"}),Y(z,{label:"最近24小时",value:"24h"}),Y(z,{label:"最近7天",value:"7d"}),Y(z,{label:"最近30天",value:"30d"})]),_:1},8,["modelValue"])])]),O("div",K,[Y(Ze,null,{default:I(()=>[Y(Je,{title:"高级筛选",name:"advanced"},{default:I(()=>[O("div",Q,[O("div",null,[r[13]||(r[13]=O("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," 操作者 ",-1)),Y(U,{modelValue:We.value.operator,"onUpdate:modelValue":r[3]||(r[3]=e=>We.value.operator=e),placeholder:"输入操作者用户名",clearable:""},null,8,["modelValue"])]),O("div",null,[r[14]||(r[14]=O("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," IP地址 ",-1)),Y(U,{modelValue:We.value.ipAddress,"onUpdate:modelValue":r[4]||(r[4]=e=>We.value.ipAddress=e),placeholder:"输入IP地址",clearable:""},null,8,["modelValue"])]),O("div",null,[r[15]||(r[15]=O("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," 自定义时间范围 ",-1)),Y(M,{modelValue:We.value.dateRange,"onUpdate:modelValue":r[5]||(r[5]=e=>We.value.dateRange=e),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])])]),O("div",X,[Y(_,{type:"primary",onClick:dt},{default:I(()=>r[16]||(r[16]=[B(" 应用筛选 ",-1)])),_:1,__:[16]}),Y(_,{onClick:nt},{default:I(()=>r[17]||(r[17]=[B(" 重置 ",-1)])),_:1,__:[17]})])]),_:1})]),_:1})])]),O("div",Z,[O("div",ee,[O("div",te,[O("div",null,[r[18]||(r[18]=O("p",{class:"text-sm text-gray-600 dark:text-gray-400"},"总日志数",-1)),O("p",ae,G(Fe.value.total),1)]),O("div",le,[Y(s,{size:20,class:"text-blue-600 dark:text-blue-400"},{default:I(()=>[Y(P(x))]),_:1})])])]),O("div",re,[O("div",se,[O("div",null,[r[19]||(r[19]=O("p",{class:"text-sm text-gray-600 dark:text-gray-400"},"用户活动",-1)),O("p",de,G(Fe.value.userActivity),1)]),O("div",ne,[Y(s,{size:20,class:"text-green-600 dark:text-green-400"},{default:I(()=>[Y(P(D))]),_:1})])])]),O("div",ie,[O("div",oe,[O("div",null,[r[20]||(r[20]=O("p",{class:"text-sm text-gray-600 dark:text-gray-400"},"管理操作",-1)),O("p",ue,G(Fe.value.adminAction),1)]),O("div",ce,[Y(s,{size:20,class:"text-orange-600 dark:text-orange-400"},{default:I(()=>[Y(P(h))]),_:1})])])]),O("div",me,[O("div",ge,[O("div",null,[r[21]||(r[21]=O("p",{class:"text-sm text-gray-600 dark:text-gray-400"},"系统错误",-1)),O("p",pe,G(Fe.value.systemError),1)]),O("div",ve,[Y(s,{size:20,class:"text-red-600 dark:text-red-400"},{default:I(()=>[Y(P(S))]),_:1})])])])]),j((V(),R("div",ye,[O("div",xe,[O("div",fe,[r[22]||(r[22]=O("h3",{class:"text-lg font-medium text-gray-900 dark:text-gray-100"}," 日志记录 ",-1)),O("div",be,[O("span",we," 显示 "+G(qe.value.length)+" / "+G($e.value.length)+" 条记录 ",1),Y(E,{modelValue:He.value,"onUpdate:modelValue":r[6]||(r[6]=e=>He.value=e),size:"small",style:{width:"100px"},onChange:st},{default:I(()=>[Y(z,{label:"20",value:20}),Y(z,{label:"50",value:50}),Y(z,{label:"100",value:100})]),_:1},8,["modelValue"])])])]),Y(gt,{data:qe.value,style:{width:"100%"},"row-class-name":"log-row"},{default:I(()=>[Y(ot,{prop:"timestamp",label:"时间",width:"180"},{default:I(({row:e})=>[O("span",ke,G(Ke(e.timestamp)),1)]),_:1}),Y(ot,{prop:"type",label:"类型",width:"120",align:"center"},{default:I(({row:e})=>[Y(ct,{type:Qe(e.type),size:"small"},{default:I(()=>[B(G(Xe(e.type)),1)]),_:2},1032,["type"])]),_:1}),Y(ot,{prop:"operator",label:"操作者",width:"150"},{default:I(({row:e})=>[O("div",_e,[O("div",{class:H(["w-6 h-6 rounded-full flex items-center justify-center",tt(e.type)])},[Y(s,{size:12},{default:I(()=>[(V(),T(C(et(e.type))))]),_:2},1024)],2),O("span",he,G(e.operator),1)])]),_:1}),Y(ot,{prop:"action",label:"操作内容","min-width":"300"},{default:I(({row:e})=>[O("div",null,[O("p",De,G(e.action),1),e.details?(V(),R("p",Ae,G(e.details),1)):L("",!0)])]),_:1}),Y(ot,{prop:"ipAddress",label:"IP地址",width:"150"},{default:I(({row:e})=>[O("span",Se,G(e.ipAddress),1)]),_:1}),Y(ot,{label:"操作",width:"100",align:"center"},{default:I(({row:e})=>[Y(_,{size:"small",onClick:t=>(Ne.value=e,void(Ge.value=!0))},{default:I(()=>[Y(s,null,{default:I(()=>[Y(P(A))]),_:1})]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])])),[[yt,Re.value]]),$e.value.length>He.value?(V(),R("div",Ve,[Y(pt,{"current-page":Me.value,"onUpdate:currentPage":r[7]||(r[7]=e=>Me.value=e),"page-size":He.value,total:$e.value.length,layout:"prev, pager, next, jumper, total",onCurrentChange:rt},null,8,["current-page","page-size","total"])])):L("",!0),Y(vt,{modelValue:Ge.value,"onUpdate:modelValue":r[8]||(r[8]=e=>Ge.value=e),title:"日志详情",width:"600px"},{default:I(()=>[Ne.value?(V(),R("div",Ce,[O("div",Ie,[O("div",null,[r[23]||(r[23]=O("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," 时间 ",-1)),O("p",je,G(Ke(Ne.value.timestamp)),1)]),O("div",null,[r[24]||(r[24]=O("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," 类型 ",-1)),Y(ct,{type:Qe(Ne.value.type),size:"small"},{default:I(()=>[B(G(Xe(Ne.value.type)),1)]),_:1},8,["type"])]),O("div",null,[r[25]||(r[25]=O("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," 操作者 ",-1)),O("p",Ue,G(Ne.value.operator),1)]),O("div",null,[r[26]||(r[26]=O("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," IP地址 ",-1)),O("p",Pe,G(Ne.value.ipAddress),1)])]),O("div",null,[r[27]||(r[27]=O("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," 操作内容 ",-1)),O("p",ze,G(Ne.value.action),1)]),Ne.value.details?(V(),R("div",Oe,[r[28]||(r[28]=O("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," 详细信息 ",-1)),O("div",Te,[O("pre",Le,G(Ne.value.details),1)])])):L("",!0)])):L("",!0)]),_:1},8,["modelValue"])])}}});export{Re as default};