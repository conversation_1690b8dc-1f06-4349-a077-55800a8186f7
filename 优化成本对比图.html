<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优化成本对比图</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 30px;
        }
        
        .title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            letter-spacing: -0.5px;
        }
        
        .subtitle {
            font-size: 16px;
            opacity: 0.9;
            font-weight: 400;
        }
        
        .content {
            padding: 40px;
            background: white;
        }
        
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .chart-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
            border: 2px solid #f1f5f9;
            transition: all 0.3s ease;
        }
        
        .chart-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 12px 30px rgba(102, 126, 234, 0.15);
        }
        
        .chart-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
        }
        
        .savings-highlight {
            background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
            text-align: center;
            border-left: 4px solid #10b981;
        }
        
        .savings-text {
            font-size: 14px;
            color: #166534;
            font-weight: 600;
        }
        
        .savings-number {
            font-size: 20px;
            color: #059669;
            font-weight: 700;
        }
        
        .summary-section {
            background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
            padding: 30px;
            border-radius: 15px;
            margin-top: 40px;
        }
        
        .summary-title {
            font-size: 24px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .benefit-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        .benefit-icon {
            font-size: 32px;
            margin-bottom: 10px;
        }
        
        .benefit-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
        }
        
        .benefit-desc {
            font-size: 13px;
            color: #64748b;
            line-height: 1.5;
        }
        
        .comparison-table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            margin-top: 30px;
        }
        
        .table-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 18px;
            font-weight: 600;
        }
        
        .table-content {
            padding: 0;
        }
        
        .table-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid #f1f5f9;
        }
        
        .table-row:last-child {
            border-bottom: none;
        }
        
        .table-row:nth-child(even) {
            background: #f8fafc;
        }
        
        .table-cell {
            font-size: 14px;
            color: #1e293b;
        }
        
        .table-cell.metric {
            font-weight: 600;
        }
        
        .table-cell.traditional {
            color: #dc2626;
            font-weight: 500;
        }
        
        .table-cell.rag {
            color: #059669;
            font-weight: 500;
        }
        
        .table-cell.savings {
            color: #7c3aed;
            font-weight: 600;
        }
        
        @media (max-width: 768px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }
            
            .table-row {
                grid-template-columns: 1fr;
                gap: 10px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">📊 传统AI vs RAG 成本效益对比</div>
            <div class="subtitle">Cost-Effectiveness Comparison: Traditional AI vs RAG</div>
        </div>
        
        <div class="content">
            <!-- 分组图表展示 -->
            <div class="charts-grid">
                <!-- Token消耗对比 -->
                <div class="chart-card">
                    <div class="chart-title">💬 Token消耗对比</div>
                    <div class="chart-container">
                        <canvas id="tokenChart"></canvas>
                    </div>
                    <div class="savings-highlight">
                        <div class="savings-text">Token消耗降低</div>
                        <div class="savings-number">83.7%</div>
                    </div>
                </div>
                
                <!-- 单次成本对比 -->
                <div class="chart-card">
                    <div class="chart-title">💰 单次对话成本对比</div>
                    <div class="chart-container">
                        <canvas id="costChart"></canvas>
                    </div>
                    <div class="savings-highlight">
                        <div class="savings-text">单次成本降低</div>
                        <div class="savings-number">83.7%</div>
                    </div>
                </div>
                
                <!-- 年度成本对比 -->
                <div class="chart-card">
                    <div class="chart-title">📈 年度运营成本对比</div>
                    <div class="chart-container">
                        <canvas id="annualChart"></canvas>
                    </div>
                    <div class="savings-highlight">
                        <div class="savings-text">年度节省</div>
                        <div class="savings-number">$244.55</div>
                    </div>
                </div>
            </div>
            
            <!-- 详细对比表格 -->
            <div class="comparison-table">
                <div class="table-header">📋 详细成本对比表</div>
                <div class="table-content">
                    <div class="table-row">
                        <div class="table-cell metric">对比指标</div>
                        <div class="table-cell metric">传统AI对话</div>
                        <div class="table-cell metric">RAG智能检索</div>
                        <div class="table-cell metric">节省幅度</div>
                    </div>
                    <div class="table-row">
                        <div class="table-cell">Token消耗量</div>
                        <div class="table-cell traditional">3000-5000个</div>
                        <div class="table-cell rag">500-800个</div>
                        <div class="table-cell savings">↓ 83.7%</div>
                    </div>
                    <div class="table-row">
                        <div class="table-cell">单次对话成本</div>
                        <div class="table-cell traditional">$0.008</div>
                        <div class="table-cell rag">$0.0013</div>
                        <div class="table-cell savings">↓ 83.7%</div>
                    </div>
                    <div class="table-row">
                        <div class="table-cell">年度运营成本</div>
                        <div class="table-cell traditional">$292</div>
                        <div class="table-cell rag">$47.45</div>
                        <div class="table-cell savings">↓ $244.55</div>
                    </div>
                    <div class="table-row">
                        <div class="table-cell">部署模式</div>
                        <div class="table-cell traditional">云服务订阅</div>
                        <div class="table-cell rag">私有化部署</div>
                        <div class="table-cell savings">一次性投入</div>
                    </div>
                </div>
            </div>

            <!-- 成本效益总结 -->
            <div class="summary-section">
                <div class="summary-title">🎯 RAG技术成本效益优势</div>
                <div class="benefits-grid">
                    <div class="benefit-card">
                        <div class="benefit-icon">💰</div>
                        <div class="benefit-title">成本大幅降低</div>
                        <div class="benefit-desc">Token消耗降低80%以上，单次对话成本仅为传统方式的20%</div>
                    </div>

                    <div class="benefit-card">
                        <div class="benefit-icon">🏠</div>
                        <div class="benefit-title">私有化部署</div>
                        <div class="benefit-desc">一次性投入，避免持续云服务费用，数据完全自主可控</div>
                    </div>

                    <div class="benefit-card">
                        <div class="benefit-icon">🔒</div>
                        <div class="benefit-title">数据安全</div>
                        <div class="benefit-desc">本地化处理，确保教育机构数据安全和隐私保护</div>
                    </div>

                    <div class="benefit-card">
                        <div class="benefit-icon">⚡</div>
                        <div class="benefit-title">精准检索</div>
                        <div class="benefit-desc">语义检索技术，只提取最相关知识片段，提高效率</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 通用图表配置
        const commonOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: 'white',
                    bodyColor: 'white',
                    borderColor: '#667eea',
                    borderWidth: 1,
                    cornerRadius: 8
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)',
                        drawBorder: false
                    },
                    ticks: {
                        font: { size: 11 },
                        color: '#64748b'
                    }
                },
                x: {
                    grid: { display: false },
                    ticks: {
                        font: { size: 12, weight: '500' },
                        color: '#1e293b'
                    }
                }
            },
            animation: {
                duration: 2000,
                easing: 'easeInOutQuart'
            }
        };

        // Token消耗对比图
        const tokenCtx = document.getElementById('tokenChart').getContext('2d');
        new Chart(tokenCtx, {
            type: 'bar',
            data: {
                labels: ['传统AI对话', 'RAG智能检索'],
                datasets: [{
                    data: [4000, 650],
                    backgroundColor: [
                        'rgba(239, 68, 68, 0.8)',
                        'rgba(16, 185, 129, 0.8)'
                    ],
                    borderColor: [
                        'rgba(239, 68, 68, 1)',
                        'rgba(16, 185, 129, 1)'
                    ],
                    borderWidth: 2,
                    borderRadius: 8,
                    borderSkipped: false
                }]
            },
            options: {
                ...commonOptions,
                scales: {
                    ...commonOptions.scales,
                    y: {
                        ...commonOptions.scales.y,
                        max: 5000,
                        ticks: {
                            ...commonOptions.scales.y.ticks,
                            callback: function(value) {
                                return value + ' Token';
                            }
                        },
                        title: {
                            display: true,
                            text: 'Token数量',
                            font: { size: 12, weight: 'bold' },
                            color: '#1e293b'
                        }
                    }
                },
                plugins: {
                    ...commonOptions.plugins,
                    tooltip: {
                        ...commonOptions.plugins.tooltip,
                        callbacks: {
                            label: function(context) {
                                const value = context.parsed.y;
                                const savings = context.dataIndex === 1 ?
                                    '（节省 ' + (((4000 - 650) / 4000) * 100).toFixed(1) + '%）' : '';
                                return context.dataset.label + ': ' + value + ' Token ' + savings;
                            }
                        }
                    }
                }
            }
        });

        // 单次成本对比图
        const costCtx = document.getElementById('costChart').getContext('2d');
        new Chart(costCtx, {
            type: 'bar',
            data: {
                labels: ['传统AI对话', 'RAG智能检索'],
                datasets: [{
                    data: [0.008, 0.0013],
                    backgroundColor: [
                        'rgba(239, 68, 68, 0.8)',
                        'rgba(16, 185, 129, 0.8)'
                    ],
                    borderColor: [
                        'rgba(239, 68, 68, 1)',
                        'rgba(16, 185, 129, 1)'
                    ],
                    borderWidth: 2,
                    borderRadius: 8,
                    borderSkipped: false
                }]
            },
            options: {
                ...commonOptions,
                scales: {
                    ...commonOptions.scales,
                    y: {
                        ...commonOptions.scales.y,
                        max: 0.01,
                        ticks: {
                            ...commonOptions.scales.y.ticks,
                            callback: function(value) {
                                return '$' + value.toFixed(4);
                            }
                        },
                        title: {
                            display: true,
                            text: '成本（美元）',
                            font: { size: 12, weight: 'bold' },
                            color: '#1e293b'
                        }
                    }
                },
                plugins: {
                    ...commonOptions.plugins,
                    tooltip: {
                        ...commonOptions.plugins.tooltip,
                        callbacks: {
                            label: function(context) {
                                const value = context.parsed.y;
                                const savings = context.dataIndex === 1 ?
                                    '（节省 ' + (((0.008 - 0.0013) / 0.008) * 100).toFixed(1) + '%）' : '';
                                return context.dataset.label + ': $' + value.toFixed(4) + ' ' + savings;
                            }
                        }
                    }
                }
            }
        });

        // 年度成本对比图
        const annualCtx = document.getElementById('annualChart').getContext('2d');
        new Chart(annualCtx, {
            type: 'bar',
            data: {
                labels: ['传统AI方案', 'RAG方案'],
                datasets: [{
                    data: [292, 47.45],
                    backgroundColor: [
                        'rgba(239, 68, 68, 0.8)',
                        'rgba(16, 185, 129, 0.8)'
                    ],
                    borderColor: [
                        'rgba(239, 68, 68, 1)',
                        'rgba(16, 185, 129, 1)'
                    ],
                    borderWidth: 2,
                    borderRadius: 8,
                    borderSkipped: false
                }]
            },
            options: {
                ...commonOptions,
                scales: {
                    ...commonOptions.scales,
                    y: {
                        ...commonOptions.scales.y,
                        max: 350,
                        ticks: {
                            ...commonOptions.scales.y.ticks,
                            callback: function(value) {
                                return '$' + value;
                            }
                        },
                        title: {
                            display: true,
                            text: '年度成本（美元）',
                            font: { size: 12, weight: 'bold' },
                            color: '#1e293b'
                        }
                    }
                },
                plugins: {
                    ...commonOptions.plugins,
                    tooltip: {
                        ...commonOptions.plugins.tooltip,
                        callbacks: {
                            label: function(context) {
                                const value = context.parsed.y;
                                const savings = context.dataIndex === 1 ?
                                    '（节省 $' + (292 - 47.45).toFixed(2) + '）' : '';
                                return context.dataset.label + ': $' + value.toFixed(2) + ' ' + savings;
                            }
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
