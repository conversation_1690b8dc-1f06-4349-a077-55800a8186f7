<template>
  <!-- 页面加载状态 -->
  <div v-if="pageLoading" class="h-full flex items-center justify-center">
    <div class="text-center">
      <el-icon :size="48" class="text-blue-500 animate-spin mb-4">
        <Loading />
      </el-icon>
      <p class="text-gray-600 dark:text-gray-400">正在加载仪表盘数据...</p>
    </div>
  </div>

  <!-- 主要内容 -->
  <div v-else class="space-y-6">
    <!-- 页面头部 -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">
        管理仪表盘
      </h1>
      <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
        系统概览和关键指标
      </p>
    </div>

    <!-- 概览统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- 总用户数 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">总用户数</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-gray-100">
              {{ stats.total_users }}
            </p>
          </div>
          <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
            <el-icon :size="24" class="text-blue-600 dark:text-blue-400">
              <User />
            </el-icon>
          </div>
        </div>
        <div class="mt-4">
          <div class="flex items-center text-sm">
            <span class="text-green-600 dark:text-green-400">今日活跃: {{ stats.active_users_today }}</span>
          </div>
        </div>
      </div>

      <!-- 知识库总数 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">知识库总数</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-gray-100">
              {{ stats.total_knowledge_bases }}
            </p>
          </div>
          <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
            <el-icon :size="24" class="text-green-600 dark:text-green-400">
              <Collection />
            </el-icon>
          </div>
        </div>
      </div>

      <!-- 文档总数 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">文档总数</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-gray-100">
              {{ stats.total_documents }}
            </p>
          </div>
          <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
            <el-icon :size="24" class="text-purple-600 dark:text-purple-400">
              <Document />
            </el-icon>
          </div>
        </div>
      </div>

      <!-- 存储使用量 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">存储使用量</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-gray-100">
              {{ formatStorage(stats.total_storage_mb) }}
            </p>
          </div>
          <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
            <el-icon :size="24" class="text-orange-600 dark:text-orange-400">
              <FolderOpened />
            </el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据可视化区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
      <!-- 用户增长趋势 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">用户增长趋势</h3>
          <div class="text-right">
            <div class="text-sm text-gray-500 dark:text-gray-400">总用户数</div>
            <div class="text-xl font-bold text-blue-600 dark:text-blue-400">
              {{ stats.total_users }}
            </div>
          </div>
        </div>
        <LineChart
          :data="userGrowthData"
          :x-axis-data="last30Days"
          height="280px"
          :theme="isDark ? 'dark' : 'light'"
          y-axis-name="用户数"
        />
        <div class="mt-4 grid grid-cols-2 gap-4 text-center">
          <div class="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div class="text-xs text-blue-600 dark:text-blue-400 font-medium">今日活跃</div>
            <div class="text-lg font-bold text-blue-700 dark:text-blue-300">
              {{ stats.active_users_today }}
            </div>
          </div>
          <div class="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <div class="text-xs text-green-600 dark:text-green-400 font-medium">月增长率</div>
            <div class="text-lg font-bold text-green-700 dark:text-green-300">+12%</div>
          </div>
        </div>
      </div>

      <!-- 系统资源分布 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">系统资源分布</h3>
          <div class="text-right">
            <div class="text-sm text-gray-500 dark:text-gray-400">总存储</div>
            <div class="text-lg font-bold text-gray-900 dark:text-gray-100">
              {{ formatStorage(stats.total_storage_mb) }}
            </div>
          </div>
        </div>
        <PieChart
          :data="systemResourceData"
          height="280px"
          :theme="isDark ? 'dark' : 'light'"
          :radius="['40%', '70%']"
        />
        <div class="mt-4 grid grid-cols-2 gap-4 text-center">
          <div class="p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
            <div class="text-xs text-orange-600 dark:text-orange-400 font-medium">知识库</div>
            <div class="text-lg font-bold text-orange-700 dark:text-orange-300">
              {{ stats.total_knowledge_bases }}
            </div>
          </div>
          <div class="p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
            <div class="text-xs text-purple-600 dark:text-purple-400 font-medium">文档</div>
            <div class="text-lg font-bold text-purple-700 dark:text-purple-300">
              {{ stats.total_documents }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 活动统计图表 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">系统活动统计</h3>
      <BarChart
        :data="activityStatsData"
        :x-axis-data="['知识库', '文档', '聊天会话', '消息']"
        height="300px"
        :theme="isDark ? 'dark' : 'light'"
        y-axis-name="数量"
      />
    </div>

    <!-- 聊天统计 -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- 聊天会话 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">聊天统计</h3>
          <el-icon class="text-blue-500">
            <ChatDotRound />
          </el-icon>
        </div>
        <div class="space-y-4">
          <div class="flex justify-between items-center">
            <span class="text-gray-600 dark:text-gray-400">总会话数</span>
            <span class="text-xl font-bold text-gray-900 dark:text-gray-100">
              {{ stats.total_chat_sessions }}
            </span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-gray-600 dark:text-gray-400">总消息数</span>
            <span class="text-xl font-bold text-gray-900 dark:text-gray-100">
              {{ stats.total_messages }}
            </span>
          </div>
        </div>
      </div>

      <!-- 快速操作 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">快速操作</h3>
          <el-icon class="text-green-500">
            <Operation />
          </el-icon>
        </div>
        <div class="space-y-3">
          <el-button 
            type="primary" 
            class="w-full" 
            @click="$router.push('/admin/users')"
          >
            <el-icon class="mr-2"><User /></el-icon>
            管理用户
          </el-button>
          <el-button 
            type="success" 
            class="w-full" 
            @click="$router.push('/admin/ai-models')"
          >
            <el-icon class="mr-2"><Setting /></el-icon>
            AI模型配置
          </el-button>
          <el-button 
            type="warning" 
            class="w-full" 
            @click="$router.push('/admin/operation-logs')"
          >
            <el-icon class="mr-2"><Document /></el-icon>
            查看日志
          </el-button>
        </div>
      </div>
    </div>

    <!-- 刷新按钮 -->
    <div class="flex justify-center">
      <el-button @click="loadStats" :loading="loading">
        <el-icon class="mr-2"><Refresh /></el-icon>
        刷新数据
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import PieChart from '@/components/charts/PieChart.vue'
import LineChart from '@/components/charts/LineChart.vue'
import BarChart from '@/components/charts/BarChart.vue'
import {
  User,
  Collection,
  Document,
  FolderOpened,
  ChatDotRound,
  Operation,
  Setting,
  Refresh,
  Loading
} from '@element-plus/icons-vue'
import { adminAPI, type SystemStats } from '@/api/admin'

// 状态管理
const pageLoading = ref(true)
const loading = ref(false)

// 统计数据
const stats = ref<SystemStats>({
  total_users: 0,
  total_knowledge_bases: 0,
  total_documents: 0,
  total_chat_sessions: 0,
  total_messages: 0,
  active_users_today: 0,
  total_storage_mb: 0
})

// 工具函数
const formatStorage = (mb: number) => {
  if (mb < 1024) {
    return `${mb.toFixed(1)} MB`
  } else if (mb < 1024 * 1024) {
    return `${(mb / 1024).toFixed(1)} GB`
  } else {
    return `${(mb / (1024 * 1024)).toFixed(1)} TB`
  }
}

// 主题检测
const isDark = computed(() => {
  return document.documentElement.classList.contains('dark')
})

// 最近30天数据
const last30Days = computed(() => {
  const days = []
  for (let i = 29; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    days.push(date.toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' }))
  }
  return days
})

// 用户增长趋势数据
const userGrowthData = computed(() => {
  // 基于当前用户数生成简单的增长趋势
  const currentUsers = stats.value.total_users || 0
  const baseUsers = Math.max(currentUsers - 29, 1)
  const growthData = Array.from({ length: 30 }, (_, i) => {
    // 简单的线性增长模拟
    return Math.floor(baseUsers + (i * (currentUsers - baseUsers) / 29))
  })

  return [
    { name: '累计用户', data: growthData, color: '#3b82f6' }
  ]
})

// 系统资源分布数据
const systemResourceData = computed(() => {
  // 使用真实的统计数据
  const kbs = stats.value.total_knowledge_bases || 0
  const docs = stats.value.total_documents || 0
  const sessions = stats.value.total_chat_sessions || 0
  const messages = Math.floor((stats.value.total_messages || 0) / 10)

  return [
    { name: '知识库', value: kbs, color: '#10b981' },
    { name: '文档', value: docs, color: '#f59e0b' },
    { name: '聊天会话', value: sessions, color: '#8b5cf6' },
    { name: '消息(×10)', value: messages, color: '#ef4444' }
  ]
})

// 活动统计数据
const activityStatsData = computed(() => {
  return [
    {
      name: '当前数量',
      data: [
        stats.value.total_knowledge_bases,
        stats.value.total_documents,
        stats.value.total_chat_sessions,
        Math.floor(stats.value.total_messages / 100)
      ],
      color: '#3b82f6'
    }
  ]
})

// 加载统计数据
const loadStats = async () => {
  try {
    loading.value = true
    stats.value = await adminAPI.getSystemStats()
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
  } finally {
    loading.value = false
  }
}

// 页面初始化
onMounted(async () => {
  try {
    await loadStats()
  } finally {
    pageLoading.value = false
  }
})
</script>
