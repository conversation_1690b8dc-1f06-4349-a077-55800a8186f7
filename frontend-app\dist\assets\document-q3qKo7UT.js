import{d as t,e}from"./index-Byt5TjPh.js";const o={getList:(e,o)=>t.get(`/documents/kb/${e}`,{params:o}),upload:(t,o,a)=>{const d=new FormData;return d.append("file",o),e.post(`/documents/upload/${t}`,d,{headers:{"Content-Type":"multipart/form-data"},onUploadProgress:t=>{if(a&&t.total){const e=Math.round(100*t.loaded/t.total);a(e)}}})},batchUpload:(t,o,a)=>{const d=new FormData;return o.forEach(t=>{d.append("files",t)}),e.post(`/documents/batch-upload/${t}`,d,{headers:{"Content-Type":"multipart/form-data"},onUploadProgress:t=>{if(a&&t.total){const e=Math.round(100*t.loaded/t.total);a(e)}}})},getDetail:e=>t.get(`/documents/${e}`),delete:e=>t.delete(`/documents/${e}`),batchDelete:e=>t.delete("/documents/batch-delete",{data:e}),vectorize:e=>t.post(`/documents/${e}/vectorize`),retry:e=>t.post(`/documents/${e}/retry`),download:e=>t.get(`/documents/${e}/download`,{responseType:"blob"})};export{o as b};