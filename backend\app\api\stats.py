"""
统计数据API
"""
from fastapi import APIRouter, Depends
from sqlmodel import Session, select, func
from app.core.database import SessionDep
from app.core.auth import get_current_active_user
from app.models.user import User
from app.models.knowledge_base import KnowledgeBase, Document
from app.models.ai import ChatSession, ChatMessage
from pydantic import BaseModel
from typing import List, Dict
from datetime import datetime, timedelta

router = APIRouter()


class DashboardStats(BaseModel):
    """仪表盘统计数据"""
    knowledge_bases: int
    documents: int
    chat_sessions: int
    storage_used: float  # GB
    storage_total: float  # GB
    storage_percent: int


class DetailedStats(BaseModel):
    """详细统计数据"""
    knowledge_bases: int
    documents: int
    chat_sessions: int
    total_messages: int
    storage_used_bytes: int
    storage_used_mb: float
    storage_used_gb: float
    storage_total_gb: float
    storage_percent: int
    recent_activity: Dict[str, int]  # 最近7天的活动统计


class ActivityStats(BaseModel):
    """活动统计"""
    date: str
    knowledge_bases_created: int
    documents_uploaded: int
    chat_sessions_created: int
    messages_sent: int


@router.get("/dashboard", response_model=DashboardStats)
async def get_dashboard_stats(
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep
):
    """获取仪表盘统计数据"""
    
    # 统计用户的知识库数量
    kb_count = session.exec(
        select(func.count(KnowledgeBase.id)).where(
            KnowledgeBase.owner_id == current_user.id
        )
    ).first() or 0
    
    # 统计用户的文档数量
    doc_count = session.exec(
        select(func.count(Document.id)).join(KnowledgeBase).where(
            KnowledgeBase.owner_id == current_user.id
        )
    ).first() or 0
    
    # 统计用户的聊天会话数量
    chat_count = session.exec(
        select(func.count(ChatSession.id)).where(
            ChatSession.user_id == current_user.id
        )
    ).first() or 0
    
    # 获取用户配额
    from app.models.user import UserQuota
    quota_statement = select(UserQuota).where(UserQuota.user_id == current_user.id)
    user_quota = session.exec(quota_statement).first()

    # 如果没有配额记录，创建默认配额
    if not user_quota:
        user_quota = UserQuota(
            user_id=current_user.id,
            max_kbs=5,
            max_docs_per_kb=100,
            max_storage_mb=1024
        )
        session.add(user_quota)
        session.commit()
        session.refresh(user_quota)

    # 统计用户的存储使用量
    storage_result = session.exec(
        select(func.sum(Document.file_size)).join(KnowledgeBase).where(
            KnowledgeBase.owner_id == current_user.id
        )
    ).first()

    # 转换为GB
    storage_used_gb = (storage_result or 0) / (1024 * 1024 * 1024)
    storage_total_gb = user_quota.max_storage_mb / 1024  # 使用用户实际配额
    storage_percent = min(int((storage_used_gb / storage_total_gb) * 100), 100) if storage_total_gb > 0 else 0
    
    return DashboardStats(
        knowledge_bases=kb_count,
        documents=doc_count,
        chat_sessions=chat_count,
        storage_used=round(storage_used_gb, 2),
        storage_total=storage_total_gb,
        storage_percent=storage_percent
    )


@router.get("/detailed", response_model=DetailedStats)
async def get_detailed_stats(
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep
):
    """获取详细统计数据"""

    # 统计用户的知识库数量
    kb_count = session.exec(
        select(func.count(KnowledgeBase.id)).where(
            KnowledgeBase.owner_id == current_user.id
        )
    ).first() or 0

    # 统计用户的文档数量
    doc_count = session.exec(
        select(func.count(Document.id)).join(KnowledgeBase).where(
            KnowledgeBase.owner_id == current_user.id
        )
    ).first() or 0

    # 统计用户的聊天会话数量
    chat_count = session.exec(
        select(func.count(ChatSession.id)).where(
            ChatSession.user_id == current_user.id
        )
    ).first() or 0

    # 统计用户的消息总数
    message_count = session.exec(
        select(func.count(ChatMessage.id)).join(ChatSession).where(
            ChatSession.user_id == current_user.id
        )
    ).first() or 0

    # 统计用户的存储使用量
    storage_result = session.exec(
        select(func.sum(Document.file_size)).join(KnowledgeBase).where(
            KnowledgeBase.owner_id == current_user.id
        )
    ).first()

    # 获取用户配额
    from app.models.user import UserQuota
    quota_statement = select(UserQuota).where(UserQuota.user_id == current_user.id)
    user_quota = session.exec(quota_statement).first()

    # 如果没有配额记录，创建默认配额
    if not user_quota:
        user_quota = UserQuota(
            user_id=current_user.id,
            max_kbs=5,
            max_docs_per_kb=100,
            max_storage_mb=1024
        )
        session.add(user_quota)
        session.commit()
        session.refresh(user_quota)

    storage_used_bytes = storage_result or 0
    storage_used_mb = storage_used_bytes / (1024 * 1024)
    storage_used_gb = storage_used_bytes / (1024 * 1024 * 1024)
    storage_total_gb = user_quota.max_storage_mb / 1024  # 使用用户实际配额
    storage_percent = min(int((storage_used_gb / storage_total_gb) * 100), 100) if storage_total_gb > 0 else 0

    # 最近7天的活动统计
    seven_days_ago = datetime.now() - timedelta(days=7)

    recent_kb_count = session.exec(
        select(func.count(KnowledgeBase.id)).where(
            KnowledgeBase.owner_id == current_user.id,
            KnowledgeBase.created_at >= seven_days_ago
        )
    ).first() or 0

    recent_doc_count = session.exec(
        select(func.count(Document.id)).join(KnowledgeBase).where(
            KnowledgeBase.owner_id == current_user.id,
            Document.created_at >= seven_days_ago
        )
    ).first() or 0

    recent_chat_count = session.exec(
        select(func.count(ChatSession.id)).where(
            ChatSession.user_id == current_user.id,
            ChatSession.created_at >= seven_days_ago
        )
    ).first() or 0

    recent_message_count = session.exec(
        select(func.count(ChatMessage.id)).join(ChatSession).where(
            ChatSession.user_id == current_user.id,
            ChatMessage.created_at >= seven_days_ago
        )
    ).first() or 0

    recent_activity = {
        "knowledge_bases_created": recent_kb_count,
        "documents_uploaded": recent_doc_count,
        "chat_sessions_created": recent_chat_count,
        "messages_sent": recent_message_count
    }

    return DetailedStats(
        knowledge_bases=kb_count,
        documents=doc_count,
        chat_sessions=chat_count,
        total_messages=message_count,
        storage_used_bytes=storage_used_bytes,
        storage_used_mb=round(storage_used_mb, 2),
        storage_used_gb=round(storage_used_gb, 2),
        storage_total_gb=storage_total_gb,
        storage_percent=storage_percent,
        recent_activity=recent_activity
    )


@router.get("/activity", response_model=List[ActivityStats])
async def get_activity_stats(
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep,
    days: int = 7
):
    """获取最近几天的活动统计"""

    activity_stats = []

    for i in range(days):
        date = datetime.now() - timedelta(days=i)
        date_start = date.replace(hour=0, minute=0, second=0, microsecond=0)
        date_end = date_start + timedelta(days=1)

        # 统计当天创建的知识库数量
        kb_count = session.exec(
            select(func.count(KnowledgeBase.id)).where(
                KnowledgeBase.owner_id == current_user.id,
                KnowledgeBase.created_at >= date_start,
                KnowledgeBase.created_at < date_end
            )
        ).first() or 0

        # 统计当天上传的文档数量
        doc_count = session.exec(
            select(func.count(Document.id)).join(KnowledgeBase).where(
                KnowledgeBase.owner_id == current_user.id,
                Document.created_at >= date_start,
                Document.created_at < date_end
            )
        ).first() or 0

        # 统计当天创建的聊天会话数量
        chat_count = session.exec(
            select(func.count(ChatSession.id)).where(
                ChatSession.user_id == current_user.id,
                ChatSession.created_at >= date_start,
                ChatSession.created_at < date_end
            )
        ).first() or 0

        # 统计当天发送的消息数量
        message_count = session.exec(
            select(func.count(ChatMessage.id)).join(ChatSession).where(
                ChatSession.user_id == current_user.id,
                ChatMessage.created_at >= date_start,
                ChatMessage.created_at < date_end
            )
        ).first() or 0

        activity_stats.append(ActivityStats(
            date=date.strftime("%Y-%m-%d"),
            knowledge_bases_created=kb_count,
            documents_uploaded=doc_count,
            chat_sessions_created=chat_count,
            messages_sent=message_count
        ))

    return activity_stats
