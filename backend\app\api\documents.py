"""
文档管理API
"""
import time
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Query
from pydantic import BaseModel
from sqlmodel import Session, select
from app.core.database import SessionDep
from app.core.auth import get_current_active_user
from app.models.user import User, UserQuota
from app.models.knowledge_base import KnowledgeBase, Document
from app.schemas.knowledge_base import DocumentResponse
# from app.utils.file_handler import save_upload_file, get_file_type, delete_file  # 不再需要
from app.config import settings
from app.services.document_service import get_document_service
from app.services.vector_service import get_vector_service
from app.services.cache_service import get_cache_service
from app.services.background_tasks import get_task_manager
from app.services.processing_monitor import get_processing_monitor
from app.config import ProcessingConfig
import asyncio
import uuid
import os

router = APIRouter()

# 全局并发控制 - 根据配置动态创建信号量
large_file_semaphore = asyncio.Semaphore(ProcessingConfig.LARGE_FILE_CONCURRENCY)
medium_file_semaphore = asyncio.Semaphore(ProcessingConfig.MEDIUM_FILE_CONCURRENCY)
small_file_semaphore = asyncio.Semaphore(ProcessingConfig.SMALL_FILE_CONCURRENCY)


class BatchDeleteRequest(BaseModel):
    """批量删除请求"""
    document_ids: List[int]


@router.get("/", response_model=List[DocumentResponse])
async def get_documents(
    kb_id: int = Query(None, description="知识库ID"),
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep
):
    """获取文档列表"""
    if kb_id:
        # 验证知识库是否属于当前用户
        kb_statement = select(KnowledgeBase).where(
            KnowledgeBase.id == kb_id,
            KnowledgeBase.owner_id == current_user.id
        )
        kb = session.exec(kb_statement).first()
        if not kb:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="知识库不存在"
            )

        # 获取指定知识库的文档
        statement = select(Document).where(Document.kb_id == kb_id)
    else:
        # 获取用户所有文档
        statement = select(Document).join(KnowledgeBase).where(
            KnowledgeBase.owner_id == current_user.id
        )

    documents = session.exec(statement).all()
    return documents


@router.post("/upload/{kb_id}", response_model=DocumentResponse)
async def upload_document(
    kb_id: int,
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep
):
    """上传文档到知识库"""
    # 验证知识库是否存在且属于当前用户
    kb_statement = select(KnowledgeBase).where(
        KnowledgeBase.id == kb_id,
        KnowledgeBase.owner_id == current_user.id
    )
    kb = session.exec(kb_statement).first()
    
    if not kb:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识库不存在"
        )

    # 获取用户配额并检查限制
    quota_statement = select(UserQuota).where(UserQuota.user_id == current_user.id)
    user_quota = session.exec(quota_statement).first()

    # 如果没有配额记录，创建默认配额
    if not user_quota:
        user_quota = UserQuota(
            user_id=current_user.id,
            max_kbs=5,
            max_docs_per_kb=100,
            max_storage_mb=1024
        )
        session.add(user_quota)
        session.commit()
        session.refresh(user_quota)

    # 检查该知识库的文档数量
    doc_count_statement = select(Document).where(Document.kb_id == kb_id)
    existing_docs = session.exec(doc_count_statement).all()

    if len(existing_docs) >= user_quota.max_docs_per_kb:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"该知识库已达到文档数量限制，最多可上传 {user_quota.max_docs_per_kb} 个文档"
        )

    # 检查文件大小
    if file.size and file.size > settings.max_file_size * 1024 * 1024:
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"文件大小超过限制 ({settings.max_file_size}MB)"
        )

    # 检查用户存储配额
    if file.size:
        file_size_mb = file.size / (1024 * 1024)

        # 计算用户当前存储使用量
        user_docs_statement = select(Document).join(KnowledgeBase).where(
            KnowledgeBase.owner_id == current_user.id
        )
        user_docs = session.exec(user_docs_statement).all()
        current_storage_mb = sum(doc.file_size or 0 for doc in user_docs) / (1024 * 1024)

        if current_storage_mb + file_size_mb > user_quota.max_storage_mb:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"存储空间不足。当前使用: {current_storage_mb:.1f}MB，配额: {user_quota.max_storage_mb}MB"
            )
    
    # 检查文件类型
    document_service = get_document_service()
    if not document_service.is_supported_file(file.filename):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不支持的文件类型"
        )
    
    try:
        document_service = get_document_service()
        vector_service = get_vector_service()

        # 检查文件类型是否支持
        if not document_service.is_supported_file(file.filename):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不支持的文件类型"
            )

        # 生成唯一文件名
        file_extension = os.path.splitext(file.filename)[1]
        unique_filename = f"{uuid.uuid4()}{file_extension}"

        # 保存文件
        file_content = await file.read()
        file_path = await document_service.save_uploaded_file(file_content, unique_filename)

        # 创建文档记录
        new_document = Document(
            kb_id=kb_id,
            uploader_id=current_user.id,
            filename=file.filename,
            storage_path=file_path,
            file_type=document_service.get_file_type(file.filename),
            file_size=file.size or 0,
            status="pending"
        )

        session.add(new_document)
        session.commit()
        session.refresh(new_document)

        # 检查文件大小，决定处理方式和并发控制
        file_size_mb = new_document.file_size / (1024 * 1024) if new_document.file_size else 0

        # 根据文件大小选择合适的信号量
        if file_size_mb > 10:
            semaphore = large_file_semaphore
            print(f"检测到大文件 ({file_size_mb:.1f}MB)，使用大文件处理队列")
        elif file_size_mb > 1:
            semaphore = medium_file_semaphore
            print(f"检测到中等文件 ({file_size_mb:.1f}MB)，使用中等文件处理队列")
        else:
            semaphore = small_file_semaphore
            print(f"检测到小文件 ({file_size_mb:.1f}MB)，使用小文件处理队列")

        # 使用对应的信号量控制并发处理
        async with semaphore:
            try:
                if file_size_mb > 10:  # 大于10MB的文件使用分批处理
                    print(f"检测到大文件 ({file_size_mb:.1f}MB)，启用分批处理模式")
                    new_document.status = "processing"
                    session.add(new_document)
                    session.commit()

                    # 使用分批处理，避免超时
                    chunks = await document_service.process_document_in_batches(
                        file_path,
                        file.filename,
                        batch_size=30  # 减小批次大小，避免超时
                    )
                else:
                    # 小文件直接处理
                    chunks = await document_service.process_document(file_path, file.filename)

                # 添加到向量索引
                success = await vector_service.add_document_chunks(
                    kb_id=kb_id,
                    document_id=new_document.id,
                    chunks=chunks
                )

                if success:
                    new_document.status = "completed"
                    new_document.error_message = None
                else:
                    new_document.status = "failed"
                    new_document.error_message = "向量化处理失败"

            except Exception as process_error:
                new_document.status = "failed"
                new_document.error_message = str(process_error)
                print(f"文档处理失败: {process_error}")

        session.add(new_document)
        session.commit()

        return new_document

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"文件上传失败: {str(e)}"
        )


@router.get("/kb/{kb_id}", response_model=List[DocumentResponse])
async def get_documents(
    kb_id: int,
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep,
    offset: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100)
):
    """获取知识库的文档列表"""
    # 验证知识库权限
    kb_statement = select(KnowledgeBase).where(
        KnowledgeBase.id == kb_id,
        KnowledgeBase.owner_id == current_user.id
    )
    kb = session.exec(kb_statement).first()
    
    if not kb:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识库不存在"
        )
    
    # 获取文档列表
    statement = (
        select(Document)
        .where(Document.kb_id == kb_id)
        .offset(offset)
        .limit(limit)
    )
    documents = session.exec(statement).all()
    
    return documents


@router.get("/{doc_id}", response_model=DocumentResponse)
async def get_document(
    doc_id: int,
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep
):
    """获取文档详情"""
    statement = select(Document).where(Document.id == doc_id)
    document = session.exec(statement).first()
    
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文档不存在"
        )
    
    # 验证权限
    kb_statement = select(KnowledgeBase).where(
        KnowledgeBase.id == document.kb_id,
        KnowledgeBase.owner_id == current_user.id
    )
    kb = session.exec(kb_statement).first()
    
    if not kb:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权访问此文档"
        )
    
    return document


@router.delete("/batch-delete")
async def batch_delete_documents(
    request: BatchDeleteRequest,
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep
):
    """批量删除文档"""
    document_service = get_document_service()
    vector_service = get_vector_service()

    deleted_count = 0
    failed_deletes = []

    for doc_id in request.document_ids:
        try:
            statement = select(Document).where(Document.id == doc_id)
            document = session.exec(statement).first()

            if not document:
                failed_deletes.append({
                    "document_id": doc_id,
                    "error": "文档不存在"
                })
                continue

            # 验证权限
            kb_statement = select(KnowledgeBase).where(
                KnowledgeBase.id == document.kb_id,
                KnowledgeBase.owner_id == current_user.id
            )
            kb = session.exec(kb_statement).first()

            if not kb:
                failed_deletes.append({
                    "document_id": doc_id,
                    "error": "无权删除此文档"
                })
                continue

            # 从向量索引中删除
            await vector_service.remove_document_chunks(document.kb_id, doc_id)

            # 删除文件
            await document_service.delete_file(document.storage_path)

            # 删除数据库记录
            session.delete(document)
            session.commit()

            deleted_count += 1

        except Exception as e:
            failed_deletes.append({
                "document_id": doc_id,
                "error": str(e)
            })

    return {
        "message": f"批量删除完成，成功: {deleted_count}, 失败: {len(failed_deletes)}",
        "deleted_count": deleted_count,
        "failed_deletes": failed_deletes
    }


@router.delete("/{doc_id}")
async def delete_document(
    doc_id: int,
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep
):
    """删除文档"""
    statement = select(Document).where(Document.id == doc_id)
    document = session.exec(statement).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文档不存在"
        )

    # 验证权限
    kb_statement = select(KnowledgeBase).where(
        KnowledgeBase.id == document.kb_id,
        KnowledgeBase.owner_id == current_user.id
    )
    kb = session.exec(kb_statement).first()

    if not kb:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权删除此文档"
        )

    # 删除文件
    document_service = get_document_service()
    await document_service.delete_file(document.storage_path)

    # 删除数据库记录
    session.delete(document)
    session.commit()

    return {"message": "文档删除成功"}


@router.post("/{doc_id}/vectorize")
async def vectorize_document(
    doc_id: int,
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep
):
    """对文档进行向量化处理"""
    document_service = get_document_service()
    vector_service = get_vector_service()
    cache_service = get_cache_service()

    statement = select(Document).where(Document.id == doc_id)
    document = session.exec(statement).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文档不存在"
        )

    # 验证权限
    kb_statement = select(KnowledgeBase).where(
        KnowledgeBase.id == document.kb_id,
        KnowledgeBase.owner_id == current_user.id
    )
    kb = session.exec(kb_statement).first()

    if not kb:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权处理此文档"
        )

    # 检查是否已经处理过
    if document.status == "completed":
        return {
            "message": "文档已经处理完成",
            "document_id": doc_id,
            "status": "completed"
        }

    # 更新文档状态为处理中
    document.status = "processing"
    document.error_message = None  # 清除之前的错误信息
    session.add(document)
    session.commit()

    try:
        # 检查缓存中是否有分块数据
        cached_chunks = await cache_service.get_document_chunks(doc_id)

        if cached_chunks:
            chunks = cached_chunks
        else:
            # 处理文档并生成分块
            chunks = await document_service.process_document(
                document.storage_path,
                document.filename
            )

            # 缓存分块数据
            await cache_service.cache_document_chunks(doc_id, chunks)

        # 添加到向量索引
        success = await vector_service.add_document_chunks(
            kb_id=document.kb_id,
            document_id=doc_id,
            chunks=chunks
        )

        if success:
            document.status = "completed"
            document.error_message = None
            session.add(document)
            session.commit()

            return {
                "message": "文档向量化处理完成",
                "document_id": doc_id,
                "status": "completed",
                "chunks_count": len(chunks)
            }
        else:
            raise Exception("向量化处理失败")

    except Exception as e:
        # 处理失败，更新状态
        document.status = "failed"
        document.error_message = str(e)
        session.add(document)
        session.commit()

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"文档处理失败: {str(e)}"
        )


@router.post("/{doc_id}/retry")
async def retry_document_processing(
    doc_id: int,
    session: SessionDep,
    current_user: User = Depends(get_current_active_user)
):
    """重试文档处理"""
    document_service = get_document_service()
    vector_service = get_vector_service()
    cache_service = get_cache_service()

    # 获取文档信息
    statement = select(Document).where(Document.id == doc_id)
    document = session.exec(statement).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文档不存在"
        )

    # 检查权限
    kb_statement = select(KnowledgeBase).where(
        KnowledgeBase.id == document.kb_id,
        KnowledgeBase.owner_id == current_user.id
    )
    kb = session.exec(kb_statement).first()

    if not kb:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权限访问此文档"
        )

    # 检查文档状态
    if document.status not in ["failed", "pending"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="只能重试失败或待处理的文档"
        )

    # 更新文档状态为处理中
    document.status = "processing"
    document.error_message = None
    session.add(document)
    session.commit()

    try:
        print(f"[重试处理] 开始重试文档 {doc_id}: {document.filename}")

        # 清除缓存的分块数据，重新处理
        try:
            await cache_service.delete("document_chunks", str(doc_id))
            print(f"[重试处理] 已清除文档 {doc_id} 的缓存数据")
        except Exception as cache_error:
            print(f"[重试处理] 清除缓存失败: {cache_error}")
            # 缓存清除失败不应该阻止重试

        # 删除之前的向量索引数据
        try:
            await vector_service.remove_document_chunks(document.kb_id, doc_id)
            print(f"[重试处理] 已删除文档 {doc_id} 的向量索引")
        except Exception as vector_error:
            print(f"[重试处理] 删除向量索引失败: {vector_error}")
            # 向量删除失败不应该阻止重试

        # 重新处理文档并生成分块
        print(f"[重试处理] 开始重新处理文档: {document.storage_path}")
        chunks = await document_service.process_document(
            document.storage_path,
            document.filename
        )
        print(f"[重试处理] 文档处理完成，生成 {len(chunks)} 个分块")

        # 缓存分块数据
        try:
            await cache_service.cache_document_chunks(doc_id, chunks)
            print(f"[重试处理] 已缓存文档 {doc_id} 的分块数据")
        except Exception as cache_error:
            print(f"[重试处理] 缓存分块数据失败: {cache_error}")
            # 缓存失败不应该阻止处理

        # 添加到向量索引
        print(f"[重试处理] 开始向量化处理")
        success = await vector_service.add_document_chunks(
            kb_id=document.kb_id,
            document_id=doc_id,
            chunks=chunks
        )

        if success:
            document.status = "completed"
            document.error_message = None
            session.add(document)
            session.commit()
            print(f"[重试处理] 文档 {doc_id} 重新处理成功")

            return {
                "message": "文档重新处理完成",
                "document_id": doc_id,
                "status": "completed",
                "chunks_count": len(chunks)
            }
        else:
            raise Exception("向量化处理失败，请检查向量服务配置")

    except Exception as e:
        print(f"[重试处理] 文档 {doc_id} 重新处理失败: {e}")
        document.status = "failed"
        document.error_message = str(e)
        session.add(document)
        session.commit()

        # 根据错误类型返回不同的HTTP状态码和错误信息
        if "无法提取到任何文本内容" in str(e):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )
        elif "不支持的文件类型" in str(e):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )
        elif "文件不存在" in str(e):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文档文件不存在，可能已被删除"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"文档重新处理失败: {str(e)}"
            )


@router.post("/batch-upload/{kb_id}")
async def batch_upload_documents(
    kb_id: int,
    files: List[UploadFile] = File(...),
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep
):
    """批量上传文档到知识库"""
    # 验证知识库是否存在且属于当前用户
    kb_statement = select(KnowledgeBase).where(
        KnowledgeBase.id == kb_id,
        KnowledgeBase.owner_id == current_user.id
    )
    kb = session.exec(kb_statement).first()

    if not kb:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识库不存在"
        )

    document_service = get_document_service()
    vector_service = get_vector_service()

    uploaded_documents = []
    failed_uploads = []

    for file in files:
        # 为每个文件创建独立的数据库会话，避免事务回滚影响其他文件
        from app.core.database import get_session
        file_session = next(get_session())

        try:
            # 检查文件类型
            if not document_service.is_supported_file(file.filename):
                failed_uploads.append({
                    "filename": file.filename,
                    "error": "不支持的文件类型"
                })
                continue

            # 检查文件名长度（数据库限制）
            if len(file.filename) > 255:
                failed_uploads.append({
                    "filename": file.filename,
                    "error": "文件名过长，请重命名后重试"
                })
                continue

            # 读取文件内容（只读取一次）
            file_content = await file.read()

            # 检查文件大小
            actual_size = len(file_content)
            if actual_size > settings.max_file_size * 1024 * 1024:
                failed_uploads.append({
                    "filename": file.filename,
                    "error": f"文件大小超过限制 ({settings.max_file_size}MB)"
                })
                continue

            # 检查是否存在同名文件
            existing_doc_statement = select(Document).where(
                Document.kb_id == kb_id,
                Document.filename == file.filename
            )
            existing_doc = file_session.exec(existing_doc_statement).first()

            # 生成唯一文件名
            file_extension = os.path.splitext(file.filename)[1]
            unique_filename = f"{int(time.time())}_{uuid.uuid4().hex[:8]}{file_extension}"

            # 保存文件
            file_path = await document_service.save_uploaded_file(file_content, unique_filename)

            # 如果存在同名文件，删除旧文件和相关数据
            if existing_doc:
                try:
                    # 删除旧文件
                    await document_service.delete_file(existing_doc.storage_path)

                    # 删除向量索引中的相关数据
                    await vector_service.remove_document_chunks(kb_id, existing_doc.id)

                    # 更新现有文档记录
                    existing_doc.storage_path = file_path
                    existing_doc.file_type = document_service.get_file_type(file.filename)
                    existing_doc.file_size = actual_size
                    existing_doc.status = "pending"
                    existing_doc.error_message = None

                    file_session.add(existing_doc)
                    file_session.commit()
                    file_session.refresh(existing_doc)
                    new_document = existing_doc

                except Exception as delete_error:
                    print(f"删除旧文件失败: {delete_error}")
                    file_session.rollback()
                    # 如果删除失败，仍然创建新文档记录
                    new_document = Document(
                        kb_id=kb_id,
                        uploader_id=current_user.id,
                        filename=file.filename,
                        storage_path=file_path,
                        file_type=document_service.get_file_type(file.filename),
                        file_size=actual_size,
                        status="pending"
                    )
                    file_session.add(new_document)
                    file_session.commit()
                    file_session.refresh(new_document)
            else:
                # 创建新文档记录
                new_document = Document(
                    kb_id=kb_id,
                    uploader_id=current_user.id,
                    filename=file.filename,
                    storage_path=file_path,
                    file_type=document_service.get_file_type(file.filename),
                    file_size=actual_size,
                    status="pending"
                )
                file_session.add(new_document)
                file_session.commit()
                file_session.refresh(new_document)

            # 文件保存成功，立即添加到成功列表
            # 文档处理将在后台异步进行
            print(f"文件 {file.filename} 上传成功，开始后台处理...")

            # 启动后台处理任务（不等待完成）
            import asyncio
            asyncio.create_task(process_document_async(
                document_id=new_document.id,
                kb_id=kb_id,
                file_path=file_path,
                filename=file.filename,
                file_size_mb=new_document.file_size / (1024 * 1024) if new_document.file_size else 0
            ))

            uploaded_documents.append(new_document)

        except Exception as e:
            print(f"文件 {file.filename} 上传失败: {str(e)}")
            import traceback
            traceback.print_exc()
            file_session.rollback()
            failed_uploads.append({
                "filename": file.filename,
                "error": str(e)
            })
        finally:
            file_session.close()

    return {
        "message": f"批量上传完成，成功: {len(uploaded_documents)}, 失败: {len(failed_uploads)}",
        "uploaded_documents": uploaded_documents,
        "failed_uploads": failed_uploads
    }


@router.get("/{doc_id}/status")
async def get_document_processing_status(
    doc_id: int,
    session: SessionDep,
    current_user: User = Depends(get_current_active_user)
):
    """获取文档处理状态"""
    # 获取文档信息
    statement = select(Document).where(Document.id == doc_id)
    document = session.exec(statement).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文档不存在"
        )

    # 检查权限
    kb_statement = select(KnowledgeBase).where(
        KnowledgeBase.id == document.kb_id,
        KnowledgeBase.owner_id == current_user.id
    )
    kb = session.exec(kb_statement).first()

    if not kb:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权限访问此文档"
        )

    # 检查是否在后台处理中
    task_manager = get_task_manager()
    is_background_processing = task_manager.is_processing(doc_id)

    return {
        "document_id": doc_id,
        "filename": document.filename,
        "status": document.status,
        "error_message": document.error_message,
        "file_size": document.file_size,
        "is_background_processing": is_background_processing,
        "created_at": document.created_at,
        "updated_at": document.updated_at
    }


@router.get("/processing-stats")
async def get_processing_stats(
    current_user: User = Depends(get_current_active_user)
):
    """获取文档处理统计信息"""
    try:
        monitor = get_processing_monitor()
        stats = await monitor.get_current_stats()
        queue_status = await monitor.get_queue_status()

        return {
            "stats": stats,
            "queue_status": queue_status,
            "config": {
                "large_file_threshold": ProcessingConfig.LARGE_FILE_THRESHOLD,
                "medium_file_threshold": ProcessingConfig.MEDIUM_FILE_THRESHOLD,
                "concurrency_limits": {
                    "large": ProcessingConfig.LARGE_FILE_CONCURRENCY,
                    "medium": ProcessingConfig.MEDIUM_FILE_CONCURRENCY,
                    "small": ProcessingConfig.SMALL_FILE_CONCURRENCY
                }
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取处理统计失败: {str(e)}")


async def process_document_async(document_id: int, kb_id: int, file_path: str, filename: str, file_size_mb: float):
    """异步处理文档，不阻塞上传响应"""
    from app.core.database import get_session

    # 创建独立的数据库会话
    session = next(get_session())

    try:
        # 获取文档记录
        document_statement = select(Document).where(Document.id == document_id)
        document = session.exec(document_statement).first()

        if not document:
            print(f"文档 {document_id} 不存在，跳过处理")
            return

        # 更新状态为处理中
        document.status = "processing"
        session.add(document)
        session.commit()

        print(f"开始异步处理文档: {filename} ({file_size_mb:.1f}MB)")

        # 获取服务实例
        document_service = get_document_service()
        vector_service = get_vector_service()

        # 根据文件大小选择合适的信号量
        if file_size_mb > 10:
            semaphore = large_file_semaphore
            print(f"使用大文件处理队列处理: {filename}")
        elif file_size_mb > 1:
            semaphore = medium_file_semaphore
            print(f"使用中等文件处理队列处理: {filename}")
        else:
            semaphore = small_file_semaphore
            print(f"使用小文件处理队列处理: {filename}")

        # 使用对应的信号量控制并发处理
        async with semaphore:
            try:
                if file_size_mb > 10:  # 大于10MB的文件使用分批处理
                    print(f"大文件分批处理: {filename}")
                    chunks = await document_service.process_document_in_batches(
                        file_path,
                        filename,
                        batch_size=30
                    )
                else:
                    # 小文件直接处理
                    chunks = await document_service.process_document(file_path, filename)

                # 添加到向量索引
                success = await vector_service.add_document_chunks(
                    kb_id=kb_id,
                    document_id=document_id,
                    chunks=chunks
                )

                if success:
                    document.status = "completed"
                    document.error_message = None
                    print(f"文档处理完成: {filename}")
                else:
                    document.status = "failed"
                    document.error_message = "向量化处理失败"
                    print(f"文档向量化失败: {filename}")

            except Exception as process_error:
                document.status = "failed"
                document.error_message = str(process_error)
                print(f"文档处理失败: {filename}, 错误: {process_error}")

        session.add(document)
        session.commit()

    except Exception as e:
        print(f"异步处理文档时发生错误: {e}")
        session.rollback()
    finally:
        session.close()