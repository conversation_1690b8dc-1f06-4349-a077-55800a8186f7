# 数据库配置
DATABASE_URL=postgresql+asyncpg://postgres:111222@postgres:5432/aiknowledgebase
POSTGRES_DB=aiknowledgebase
POSTGRES_USER=postgres
POSTGRES_PASSWORD=111222
DATABASE_HOST=postgres
DATABASE_PORT=5432
DATABASE_NAME=aiknowledgebase

# Redis配置
REDIS_URL=redis://redis:6379/0

# JWT配置
SECRET_KEY=abcXyz123_4x9KpQvE8jHmN2qRtSvWnZr5t7w-
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=1440

# CORS配置
ALLOWED_ORIGINS=http://localhost,http://localhost:80,http://localhost:3000,https://aiknowledgebase.csicollege.cn,http://aiknowledgebase.csicollege.cn

# 文件上传配置
UPLOAD_DIR=/app/uploads
MAX_FILE_SIZE=52428800

# AI服务配置
SILICONFLOW_API_KEY=sk-ltvyukqqyhwhedkxyqhsqnrtqehlfdzpflskkfkqwetoiixm
DEEPSEEK_API_KEY=""
OPENAI_API_KEY=""
ANTHROPIC_API_KEY=""
GOOGLE_API_KEY=""

# 应用配置
APP_NAME=AI Knowledge Base
APP_VERSION=1.0.0
ENVIRONMENT=production
DEBUG=false
