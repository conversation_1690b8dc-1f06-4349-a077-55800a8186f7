<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧教育AI助教生成平台 - 系统架构图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
        }
        .content {
            padding: 40px;
        }
        .architecture-section {
            margin-bottom: 40px;
        }
        .section-title {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        .mermaid {
            text-align: center;
            margin: 30px 0;
        }
        .tech-stack {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .tech-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #e74c3c;
        }
        .tech-card h3 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 18px;
        }
        .tech-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .tech-list li {
            padding: 5px 0;
            color: #555;
            position: relative;
            padding-left: 20px;
        }
        .tech-list li:before {
            content: "▶";
            position: absolute;
            left: 0;
            color: #3498db;
        }
        .features {
            background: #ecf0f1;
            border-radius: 10px;
            padding: 25px;
            margin-top: 30px;
        }
        .features h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        .feature-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .feature-item strong {
            color: #e74c3c;
        }
        .data-flow {
            background: #fff;
            border: 2px solid #3498db;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .flow-step {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .step-number {
            background: #3498db;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
        }
        .innovation-points {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 25px;
            margin-top: 30px;
        }
        .innovation-points h3 {
            margin-bottom: 20px;
        }
        .innovation-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .innovation-item {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            backdrop-filter: blur(10px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>智慧教育AI助教生成平台</h1>
            <p>基于RAG技术的个性化教学智能体系统架构</p>
        </div>
        
        <div class="content">
            <div class="architecture-section">
                <h2 class="section-title">🏗️ 系统整体架构</h2>
                <div class="mermaid">
                    graph TB
                        subgraph "前端展示层 Frontend Layer"
                            A1[教师工作台<br/>Vue.js + TypeScript]
                            A2[学生学习界面<br/>Element Plus UI]
                            A3[管理后台<br/>Chart.js/ECharts可视化]
                        end

                        subgraph "API网关层 Gateway Layer"
                            B1[Nginx反向代理<br/>负载均衡]
                            B2[JWT认证授权<br/>权限控制]
                            B3[API限流<br/>熔断保护]
                        end

                        subgraph "业务服务层 Business Service Layer"
                            C1[用户管理服务<br/>FastAPI + SQLModel]
                            C2[知识库管理<br/>文档解析引擎]
                            C3[对话管理<br/>上下文维护]
                            C4[AI模型调度<br/>智能路由算法]
                        end

                        subgraph "AI算法层 AI Algorithm Layer"
                            D1[多模型融合<br/>GPT/Claude/通义千问]
                            D2[RAG检索引擎<br/>FAISS + 余弦相似度]
                            D3[向量化算法<br/>Sentence-BERT/BGE-M3]
                            D4[语义分块<br/>智能文档分割]
                            D5[流式响应<br/>SSE实时推送]
                        end

                        subgraph "数据存储层 Data Storage Layer"
                            E1[PostgreSQL<br/>关系数据 + 索引优化]
                            E2[FAISS向量库<br/>768维语义向量]
                            E3[Redis缓存<br/>会话 + 检索缓存]
                            E4[MinIO对象存储<br/>文档 + 媒体文件]
                        end

                        subgraph "基础设施层 Infrastructure Layer"
                            F1[Docker容器化<br/>微服务部署]
                            F2[Prometheus监控<br/>性能指标收集]
                            F3[Grafana仪表板<br/>可视化监控]
                            F4[ELK日志系统<br/>日志聚合分析]
                        end

                        A1 --> B1
                        A2 --> B1
                        A3 --> B1
                        B1 --> B2
                        B1 --> B3
                        B2 --> C1
                        B3 --> C2
                        B3 --> C3
                        B3 --> C4
                        C2 --> D3
                        C2 --> D4
                        C3 --> D1
                        C3 --> D2
                        C4 --> D1
                        D1 --> D5
                        D2 --> D3
                        D3 --> E2
                        D4 --> E2
                        C1 --> E1
                        C2 --> E1
                        C3 --> E3
                        D5 --> E3
                        C2 --> E4

                        F1 -.-> C1
                        F1 -.-> C2
                        F1 -.-> C3
                        F1 -.-> C4
                        F2 -.-> D1
                        F2 -.-> D2
                        F3 -.-> F2
                        F4 -.-> C1
                </div>
            </div>

            <div class="architecture-section">
                <h2 class="section-title">🔄 数据流程图</h2>
                <div class="data-flow">
                    <h3>AI助教生成与对话流程</h3>
                    <div class="flow-step">
                        <div class="step-number">1</div>
                        <div>教师上传教学文档到知识库管理系统</div>
                    </div>
                    <div class="flow-step">
                        <div class="step-number">2</div>
                        <div>系统自动解析文档，提取文本内容并进行预处理</div>
                    </div>
                    <div class="flow-step">
                        <div class="step-number">3</div>
                        <div>向量化处理模块将文本转换为高维向量并存储到向量数据库</div>
                    </div>
                    <div class="flow-step">
                        <div class="step-number">4</div>
                        <div>学生提出问题，RAG检索引擎在向量数据库中搜索相关知识</div>
                    </div>
                    <div class="flow-step">
                        <div class="step-number">5</div>
                        <div>AI模型调度器选择最适合的大语言模型进行回答生成</div>
                    </div>
                    <div class="flow-step">
                        <div class="step-number">6</div>
                        <div>流式响应模块将AI回答实时推送给用户界面</div>
                    </div>
                </div>
            </div>

            <div class="tech-stack">
                <div class="tech-card">
                    <h3>🎨 前端技术栈</h3>
                    <ul class="tech-list">
                        <li>Vue.js 3 - 现代化前端框架</li>
                        <li>TypeScript - 类型安全</li>
                        <li>Element Plus - UI组件库</li>
                        <li>Pinia - 状态管理</li>
                        <li>Chart.js/ECharts - 数据可视化</li>
                        <li>Vite - 构建工具</li>
                    </ul>
                </div>
                
                <div class="tech-card">
                    <h3>⚙️ 后端技术栈</h3>
                    <ul class="tech-list">
                        <li>FastAPI - 高性能Web框架</li>
                        <li>SQLModel - 数据库ORM</li>
                        <li>PostgreSQL - 关系数据库</li>
                        <li>Redis - 缓存数据库</li>
                        <li>Uvicorn - ASGI服务器</li>
                        <li>Pydantic - 数据验证</li>
                    </ul>
                </div>
                
                <div class="tech-card">
                    <h3>🤖 AI技术栈</h3>
                    <ul class="tech-list">
                        <li>OpenAI GPT - 大语言模型</li>
                        <li>Claude/Gemini - 多模型支持</li>
                        <li>Sentence Transformers - 文本向量化</li>
                        <li>FAISS/Chroma - 向量数据库</li>
                        <li>LangChain - AI应用框架</li>
                        <li>Streamlit - 原型开发</li>
                    </ul>
                </div>
                
                <div class="tech-card">
                    <h3>🚀 部署技术栈</h3>
                    <ul class="tech-list">
                        <li>Docker - 容器化部署</li>
                        <li>Docker Compose - 服务编排</li>
                        <li>Nginx - 反向代理</li>
                        <li>GitHub Actions - CI/CD</li>
                        <li>Prometheus - 监控告警</li>
                        <li>Grafana - 数据可视化</li>
                    </ul>
                </div>
            </div>

            <div class="features">
                <h3>🎯 核心功能特性</h3>
                <div class="feature-grid">
                    <div class="feature-item">
                        <strong>智能对话</strong><br>
                        基于RAG技术的专业问答，支持多轮对话和上下文理解
                    </div>
                    <div class="feature-item">
                        <strong>知识库管理</strong><br>
                        支持多格式文档上传，自动解析和向量化存储
                    </div>
                    <div class="feature-item">
                        <strong>多模型融合</strong><br>
                        集成多种AI模型，智能调度最适合的模型
                    </div>
                    <div class="feature-item">
                        <strong>可视化教学</strong><br>
                        支持图表生成，增强教学表达效果
                    </div>
                    <div class="feature-item">
                        <strong>用户权限</strong><br>
                        分级权限管理，保障数据安全和隐私
                    </div>
                    <div class="feature-item">
                        <strong>流式响应</strong><br>
                        实时响应技术，提供流畅的交互体验
                    </div>
                </div>
            </div>

            <div class="innovation-points">
                <h3>💡 技术创新亮点</h3>
                <div class="innovation-grid">
                    <div class="innovation-item">
                        <strong>RAG技术应用</strong><br>
                        检索增强生成，避免AI幻觉问题
                    </div>
                    <div class="innovation-item">
                        <strong>多模型智能调度</strong><br>
                        根据场景自动选择最优AI模型
                    </div>
                    <div class="innovation-item">
                        <strong>领域专家AI生成</strong><br>
                        从通用AI到专业AI的智能转化
                    </div>
                    <div class="innovation-item">
                        <strong>教育场景深度适配</strong><br>
                        专为教学设计的功能和交互
                    </div>
                    <div class="innovation-item">
                        <strong>探究式对话引导</strong><br>
                        AI主动引导，培养批判性思维
                    </div>
                    <div class="innovation-item">
                        <strong>全流程智能覆盖</strong><br>
                        备课、授课、评价全环节支持
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            }
        });
    </script>
</body>
</html>
