<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>综合成本对比图</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 30px;
        }
        
        .title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            letter-spacing: -0.5px;
        }
        
        .subtitle {
            font-size: 16px;
            opacity: 0.9;
            font-weight: 400;
        }
        
        .content {
            padding: 40px;
            background: white;
        }
        
        .chart-container {
            position: relative;
            height: 500px;
            margin-bottom: 40px;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
        }
        
        .metrics-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .metric-card {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            border-left: 4px solid #667eea;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        .metric-title {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 5px;
        }
        
        .metric-change {
            font-size: 12px;
            font-weight: 600;
            padding: 3px 8px;
            border-radius: 12px;
            background: #dcfce7;
            color: #166534;
        }
        
        .insights {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-top: 40px;
        }
        
        .insight-card {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            padding: 25px;
            border-radius: 12px;
            border-left: 4px solid #667eea;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        .insight-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .insight-content {
            color: #64748b;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .legend-custom {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
        }
        
        .traditional-color {
            background: linear-gradient(135deg, #f87171 0%, #ef4444 100%);
        }
        
        .rag-color {
            background: linear-gradient(135deg, #34d399 0%, #10b981 100%);
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
            padding: 25px;
            border-radius: 15px;
            margin-top: 30px;
            text-align: center;
        }
        
        .highlight-title {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 15px;
        }
        
        .highlight-text {
            color: #64748b;
            font-size: 16px;
            line-height: 1.6;
        }
        
        .highlight-number {
            color: #667eea;
            font-weight: 700;
            font-size: 18px;
        }
        
        @media (max-width: 768px) {
            .chart-container {
                height: 400px;
                padding: 20px;
            }
            
            .metrics-summary {
                grid-template-columns: 1fr;
            }
            
            .legend-custom {
                flex-direction: column;
                align-items: center;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">📊 传统AI vs RAG 综合成本对比</div>
            <div class="subtitle">Comprehensive Cost Comparison: Traditional AI vs RAG</div>
        </div>
        
        <div class="content">
            <!-- 核心指标总览 -->
            <div class="metrics-summary">
                <div class="metric-card">
                    <div class="metric-title">Token消耗降低</div>
                    <div class="metric-value">80%</div>
                    <div class="metric-change">大幅优化</div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-title">单次成本降低</div>
                    <div class="metric-value">83.7%</div>
                    <div class="metric-change">显著节省</div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-title">年度节省</div>
                    <div class="metric-value">$244</div>
                    <div class="metric-change">成本优化</div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-title">投资回报</div>
                    <div class="metric-value">500%+</div>
                    <div class="metric-change">长期收益</div>
                </div>
            </div>
            
            <!-- 图例 -->
            <div class="legend-custom">
                <div class="legend-item">
                    <div class="legend-color traditional-color"></div>
                    <span>传统AI对话</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color rag-color"></div>
                    <span>RAG智能检索</span>
                </div>
            </div>
            
            <!-- 综合对比图表 -->
            <div class="chart-container">
                <canvas id="comprehensiveChart"></canvas>
            </div>
            
            <!-- 重点总结 -->
            <div class="highlight-box">
                <div class="highlight-title">🎯 RAG技术成本效益总结</div>
                <div class="highlight-text">
                    通过RAG技术的精准检索机制，系统将Token消耗从<span class="highlight-number">3000-5000个</span>降低至<span class="highlight-number">500-800个</span>，
                    降幅达<span class="highlight-number">80%</span>。单次对话成本仅为传统方式的<span class="highlight-number">20%</span>，
                    年度运营成本可节省<span class="highlight-number">数万元</span>。结合私有化部署的一次性投入模式，
                    为教育机构提供了<span class="highlight-number">高性价比</span>的AI解决方案。
                </div>
            </div>

            <!-- 详细分析 -->
            <div class="insights">
                <div class="insight-card">
                    <div class="insight-title">
                        💰 Token消耗优化
                    </div>
                    <div class="insight-content">
                        RAG技术通过语义检索只提取最相关的知识片段，避免了传统AI需要处理大量无关上下文的问题，将Token消耗从平均4000个降低至650个，优化幅度达83.7%。
                    </div>
                </div>

                <div class="insight-card">
                    <div class="insight-title">
                        📈 成本结构优化
                    </div>
                    <div class="insight-content">
                        传统AI采用按Token计费的云服务模式，成本随使用量线性增长。RAG方案采用私有化部署，一次性投入后边际成本接近零，长期使用更经济。
                    </div>
                </div>

                <div class="insight-card">
                    <div class="insight-title">
                        🔒 数据安全价值
                    </div>
                    <div class="insight-content">
                        私有化部署确保教育数据完全本地化处理，避免了数据泄露风险和合规成本。对于教育机构而言，数据安全的价值远超成本节省。
                    </div>
                </div>

                <div class="insight-card">
                    <div class="insight-title">
                        🎓 教育场景适配
                    </div>
                    <div class="insight-content">
                        教育机构通常预算有限但使用频繁，RAG方案的低边际成本特性完美匹配这一需求，为教育数字化转型提供了可持续的技术路径。
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 综合成本对比图表
        const ctx = document.getElementById('comprehensiveChart').getContext('2d');

        // 创建双Y轴图表
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['Token消耗量', '单次对话成本', '年度运营成本'],
                datasets: [
                    {
                        label: '传统AI对话',
                        data: [4000, 8, 292], // Token数量, 成本×1000, 年度成本
                        backgroundColor: 'rgba(239, 68, 68, 0.8)',
                        borderColor: 'rgba(239, 68, 68, 1)',
                        borderWidth: 2,
                        borderRadius: 8,
                        borderSkipped: false,
                        yAxisID: 'y'
                    },
                    {
                        label: 'RAG智能检索',
                        data: [650, 1.3, 47.45], // Token数量, 成本×1000, 年度成本
                        backgroundColor: 'rgba(16, 185, 129, 0.8)',
                        borderColor: 'rgba(16, 185, 129, 1)',
                        borderWidth: 2,
                        borderRadius: 8,
                        borderSkipped: false,
                        yAxisID: 'y'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                plugins: {
                    title: {
                        display: true,
                        text: '传统AI对话 vs RAG智能检索 - 全方位成本对比',
                        font: {
                            size: 18,
                            weight: 'bold'
                        },
                        color: '#1e293b',
                        padding: 20
                    },
                    legend: {
                        display: false // 使用自定义图例
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: '#667eea',
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            title: function(context) {
                                return context[0].label;
                            },
                            label: function(context) {
                                const datasetLabel = context.dataset.label;
                                const value = context.parsed.y;
                                const label = context.label;

                                if (label === 'Token消耗量') {
                                    return datasetLabel + ': ' + value + ' Token';
                                } else if (label === '单次对话成本') {
                                    const actualCost = value / 1000; // 还原真实成本
                                    return datasetLabel + ': $' + actualCost.toFixed(4);
                                } else if (label === '年度运营成本') {
                                    return datasetLabel + ': $' + value.toFixed(2);
                                }
                            },
                            afterBody: function(context) {
                                if (context.length === 2) {
                                    const traditional = context[0].parsed.y;
                                    const rag = context[1].parsed.y;
                                    const savings = ((traditional - rag) / traditional * 100).toFixed(1);
                                    return ['', '节省比例: ' + savings + '%'];
                                }
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            font: {
                                size: 12,
                                weight: '500'
                            },
                            color: '#1e293b'
                        },
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        beginAtZero: true,
                        max: 4500,
                        ticks: {
                            callback: function(value) {
                                if (value >= 1000) {
                                    return (value / 1000).toFixed(1) + 'k';
                                }
                                return value;
                            },
                            font: {
                                size: 11
                            },
                            color: '#64748b'
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)',
                            drawBorder: false
                        },
                        title: {
                            display: true,
                            text: 'Token数量 / 成本×1000 / 年度成本',
                            font: {
                                size: 14,
                                weight: 'bold'
                            },
                            color: '#1e293b'
                        }
                    }
                },
                animation: {
                    duration: 2500,
                    easing: 'easeInOutQuart'
                }
            }
        });

        // 添加数据标签显示
        Chart.register({
            id: 'dataLabels',
            afterDatasetsDraw: function(chart) {
                const ctx = chart.ctx;
                chart.data.datasets.forEach((dataset, datasetIndex) => {
                    const meta = chart.getDatasetMeta(datasetIndex);
                    meta.data.forEach((bar, index) => {
                        const data = dataset.data[index];
                        let label = '';

                        if (chart.data.labels[index] === 'Token消耗量') {
                            label = data + ' Token';
                        } else if (chart.data.labels[index] === '单次对话成本') {
                            const actualCost = data / 1000; // 还原真实成本
                            label = '$' + actualCost.toFixed(4);
                        } else if (chart.data.labels[index] === '年度运营成本') {
                            label = '$' + data.toFixed(0);
                        }

                        ctx.fillStyle = '#1e293b';
                        ctx.font = 'bold 11px Inter';
                        ctx.textAlign = 'center';
                        ctx.fillText(label, bar.x, bar.y - 5);

                        // 添加节省比例标签
                        if (datasetIndex === 1) { // RAG数据
                            const traditionalData = chart.data.datasets[0].data[index];
                            const ragData = data;
                            const savings = ((traditionalData - ragData) / traditionalData * 100).toFixed(1);

                            ctx.fillStyle = '#10b981';
                            ctx.font = 'bold 10px Inter';
                            ctx.fillText('↓' + savings + '%', bar.x, bar.y - 20);
                        }
                    });
                });
            }
        });
    </script>
</body>
</html>
