<template>
  <div style="padding: 20px; font-family: Arial, sans-serif;">
    <h1 style="color: #333;">🚀 最小化测试页面</h1>

    <div style="background: #f0f0f0; padding: 15px; margin: 10px 0; border-radius: 5px;">
      <h2>基本信息</h2>
      <p><strong>当前时间:</strong> {{ currentTime }}</p>
      <p><strong>页面状态:</strong> <span style="color: green;">✅ 正常加载</span></p>
      <p><strong>Vue响应性:</strong> <span style="color: green;">✅ {{ counter }}</span></p>
    </div>

    <div style="background: #e8f4fd; padding: 15px; margin: 10px 0; border-radius: 5px;">
      <h2>交互测试</h2>
      <button
        @click="counter++"
        style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px;"
      >
        点击计数: {{ counter }}
      </button>

      <button
        @click="testAlert"
        style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px;"
      >
        测试弹窗
      </button>
    </div>

    <div style="background: #d4edda; padding: 15px; margin: 10px 0; border-radius: 5px;">
      <h2>输入测试</h2>
      <input
        v-model="inputText"
        placeholder="输入一些文字测试..."
        style="width: 300px; padding: 8px; border: 1px solid #ccc; border-radius: 3px; margin: 5px;"
      />
      <p><strong>你输入的内容:</strong> {{ inputText }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

const currentTime = ref('')
const counter = ref(0)
const inputText = ref('')

const updateTime = () => {
  currentTime.value = new Date().toLocaleString()
}

const testAlert = () => {
  alert('JavaScript正常工作！✅')
}

onMounted(() => {
  updateTime()
  setInterval(updateTime, 1000)
  console.log('🚀 最小化测试页面已加载')
})
</script>

<style scoped>
button:hover {
  opacity: 0.8;
  transform: translateY(-1px);
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

pre {
  font-size: 12px;
  max-height: 200px;
}
</style>
