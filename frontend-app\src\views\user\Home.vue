<template>
  <!-- 页面加载状态 -->
  <div v-if="pageLoading" class="h-full flex items-center justify-center">
    <div class="text-center">
      <el-icon :size="48" class="text-blue-500 animate-spin mb-4">
        <Loading />
      </el-icon>
      <p class="text-gray-600 dark:text-gray-400">正在加载数据...</p>
    </div>
  </div>

  <!-- 主要内容 -->
  <div v-else class="space-y-6">
    <!-- 欢迎区域 -->
    <div class="relative overflow-hidden card-tech p-8 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-blue-900/30 dark:via-indigo-900/30 dark:to-purple-900/30">
      <!-- 背景装饰 -->
      <div class="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-blue-200/20 to-purple-200/20 rounded-full -translate-y-32 translate-x-32 dark:from-blue-600/10 dark:to-purple-600/10"></div>
      <div class="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-indigo-200/20 to-pink-200/20 rounded-full translate-y-24 -translate-x-24 dark:from-indigo-600/10 dark:to-pink-600/10"></div>

      <div class="relative z-10 flex items-center justify-between">
        <div class="flex-1">
          <div class="flex items-center space-x-3 mb-3">
            <div class="w-2 h-8 bg-gradient-to-b from-blue-500 to-purple-600 rounded-full"></div>
            <h1 class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              欢迎回来，{{ userStore.user?.display_name || userStore.user?.username }}！
            </h1>
          </div>
          <p class="text-lg text-gray-600 dark:text-gray-300 mb-6">
            今天是 {{ formatDate(new Date()) }}，开始您的知识探索之旅吧
          </p>

          <!-- 通知和待办 -->
          <div class="flex flex-wrap items-center gap-6 text-sm">
            <div class="flex items-center space-x-2 bg-white/50 dark:bg-gray-800/50 px-4 py-2 rounded-full backdrop-blur-sm">
              <el-icon class="text-blue-500"><Bell /></el-icon>
              <span class="text-gray-700 dark:text-gray-300 font-medium">
                {{ stats.unreadNotifications }} 条未读通知
              </span>
            </div>
            <div class="flex items-center space-x-2 bg-white/50 dark:bg-gray-800/50 px-4 py-2 rounded-full backdrop-blur-sm">
              <el-icon class="text-green-500"><Check /></el-icon>
              <span class="text-gray-700 dark:text-gray-300 font-medium">
                {{ stats.pendingTasks }} 个待办任务
              </span>
            </div>
          </div>
        </div>

        <div class="hidden lg:block ml-8">
          <div class="relative">
            <div class="w-24 h-24 bg-gradient-to-br from-blue-500 via-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center glow-effect transform rotate-3 hover:rotate-0 transition-transform duration-300">
              <el-icon :size="48" class="text-white">
                <User />
              </el-icon>
            </div>
            <div class="absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full border-2 border-white dark:border-gray-800 animate-pulse"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计概览卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div class="group card-tech p-6 hover:scale-105 hover:shadow-xl transition-all duration-300 border-l-4 border-blue-500">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">知识库数量</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-gray-100">
              {{ stats.knowledge_bases }}
            </p>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900 dark:to-blue-800 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
            <el-icon :size="28" class="text-blue-600 dark:text-blue-400">
              <Collection />
            </el-icon>
          </div>
        </div>
        <div class="mt-4 pt-4 border-t border-gray-100 dark:border-gray-700">
          <div class="flex items-center text-sm">
            <el-icon class="text-green-500 mr-1"><TrendCharts /></el-icon>
            <span class="text-green-600 dark:text-green-400 font-semibold">+{{ stats.recent_activity?.knowledge_bases_created || 0 }}</span>
            <span class="text-gray-500 ml-1">最近7天</span>
          </div>
        </div>
      </div>

      <div class="group card-tech p-6 hover:scale-105 hover:shadow-xl transition-all duration-300 border-l-4 border-green-500">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">文档总数</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-gray-100">
              {{ stats.documents }}
            </p>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-green-100 to-green-200 dark:from-green-900 dark:to-green-800 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
            <el-icon :size="28" class="text-green-600 dark:text-green-400">
              <Document />
            </el-icon>
          </div>
        </div>
        <div class="mt-4 pt-4 border-t border-gray-100 dark:border-gray-700">
          <div class="flex items-center text-sm">
            <el-icon class="text-green-500 mr-1"><TrendCharts /></el-icon>
            <span class="text-green-600 dark:text-green-400 font-semibold">+{{ stats.recent_activity?.documents_uploaded || 0 }}</span>
            <span class="text-gray-500 ml-1">最近7天</span>
          </div>
        </div>
      </div>

      <div class="group card-tech p-6 hover:scale-105 hover:shadow-xl transition-all duration-300 border-l-4 border-purple-500">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">聊天会话</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-gray-100">
              {{ stats.chat_sessions }}
            </p>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-900 dark:to-purple-800 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
            <el-icon :size="28" class="text-purple-600 dark:text-purple-400">
              <ChatDotRound />
            </el-icon>
          </div>
        </div>
        <div class="mt-4 pt-4 border-t border-gray-100 dark:border-gray-700">
          <div class="flex items-center text-sm">
            <el-icon class="text-green-500 mr-1"><TrendCharts /></el-icon>
            <span class="text-green-600 dark:text-green-400 font-semibold">+{{ stats.recent_activity?.chat_sessions_created || 0 }}</span>
            <span class="text-gray-500 ml-1">最近7天</span>
          </div>
        </div>
      </div>

      <div class="group card-tech p-6 hover:scale-105 hover:shadow-xl transition-all duration-300 border-l-4 border-orange-500">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">存储使用</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {{ formatStorage(quota?.current_storage_mb || 0) }}
            </p>
            <p class="text-sm text-gray-500">
              / {{ formatStorage(quota?.max_storage_mb || 1024) }}
            </p>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-orange-100 to-orange-200 dark:from-orange-900 dark:to-orange-800 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
            <el-icon :size="28" class="text-orange-600 dark:text-orange-400">
              <FolderOpened />
            </el-icon>
          </div>
        </div>
        <div class="mt-4 pt-4 border-t border-gray-100 dark:border-gray-700">
          <el-progress
            :percentage="storagePercentage"
            :color="storagePercentage > 80 ? '#f56565' : storagePercentage > 60 ? '#f6ad55' : '#48bb78'"
            :show-text="false"
            :stroke-width="8"
            class="mb-2"
          />
          <div class="flex justify-between text-xs text-gray-500">
            <span class="font-medium">{{ formatStorage(quota?.current_storage_mb || 0) }}</span>
            <span>{{ storagePercentage.toFixed(1) }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据可视化区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- 存储使用情况饼图 -->
      <div class="card-tech p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">存储使用分布</h3>
          <div class="text-right">
            <div class="text-sm text-gray-500 dark:text-gray-400">总容量</div>
            <div class="text-lg font-bold text-gray-900 dark:text-gray-100">
              {{ formatStorage(quota?.max_storage_mb || 1024) }}
            </div>
          </div>
        </div>
        <PieChart
          :data="storageChartData"
          height="280px"
          :theme="isDark ? 'dark' : 'light'"
          :radius="['45%', '75%']"
        />
        <div class="mt-4 grid grid-cols-2 gap-4 text-center">
          <div class="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div class="text-xs text-blue-600 dark:text-blue-400 font-medium">已使用</div>
            <div class="text-lg font-bold text-blue-700 dark:text-blue-300">
              {{ formatStorage(quota?.current_storage_mb || 0) }}
            </div>
          </div>
          <div class="p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
            <div class="text-xs text-gray-600 dark:text-gray-400 font-medium">使用率</div>
            <div class="text-lg font-bold text-gray-700 dark:text-gray-300">
              {{ storagePercentage }}%
            </div>
          </div>
        </div>
      </div>

      <!-- 活动趋势图 -->
      <div class="card-tech p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">最近7天活动</h3>
          <div class="flex items-center space-x-4 text-sm">
            <div class="flex items-center space-x-1">
              <div class="w-3 h-3 bg-green-500 rounded-full"></div>
              <span class="text-gray-600 dark:text-gray-400">文档上传</span>
            </div>
            <div class="flex items-center space-x-1">
              <div class="w-3 h-3 bg-purple-500 rounded-full"></div>
              <span class="text-gray-600 dark:text-gray-400">聊天会话</span>
            </div>
            <div class="flex items-center space-x-1">
              <div class="w-3 h-3 bg-orange-500 rounded-full"></div>
              <span class="text-gray-600 dark:text-gray-400">知识库创建</span>
            </div>
          </div>
        </div>
        <LineChart
          :data="activityChartData"
          :x-axis-data="last7Days"
          height="280px"
          :theme="isDark ? 'dark' : 'light'"
          y-axis-name="数量"
        />
        <div class="mt-4 grid grid-cols-3 gap-4 text-center">
          <div class="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <div class="text-xs text-green-600 dark:text-green-400 font-medium">今日文档</div>
            <div class="text-lg font-bold text-green-700 dark:text-green-300">{{ todayStats.documents_uploaded }}</div>
          </div>
          <div class="p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
            <div class="text-xs text-purple-600 dark:text-purple-400 font-medium">今日聊天</div>
            <div class="text-lg font-bold text-purple-700 dark:text-purple-300">{{ todayStats.chat_sessions_created }}</div>
          </div>
          <div class="p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
            <div class="text-xs text-orange-600 dark:text-orange-400 font-medium">今日知识库</div>
            <div class="text-lg font-bold text-orange-700 dark:text-orange-300">{{ todayStats.knowledge_bases_created }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要功能区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 快速访问 -->
      <div class="lg:col-span-2">
        <div class="card-tech p-6">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
              最近访问
            </h2>
            <el-button text type="primary" @click="$router.push('/user/knowledge-base')">
              查看全部
            </el-button>
          </div>

          <div v-if="recentItems.length === 0" class="text-center py-8">
            <el-icon :size="48" class="text-gray-400 mb-4">
              <Box />
            </el-icon>
            <p class="text-gray-500 dark:text-gray-400">暂无最近访问记录</p>
            <el-button type="primary" class="mt-4" @click="$router.push('/user/knowledge-base')">
              创建第一个知识库
            </el-button>
          </div>

          <div v-else class="space-y-3">
            <div
              v-for="item in recentItems"
              :key="item.id"
              class="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-dark-700 transition-colors cursor-pointer"
              @click="handleItemClick(item)"
            >
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 rounded-lg flex items-center justify-center"
                     :class="getItemIconClass(item.type)">
                  <el-icon :size="20">
                    <component :is="getItemIcon(item.type)" />
                  </el-icon>
                </div>
                <div>
                  <h3 class="font-medium text-gray-900 dark:text-gray-100">
                    {{ item.name }}
                  </h3>
                  <p class="text-sm text-gray-500 dark:text-gray-400">
                    {{ item.description || getItemTypeText(item.type) }}
                  </p>
                </div>
              </div>
              <div class="text-right">
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  {{ formatRelativeTime(item.lastAccessed) }}
                </p>
                <p class="text-xs text-gray-400">
                  {{ item.type === 'knowledge_base' ? `${item.documentCount} 个文档` : '' }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 功能卡片 -->
      <div class="space-y-6">
        <div class="card-tech p-6">
          <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">
            快速操作
          </h2>
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <el-button
              v-for="action in quickActions"
              :key="action.name"
              :type="action.type"
              class="h-12 flex items-center justify-center text-sm font-medium hover:scale-105 transition-all duration-200"
              @click="action.handler"
            >
              <el-icon class="mr-2" :size="18">
                <component :is="action.icon" />
              </el-icon>
              {{ action.name }}
            </el-button>
          </div>
        </div>

        <!-- 系统状态 -->
        <div class="card-tech p-6">
          <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">
            系统状态
          </h2>
          <div class="space-y-4">
            <div class="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <div class="flex items-center space-x-3">
                <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">API 状态</span>
              </div>
              <el-tag type="success" size="small" effect="light">正常</el-tag>
            </div>
            <div class="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div class="flex items-center space-x-3">
                <div class="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">文档处理</span>
              </div>
              <el-tag type="primary" size="small" effect="light">运行中</el-tag>
            </div>
            <div class="flex items-center justify-between p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <div class="flex items-center space-x-3">
                <div class="w-3 h-3 bg-purple-500 rounded-full animate-pulse"></div>
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">AI 模型</span>
              </div>
              <el-tag type="success" size="small" effect="light">可用</el-tag>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { statsAPI } from '@/api/stats'
import { knowledgeBaseAPI } from '@/api/knowledgeBase'
import { chatAPI } from '@/api/chat'
import { authAPI, type UserQuota } from '@/api/auth'
import { ElMessage } from 'element-plus'
import PieChart from '@/components/charts/PieChart.vue'
import LineChart from '@/components/charts/LineChart.vue'
import {
  User,
  Bell,
  Check,
  Collection,
  Document,
  ChatDotRound,
  FolderOpened,
  TrendCharts,
  Box,
  Plus,
  Upload,
  MessageBox,
  Setting,
  Loading
} from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useAuthStore()

// 页面加载状态
const pageLoading = ref(false)

// 统计数据
const stats = ref({
  knowledge_bases: 0,
  documents: 0,
  chat_sessions: 0,
  total_messages: 0,
  storage_used_bytes: 0,
  storage_used_mb: 0,
  storage_used_gb: 0,
  storage_total_gb: 10,
  storage_percent: 0,
  recent_activity: {
    knowledge_bases_created: 0,
    documents_uploaded: 0,
    chat_sessions_created: 0,
    messages_sent: 0
  },
  unreadNotifications: 0,
  pendingTasks: 0
})

// 用户配额数据
const quota = ref<UserQuota | null>(null)

const loading = ref(false)

// 最近访问的项目
const recentItems = ref<any[]>([])

// 图标映射
const iconMap: Record<string, any> = {
  Collection,
  ChatDotRound,
  Document,
  Box,
  Plus,
  MessageBox,
  FolderOpened,
  Upload,
  Setting
}

// 快速操作
const quickActions = [
  {
    name: '创建知识库',
    icon: iconMap.Plus,
    type: 'primary',
    handler: () => router.push('/user/knowledge-base')
  },
  {
    name: '上传文档',
    icon: iconMap.Upload,
    type: 'success',
    handler: () => router.push('/user/knowledge-base')
  },
  {
    name: '开始对话',
    icon: iconMap.MessageBox,
    type: 'info',
    handler: () => router.push('/user/chat')
  },
  {
    name: '系统设置',
    icon: iconMap.Setting,
    type: 'default',
    handler: () => router.push('/user/settings')
  }
]

// 计算存储使用百分比
const storagePercentage = computed(() => {
  if (!quota.value || quota.value.max_storage_mb === 0) return 0
  return Math.round(((quota.value.current_storage_mb || 0) / quota.value.max_storage_mb) * 100)
})

// 主题检测
const isDark = computed(() => {
  return document.documentElement.classList.contains('dark')
})

// 存储使用分布数据
const storageChartData = computed(() => {
  // 使用真实的用户配额数据
  const used = quota.value?.current_storage_mb || 0
  const total = quota.value?.max_storage_mb || 1024
  const available = Math.max(0, total - used)

  return [
    {
      name: `已使用`,
      value: used,
      color: used > total * 0.8 ? '#ef4444' : used > total * 0.6 ? '#f59e0b' : '#3b82f6',
      actualValue: used,
      displayName: `已使用 (${formatStorage(used)})`
    },
    {
      name: `可用空间`,
      value: available,
      color: '#e5e7eb',
      actualValue: available,
      displayName: `可用空间 (${formatStorage(available)})`
    }
  ]
})

// 最近7天数据
const last7Days = computed(() => {
  const days = []
  for (let i = 6; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    days.push(date.toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' }))
  }
  return days
})

// 活动趋势数据
const activityData = ref<any[]>([])

const activityChartData = computed(() => {
  if (activityData.value.length === 0) {
    // 如果没有数据，返回空数组
    return [
      { name: '文档上传', data: Array(7).fill(0), color: '#10b981' },
      { name: '聊天会话', data: Array(7).fill(0), color: '#8b5cf6' },
      { name: '知识库创建', data: Array(7).fill(0), color: '#f59e0b' }
    ]
  }

  // 使用真实的API数据
  const documentsData = activityData.value.map(d => d.documents_uploaded)
  const chatsData = activityData.value.map(d => d.chat_sessions_created)
  const knowledgeBasesData = activityData.value.map(d => d.knowledge_bases_created)

  return [
    { name: '文档上传', data: documentsData, color: '#10b981' },
    { name: '聊天会话', data: chatsData, color: '#8b5cf6' },
    { name: '知识库创建', data: knowledgeBasesData, color: '#f59e0b' }
  ]
})

// 今日统计数据
const todayStats = computed(() => {
  if (activityData.value.length === 0) {
    return {
      documents_uploaded: 0,
      chat_sessions_created: 0,
      knowledge_bases_created: 0
    }
  }

  // 获取今天的日期字符串 (YYYY-MM-DD格式)
  const todayStr = new Date().toISOString().split('T')[0]

  // 查找今天的数据
  const todayData = activityData.value.find(item => item.date === todayStr)

  return {
    documents_uploaded: todayData?.documents_uploaded || 0,
    chat_sessions_created: todayData?.chat_sessions_created || 0,
    knowledge_bases_created: todayData?.knowledge_bases_created || 0
  }
})

// 格式化日期
const formatDate = (date: Date) => {
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
}

// 格式化存储大小
const formatStorage = (mb: number) => {
  if (mb < 1024) {
    return `${mb.toFixed(1)} MB`
  } else {
    return `${(mb / 1024).toFixed(1)} GB`
  }
}

// 格式化相对时间
const formatRelativeTime = (date: Date) => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 60) {
    return `${minutes} 分钟前`
  } else if (hours < 24) {
    return `${hours} 小时前`
  } else {
    return `${days} 天前`
  }
}

// 获取项目图标
const getItemIcon = (type: string) => {
  switch (type) {
    case 'knowledge_base':
      return iconMap.Collection
    case 'chat_session':
      return iconMap.ChatDotRound
    case 'document':
      return iconMap.Document
    default:
      return iconMap.Box
  }
}

// 获取项目图标样式类
const getItemIconClass = (type: string) => {
  switch (type) {
    case 'knowledge_base':
      return 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
    case 'chat_session':
      return 'bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400'
    case 'document':
      return 'bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400'
    default:
      return 'bg-gray-100 dark:bg-gray-900 text-gray-600 dark:text-gray-400'
  }
}

// 获取项目类型文本
const getItemTypeText = (type: string) => {
  switch (type) {
    case 'knowledge_base':
      return '知识库'
    case 'chat_session':
      return '对话会话'
    case 'document':
      return '文档'
    default:
      return '未知类型'
  }
}

// 处理项目点击
const handleItemClick = (item: any) => {
  switch (item.type) {
    case 'knowledge_base':
      router.push(`/user/knowledge-base/${item.id}/documents`)
      break
    case 'chat_session':
      router.push(`/user/chat?session=${item.id}`)
      break
    case 'document':
      // 处理文档点击
      break
  }
}

// 获取用户统计数据
const fetchUserStats = async () => {
  loading.value = true
  try {
    // 并行加载统计数据和配额数据
    const [statsData, quotaData] = await Promise.all([
      statsAPI.getDetailedStats(),
      authAPI.getUserQuota()
    ])

    stats.value = {
      ...statsData,
      storage_used_gb: statsData.storage_used_bytes / (1024 * 1024 * 1024),
      storage_total_gb: 10, // 默认10GB限制
      unreadNotifications: Math.floor(Math.random() * 5), // 模拟通知数据
      pendingTasks: Math.floor(Math.random() * 3) // 模拟待办数据
    }

    quota.value = quotaData
  } catch (error) {
    console.error('获取统计数据失败:', error)
    ElMessage.error('获取统计数据失败')
  } finally {
    loading.value = false
  }
}

// 获取活动数据
const fetchActivityData = async () => {
  try {
    const response = await statsAPI.getActivityStats({ days: 7 }) // 获取最近7天的数据
    activityData.value = response
  } catch (error) {
    console.error('获取活动数据失败:', error)
    // 设置空数据，图表会显示0值
    activityData.value = []
  }
}

// 获取最近访问的项目
const fetchRecentItems = async () => {
  try {
    const items = []

    // 获取最近的知识库
    const knowledgeBases = await knowledgeBaseAPI.getList({ limit: 3 })
    for (const kb of knowledgeBases.slice(0, 2)) {
      items.push({
        id: kb.id,
        name: kb.name,
        description: kb.description,
        type: 'knowledge_base',
        lastAccessed: new Date(kb.updated_at),
        documentCount: 0 // 暂时设为0，后续可以添加文档计数API
      })
    }

    // 获取最近的聊天会话
    const chatSessions = await chatAPI.getSessions({ limit: 2 })
    for (const session of chatSessions.slice(0, 1)) {
      items.push({
        id: session.id,
        name: session.title || '未命名对话',
        description: '聊天会话',
        type: 'chat_session',
        lastAccessed: new Date(session.updated_at),
        documentCount: 0
      })
    }

    // 按最后访问时间排序
    items.sort((a, b) => b.lastAccessed.getTime() - a.lastAccessed.getTime())

    recentItems.value = items.slice(0, 3)
  } catch (error) {
    console.error('获取最近访问项目失败:', error)
    // 不显示错误消息，因为这不是关键功能
  }
}

onMounted(async () => {
  // 立即显示页面，后台加载数据
  Promise.all([
    fetchUserStats(),
    fetchActivityData(),
    fetchRecentItems()
  ]).catch(error => {
    console.error('加载数据失败:', error)
  })
})
</script>
