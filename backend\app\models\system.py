"""
系统管理相关模型
"""
from datetime import datetime
from typing import Optional
from sqlmodel import SQLModel, Field
from .base import BaseModel


class OperationLog(BaseModel, table=True):
    """操作日志表"""
    __tablename__ = "operation_logs"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: Optional[int] = Field(default=None, foreign_key="users.id")  # 可为空，代表系统操作
    action: str = Field(max_length=100)  # create_kb, delete_doc, disable_user等
    target_type: Optional[str] = Field(default=None, max_length=50)  # knowledge_base, document, user等
    target_id: Optional[int] = Field(default=None)
    details: Optional[str] = Field(default=None)  # JSON字符串存储操作详情
    ip_address: Optional[str] = Field(default=None, max_length=45)


class SystemSetting(SQLModel, table=True):
    """系统设置表"""
    __tablename__ = "system_settings"
    
    key: str = Field(primary_key=True, max_length=50)  # allow_registration等
    value: Optional[str] = Field(default=None)
    description: Optional[str] = Field(default=None)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
