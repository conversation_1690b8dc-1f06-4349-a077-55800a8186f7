var t;(()=>{function e(t,e,a,s,l,r,d){try{var o=t[r](d),n=o.value}catch(t){return void a(t)}o.done?e(n):Promise.resolve(n).then(s,l)}t=function(t){return function(){var a=this,s=arguments;return new Promise(function(l,r){var d=t.apply(a,s);function o(t){e(d,l,r,o,n,"next",t)}function n(t){e(d,l,r,o,n,"throw",t)}o(void 0)})}}})();import{E as e,N as a,c as s}from"./elementPlus-Di4PDIm8.js";import{bB as l,bH as r,bO as d,bV as o,b_ as n,bn as i,bt as g,c1 as c,ca as u,dB as x,dN as m,dU as v,d_ as b,dc as f,dd as y,dg as h,dj as p,dk as _,dl as k,dy as w,ed as j}from"./vendor-BJ-uKP15.js";import"./_plugin-vue_export-helper-CjD0mXop.js";import{b as B}from"./admin-BSai4urc.js";import{b as C,c as z}from"./LineChart-Bgmbl9FC.js";import{b as $}from"./BarChart-r5W9w9bw.js";const D={key:0,class:"h-full flex items-center justify-center"},M={class:"text-center"},F={key:1,class:"space-y-6"},L={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"},N={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6 hover:shadow-lg transition-shadow"},P={class:"flex items-center justify-between"},S={class:"text-3xl font-bold text-gray-900 dark:text-gray-100"},A={class:"w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center"},E={class:"mt-4"},H={class:"flex items-center text-sm"},G={class:"text-green-600 dark:text-green-400"},I={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6 hover:shadow-lg transition-shadow"},O={class:"flex items-center justify-between"},T={class:"text-3xl font-bold text-gray-900 dark:text-gray-100"},U={class:"w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center"},V={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6 hover:shadow-lg transition-shadow"},q={class:"flex items-center justify-between"},J={class:"text-3xl font-bold text-gray-900 dark:text-gray-100"},K={class:"w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center"},Q={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6 hover:shadow-lg transition-shadow"},R={class:"flex items-center justify-between"},W={class:"text-3xl font-bold text-gray-900 dark:text-gray-100"},X={class:"w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center"},Y={class:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6"},Z={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},tt={class:"flex items-center justify-between mb-4"},et={class:"text-right"},at={class:"text-xl font-bold text-blue-600 dark:text-blue-400"},st={class:"mt-4 grid grid-cols-2 gap-4 text-center"},lt={class:"p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg"},rt={class:"text-lg font-bold text-blue-700 dark:text-blue-300"},dt={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},ot={class:"flex items-center justify-between mb-4"},nt={class:"text-right"},it={class:"text-lg font-bold text-gray-900 dark:text-gray-100"},gt={class:"mt-4 grid grid-cols-2 gap-4 text-center"},ct={class:"p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg"},ut={class:"text-lg font-bold text-orange-700 dark:text-orange-300"},xt={class:"p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg"},mt={class:"text-lg font-bold text-purple-700 dark:text-purple-300"},vt={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6"},bt={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ft={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},yt={class:"flex items-center justify-between mb-4"},ht={class:"space-y-4"},pt={class:"flex justify-between items-center"},_t={class:"text-xl font-bold text-gray-900 dark:text-gray-100"},kt={class:"flex justify-between items-center"},wt={class:"text-xl font-bold text-gray-900 dark:text-gray-100"},jt={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},Bt={class:"flex items-center justify-between mb-4"},Ct={class:"space-y-3"},zt={class:"flex justify-center"};var $t=k({__name:"Dashboard",setup(k){const $t=v(!0),Dt=v(!1),Mt=v({total_users:0,total_knowledge_bases:0,total_documents:0,total_chat_sessions:0,total_messages:0,active_users_today:0,total_storage_mb:0}),Ft=t=>t<1024?`${t.toFixed(1)} MB`:t<1048576?`${(t/1024).toFixed(1)} GB`:`${(t/1048576).toFixed(1)} TB`,Lt=f(()=>document.documentElement.classList.contains("dark")),Nt=f(()=>{const t=[];for(let e=29;e>=0;e--){const a=new Date;a.setDate(a.getDate()-e),t.push(a.toLocaleDateString("zh-CN",{month:"numeric",day:"numeric"}))}return t}),Pt=f(()=>{const t=Mt.value.total_users||0,e=Math.max(t-29,1);return[{name:"累计用户",data:Array.from({length:30},(a,s)=>Math.floor(e+s*(t-e)/29)),color:"#3b82f6"}]}),St=f(()=>[{name:"知识库",value:Mt.value.total_knowledge_bases||0,color:"#10b981"},{name:"文档",value:Mt.value.total_documents||0,color:"#f59e0b"},{name:"聊天会话",value:Mt.value.total_chat_sessions||0,color:"#8b5cf6"},{name:"消息(×10)",value:Math.floor((Mt.value.total_messages||0)/10),color:"#ef4444"}]),At=f(()=>[{name:"当前数量",data:[Mt.value.total_knowledge_bases,Mt.value.total_documents,Mt.value.total_chat_sessions,Math.floor(Mt.value.total_messages/100)],color:"#3b82f6"}]),Et=(Ht=t(function*(){try{Dt.value=!0,Mt.value=yield B.getSystemStats()}catch(t){s.error("加载统计数据失败")}finally{Dt.value=!1}}),function(){return Ht.apply(this,arguments)});var Ht;return w(t(function*(){try{yield Et()}finally{$t.value=!1}})),(t,s)=>{const v=a,f=e;return $t.value?(x(),h("div",D,[y("div",M,[_(v,{size:48,class:"text-blue-500 animate-spin mb-4"},{default:m(()=>[_(b(d))]),_:1}),s[3]||(s[3]=y("p",{class:"text-gray-600 dark:text-gray-400"},"正在加载仪表盘数据...",-1))])])):(x(),h("div",F,[s[25]||(s[25]=y("div",null,[y("h1",{class:"text-2xl font-bold text-gray-900 dark:text-gray-100"}," 管理仪表盘 "),y("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," 系统概览和关键指标 ")],-1)),y("div",L,[y("div",N,[y("div",P,[y("div",null,[s[4]||(s[4]=y("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"总用户数",-1)),y("p",S,j(Mt.value.total_users),1)]),y("div",A,[_(v,{size:24,class:"text-blue-600 dark:text-blue-400"},{default:m(()=>[_(b(u))]),_:1})])]),y("div",E,[y("div",H,[y("span",G,"今日活跃: "+j(Mt.value.active_users_today),1)])])]),y("div",I,[y("div",O,[y("div",null,[s[5]||(s[5]=y("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"知识库总数",-1)),y("p",T,j(Mt.value.total_knowledge_bases),1)]),y("div",U,[_(v,{size:24,class:"text-green-600 dark:text-green-400"},{default:m(()=>[_(b(g))]),_:1})])])]),y("div",V,[y("div",q,[y("div",null,[s[6]||(s[6]=y("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"文档总数",-1)),y("p",J,j(Mt.value.total_documents),1)]),y("div",K,[_(v,{size:24,class:"text-purple-600 dark:text-purple-400"},{default:m(()=>[_(b(l))]),_:1})])])]),y("div",Q,[y("div",R,[y("div",null,[s[7]||(s[7]=y("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"存储使用量",-1)),y("p",W,j(Ft(Mt.value.total_storage_mb)),1)]),y("div",X,[_(v,{size:24,class:"text-orange-600 dark:text-orange-400"},{default:m(()=>[_(b(r))]),_:1})])])])]),y("div",Y,[y("div",Z,[y("div",tt,[s[9]||(s[9]=y("h3",{class:"text-lg font-semibold text-gray-900 dark:text-gray-100"},"用户增长趋势",-1)),y("div",et,[s[8]||(s[8]=y("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"总用户数",-1)),y("div",at,j(Mt.value.total_users),1)])]),_(C,{data:Pt.value,"x-axis-data":Nt.value,height:"280px",theme:Lt.value?"dark":"light","y-axis-name":"用户数"},null,8,["data","x-axis-data","theme"]),y("div",st,[y("div",lt,[s[10]||(s[10]=y("div",{class:"text-xs text-blue-600 dark:text-blue-400 font-medium"},"今日活跃",-1)),y("div",rt,j(Mt.value.active_users_today),1)]),s[11]||(s[11]=y("div",{class:"p-3 bg-green-50 dark:bg-green-900/20 rounded-lg"},[y("div",{class:"text-xs text-green-600 dark:text-green-400 font-medium"},"月增长率"),y("div",{class:"text-lg font-bold text-green-700 dark:text-green-300"},"+12%")],-1))])]),y("div",dt,[y("div",ot,[s[13]||(s[13]=y("h3",{class:"text-lg font-semibold text-gray-900 dark:text-gray-100"},"系统资源分布",-1)),y("div",nt,[s[12]||(s[12]=y("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"总存储",-1)),y("div",it,j(Ft(Mt.value.total_storage_mb)),1)])]),_(z,{data:St.value,height:"280px",theme:Lt.value?"dark":"light",radius:["40%","70%"]},null,8,["data","theme"]),y("div",gt,[y("div",ct,[s[14]||(s[14]=y("div",{class:"text-xs text-orange-600 dark:text-orange-400 font-medium"},"知识库",-1)),y("div",ut,j(Mt.value.total_knowledge_bases),1)]),y("div",xt,[s[15]||(s[15]=y("div",{class:"text-xs text-purple-600 dark:text-purple-400 font-medium"},"文档",-1)),y("div",mt,j(Mt.value.total_documents),1)])])])]),y("div",vt,[s[16]||(s[16]=y("h3",{class:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4"},"系统活动统计",-1)),_($,{data:At.value,"x-axis-data":["知识库","文档","聊天会话","消息"],height:"300px",theme:Lt.value?"dark":"light","y-axis-name":"数量"},null,8,["data","theme"])]),y("div",bt,[y("div",ft,[y("div",yt,[s[17]||(s[17]=y("h3",{class:"text-lg font-semibold text-gray-900 dark:text-gray-100"},"聊天统计",-1)),_(v,{class:"text-blue-500"},{default:m(()=>[_(b(i))]),_:1})]),y("div",ht,[y("div",pt,[s[18]||(s[18]=y("span",{class:"text-gray-600 dark:text-gray-400"},"总会话数",-1)),y("span",_t,j(Mt.value.total_chat_sessions),1)]),y("div",kt,[s[19]||(s[19]=y("span",{class:"text-gray-600 dark:text-gray-400"},"总消息数",-1)),y("span",wt,j(Mt.value.total_messages),1)])])]),y("div",jt,[y("div",Bt,[s[20]||(s[20]=y("h3",{class:"text-lg font-semibold text-gray-900 dark:text-gray-100"},"快速操作",-1)),_(v,{class:"text-green-500"},{default:m(()=>[_(b(o))]),_:1})]),y("div",Ct,[_(f,{type:"primary",class:"w-full",onClick:s[0]||(s[0]=e=>t.$router.push("/admin/users"))},{default:m(()=>[_(v,{class:"mr-2"},{default:m(()=>[_(b(u))]),_:1}),s[21]||(s[21]=p(" 管理用户 ",-1))]),_:1,__:[21]}),_(f,{type:"success",class:"w-full",onClick:s[1]||(s[1]=e=>t.$router.push("/admin/ai-models"))},{default:m(()=>[_(v,{class:"mr-2"},{default:m(()=>[_(b(c))]),_:1}),s[22]||(s[22]=p(" AI模型配置 ",-1))]),_:1,__:[22]}),_(f,{type:"warning",class:"w-full",onClick:s[2]||(s[2]=e=>t.$router.push("/admin/operation-logs"))},{default:m(()=>[_(v,{class:"mr-2"},{default:m(()=>[_(b(l))]),_:1}),s[23]||(s[23]=p(" 查看日志 ",-1))]),_:1,__:[23]})])])]),y("div",zt,[_(f,{onClick:Et,loading:Dt.value},{default:m(()=>[_(v,{class:"mr-2"},{default:m(()=>[_(b(n))]),_:1}),s[24]||(s[24]=p(" 刷新数据 ",-1))]),_:1,__:[24]},8,["loading"])])]))}}});export{$t as default};