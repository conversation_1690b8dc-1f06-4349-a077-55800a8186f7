<template>
  <!-- 页面加载状态 -->
  <div v-if="pageLoading" class="h-full flex items-center justify-center">
    <div class="text-center">
      <el-icon :size="48" class="text-blue-500 animate-spin mb-4">
        <Loading />
      </el-icon>
      <p class="text-gray-600 dark:text-gray-400">正在加载AI模型数据...</p>
    </div>
  </div>

  <!-- 主要内容 -->
  <div v-else class="space-y-6">
    <!-- 页面头部 -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">
          AI模型管理
        </h1>
        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
          管理系统中的AI模型配置，设置API密钥和参数
        </p>
      </div>
      <div class="mt-4 sm:mt-0">
        <el-button @click="loadData" :loading="loading">
          <el-icon class="mr-2"><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 提供商筛选 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <div class="flex items-center space-x-4">
        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">提供商筛选：</span>
        <el-select v-model="selectedProviderId" placeholder="选择提供商" style="width: 200px" @change="loadModels">
          <el-option label="全部提供商" value="" />
          <el-option 
            v-for="provider in providers" 
            :key="provider.id" 
            :label="provider.display_name" 
            :value="provider.id" 
          />
        </el-select>
      </div>
    </div>

    <!-- AI模型列表 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow" v-loading="loading">
      <el-table :data="models" style="width: 100%">
        <el-table-column prop="model_name" label="模型名称" min-width="200">
          <template #default="{ row }">
            <div>
              <div class="font-medium text-gray-900 dark:text-gray-100">
                {{ row.display_name }}
              </div>
              <div class="text-sm text-gray-500">
                {{ row.model_name }}
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="provider_id" label="提供商" width="150">
          <template #default="{ row }">
            <span class="text-gray-600 dark:text-gray-400">
              {{ getProviderName(row.provider_id) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="is_active" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'" size="small">
              {{ row.is_active ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="allow_system_key_use" label="系统密钥" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="row.allow_system_key_use ? 'success' : 'info'" size="small">
              {{ row.allow_system_key_use ? '允许' : '不允许' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="max_tokens" label="最大Token" width="120" align="center">
          <template #default="{ row }">
            <span class="text-gray-600 dark:text-gray-400">
              {{ row.max_tokens || '无限制' }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="supports_streaming" label="流式输出" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="row.supports_streaming ? 'success' : 'info'" size="small">
              {{ row.supports_streaming ? '支持' : '不支持' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="cost_per_1k_tokens" label="费用/1K Token" width="150" align="center">
          <template #default="{ row }">
            <span class="text-gray-600 dark:text-gray-400">
              {{ row.cost_per_1k_tokens ? `$${row.cost_per_1k_tokens}` : '未设置' }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="120" align="center" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="editModel(row)">
              <el-icon><Edit /></el-icon>
              配置
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 编辑模型对话框 -->
    <el-dialog
      v-model="showEditDialog"
      title="编辑AI模型配置"
      width="600px"
      @close="resetForm"
    >
      <el-form :model="modelForm" :rules="modelFormRules" ref="modelFormRef" label-width="150px">
        <el-form-item label="显示名称" prop="display_name">
          <el-input
            v-model="modelForm.display_name"
            placeholder="请输入显示名称"
          />
        </el-form-item>

        <el-form-item label="启用状态" prop="is_active">
          <el-switch
            v-model="modelForm.is_active"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>

        <el-form-item label="系统API密钥" prop="system_api_key">
          <el-input
            v-model="modelForm.system_api_key"
            placeholder="请输入系统API密钥"
            type="password"
            show-password
          />
          <div class="text-xs text-gray-500 mt-1">
            留空表示不使用系统密钥，用户需要自己配置
          </div>
        </el-form-item>

        <el-form-item label="允许使用系统密钥" prop="allow_system_key_use">
          <el-switch
            v-model="modelForm.allow_system_key_use"
            active-text="允许"
            inactive-text="不允许"
          />
          <div class="text-xs text-gray-500 mt-1">
            允许用户使用系统配置的API密钥
          </div>
        </el-form-item>

        <el-form-item label="最大Token数" prop="max_tokens">
          <div class="space-y-2">
            <el-radio-group v-model="tokenLimitType" @change="handleTokenLimitChange">
              <el-radio value="unlimited">无限制（使用模型默认值）</el-radio>
              <el-radio value="custom">自定义限制</el-radio>
            </el-radio-group>
            <el-input-number
              v-if="tokenLimitType === 'custom'"
              v-model="modelForm.max_tokens"
              :min="1"
              :max="1000000"
              placeholder="如4000"
              style="width: 100%"
            />
            <div class="text-xs text-gray-500">
              注意：这是单次对话的最大token数，不是总使用量限制
            </div>
          </div>
        </el-form-item>

        <el-form-item label="支持流式输出" prop="supports_streaming">
          <el-switch
            v-model="modelForm.supports_streaming"
            active-text="支持"
            inactive-text="不支持"
          />
        </el-form-item>

        <el-form-item label="费用/1K Token" prop="cost_per_1k_tokens">
          <el-input-number
            v-model="modelForm.cost_per_1k_tokens"
            :min="0"
            :precision="4"
            :step="0.001"
            placeholder="美元"
            style="width: 100%"
          />
          <div class="text-xs text-gray-500 mt-1">
            用于成本统计，单位：美元
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showEditDialog = false">取消</el-button>
          <el-button type="primary" @click="saveModel" :loading="saving">
            保存配置
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Refresh,
  Edit,
  Loading
} from '@element-plus/icons-vue'
import { adminAPI, type AIProvider, type AIModelAdmin, type UpdateAIModelRequest } from '@/api/admin'

// 状态管理
const pageLoading = ref(true)
const loading = ref(false)
const saving = ref(false)

// 数据
const providers = ref<AIProvider[]>([])
const models = ref<AIModelAdmin[]>([])
const selectedProviderId = ref<number | string>('')

// 对话框状态
const showEditDialog = ref(false)
const editingModel = ref<AIModelAdmin | null>(null)

// 表单数据
const modelForm = reactive({
  display_name: '',
  is_active: true,
  system_api_key: '',
  allow_system_key_use: false,
  max_tokens: null as number | null,
  supports_streaming: true,
  cost_per_1k_tokens: null as number | null
})

// Token限制类型
const tokenLimitType = ref<'unlimited' | 'custom'>('unlimited')

// 表单引用
const modelFormRef = ref()

// 表单验证规则
const modelFormRules = {
  display_name: [
    { required: true, message: '请输入显示名称', trigger: 'blur' }
  ]
}

// 工具函数
const getProviderName = (providerId: number) => {
  const provider = providers.value.find(p => p.id === providerId)
  return provider?.display_name || '未知提供商'
}

// 加载提供商列表
const loadProviders = async () => {
  try {
    providers.value = await adminAPI.getAIProviders()
  } catch (error) {
    console.error('加载AI提供商失败:', error)
    ElMessage.error('加载AI提供商失败')
  }
}

// 加载模型列表
const loadModels = async () => {
  try {
    loading.value = true
    models.value = await adminAPI.getAIModels(
      typeof selectedProviderId.value === 'number' ? selectedProviderId.value : undefined
    )
  } catch (error) {
    console.error('加载AI模型失败:', error)
    ElMessage.error('加载AI模型失败')
  } finally {
    loading.value = false
  }
}

// 加载所有数据
const loadData = async () => {
  await Promise.all([
    loadProviders(),
    loadModels()
  ])
}

// Token限制类型变化处理
const handleTokenLimitChange = (value: 'unlimited' | 'custom') => {
  if (value === 'unlimited') {
    modelForm.max_tokens = null
  } else if (value === 'custom' && !modelForm.max_tokens) {
    modelForm.max_tokens = 4000
  }
}

// 编辑模型
const editModel = (model: AIModelAdmin) => {
  editingModel.value = model
  modelForm.display_name = model.display_name
  modelForm.is_active = model.is_active
  modelForm.system_api_key = model.system_api_key || ''
  modelForm.allow_system_key_use = model.allow_system_key_use
  modelForm.max_tokens = model.max_tokens || null
  modelForm.supports_streaming = model.supports_streaming
  modelForm.cost_per_1k_tokens = model.cost_per_1k_tokens || null

  // 设置token限制类型
  tokenLimitType.value = model.max_tokens ? 'custom' : 'unlimited'

  showEditDialog.value = true
}

// 保存模型配置
const saveModel = async () => {
  try {
    await modelFormRef.value?.validate()
    if (!editingModel.value) return

    saving.value = true
    const updateData: UpdateAIModelRequest = {
      display_name: modelForm.display_name,
      is_active: modelForm.is_active,
      system_api_key: modelForm.system_api_key || undefined,
      allow_system_key_use: modelForm.allow_system_key_use,
      max_tokens: modelForm.max_tokens,
      supports_streaming: modelForm.supports_streaming,
      cost_per_1k_tokens: modelForm.cost_per_1k_tokens !== null ? modelForm.cost_per_1k_tokens : undefined
    }

    await adminAPI.updateAIModel(editingModel.value.id, updateData)
    ElMessage.success('模型配置更新成功')
    showEditDialog.value = false
    await loadModels()
  } catch (error) {
    console.error('更新模型配置失败:', error)
    ElMessage.error('更新模型配置失败')
  } finally {
    saving.value = false
  }
}

// 重置表单
const resetForm = () => {
  editingModel.value = null
  modelForm.display_name = ''
  modelForm.is_active = true
  modelForm.system_api_key = ''
  modelForm.allow_system_key_use = false
  modelForm.max_tokens = null
  modelForm.supports_streaming = true
  modelForm.cost_per_1k_tokens = null
  modelFormRef.value?.resetFields()
}

// 页面初始化
onMounted(async () => {
  try {
    await loadData()
  } finally {
    pageLoading.value = false
  }
})
</script>
