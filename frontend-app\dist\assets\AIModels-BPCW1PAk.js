var e;(()=>{function a(e,a,l,t,s,o,d){try{var n=e[o](d),i=n.value}catch(e){return void l(e)}n.done?a(i):Promise.resolve(i).then(t,s)}e=function(e){return function(){var l=this,t=arguments;return new Promise(function(s,o){var d=e.apply(l,t);function n(e){a(d,s,o,n,i,"next",e)}function i(e){a(d,s,o,n,i,"throw",e)}n(void 0)})}}})();import{B as a,E as l,L as t,N as s,c as o,d,f as n,g as i,h as u,l as _,m as r,n as m,o as p,p as c,u as y,y as v,z as k}from"./elementPlus-Di4PDIm8.js";import{bD as f,bO as x,b_ as g,d8 as w,dB as h,dD as b,dN as V,dO as A,dS as I,dU as U,d_ as P,dd as C,de as j,df as z,dg as T,dj as B,dk as M,dl as D,dy as F,ed as K}from"./vendor-BJ-uKP15.js";import{b as N}from"./admin-BSai4urc.js";const O={key:0,class:"h-full flex items-center justify-center"},$={class:"text-center"},q={key:1,class:"space-y-6"},E={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},L={class:"mt-4 sm:mt-0"},R={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},S={class:"flex items-center space-x-4"},G={class:"bg-white dark:bg-gray-800 rounded-lg shadow"},H={class:"font-medium text-gray-900 dark:text-gray-100"},J={class:"text-sm text-gray-500"},Q={class:"text-gray-600 dark:text-gray-400"},W={class:"text-gray-600 dark:text-gray-400"},X={class:"text-gray-600 dark:text-gray-400"},Y={class:"space-y-2"},Z={class:"dialog-footer"};var ee=D({__name:"AIModels",setup(D){const ee=U(!0),ae=U(!1),le=U(!1),te=U([]),se=U([]),oe=U(""),de=U(!1),ne=U(null),ie=I({display_name:"",is_active:!0,system_api_key:"",allow_system_key_use:!1,max_tokens:null,supports_streaming:!0,cost_per_1k_tokens:null}),ue=U("unlimited"),_e=U(),re={display_name:[{required:!0,message:"请输入显示名称",trigger:"blur"}]},me=e=>{const a=te.value.find(a=>a.id===e);return(null==a?void 0:a.display_name)||"未知提供商"},pe=(ce=e(function*(){try{te.value=yield N.getAIProviders()}catch(e){o.error("加载AI提供商失败")}}),function(){return ce.apply(this,arguments)});var ce;const ye=(ve=e(function*(){try{ae.value=!0,se.value=yield N.getAIModels("number"==typeof oe.value?oe.value:void 0)}catch(e){o.error("加载AI模型失败")}finally{ae.value=!1}}),function(){return ve.apply(this,arguments)});var ve;const ke=(fe=e(function*(){yield Promise.all([pe(),ye()])}),function(){return fe.apply(this,arguments)});var fe;const xe=e=>{"unlimited"===e?ie.max_tokens=null:"custom"!==e||ie.max_tokens||(ie.max_tokens=4e3)},ge=(we=e(function*(){try{var e;if(yield null===(e=_e.value)||void 0===e?void 0:e.validate(),!ne.value)return;le.value=!0;const a={display_name:ie.display_name,is_active:ie.is_active,system_api_key:ie.system_api_key||void 0,allow_system_key_use:ie.allow_system_key_use,max_tokens:ie.max_tokens,supports_streaming:ie.supports_streaming,cost_per_1k_tokens:null!==ie.cost_per_1k_tokens?ie.cost_per_1k_tokens:void 0};yield N.updateAIModel(ne.value.id,a),o.success("模型配置更新成功"),de.value=!1,yield ye()}catch(a){o.error("更新模型配置失败")}finally{le.value=!1}}),function(){return we.apply(this,arguments)});var we;const he=()=>{var e;ne.value=null,ie.display_name="",ie.is_active=!0,ie.system_api_key="",ie.allow_system_key_use=!1,ie.max_tokens=null,ie.supports_streaming=!0,ie.cost_per_1k_tokens=null,null===(e=_e.value)||void 0===e||e.resetFields()};return F(e(function*(){try{yield ke()}finally{ee.value=!1}})),(e,o)=>{const I=s,U=l,D=_,F=r,N=i,pe=v,ce=n,ve=t,fe=c,we=u,be=k,Ve=a,Ae=m,Ie=p,Ue=y,Pe=d;return ee.value?(h(),T("div",O,[C("div",$,[M(I,{size:48,class:"text-blue-500 animate-spin mb-4"},{default:V(()=>[M(P(x))]),_:1}),o[11]||(o[11]=C("p",{class:"text-gray-600 dark:text-gray-400"},"正在加载AI模型数据...",-1))])])):(h(),T("div",q,[C("div",E,[o[13]||(o[13]=C("div",null,[C("h1",{class:"text-2xl font-bold text-gray-900 dark:text-gray-100"}," AI模型管理 "),C("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," 管理系统中的AI模型配置，设置API密钥和参数 ")],-1)),C("div",L,[M(U,{onClick:ke,loading:ae.value},{default:V(()=>[M(I,{class:"mr-2"},{default:V(()=>[M(P(g))]),_:1}),o[12]||(o[12]=B(" 刷新 ",-1))]),_:1,__:[12]},8,["loading"])])]),C("div",R,[C("div",S,[o[14]||(o[14]=C("span",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"提供商筛选：",-1)),M(F,{modelValue:oe.value,"onUpdate:modelValue":o[0]||(o[0]=e=>oe.value=e),placeholder:"选择提供商",style:{width:"200px"},onChange:ye},{default:V(()=>[M(D,{label:"全部提供商",value:""}),(h(!0),T(w,null,b(te.value,e=>(h(),j(D,{key:e.id,label:e.display_name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])]),A((h(),T("div",G,[M(ce,{data:se.value,style:{width:"100%"}},{default:V(()=>[M(N,{prop:"model_name",label:"模型名称","min-width":"200"},{default:V(({row:e})=>[C("div",null,[C("div",H,K(e.display_name),1),C("div",J,K(e.model_name),1)])]),_:1}),M(N,{prop:"provider_id",label:"提供商",width:"150"},{default:V(({row:e})=>[C("span",Q,K(me(e.provider_id)),1)]),_:1}),M(N,{prop:"is_active",label:"状态",width:"100",align:"center"},{default:V(({row:e})=>[M(pe,{type:e.is_active?"success":"danger",size:"small"},{default:V(()=>[B(K(e.is_active?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),M(N,{prop:"allow_system_key_use",label:"系统密钥",width:"120",align:"center"},{default:V(({row:e})=>[M(pe,{type:e.allow_system_key_use?"success":"info",size:"small"},{default:V(()=>[B(K(e.allow_system_key_use?"允许":"不允许"),1)]),_:2},1032,["type"])]),_:1}),M(N,{prop:"max_tokens",label:"最大Token",width:"120",align:"center"},{default:V(({row:e})=>[C("span",W,K(e.max_tokens||"无限制"),1)]),_:1}),M(N,{prop:"supports_streaming",label:"流式输出",width:"120",align:"center"},{default:V(({row:e})=>[M(pe,{type:e.supports_streaming?"success":"info",size:"small"},{default:V(()=>[B(K(e.supports_streaming?"支持":"不支持"),1)]),_:2},1032,["type"])]),_:1}),M(N,{prop:"cost_per_1k_tokens",label:"费用/1K Token",width:"150",align:"center"},{default:V(({row:e})=>[C("span",X,K(e.cost_per_1k_tokens?`$${e.cost_per_1k_tokens}`:"未设置"),1)]),_:1}),M(N,{label:"操作",width:"120",align:"center",fixed:"right"},{default:V(({row:e})=>[M(U,{size:"small",onClick:a=>{return ne.value=l=e,ie.display_name=l.display_name,ie.is_active=l.is_active,ie.system_api_key=l.system_api_key||"",ie.allow_system_key_use=l.allow_system_key_use,ie.max_tokens=l.max_tokens||null,ie.supports_streaming=l.supports_streaming,ie.cost_per_1k_tokens=l.cost_per_1k_tokens||null,ue.value=l.max_tokens?"custom":"unlimited",void(de.value=!0);var l}},{default:V(()=>[M(I,null,{default:V(()=>[M(P(f))]),_:1}),o[15]||(o[15]=B(" 配置 ",-1))]),_:2,__:[15]},1032,["onClick"])]),_:1})]),_:1},8,["data"])])),[[Pe,ae.value]]),M(Ue,{modelValue:de.value,"onUpdate:modelValue":o[10]||(o[10]=e=>de.value=e),title:"编辑AI模型配置",width:"600px",onClose:he},{footer:V(()=>[C("div",Z,[M(U,{onClick:o[9]||(o[9]=e=>de.value=!1)},{default:V(()=>o[22]||(o[22]=[B("取消",-1)])),_:1,__:[22]}),M(U,{type:"primary",onClick:ge,loading:le.value},{default:V(()=>o[23]||(o[23]=[B(" 保存配置 ",-1)])),_:1,__:[23]},8,["loading"])])]),default:V(()=>[M(Ie,{model:ie,rules:re,ref_key:"modelFormRef",ref:_e,"label-width":"150px"},{default:V(()=>[M(fe,{label:"显示名称",prop:"display_name"},{default:V(()=>[M(ve,{modelValue:ie.display_name,"onUpdate:modelValue":o[1]||(o[1]=e=>ie.display_name=e),placeholder:"请输入显示名称"},null,8,["modelValue"])]),_:1}),M(fe,{label:"启用状态",prop:"is_active"},{default:V(()=>[M(we,{modelValue:ie.is_active,"onUpdate:modelValue":o[2]||(o[2]=e=>ie.is_active=e),"active-text":"启用","inactive-text":"禁用"},null,8,["modelValue"])]),_:1}),M(fe,{label:"系统API密钥",prop:"system_api_key"},{default:V(()=>[M(ve,{modelValue:ie.system_api_key,"onUpdate:modelValue":o[3]||(o[3]=e=>ie.system_api_key=e),placeholder:"请输入系统API密钥",type:"password","show-password":""},null,8,["modelValue"]),o[16]||(o[16]=C("div",{class:"text-xs text-gray-500 mt-1"}," 留空表示不使用系统密钥，用户需要自己配置 ",-1))]),_:1,__:[16]}),M(fe,{label:"允许使用系统密钥",prop:"allow_system_key_use"},{default:V(()=>[M(we,{modelValue:ie.allow_system_key_use,"onUpdate:modelValue":o[4]||(o[4]=e=>ie.allow_system_key_use=e),"active-text":"允许","inactive-text":"不允许"},null,8,["modelValue"]),o[17]||(o[17]=C("div",{class:"text-xs text-gray-500 mt-1"}," 允许用户使用系统配置的API密钥 ",-1))]),_:1,__:[17]}),M(fe,{label:"最大Token数",prop:"max_tokens"},{default:V(()=>[C("div",Y,[M(Ve,{modelValue:ue.value,"onUpdate:modelValue":o[5]||(o[5]=e=>ue.value=e),onChange:xe},{default:V(()=>[M(be,{value:"unlimited"},{default:V(()=>o[18]||(o[18]=[B("无限制（使用模型默认值）",-1)])),_:1,__:[18]}),M(be,{value:"custom"},{default:V(()=>o[19]||(o[19]=[B("自定义限制",-1)])),_:1,__:[19]})]),_:1},8,["modelValue"]),"custom"===ue.value?(h(),j(Ae,{key:0,modelValue:ie.max_tokens,"onUpdate:modelValue":o[6]||(o[6]=e=>ie.max_tokens=e),min:1,max:1e6,placeholder:"如4000",style:{width:"100%"}},null,8,["modelValue"])):z("",!0),o[20]||(o[20]=C("div",{class:"text-xs text-gray-500"}," 注意：这是单次对话的最大token数，不是总使用量限制 ",-1))])]),_:1}),M(fe,{label:"支持流式输出",prop:"supports_streaming"},{default:V(()=>[M(we,{modelValue:ie.supports_streaming,"onUpdate:modelValue":o[7]||(o[7]=e=>ie.supports_streaming=e),"active-text":"支持","inactive-text":"不支持"},null,8,["modelValue"])]),_:1}),M(fe,{label:"费用/1K Token",prop:"cost_per_1k_tokens"},{default:V(()=>[M(Ae,{modelValue:ie.cost_per_1k_tokens,"onUpdate:modelValue":o[8]||(o[8]=e=>ie.cost_per_1k_tokens=e),min:0,precision:4,step:.001,placeholder:"美元",style:{width:"100%"}},null,8,["modelValue"]),o[21]||(o[21]=C("div",{class:"text-xs text-gray-500 mt-1"}," 用于成本统计，单位：美元 ",-1))]),_:1,__:[21]})]),_:1},8,["model"])]),_:1},8,["modelValue"])]))}}});export{ee as default};