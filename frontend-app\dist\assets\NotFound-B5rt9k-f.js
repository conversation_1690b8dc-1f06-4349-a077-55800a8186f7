import{E as s}from"./elementPlus-Di4PDIm8.js";import{cU as t,dB as a,dN as e,dd as d,dg as r,dj as n,dk as o,dl as i}from"./vendor-BJ-uKP15.js";import{b as l}from"./index-Byt5TjPh.js";const m={class:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-dark-900"},c={class:"text-center"},u={class:"space-x-4"};var x=i({__name:"NotFound",setup(i){const x=t(),p=l(),g=()=>{x.go(-1)},f=()=>{var s;p.isAuthenticated?(null===(s=p.user)||void 0===s?void 0:s.is_admin)?x.push("/admin/dashboard"):x.push("/user/home"):x.push("/login")};return(t,i)=>{const l=s;return a(),r("div",m,[d("div",c,[i[2]||(i[2]=d("div",{class:"mb-8"},[d("h1",{class:"text-9xl font-bold text-gradient"},"404")],-1)),i[3]||(i[3]=d("h2",{class:"text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4"}," 页面未找到 ",-1)),i[4]||(i[4]=d("p",{class:"text-gray-600 dark:text-gray-400 mb-8 max-w-md"}," 抱歉，您访问的页面不存在。可能是链接错误或页面已被移除。 ",-1)),d("div",u,[o(l,{type:"primary",onClick:g},{default:e(()=>i[0]||(i[0]=[n(" 返回上页 ",-1)])),_:1,__:[0]}),o(l,{onClick:f},{default:e(()=>i[1]||(i[1]=[n(" 回到首页 ",-1)])),_:1,__:[1]})])])])}}});export{x as default};