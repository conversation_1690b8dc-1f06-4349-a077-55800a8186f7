"""
完整版向量化和搜索服务
使用Redis作为向量数据库，支持文档向量化和语义搜索
"""
import logging
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
import redis
import json
import hashlib
import asyncio
import aiohttp
from openai import AsyncOpenAI

logger = logging.getLogger(__name__)

class VectorServiceError(Exception):
    """向量服务异常"""
    pass

class EmbeddingService:
    """嵌入向量服务"""
    
    def __init__(self, api_key: str = "sk-ltvyukqqyhwhedkxyqhsqnrtqehlfdzpflskkfkqwetoiixm"):
        self.api_key = api_key
        self.client = AsyncOpenAI(
            api_key=api_key,
            base_url="https://api.siliconflow.cn/v1"
        )
        self.model = "BAAI/bge-m3"  # 硅基流动支持的嵌入模型
    
    def _clean_text(self, text: str) -> str:
        """清理文本，确保API兼容性"""
        if not text or not isinstance(text, str):
            return ""

        # 移除控制字符和特殊字符
        import re
        # 保留基本的中英文字符、数字、标点符号和空白字符
        cleaned = re.sub(r'[^\w\s\u4e00-\u9fff\u3000-\u303f\uff00-\uffef.,!?;:()[]{}"\'-]', '', text)

        # 限制长度，避免API限制
        if len(cleaned) > 8000:  # 保守的长度限制
            cleaned = cleaned[:8000]

        # 确保不为空
        if not cleaned.strip():
            return "空文档内容"

        return cleaned.strip()

    async def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """获取文本的嵌入向量"""
        try:
            # 清理和验证文本
            cleaned_texts = []
            for text in texts:
                cleaned = self._clean_text(text)
                cleaned_texts.append(cleaned)

            # 过滤空文本
            if not cleaned_texts or all(not t.strip() for t in cleaned_texts):
                logger.warning("所有文本都为空，返回随机向量")
                import random
                return [[random.random() for _ in range(1024)] for _ in texts]

            logger.info(f"正在获取 {len(cleaned_texts)} 个文本的嵌入向量")

            response = await self.client.embeddings.create(
                model=self.model,
                input=cleaned_texts
            )

            embeddings = []
            for data in response.data:
                embeddings.append(data.embedding)

            logger.info(f"成功获取 {len(embeddings)} 个嵌入向量")
            return embeddings

        except Exception as e:
            logger.error(f"获取嵌入向量失败: {e}")
            logger.error(f"输入文本数量: {len(texts)}")
            for i, text in enumerate(texts[:3]):  # 只记录前3个文本的信息
                logger.error(f"文本 {i+1} 长度: {len(text)}, 前100字符: {text[:100]}")

            # 如果API失败，返回随机向量作为fallback
            import random
            return [[random.random() for _ in range(1024)] for _ in texts]
    
    async def get_embedding(self, text: str) -> List[float]:
        """获取单个文本的嵌入向量"""
        embeddings = await self.get_embeddings([text])
        if embeddings:
            return embeddings[0]
        else:
            import random
            return [random.random() for _ in range(1024)]  # 返回随机向量而不是全零向量

class VectorService:
    """完整版向量化和搜索服务"""
    
    def __init__(self, redis_host: str = "localhost", redis_port: int = 6379, redis_db: int = 0):
        self.redis_url = f"redis://{redis_host}:{redis_port}/{redis_db}"
        self.redis_client = None
        self.embedding_service = EmbeddingService()
        
    async def initialize(self):
        """初始化服务"""
        try:
            # 初始化Redis连接
            logger.info(f"向量服务连接Redis: {self.redis_url}")
            self.redis_client = redis.from_url(self.redis_url, decode_responses=False)
            self.redis_client.ping()

            logger.info("完整版向量服务初始化成功")

        except Exception as e:
            logger.error(f"向量服务初始化失败: {e}")
            logger.warning("向量服务将在模拟模式下运行")
            self.redis_client = None
    
    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """计算余弦相似度"""
        try:
            vec1 = np.array(vec1)
            vec2 = np.array(vec2)
            
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            return dot_product / (norm1 * norm2)
            
        except Exception as e:
            logger.error(f"计算余弦相似度失败: {e}")
            return 0.0
    
    def _get_kb_key(self, kb_id: int) -> str:
        """获取知识库键名"""
        return f"kb_{kb_id}_vectors"
    
    def _get_chunk_key(self, kb_id: int, document_id: int, chunk_id: int) -> str:
        """获取分块键名"""
        return f"kb_{kb_id}_doc_{document_id}_chunk_{chunk_id}"
    
    async def create_knowledge_base_index(self, kb_id: int) -> bool:
        """为知识库创建向量索引"""
        try:
            if self.redis_client:
                kb_key = self._get_kb_key(kb_id)
                # 创建知识库元数据
                metadata = {
                    "created_at": str(asyncio.get_event_loop().time()),
                    "kb_id": kb_id,
                    "vector_dim": 1024
                }
                # 使用兼容的hset语法
                for key, value in metadata.items():
                    self.redis_client.hset(f"{kb_key}_meta", key, value)
            
            logger.info(f"成功创建知识库 {kb_id} 的向量索引")
            return True
            
        except Exception as e:
            logger.error(f"创建知识库索引失败 {kb_id}: {e}")
            return False
    
    async def delete_knowledge_base_index(self, kb_id: int) -> bool:
        """删除知识库向量索引"""
        try:
            if self.redis_client:
                kb_key = self._get_kb_key(kb_id)
                
                # 删除所有相关的键
                pattern = f"{kb_key}*"
                keys = []
                for key in self.redis_client.scan_iter(match=pattern):
                    keys.append(key)
                
                if keys:
                    self.redis_client.delete(*keys)
            
            logger.info(f"成功删除知识库 {kb_id} 的向量索引")
            return True
            
        except Exception as e:
            logger.error(f"删除知识库索引失败 {kb_id}: {e}")
            return False
    
    async def add_document_chunks(
        self, 
        kb_id: int, 
        document_id: int, 
        chunks: List[Dict[str, Any]]
    ) -> bool:
        """添加文档分块到向量索引"""
        try:
            if not chunks:
                return True
            
            # 确保知识库索引存在
            await self.create_knowledge_base_index(kb_id)
            
            # 提取所有文本内容
            texts = [chunk.get('content', '') for chunk in chunks]

            # 分批处理向量化，避免API限制
            batch_size = 50  # 设置批次大小为50，小于API限制的64
            all_embeddings = []

            logger.info(f"开始向量化处理，总分块数: {len(texts)}, 批次大小: {batch_size}")

            for i in range(0, len(texts), batch_size):
                batch_texts = texts[i:i + batch_size]
                logger.info(f"处理批次 {i//batch_size + 1}/{(len(texts) + batch_size - 1)//batch_size}, 分块数: {len(batch_texts)}")

                try:
                    batch_embeddings = await self.embedding_service.get_embeddings(batch_texts)
                    all_embeddings.extend(batch_embeddings)
                except Exception as e:
                    logger.error(f"批次 {i//batch_size + 1} 向量化失败: {e}")
                    # 如果批次失败，尝试更小的批次
                    smaller_batch_size = 20
                    for j in range(i, min(i + batch_size, len(texts)), smaller_batch_size):
                        smaller_batch = texts[j:j + smaller_batch_size]
                        try:
                            smaller_embeddings = await self.embedding_service.get_embeddings(smaller_batch)
                            all_embeddings.extend(smaller_embeddings)
                        except Exception as smaller_e:
                            logger.error(f"小批次向量化也失败: {smaller_e}")
                            # 如果还是失败，跳过这些分块
                            all_embeddings.extend([[0.0] * 1536] * len(smaller_batch))  # 使用零向量占位

            embeddings = all_embeddings
            
            if self.redis_client:
                kb_key = self._get_kb_key(kb_id)
                
                # 存储每个分块的向量和元数据
                for i, (chunk, embedding) in enumerate(zip(chunks, embeddings)):
                    chunk_key = self._get_chunk_key(kb_id, document_id, i)
                    
                    chunk_data = {
                        "document_id": document_id,
                        "chunk_id": i,
                        "content": chunk.get('content', ''),
                        "metadata": json.dumps(chunk.get('metadata', {})),
                        "embedding": json.dumps(embedding)
                    }
                    
                    # 存储分块数据（使用兼容的hset语法）
                    for key, value in chunk_data.items():
                        self.redis_client.hset(chunk_key, key, value)
                    
                    # 将分块键添加到知识库索引
                    self.redis_client.sadd(kb_key, chunk_key)
            
            logger.info(f"成功添加文档 {document_id} 的 {len(chunks)} 个分块到知识库 {kb_id}")
            return True
            
        except Exception as e:
            logger.error(f"添加文档分块失败: {e}")
            return False
    
    async def remove_document_chunks(self, kb_id: int, document_id: int) -> bool:
        """从向量索引中移除文档分块"""
        try:
            if self.redis_client:
                kb_key = self._get_kb_key(kb_id)
                
                # 查找所有属于该文档的分块
                chunk_keys = self.redis_client.smembers(kb_key)
                
                keys_to_remove = []
                for chunk_key in chunk_keys:
                    chunk_data = self.redis_client.hgetall(chunk_key)
                    if chunk_data and int(chunk_data.get(b'document_id', 0)) == document_id:
                        keys_to_remove.append(chunk_key)
                
                # 删除分块数据和索引
                if keys_to_remove:
                    self.redis_client.delete(*keys_to_remove)
                    self.redis_client.srem(kb_key, *keys_to_remove)
            
            logger.info(f"成功移除文档 {document_id} 的分块从知识库 {kb_id}")
            return True
            
        except Exception as e:
            logger.error(f"移除文档分块失败: {e}")
            return False
    
    async def search_similar_chunks(
        self,
        kb_ids: List[int],
        query: str,
        top_k: int = 5,
        similarity_threshold: float = 0.4
    ) -> List[Dict[str, Any]]:
        """在指定知识库中搜索相似的文档分块"""
        try:
            # 获取查询的嵌入向量
            query_embedding = await self.embedding_service.get_embedding(query)
            all_results = []

            for kb_id in kb_ids:
                if self.redis_client:
                    kb_key = self._get_kb_key(kb_id)
                    chunk_keys = self.redis_client.smembers(kb_key)

                    for chunk_key in chunk_keys:
                        chunk_data = self.redis_client.hgetall(chunk_key)
                        if not chunk_data:
                            continue

                        try:
                            # 解析嵌入向量
                            embedding = json.loads(chunk_data[b'embedding'].decode('utf-8'))
                            content = chunk_data[b'content'].decode('utf-8')

                            # 计算相似度
                            similarity = self._cosine_similarity(query_embedding, embedding)

                            if similarity >= similarity_threshold:
                                all_results.append({
                                    "kb_id": kb_id,
                                    "document_id": int(chunk_data[b'document_id']),
                                    "chunk_id": int(chunk_data[b'chunk_id']),
                                    "content": content,
                                    "metadata": json.loads(chunk_data[b'metadata'].decode('utf-8')),
                                    "similarity": similarity
                                })
                        except (json.JSONDecodeError, KeyError, ValueError) as e:
                            logger.warning(f"解析分块数据失败: {e}")
                            continue

            # 按相似度排序并返回top_k结果
            all_results.sort(key=lambda x: x['similarity'], reverse=True)
            return all_results[:top_k]

        except Exception as e:
            logger.error(f"搜索相似分块失败: {e}")
            return []
    
    async def debug_knowledge_base_data(self, kb_id: int) -> Dict[str, Any]:
        """调试知识库数据"""
        debug_info = {
            "kb_id": kb_id,
            "redis_connected": self.redis_client is not None,
            "kb_key": self._get_kb_key(kb_id),
            "chunk_count": 0,
            "chunks": []
        }

        if self.redis_client:
            kb_key = self._get_kb_key(kb_id)
            chunk_keys = self.redis_client.smembers(kb_key)
            debug_info["chunk_count"] = len(chunk_keys)

            for chunk_key in list(chunk_keys)[:5]:  # 只显示前5个分块
                chunk_data = self.redis_client.hgetall(chunk_key)
                if chunk_data:
                    debug_info["chunks"].append({
                        "key": chunk_key.decode('utf-8') if isinstance(chunk_key, bytes) else chunk_key,
                        "content_length": len(chunk_data.get(b'content', b'')),
                        "has_embedding": b'embedding' in chunk_data,
                        "content_preview": chunk_data.get(b'content', b'')[:100].decode('utf-8', errors='ignore')
                    })

        return debug_info

    async def get_knowledge_context(
        self,
        kb_ids: List[int],
        query: str,
        max_chunks: int = 50,  # 默认较大值，支持显示所有相关
        similarity_threshold: float = 0.4,
        max_context_length: int = 25000  # 最大上下文长度限制
    ) -> str:
        """获取知识库上下文用于AI聊天（仅返回文本）"""
        context_data = await self.get_knowledge_context_with_sources(
            kb_ids=kb_ids,
            query=query,
            max_chunks=max_chunks,
            similarity_threshold=similarity_threshold,
            max_context_length=max_context_length
        )
        return context_data["context"]

    async def get_knowledge_context_with_sources(
        self,
        kb_ids: List[int],
        query: str,
        max_chunks: int = 10,  # 默认较大值，支持显示所有相关
        similarity_threshold: float = 0.5,
        max_context_length: int = 25000  # 最大上下文长度限制，避免超过模型token限制
    ) -> Dict[str, Any]:
        """获取知识库上下文和来源信息用于AI聊天"""
        try:
            print(f"[向量服务] 开始搜索相似分块，知识库IDs: {kb_ids}")
            print(f"[向量服务] 查询: {query}")
            print(f"[向量服务] 相似度阈值: {similarity_threshold}")

            similar_chunks = await self.search_similar_chunks(
                kb_ids=kb_ids,
                query=query,
                top_k=max_chunks,
                similarity_threshold=similarity_threshold
            )

            print(f"[向量服务] 找到 {len(similar_chunks)} 个相似分块")

            if not similar_chunks:
                print("[向量服务] 没有找到相似分块，返回空上下文")
                return {"context": "", "sources": []}

            # 获取文档信息用于构建来源
            from app.core.database import get_session
            from app.models.knowledge_base import Document
            from sqlmodel import select

            session = next(get_session())
            try:
                context_parts = []
                sources = []
                current_length = 0

                for i, chunk in enumerate(similar_chunks):
                    print(f"[向量服务] 分块 {i+1}: 相关度={chunk['similarity']:.2f}, 内容长度={len(chunk['content'])}")

                    # 构建这个分块的上下文文本
                    chunk_text = f"[相关度: {chunk['similarity']:.2f}] {chunk['content']}"
                    chunk_length = len(chunk_text) + 2  # +2 for "\n\n"

                    # 检查是否会超过长度限制
                    if current_length + chunk_length > max_context_length:
                        print(f"[向量服务] 达到最大上下文长度限制 ({max_context_length})，停止添加更多内容")
                        break

                    # 添加到上下文
                    context_parts.append(chunk_text)
                    current_length += chunk_length

                    # 获取文档信息
                    doc_statement = select(Document).where(Document.id == chunk['document_id'])
                    document = session.exec(doc_statement).first()

                    if document:
                        # 构建来源信息
                        source = {
                            "documentId": document.id,
                            "documentName": document.filename,
                            "relevance": chunk['similarity'],
                            "excerpt": chunk['content'][:200] + "..." if len(chunk['content']) > 200 else chunk['content']
                        }
                        sources.append(source)

                context = "\n\n".join(context_parts)
                print(f"[向量服务] 生成的上下文长度: {len(context)} (限制: {max_context_length})")
                print(f"[向量服务] 生成的来源数量: {len(sources)}")
                print(f"[向量服务] 实际使用的分块数量: {len(context_parts)} / {len(similar_chunks)}")

                return {"context": context, "sources": sources}

            finally:
                session.close()

        except Exception as e:
            logger.error(f"获取知识上下文失败: {e}")
            print(f"[向量服务] 错误: {e}")
            return {"context": "", "sources": []}

# 全局向量服务实例
vector_service = None

def get_vector_service() -> VectorService:
    """获取向量服务实例"""
    global vector_service
    if vector_service is None:
        vector_service = VectorService()
    return vector_service
