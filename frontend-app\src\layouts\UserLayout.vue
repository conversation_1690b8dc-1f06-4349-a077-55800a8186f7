<template>
  <div class="min-h-screen w-full bg-gray-50 dark:bg-dark-900">
    <!-- 顶部导航栏 -->
    <header
      v-show="!uiStore.focusMode"
      class="bg-white dark:bg-dark-800 shadow-sm border-b border-gray-200 dark:border-dark-700 transition-all duration-300"
    >
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo和导航 -->
          <div class="flex items-center space-x-8">
            <div class="flex-shrink-0">
              <img src="/logo.png" alt="CogniSynth Logo" class="h-10" />
            </div>
            <nav class="hidden md:flex space-x-6">
              <router-link
                v-for="item in navigation"
                :key="item.name"
                :to="item.to"
                class="px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                :class="[
                  $route.path.startsWith(item.to) 
                    ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300' 
                    : 'text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400'
                ]"
              >
                <el-icon class="mr-2">
                  <component :is="iconMap[item.icon]" />
                </el-icon>
                {{ item.name }}
              </router-link>
            </nav>
          </div>

          <!-- 右侧功能区 -->
          <div class="flex items-center space-x-4">
            <!-- 通知中心 -->
            <el-badge :value="notificationCount" :hidden="notificationCount === 0">
              <el-button circle size="large" @click="showNotifications = true">
                <el-icon><Bell /></el-icon>
              </el-button>
            </el-badge>

            <!-- 主题切换 -->
            <el-button circle size="large" @click="toggleTheme">
              <el-icon>
                <Sunny v-if="isDark" />
                <Moon v-else />
              </el-icon>
            </el-button>

            <!-- 用户菜单 -->
            <el-dropdown @command="handleUserMenuCommand">
              <div class="flex items-center space-x-2 cursor-pointer p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-700">
                <el-avatar :size="32" :src="userStore.user?.avatar_url">
                  {{ userStore.user?.username?.charAt(0).toUpperCase() }}
                </el-avatar>
                <span class="hidden md:block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ userStore.user?.display_name || userStore.user?.username }}
                </span>
                <el-icon class="text-gray-400">
                  <ArrowDown />
                </el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">
                    <el-icon><User /></el-icon>
                    个人资料
                  </el-dropdown-item>
                  <el-dropdown-item command="settings">
                    <el-icon><Setting /></el-icon>
                    设置
                  </el-dropdown-item>
                  <el-dropdown-item divided command="logout">
                    <el-icon><SwitchButton /></el-icon>
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
    </header>

    <!-- 主内容区域 -->
    <main :class="isFullScreenPage ? (uiStore.focusMode ? 'h-screen w-full' : 'h-[calc(100vh-4rem)] w-full') : 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8'">
      <router-view />
    </main>

    <!-- 通知抽屉 -->
    <el-drawer
      v-model="showNotifications"
      title="通知中心"
      direction="rtl"
      size="400px"
    >
      <div class="space-y-4">
        <div v-if="notifications.length === 0" class="text-center text-gray-500 py-8">
          暂无通知
        </div>
        <div
          v-for="notification in notifications"
          :key="notification.id"
          class="p-4 bg-gray-50 dark:bg-dark-800 rounded-lg"
        >
          <div class="flex justify-between items-start">
            <div>
              <h4 class="font-medium text-gray-900 dark:text-gray-100">
                {{ notification.title }}
              </h4>
              <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {{ notification.content }}
              </p>
              <span class="text-xs text-gray-400 mt-2 block">
                {{ formatTime(notification.createdAt) }}
              </span>
            </div>
            <el-button size="small" text @click="markAsRead(notification.id)">
              标记已读
            </el-button>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useSettingsStore } from '@/stores/settings'
import { useUIStore } from '@/stores/ui'
import { 
  Bell, 
  Sunny, 
  Moon, 
  User, 
  Setting, 
  SwitchButton, 
  ArrowDown,
  House,
  Collection,
  ChatDotRound,
  Setting as SettingIcon
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const userStore = useAuthStore()
const settingsStore = useSettingsStore()
const uiStore = useUIStore()

// 检测是否是全屏页面（如聊天页面）
const isFullScreenPage = computed(() => {
  return route.name === 'Chat'
})

// 主题相关
const isDark = computed(() => {
  return document.documentElement.classList.contains('dark')
})

const toggleTheme = async () => {
  const currentTheme = settingsStore.userSettings.theme
  let newTheme: 'light' | 'dark' | 'system'

  if (currentTheme === 'light') {
    newTheme = 'dark'
  } else if (currentTheme === 'dark') {
    newTheme = 'system'
  } else {
    newTheme = 'light'
  }

  await settingsStore.applyTheme(newTheme)
}

// 图标映射
const iconMap: Record<string, any> = {
  House,
  Collection,
  ChatDotRound,
  SettingIcon
}

// 导航菜单
const navigation = [
  { name: '首页', to: '/user/home', icon: 'House' },
  { name: '知识库', to: '/user/knowledge-base', icon: 'Collection' },
  { name: '智能对话', to: '/user/chat', icon: 'ChatDotRound' },
  { name: '设置', to: '/user/settings', icon: 'SettingIcon' },
]

// 通知相关
const showNotifications = ref(false)
const notifications = ref([
  {
    id: 1,
    title: '文档处理完成',
    content: '您上传的文档"技术文档.pdf"已处理完成',
    createdAt: new Date().toISOString(),
    read: false
  }
])

const notificationCount = computed(() => 
  notifications.value.filter(n => !n.read).length
)

// 用户菜单处理
const handleUserMenuCommand = (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/user/settings')
      break
    case 'settings':
      router.push('/user/settings')
      break
    case 'logout':
      userStore.logout()
      router.push('/login')
      break
  }
}

// 通知处理
const markAsRead = (id: number) => {
  const notification = notifications.value.find(n => n.id === id)
  if (notification) {
    notification.read = true
  }
}

const formatTime = (time: string) => {
  return new Date(time).toLocaleString('zh-CN')
}
</script>
