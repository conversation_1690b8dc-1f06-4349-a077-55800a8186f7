import{d as e}from"./index-Byt5TjPh.js";const t={getSystemStats:()=>e.get("/admin/stats"),getUsers:t=>e.get("/admin/users",{params:t}),createUser:t=>e.post("/admin/users",t),updateUser:(t,s)=>e.put(`/admin/users/${t}`,s),deleteUser:t=>e.delete(`/admin/users/${t}`),updateUserStatus:(t,s)=>e.put(`/admin/users/${t}/status`,s),getUserQuota:t=>e.get(`/admin/users/${t}/quota`),updateUserQuota:(t,s)=>e.put(`/admin/users/${t}/quota`,s),getAIProviders:()=>e.get("/admin/ai-providers"),createAIProvider:t=>e.post("/admin/ai-providers",t),updateAIProvider:(t,s)=>e.put(`/admin/ai-providers/${t}`,s),deleteAIProvider:t=>e.delete(`/admin/ai-providers/${t}`),getAIModels:t=>e.get("/admin/ai-models",{params:t?{provider_id:t}:void 0}),createAIModel:t=>e.post("/admin/ai-models",t),updateAIModel:(t,s)=>e.put(`/admin/ai-models/${t}`,s),deleteAIModel:t=>e.delete(`/admin/ai-models/${t}`),getLogs:t=>e.get("/admin/logs",{params:t}),getSettings:()=>e.get("/admin/settings"),updateSetting:(t,s)=>e.put(`/admin/settings/${t}`,s)};export{t as b};