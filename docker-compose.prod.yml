services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: ai-knowledge-postgres-prod
    environment:
      POSTGRES_DB: aiknowledgebase
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 111222
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"  # 改为5433端口避免冲突
    networks:
      - ai-network
    restart: unless-stopped

  # Redis向量存储
  redis:
    image: redis:7-alpine
    container_name: ai-knowledge-redis-prod
    command: redis-server --appendonly yes
    volumes:
      - redis_data_prod:/data
    ports:
      - "6379:6379" 
    networks:
      - ai-network
    restart: unless-stopped

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ai-knowledge-backend-prod
    environment:
      DEBUG: false
      ENVIRONMENT: production
      DATABASE_URL: postgresql+asyncpg://postgres:111222@postgres:5432/aiknowledgebase
      REDIS_URL: redis://redis:6379/0
      redis_url: redis://redis:6379/0
      redis_host: redis
      redis_port: 6379
      redis_db: 0
      ALLOWED_ORIGINS: https://aiknowledgebase.csicollege.cn,http://aiknowledgebase.csicollege.cn,https://aiknowledgebase.csicollege.cn:3443,http://aiknowledgebase.csicollege.cn:3000
    depends_on:
      - postgres
      - redis
    networks:
      - ai-network
    restart: unless-stopped

  # 前端Nginx服务
  frontend:
    build:
      context: ./frontend-app
      dockerfile: Dockerfile
    container_name: ai-knowledge-frontend-prod
    ports:
      - "3000:80"    # HTTP端口
      - "3443:443"   # HTTPS端口
    volumes:
      - /www/wwwroot/aiknowledgebase/ssl:/www/wwwroot/aiknowledgebase/ssl:ro
      - ./nginx.prod.conf:/etc/nginx/nginx.conf:ro
    environment:
      - NGINX_HOST=aiknowledgebase.csicollege.cn
    depends_on:
      - backend
    networks:
      - ai-network
    restart: unless-stopped

# 数据卷定义
volumes:
  postgres_data_prod:
    driver: local
  redis_data_prod:
    driver: local

# 网络定义
networks:
  ai-network:
    driver: bridge
