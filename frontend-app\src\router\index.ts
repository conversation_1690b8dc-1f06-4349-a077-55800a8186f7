import { createRouter, createWebHistory, type RouteLocationNormalized, type NavigationGuardNext } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/login'
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/Login.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/test',
      name: 'Test',
      component: () => import('@/views/Test.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/api-test',
      name: 'APITest',
      component: () => import('@/views/APITest.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/chart-demo',
      name: 'ChartDemo',
      component: () => import('@/views/ChartDemo.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/chart-html-demo',
      name: 'ChartHtmlDemo',
      component: () => import('@/views/ChartHtmlDemo.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/test-page',
      name: 'TestPage',
      component: () => import('@/views/TestPage.vue'),
      meta: { requiresAuth: true, requiresAdmin: true }
    },
    {
      path: '/user',
      component: () => import('@/layouts/UserLayout.vue'),
      meta: { requiresAuth: true, role: 'user' },
      children: [
        {
          path: '',
          redirect: '/user/home'
        },
        {
          path: 'home',
          name: 'Home',
          component: () => import('@/views/user/Home.vue')
        },
        {
          path: 'knowledge-base',
          name: 'KnowledgeBase',
          component: () => import('@/views/user/KnowledgeBase.vue')
        },
        {
          path: 'knowledge-base/:id/documents',
          name: 'DocumentList',
          component: () => import('@/views/user/DocumentList.vue')
        },
        {
          path: 'chat',
          name: 'Chat',
          component: () => import('@/views/user/Chat.vue')
        },
        {
          path: 'settings',
          name: 'Settings',
          component: () => import('@/views/user/Settings.vue')
        }
      ]
    },
    {
      path: '/admin',
      component: () => import('@/layouts/AdminLayout.vue'),
      meta: { requiresAuth: true, role: 'admin' },
      children: [
        {
          path: '',
          redirect: '/admin/dashboard'
        },
        {
          path: 'dashboard',
          name: 'AdminDashboard',
          component: () => import('@/views/admin/Dashboard.vue')
        },
        {
          path: 'users',
          name: 'AdminUsers',
          component: () => import('@/views/admin/Users.vue')
        },
        {
          path: 'ai-models',
          name: 'AdminAIModels',
          component: () => import('@/views/admin/AIModels.vue')
        },
        {
          path: 'operation-logs',
          name: 'AdminOperationLogs',
          component: () => import('@/views/admin/OperationLogs.vue')
        },
        {
          path: 'settings',
          name: 'AdminSettings',
          component: () => import('@/views/admin/Settings.vue')
        },
        {
          path: 'logs',
          name: 'AdminLogs',
          component: () => import('@/views/admin/Logs.vue')
        }
      ]
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('@/views/NotFound.vue')
    }
  ]
})

// 路由守卫
router.beforeEach(async (to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) => {
  const authStore = useAuthStore()

  console.log(`🛣️ 路由导航: ${from.path} -> ${to.path}`)

  // 基本重定向
  if (to.path === '/') {
    next('/login')
    return
  }

  // 如果不需要认证，直接通过
  if (!to.meta.requiresAuth) {
    next()
    return
  }

  // 检查是否已登录
  if (!authStore.isAuthenticated) {
    console.log('❌ 用户未认证，重定向到登录页')
    // 如果有token但没有用户信息，尝试获取用户信息
    if (authStore.token) {
      console.log('🔍 发现token，尝试获取用户信息')
      const success = await authStore.fetchUserInfo()
      if (!success) {
        console.log('❌ 获取用户信息失败，清除token')
        authStore.clearAuth()
        next('/login')
        return
      }
    } else {
      next('/login')
      return
    }
  }

  // 检查角色权限
  const requiredRole = to.meta.role as string
  if (requiredRole) {
    console.log(`🔐 检查角色权限: 需要${requiredRole}, 当前用户是${authStore.isAdmin ? 'admin' : 'user'}`)
    console.log('👤 当前用户:', {
      username: authStore.user?.username,
      is_admin: authStore.user?.is_admin
    })

    if (requiredRole === 'admin' && !authStore.isAdmin) {
      console.log('❌ 非管理员用户尝试访问管理员页面')
      next('/user/home')
      return
    }
    if (requiredRole === 'user' && authStore.isAdmin) {
      console.log('❌ 管理员用户尝试访问用户页面')
      next('/admin/dashboard')
      return
    }
  }

  console.log('✅ 路由权限检查通过')
  next()
})

export default router
