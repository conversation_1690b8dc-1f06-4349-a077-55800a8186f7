<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基于RAG技术的个性化教学智能体系统架构</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 24px;
        }
        
        .mermaid {
            text-align: center;
            background: white;
        }
        
        /* 强制所有节点都有柔和的背景色 */
        .mermaid .node rect {
            fill: #f8f9fa !important;
            stroke: #6c757d !important;
            stroke-width: 2px !important;
        }
        
        .mermaid text {
            fill: #2c3e50 !important;
            font-size: 12px !important;
            font-weight: 500 !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>基于RAG技术的个性化教学智能体系统架构</h1>
        
        <div class="mermaid">
            flowchart TD
                %% 用户界面层
                A1[Vue.js + TypeScript + Element Plus UI<br/>用户交互界面 + 智能对话 + 文件上传]
                A2[Chat.js + TG-web SDK + 多端适配<br/>多平台支持 + 实时通信 + 响应式设计]
                
                %% 网关服务层
                B1[Nginx反向代理<br/>负载均衡 + 静态资源]
                B2[CORS跨域处理<br/>请求路由 + 安全策略]
                B3[JWT身份认证<br/>权限验证 + 会话管理]
                
                %% 应用服务层
                C1[用户管理<br/>多角色权限 + 数据统计 + 历史记录]
                C2[智能对话<br/>多轮对话 + 上下文管理 + 意图识别]
                C3[文档处理<br/>多格式解析 + 上传管理 + 历史记录]
                C4[AI核心服务<br/>模型调用 + 参数优化 + 性能监控]
                
                %% AI核心层
                D1[RAG检索系统<br/>FAISS检索 + 语义匹配 + 相似度计算]
                D2[OpenAI GPT-4 + Claude-3 Deepseek<br/>多模型支持 + 模型切换]
                D3[文档解析<br/>支持多种格式 + 智能分块 + 元数据提取]
                D4[向量数据库<br/>Chroma + 向量存储 + 相似度搜索]
                D5[缓存机制<br/>Redis缓存 + 会话存储]
                
                %% 数据持久层
                E1[用户数据 + 对话历史 + 文档元数据<br/>关系型数据 + 事务处理]
                E2[FAISS向量库<br/>文档向量 + 高效检索 + 索引优化]
                E3[企业知识库<br/>多源数据 + 结构化存储 + 版本控制]
                E4[主数据库<br/>MySQL/PostgreSQL + 备份策略 + 性能优化]
                
                %% 连接关系
                A1 --> B1
                A2 --> B1
                B1 --> B2
                B2 --> B3
                B3 --> C1
                B3 --> C2
                B3 --> C3
                B3 --> C4
                C1 --> D5
                C2 --> D1
                C2 --> D2
                C3 --> D3
                C4 --> D1
                C4 --> D2
                D1 --> D4
                D2 --> D5
                D3 --> D4
                C1 --> E4
                C2 --> E4
                C3 --> E4
                D1 --> E2
                D3 --> E3
                D4 --> E2
                D5 --> E4
        </div>
    </div>

    <script>
        // 初始化Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'base',
            themeVariables: {
                primaryColor: '#f8f9fa',
                primaryTextColor: '#2c3e50',
                primaryBorderColor: '#6c757d',
                lineColor: '#6c757d',
                secondaryColor: '#ffffff',
                tertiaryColor: '#f8f9fa',
                background: '#ffffff',
                mainBkg: '#f8f9fa',
                secondBkg: '#ffffff',
                tertiaryBkg: '#f8f9fa'
            },
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            }
        });

        // 渲染完成后强制应用颜色
        setTimeout(() => {
            const rects = document.querySelectorAll('.mermaid rect');
            const colors = [
                '#f8f9fa', '#f1f8e9', '#fff8e1', '#fce4ec', '#e8f5e8',
                '#e3f2fd', '#f3e5f5', '#fff3e0', '#e0f2f1', '#fafafa'
            ];
            
            rects.forEach((rect, index) => {
                const colorIndex = index % colors.length;
                rect.style.fill = colors[colorIndex];
                rect.style.stroke = '#6c757d';
                rect.style.strokeWidth = '2px';
                rect.setAttribute('fill', colors[colorIndex]);
                rect.setAttribute('stroke', '#6c757d');
                rect.setAttribute('stroke-width', '2');
            });
            
            // 确保文字清晰
            const texts = document.querySelectorAll('.mermaid text');
            texts.forEach(text => {
                text.style.fill = '#2c3e50';
                text.style.fontSize = '12px';
                text.style.fontWeight = '500';
            });
        }, 1000);
    </script>
</body>
</html>
